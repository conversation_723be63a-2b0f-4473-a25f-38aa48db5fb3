import openpyxl
from openpyxl.styles import Font, Alignment

# HTTP Status Code Data
http_status_codes = [
    ["Category", "Status Code", "Description"],
    ["1xx Informational Responses", "100",
        "Continue: The server has received the request headers, and the client can proceed to send the request body."],
    ["1xx Informational Responses", "101",
        "Switching Protocols: The requester has asked the server to switch protocols."],
    ["1xx Informational Responses", "102",
        "Processing (WebDAV): The server is processing the request but has not completed it."],
    ["2xx Success", "200", "OK: The request was successful."],
    ["2xx Success", "201",
        "Created: The request was successful, and a new resource was created."],
    ["2xx Success", "202",
        "Accepted: The request has been accepted for processing but is not yet completed."],
    ["2xx Success", "203",
        "Non-Authoritative Information: The response is from a third-party copy."],
    ["2xx Success", "204",
        "No Content: The request was successful, but there's no content to return."],
    ["2xx Success", "205",
        "Reset Content: The client should reset the view (e.g., form fields)."],
    ["2xx Success", "206", "Partial Content: The server is delivering part of the resource."],
    ["3xx Redirection", "300",
        "Multiple Choices: Multiple options for the resource are available."],
    ["3xx Redirection", "301",
        "Moved Permanently: The resource has been permanently moved to a new URI."],
    ["3xx Redirection", "302", "Found: The resource is temporarily located elsewhere."],
    ["3xx Redirection", "303",
        "See Other: The response can be found under another URI using GET."],
    ["3xx Redirection", "304",
        "Not Modified: The resource has not been modified since the last request."],
    ["3xx Redirection", "307",
        "Temporary Redirect: The resource is temporarily located at a different URI."],
    ["3xx Redirection", "308",
        "Permanent Redirect: The resource has been moved permanently."],
    ["4xx Client Errors", "400",
        "Bad Request: The server could not understand the request due to invalid syntax."],
    ["4xx Client Errors", "401", "Unauthorized: Authentication is required."],
    ["4xx Client Errors", "402", "Payment Required: Reserved for future use."],
    ["4xx Client Errors", "403",
        "Forbidden: The server understands the request but refuses to authorize it."],
    ["4xx Client Errors", "404", "Not Found: The requested resource could not be found."],
    ["4xx Client Errors", "405",
        "Method Not Allowed: The HTTP method is not allowed for the resource."],
    ["4xx Client Errors", "406",
        "Not Acceptable: The requested resource is not available in the format specified."],
    ["4xx Client Errors", "407",
        "Proxy Authentication Required: Authentication is required for the proxy."],
    ["4xx Client Errors", "408",
        "Request Timeout: The server timed out waiting for the request."],
    ["4xx Client Errors", "409",
        "Conflict: The request conflicts with the current state of the server."],
    ["4xx Client Errors", "410", "Gone: The resource is no longer available."],
    ["4xx Client Errors", "411",
        "Length Required: The request did not specify the content length."],
    ["4xx Client Errors", "412",
        "Precondition Failed: The server does not meet one of the preconditions."],
    ["4xx Client Errors", "413", "Payload Too Large: The request payload is too large."],
    ["4xx Client Errors", "414",
        "URI Too Long: The URI is too long for the server to process."],
    ["4xx Client Errors", "415",
        "Unsupported Media Type: The media type is not supported."],
    ["4xx Client Errors", "416",
        "Range Not Satisfiable: The requested range cannot be satisfied."],
    ["4xx Client Errors", "417",
        "Expectation Failed: The server cannot meet the expectation in the request header."],
    ["4xx Client Errors", "418",
        "I'm a teapot: An April Fools' joke from RFC 2324 (Hyper Text Coffee Pot Control Protocol)."],
    ["4xx Client Errors", "422",
        "Unprocessable Entity: The request was well-formed but could not be processed."],
    ["4xx Client Errors", "426",
        "Upgrade Required: The client should switch to a different protocol."],
    ["4xx Client Errors", "429",
        "Too Many Requests: The user has sent too many requests in a given timeframe."],
    ["5xx Server Errors", "500",
        "Internal Server Error: A generic error occurred on the server."],
    ["5xx Server Errors", "501",
        "Not Implemented: The server does not support the functionality required."],
    ["5xx Server Errors", "502",
        "Bad Gateway: The server received an invalid response from the upstream server."],
    ["5xx Server Errors", "503",
        "Service Unavailable: The server is temporarily unable to handle the request."],
    ["5xx Server Errors", "504",
        "Gateway Timeout: The server did not receive a timely response from the upstream server."],
    ["5xx Server Errors", "505",
        "HTTP Version Not Supported: The HTTP version used in the request is not supported."],
    ["5xx Server Errors", "507",
        "Insufficient Storage (WebDAV): The server is out of space."],
    ["5xx Server Errors", "508",
        "Loop Detected (WebDAV): The server detected an infinite loop while processing the request."],
    ["5xx Server Errors", "510",
        "Not Extended: Further extensions are required for the server to fulfill the request."],
]

# Create an Excel workbook and add a worksheet
wb = openpyxl.Workbook()
sheet = wb.active
sheet.title = "HTTP Status Codes"

# Write the data to Excel
for row_idx, row_data in enumerate(http_status_codes, start=1):
    for col_idx, value in enumerate(row_data, start=1):
        cell = sheet.cell(row=row_idx, column=col_idx)
        cell.value = value
        # Style the header row
        if row_idx == 1:
            cell.font = Font(bold=True)
            cell.alignment = Alignment(horizontal="center", vertical="center")

# Adjust column width
for col in sheet.columns:
    max_length = max(len(str(cell.value)) for cell in col if cell.value) + 2
    col_letter = col[0].column_letter
    sheet.column_dimensions[col_letter].width = max_length

# Save the Excel file
file_name = "HTTP_Status_Codes.xlsx"
wb.save(file_name)

print(f"Excel file '{file_name}' created successfully!")
