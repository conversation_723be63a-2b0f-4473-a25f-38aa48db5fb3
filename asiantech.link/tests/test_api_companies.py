# Python
from copy import deepcopy
import json
import random
import string
from django.test import TestCase, Client
from requests import post, get, patch
from api.models.black_listed_access_token import UserAccessToken

BASE_URL = 'http://***********:8000/'

access_token = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoiYWNjZXNzIiwiZXhwIjoxNzIyMzE1NDA5LCJpYXQiOjE3MjIyMjkwMDksImp0aSI6ImI4YTgyMTBiNDU4NDQ4MjZiNTc1MzNhMTg1MGZkZDc1IiwidXNlcl9pZCI6NH0.YvgwH_cvnKQpnQCY4QQTf-DYaHN8J5LjVkwO2ECZ1Go'

# Function to generate a random email


def generate_random_email():
    domains = ["gmail.com", "yahoo.com", "outlook.com"]
    username = ''.join(random.choices(
        string.ascii_lowercase + string.digits, k=8))
    domain = random.choice(domains)
    return f"{username}@{domain}"


random_email = generate_random_email()


class URLTest(TestCase):
    # Sample data for testing

    sample_data = {
        "user": {
            "email": random_email,
            "password": "P@ssWorD123",
            "first_name": "<PERSON>",
            "last_name": "string"
        },
        "company": {
            "name": "string",
            "introduction_url": "http://***********:8000/api/swagger/",
            "employees_type": 1,
            "country_code": "VN",
            "address_code": "VN-54",
            "address": "string",
            "tel": "+840397451538",
            "contact_mail": "<EMAIL>"
        }
    }

    def make_invalid_data(self, **invalid_fields):
        data = deepcopy(self.sample_data)
        for key, value in invalid_fields.items():
            keys = key.split('__')
        sub_data = data
        for sub_key in keys[:-1]:
            sub_data = sub_data[sub_key]
            sub_data[keys[-1]] = value
        return data

    def setUp(self):
        self.client = Client()
        self.access_token = access_token
        self.headers = {
            'Authorization': f'Bearer {self.access_token}',
            'Accept-Language': 'en',
            'Accept': 'application/json',
            'Content-Type': 'application/json'
        }

    def test_company_details_url(self):
        url = f"{BASE_URL}/api/company/details/"
        response = get(url, headers=self.headers)

        self.assertEqual(response.status_code, 200)

    def test_update_company_details(self):
        url = f"{BASE_URL}/api/company/details/"
        response = patch(url, data=json.dumps(
            {"name": "ABC"}), headers=self.headers)

        self.assertEqual(response.status_code, 200)
        response_data = response.json()['data']
        self.assertEqual(response_data['name'], 'ABC')

    def test_list_support_company(self):
        url = f"{BASE_URL}/api/company/list-support-company/"
        response = get(url, headers=self.headers)

        self.assertEqual(response.status_code, 200)

    def test_create_company_register(self):
        url = f"{BASE_URL}/api/company/register/"
        response = post(url,
                        data=json.dumps(self.sample_data),
                        headers=self.headers)

        self.assertEqual(response.status_code, 201)

    # Case: invalid email
    def test_create_company_register_with_invalid_email(self):
        url = f"{BASE_URL}/api/company/register/"
        invalid_data = self.make_invalid_data(user__email='user01')
        response = post(url,
                        data=json.dumps(invalid_data),
                        headers=self.headers)

        self.assertEqual(response.status_code, 400)
        response_errors = response.json()['errors']
        print('response_errors', response_errors)
        self.assertEqual(response_errors[0]['message'], 'email')

    # Case: email is registered
    def test_create_company_register_with_email_is_register(self):
        url = f"{BASE_URL}/api/company/register/"
        invalid_data = self.make_invalid_data(user__email='<EMAIL>')
        response = post(url,
                        data=json.dumps(invalid_data),
                        headers=self.headers)

        self.assertEqual(response.status_code, 400)
        response_errors = response.json()['errors']
        print('response_errors', response_errors)
        self.assertEqual(response_errors[0]['message'], 'Email already exists')

    # Case: invalid password length
    def test_create_company_register_with_invalid_password_length(self):
        url = f"{BASE_URL}/api/company/register/"
        invalid_data = self.make_invalid_data(user__password='1234567')
        response = post(url,
                        data=json.dumps(invalid_data),
                        headers=self.headers)

        self.assertEqual(response.status_code, 400)
        response_errors = response.json()['errors']
        print('response_errors', response_errors)
        self.assertEqual(
            response_errors[0]['message'], 'Password length must be between 8 and 20 characters')

    # Case: password must contain at least one uppercase letter
    def test_create_company_register_with_password_must_contain_at_least_one_uppercase_letter(self):
        url = f"{BASE_URL}/api/company/register/"
        invalid_data = self.make_invalid_data(user__password='12345678')
        response = post(url,
                        data=json.dumps(invalid_data),
                        headers=self.headers)

        self.assertEqual(response.status_code, 400)
        response_errors = response.json()['errors']
        print('response_errors', response_errors)
        self.assertEqual(
            response_errors[0]['message'], 'Password must contain at least one uppercase letter')

    # Case: password must contain at least one lowercase letter
    def test_create_company_register_with_password_must_contain_at_least_one_lowercase_letter(self):
        url = f"{BASE_URL}/api/company/register/"
        invalid_data = self.make_invalid_data(user__password='12345678A')
        response = post(url,
                        data=json.dumps(invalid_data),
                        headers=self.headers)

        self.assertEqual(response.status_code, 400)
        response_errors = response.json()['errors']
        print('response_errors', response_errors)
        self.assertEqual(
            response_errors[0]['message'], 'Password must contain at least one lowercase letter')

    # Case: password must contain at least one special character
    def test_create_company_register_with_password_must_contain_at_least_one_special_character(self):
        url = f"{BASE_URL}/api/company/register/"
        invalid_data = self.make_invalid_data(user__password='12345678Aa')
        response = post(url,
                        data=json.dumps(invalid_data),
                        headers=self.headers)

        self.assertEqual(response.status_code, 400)
        response_errors = response.json()['errors']
        print('response_errors', response_errors)
        self.assertEqual(
            response_errors[0]['message'], 'Password must contain at least one special character')

    # Case: invalid introduction_url
    def test_create_company_register_with_invalid_introduction_url(self):
        url = f"{BASE_URL}/api/company/register/"
        invalid_data = self.make_invalid_data(company__introduction_url='123')
        response = post(url,
                        data=json.dumps(invalid_data),
                        headers=self.headers)

        self.assertEqual(response.status_code, 400)
        response_errors = response.json()['errors']
        print('response_errors', response_errors)
        self.assertEqual(response_errors[0]['message'], 'introduction_url')

    # Case: invalid country_code
    def test_create_company_register_with_invalid_country_code(self):
        url = f"{BASE_URL}/api/company/register/"
        invalid_data = self.make_invalid_data(company__country_code='123')
        response = post(url,
                        data=json.dumps(invalid_data),
                        headers=self.headers)

        self.assertEqual(response.status_code, 400)
        response_errors = response.json()['errors']
        print('response_errors', response_errors)
        self.assertEqual(response_errors[0]
                         ['message'], 'Invalid country code.')

    # Case: invalid address_code
    def test_create_company_register_with_invalid_address_code(self):
        url = f"{BASE_URL}/api/company/register/"
        invalid_data = self.make_invalid_data(company__address_code='123')
        response = post(url,
                        data=json.dumps(invalid_data),
                        headers=self.headers)

        self.assertEqual(response.status_code, 400)
        response_errors = response.json()['errors']
        print('response_errors', response_errors)
        self.assertEqual(response_errors[0]
                         ['message'], 'Invalid address code.')

    # Case:Invalid phone number.
    def test_create_company_register_with_invalid_phone_number(self):
        url = f"{BASE_URL}/api/company/register/"
        invalid_data = self.make_invalid_data(company__tel='+843546346adsd')
        response = post(url,
                        data=json.dumps(invalid_data),
                        headers=self.headers)

        self.assertEqual(response.status_code, 400)
        response_errors = response.json()['errors']
        print('response_errors', response_errors)
        self.assertEqual(response_errors[0]
                         ['message'], 'Invalid phone number.')

    # Case:Invalid phone number format.
    def test_create_company_register_with_invalid_phone_number_format(self):
        url = f"{BASE_URL}/api/company/register/"
        invalid_data = self.make_invalid_data(company__tel='840397')
        response = post(url,
                        data=json.dumps(invalid_data),
                        headers=self.headers)

        self.assertEqual(response.status_code, 400)
        response_errors = response.json()['errors']
        print('response_errors', response_errors)
        self.assertEqual(
            response_errors[0]['message'], 'Invalid phone number format.')
