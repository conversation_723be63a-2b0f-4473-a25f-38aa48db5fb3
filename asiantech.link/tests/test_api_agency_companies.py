# Python
from django.test import TestCase, Client
from requests import delete, post, get
from api.models.black_listed_access_token import UserAccessToken

BASE_URL = 'http://162.43.54.6:8000/'

access_token = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoiYWNjZXNzIiwiZXhwIjoxNzIyNTkxNjgwLCJpYXQiOjE3MjI1MDUyODAsImp0aSI6ImM0NjViZjgyZmU2ODQzM2VhY2M2NmZlM2NhMmRhMTg1IiwidXNlcl9pZCI6ODR9.6Zt57d5a_XtNE2vyCmFxo27hvUj8fXM0rkMrjJ1o2Cg'


class URLTest(TestCase):
    def setUp(self):
        self.client = Client()
        self.access_token = access_token

    # API list-agency-company
    def test_agency_company_list(self):
        url = f"{BASE_URL}/api/company/list-agency-company/"
        response = get(url, headers={
            'Authorization': str('Bearer '+self.access_token)
        })

        self.assertEqual(response.status_code, 200)

    def test_agency_company_list_with_contact_mail(self):
        url = f"{BASE_URL}/api/company/list-agency-company/"
        contact_mail = '<EMAIL>'
        response = get(url, 
                    headers={'Authorization': str('Bearer '+self.access_token)},
                    params={'contact_mail': contact_mail}

        )

        self.assertEqual(response.status_code, 200)
        response_data = response.json()['data']
      
        for item in response_data:
            self.assertEqual(item['contact_mail'], contact_mail)

    def test_agency_company_list_with_contact_mail_do_not_match(self):
        url = f"{BASE_URL}/api/company/list-agency-company/"
        contact_mail = '<EMAIL>'
        response = get(url, 
                    headers={'Authorization': str('Bearer '+self.access_token)},
                    params={'contact_mail': contact_mail}

        )

        self.assertEqual(response.status_code, 200)
        response_data = response.json()['data']
       
        for item in response_data:
            self.assertNotEqual(item['contact_mail'], contact_mail)

    # API get-list-of-select-agency-companies
    def test_get_list_of_select_agency_companies(self):
        url = f"{BASE_URL}/api/company/get-list-of-select-agency-companies/"
        response = get(url, headers={
            'Authorization': str('Bearer '+self.access_token)
        })

        self.assertEqual(response.status_code, 200)
    
    # API add-agency-company
    def test_agency_company_not_found(self):
        url = f"{BASE_URL}/api/company/add-agency-company/"
        data = {'company_id': 0,}
        response = post(url, 
                       data=data,
                       headers={
            'Authorization': str('Bearer '+self.access_token)
        })
  
        response_data = response.json()
        self.assertEqual(response.status_code, 404)
        self.assertEqual(response_data['message'], 'Agency company not found.')

    def test_add_agency_company(self):
        url = f"{BASE_URL}/api/company/add-agency-company/"
        data = {'company_id': 1,}
        response = post(url, 
                       data=data,
                       headers={
            'Authorization': str('Bearer '+self.access_token)
        })
  
        response_data = response.json()
        
        if response_data['message'] == 'Success':
            print('case 1: add new or deleted agency company')
            self.assertEqual(response.status_code, 200)
        else:
            response_data['message'] == 'This agency company is already linked with the engineer.'
            self.assertEqual(response.status_code, 400)
        
    # API delete-agency-company
    def test_delete_agency_company(self):
        url = f"{BASE_URL}/api/company/delete-agency-company/"
        data = {'company_id': 5,}
        response = delete(url, 
                       data=data,
                       headers={
            'Authorization': str('Bearer '+self.access_token)
        }) 
        response_data = response.json()
    
        if(response.status_code == 200): 
            print('case 1')            
            self.assertEqual(response_data['message'], 'Agency company successfully deleted.')
        elif (response.status_code == 409):
            print('case 2')    
            self.assertEqual(response_data['message'], 'This agency company has already been deleted.')
        elif (response.status_code == 404):
            print('case 3')    
            self.assertEqual(response_data['message'], 'Agency company not found.')
        else:
            print('case 4')    
            self.assertEqual(response_data['message'], 'This agency company is not linked with the engineer.')


    
