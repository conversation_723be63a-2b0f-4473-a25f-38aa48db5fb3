# Python
from django.test import TestCase, Client
from requests import get, patch, put
from api.models.black_listed_access_token import UserAccessToken

BASE_URL = 'http://162.43.54.6:8000/'

access_token = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoiYWNjZXNzIiwiZXhwIjoxNzIyMzE1NDA5LCJpYXQiOjE3MjIyMjkwMDksImp0aSI6ImI4YTgyMTBiNDU4NDQ4MjZiNTc1MzNhMTg1MGZkZDc1IiwidXNlcl9pZCI6NH0.YvgwH_cvnKQpnQCY4QQTf-DYaHN8J5LjVkwO2ECZ1Go'


class URLTest(TestCase):
    def setUp(self):
        self.client = Client()
        self.access_token = access_token

    def test_user_profile_url(self):
        url = f"{BASE_URL}/api/profile/"
        response = get(url, headers={
            'Authorization': str('Bearer '+self.access_token)
        })

        self.assertEqual(response.status_code, 200)

    def test_update_profile(self):
        url = f"{BASE_URL}/api/profile/"
        response = patch(url, data={
            "first_name": "ABC",
        }, headers={
            'Authorization': str('Bearer '+self.access_token)
        })

        self.assertEqual(response.status_code, 200)
        response_data = response.json()['data']
        self.assertEqual(response_data['first_name'], 'ABC')

    def test_update_password(self):
        url = f"{BASE_URL}/api/profile/update-password/"
        response = put(url, data={
            "current_password": "P@ssWorD123a",
            "new_password": "P@ssWorD123",
        }, headers={
            'Authorization': str('Bearer '+self.access_token),
            'Accept-Language': 'en'
        })

        self.assertEqual(response.status_code, 200)
        response = response.json()
        self.assertEqual(response['message'], 'Password updated successfully.')
