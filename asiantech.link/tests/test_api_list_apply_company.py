# Python
from django.test import TestCase, Client
from requests import delete, post, get
from api.models.black_listed_access_token import UserAccessToken

BASE_URL = 'http://162.43.54.6:8000/'

access_token = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoiYWNjZXNzIiwiZXhwIjoxNzI0MjExNDkyLCJpYXQiOjE3MjQxMjUwOTIsImp0aSI6ImQwODI2ZWZjZjIzZTRkNjU5Y2QxZWFiMGNkZDViNmU4IiwidXNlcl9pZCI6NDh9.dwxTlbGoIb6m9jQ0uxQiosnVamTbvjVuqetzJ_TNj7w'


class URLTest(TestCase):
    def setUp(self):
        self.client = Client()
        self.access_token = access_token
        self.headers = {
            'Authorization': str('Bearer '+self.access_token)
        }

    # API list-apply-company
    def test_apply_company_list(self):
        url = f"{BASE_URL}/api/company/list-apply-company/"
        response = get(url, headers=self.headers)

        self.assertEqual(response.status_code, 200)

    # Get list no filter
    def test_list_apply_company_with_no_filter(self):
        url = f"{BASE_URL}/api/company/list-apply-company/"
        response = get(url,headers=self.headers)

        self.assertEqual(response.status_code, 200)

    # Get list with filter by ordering
    def test_list_apply_company_with_filter_by_asc_ordering(self):
        url = f"{BASE_URL}/api/company/list-apply-company/"
        params = {
            'ordering': 'progress_update_datetime'
        }
        response = get(url, headers=self.headers, params=params)
        self.assertEqual(response.status_code, 200)

        results = response.json()['data']['results']
        progress_update_datetimes = [item['progress_update_datetime'] for item in results]
        # Check if the progress_update_datetimes are sorted in ascending order
        self.assertEqual(progress_update_datetimes, sorted(progress_update_datetimes))

    def test_list_apply_company_with_filter_by_desc_ordering(self):
        url = f"{BASE_URL}/api/company/list-apply-company/"
        params = {
            'ordering': '-progress_update_datetime'
        }
        response = get(url, headers=self.headers, params=params)
        self.assertEqual(response.status_code, 200)

        results = response.json()['data']['results']
        progress_update_datetimes = [item['progress_update_datetime'] for item in results]
        # Check if the progress_update_datetimes are sorted in descending order
        self.assertEqual(progress_update_datetimes, sorted(progress_update_datetimes, reverse=True))

    def test_list_apply_company_with_filter_by_invalid_ordering(self):
        url = f"{BASE_URL}/api/company/list-apply-company/"
        params = {
            'ordering': 'progress_update_datetimes'
        }
        response = get(url, headers=self.headers, params=params)
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.json()['data'], [])
        self.assertEqual(response.json()['message'], "Invalid ordering field.")

    # Get list with filter by recruit_progress_code
    def test_list_apply_company_with_filter_by_recruit_progress_code_0(self):
        url = f"{BASE_URL}/api/company/list-apply-company/"
        params = {
            'apply_status_filter_code': 0
        }
        response = get(url, headers=self.headers, params=params)
        self.assertEqual(response.status_code, 200)
    
    def test_list_apply_company_with_filter_by_recruit_progress_code_1(self):
        url = f"{BASE_URL}/api/company/list-apply-company/"
        params = {
            'apply_status_filter_code': 1
        }
        response = get(url, headers=self.headers, params=params)
        self.assertEqual(response.status_code, 200)
        
        results = response.json()['data']['results']
        if results:
            valid_code = {50, 60}
            for item in results:
                self.assertIn(item['recruit_progress_code'], valid_code)
        else:
            self.assertEqual(results, [])

    def test_list_apply_company_with_filter_by_recruit_progress_code_2(self):
        url = f"{BASE_URL}/api/company/list-apply-company/"
        params = {
            'apply_status_filter_code': 2
        }
        response = get(url, headers=self.headers, params=params)
        self.assertEqual(response.status_code, 200)
        
        results = response.json()['data']['results']
        if results:
            valid_code = {30, 31, 32, 40}
            for item in results:
                self.assertIn(item['recruit_progress_code'], valid_code)
        else:
            self.assertEqual(results, [])

    def test_list_apply_company_with_filter_by_recruit_progress_code_3(self):
        url = f"{BASE_URL}/api/company/list-apply-company/"
        params = {
            'apply_status_filter_code': 3
        }
        response = get(url, headers=self.headers, params=params)
        self.assertEqual(response.status_code, 200)
        
        results = response.json()['data']['results']
        if results:
            for item in results:
                self.assertIn(item['recruit_progress_code'], {20})
        else:
            self.assertEqual(results, [])
    
    def test_list_apply_company_with_filter_by_recruit_progress_code_4(self):
        url = f"{BASE_URL}/api/company/list-apply-company/"
        params = {
            'apply_status_filter_code': 4
        }
        response = get(url, headers=self.headers, params=params)
        self.assertEqual(response.status_code, 200)
        
        results = response.json()['data']['results']
        if results:
            for item in results:
                self.assertIn(item['recruit_progress_code'], {1})
        else:
            self.assertEqual(results, [])

    def test_list_apply_company_with_filter_by_recruit_progress_code_5(self):
        url = f"{BASE_URL}/api/company/list-apply-company/"
        params = {
            'apply_status_filter_code': 5
        }
        response = get(url, headers=self.headers, params=params)
        self.assertEqual(response.status_code, 200)
        
        results = response.json()['data']['results']
        if results:
            valid_code = {90, 91, 92}
            for item in results:
                self.assertIn(item['recruit_progress_code'], valid_code)
        else:
            self.assertEqual(results, [])

    def test_list_apply_company_with_filter_by_invalid_recruit_progress_code(self):
        url = f"{BASE_URL}/api/company/list-apply-company/"
        params = {
            'apply_status_filter_code': 7
        }
        response = get(url, headers=self.headers, params=params)
        self.assertEqual(response.status_code, 400)
        
        errors = response.json()['errors']
        self.assertEqual(errors[0]['message'], "Invalid recruit_progress_code")
        

    # Get list with filter by page_size
    def test_list_apply_company_with_filter_by_page_size(self):
        url = f"{BASE_URL}/api/company/list-apply-company/"
        params = {
            'page_size': 2
        }
        response = get(url, headers=self.headers, params=params)
        self.assertEqual(response.status_code, 200)

        results = response.json()['data']['results']
        self.assertEqual(len(results), params['page_size'])