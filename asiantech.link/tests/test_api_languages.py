# Python
from inspect import _empty
from django.test import TestCase, Client
from requests import get
from api.models.black_listed_access_token import UserAccessToken

BASE_URL = 'http://162.43.54.6:8000/'

access_token = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoiYWNjZXNzIiwiZXhwIjoxNzIyMzE1NDA5LCJpYXQiOjE3MjIyMjkwMDksImp0aSI6ImI4YTgyMTBiNDU4NDQ4MjZiNTc1MzNhMTg1MGZkZDc1IiwidXNlcl9pZCI6NH0.YvgwH_cvnKQpnQCY4QQTf-DYaHN8J5LjVkwO2ECZ1Go'


class URLTest(TestCase):
    def setUp(self):
        self.client = Client()
        self.access_token = access_token

    def test_current_language_url_is_en(self):
        url = f"{BASE_URL}/api/language/current_language/"
        response = get(
            url,
            headers={
                'Authorization': f'Bearer {self.access_token}',
                'Accept-Language': 'en',
                'Content-Type': 'application/json'
            }
        )

        self.assertEqual(response.status_code, 200)
        response_data = response.json()['data']
        print('response_data', response_data)
        self.assertEqual(response_data['message'], 'Current language is: en')

    # def test_current_language_url_is_empty(self):
    #     url = f"{BASE_URL}/api/language/current_language/"
    #     response = get(
    #         url,
    #         headers={
    #             'Authorization': f'Bearer {self.access_token}',
    #             'Accept-Language': '',
    #             'Content-Type': 'application/json'
    #         }
    #     )

    #     self.assertEqual(response.status_code, 200)
    #     response_data = response.json()['data']
    #     print('response_data', response_data)
    #     self.assertEqual(response_data['message'], 'Current language is: en')

    def test_current_language_url_is_ja(self):
        url = f"{BASE_URL}/api/language/current_language/"
        response = get(
            url,
            headers={
                'Authorization': f'Bearer {self.access_token}',
                'Accept-Language': 'ja',
                'Content-Type': 'application/json'
            }
        )

        self.assertEqual(response.status_code, 200)
        response_data = response.json()['data']
        print('response_data', response_data)
        self.assertEqual(response_data['message'], '現在の言語は: ja')

    def test_current_language_url_is_not_supported(self):
        url = f"{BASE_URL}/api/language/current_language/"
        response = get(
            url,
            headers={
                'Authorization': f'Bearer {self.access_token}',
                'Accept-Language': 'not support',
                'Content-Type': 'application/json'
            }
        )

        self.assertEqual(response.status_code, 200)
        response_data = response.json()['data']
        print('response_data', response_data)
        self.assertEqual(response_data['message'], '現在の言語は: ja')

    def test_support_language(self):
        url = f"{BASE_URL}/api/language/support_language/"
        response = get(url, headers={
            'Authorization': str('Bearer '+self.access_token)
        })

        self.assertEqual(response.status_code, 200)
        response_data = response.json()['data']
        print('response_data', response_data)
        self.assertEqual(response_data['support'], ['en', 'ja', 'my', 'id', 'ne'])

    
