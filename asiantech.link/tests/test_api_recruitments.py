# Python
from copy import deepcopy
import json
from django.test import TestCase, Client
from requests import delete, post, get
import urllib.parse
from api.models.black_listed_access_token import UserAccessToken

BASE_URL = 'http://162.43.54.6:8000/'

access_token = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoiYWNjZXNzIiwiZXhwIjoxNzIyMzE1NDA5LCJpYXQiOjE3MjIyMjkwMDksImp0aSI6ImI4YTgyMTBiNDU4NDQ4MjZiNTc1MzNhMTg1MGZkZDc1IiwidXNlcl9pZCI6NH0.YvgwH_cvnKQpnQCY4QQTf-DYaHN8J5LjVkwO2ECZ1Go'

class URLTest(TestCase):
    # Sample data for testing
    sample_data = {
        "catch_copy": "string",
        "content": "string",
        "job_code": "str",
        "employ_code": "str",
        "place_code1": "VN-54",
        "place_code2": "VN-54",
        "place_code3": "VN-54",
        "payroll_price_from": "20",
        "payroll_price_to": "30",
        "country_code": "VN",
        "age_from": 20,
        "age_to": 30,
        "sex_type": 1,
        "pref_code1": "VN-54",
        "pref_code2": "VN-54",
        "pref_code3": "VN-54",
        "payroll_code": "VND",
        "last_academic_code": "str",
        "language_code1": "st",
        "language_level_type1": 1,
        "language_code2": "st",
        "language_level_type2": 2,
        "experienced_job_code": "str",
        "years_of_experience": 3,
        "skill_job_code1": "str",
        "skill_code1": "str",
        "skill_level_type1": 1,
        "skill_job_code2": "st",
        "skill_code2": "str",
        "skill_level_type2": 2,
        "skill_job_code3": "st",
        "skill_code3": "str",
        "skill_level_type3": 3,
        "licence_code1": "str",
        "licence_point1": 80,
        "licence_code2": "str",
        "licence_point2": 90,
        "licence_code3": "str",
        "licence_point3": 100,
        "start_date": "2024-07-25T06:26:24.861Z",
        "end_date": "2024-07-25T06:26:24.861Z",
        "support_company": 3
    }
    def make_invalid_data(self, **invalid_fields):
        data = deepcopy(self.sample_data)
        for key, value in invalid_fields.items():
            data[key] = value
        return data

    def setUp(self):
        self.client = Client()
        self.access_token = access_token
        self.headers = {
            'Authorization': f'Bearer {self.access_token}',
            'Accept-Language': 'en',
            'Accept': 'application/json',
            'Content-Type': 'application/json' 
        }
    
    def test_create_recruitment(self):
        url = f"{BASE_URL}/api/recruitment/create/"
        response = post(url, 
                        data=json.dumps(self.sample_data),
                        headers=self.headers)
                        
        self.assertEqual(response.status_code, 201)

    # Case: invalid payroll_price_from and payroll_price_to
    def test_create_recruitment_with_invalid_payroll_price_from_and_payroll_price_to(self):
        url = f"{BASE_URL}/api/recruitment/create/"
        invalid_data = self.make_invalid_data(payroll_price_from='string', payroll_price_to='10000000000')
        response = post(url, 
                        data=json.dumps(invalid_data),
                        headers=self.headers)                     

        self.assertEqual(response.status_code, 400)
        response_errors = response.json()['errors']
        print('response_errors', response_errors)
        self.assertEqual(response_errors[0]['message'], 'A valid number is required.')
        self.assertEqual(response_errors[0]['field'], 'payroll_price_from')
        self.assertEqual(response_errors[1]['message'], 'Ensure that there are no more than 10 digits in total.')
        self.assertEqual(response_errors[1]['field'], 'payroll_price_to')
    
    # Case: payroll_price_from must be less than payroll_price_to
    def test_create_recruitment_with_payroll_price_from_larger_than_payroll_price_to(self):
        url = f"{BASE_URL}/api/recruitment/create/"
        invalid_data = self.make_invalid_data(payroll_price_from='40000', payroll_price_to='30000')
        response = post(url, 
                        data=json.dumps(invalid_data),
                        headers=self.headers)
                        
        self.assertEqual(response.status_code, 400)
        response_errors = response.json()['errors']
        print('response_errors', response_errors)
        self.assertEqual(response_errors[0]['message'], 'payroll_price_from must be less than payroll_price_to')
        self.assertEqual(response_errors[0]['field'], 'payroll_price_from')
    
    # Case: invalid payroll_price_from, payroll_price_to format
    def test_create_recruitment_with_invalid_payroll_price_about_digit(self):
        url = f"{BASE_URL}/api/recruitment/create/"
        invalid_data = self.make_invalid_data(payroll_price_from='40000000')
        response = post(url, 
                        data=json.dumps(invalid_data),
                        headers=self.headers)
                        
        self.assertEqual(response.status_code, 400)
        response_errors = response.json()['errors']
        print('response_errors', response_errors)
        self.assertEqual(response_errors[0]['message'], 'Ensure that there are no more than 7 digits before the decimal point.')
        self.assertEqual(response_errors[0]['field'], 'payroll_price_from')

    def test_create_recruitment_with_payroll_price_about_data_type(self):
        url = f"{BASE_URL}/api/recruitment/create/"
        invalid_data = self.make_invalid_data(payroll_price_from='40.000.000')
        response = post(url, 
                        data=json.dumps(invalid_data),
                        headers=self.headers)
                        
        self.assertEqual(response.status_code, 400)
        response_errors = response.json()['errors']
        print('response_errors', response_errors)
        self.assertEqual(response_errors[0]['message'], 'A valid number is required.')
        self.assertEqual(response_errors[0]['field'], 'payroll_price_from')
    
    # Case: invalid support_company
    def test_create_recruitment_with_invalid_support_company(self):
        url = f"{BASE_URL}/api/recruitment/create/"
        invalid_data = self.make_invalid_data(support_company='0')
        response = post(url, 
                        data=json.dumps(invalid_data),
                        headers=self.headers)
                        
        self.assertEqual(response.status_code, 400)
        response_errors = response.json()['errors']
        print('response_errors', response_errors)
        self.assertEqual(response_errors[0]['message'], 'Invalid pk "0" - object does not exist.')

    # Case: invalid sex_type
    def test_create_recruitment_with_invalid_sex_type(self):
        url = f"{BASE_URL}/api/recruitment/create/"
        invalid_data = self.make_invalid_data(sex_type = 16)
        response = post(url, 
                        data=json.dumps(invalid_data),
                        headers=self.headers)                  

        self.assertEqual(response.status_code, 400)
        response_errors = response.json()['errors']
        print('response_errors', response_errors)
        self.assertEqual(response_errors[0]['message'], 'Invalid sex type')
    
    # Case: invalid country_code
    def test_create_recruitment_with_invalid_country_code(self):
        url = f"{BASE_URL}/api/recruitment/create/"
        invalid_data = self.make_invalid_data(country_code = 'ABC')
        response = post(url, 
                        data=json.dumps(invalid_data),
                        headers=self.headers)
                        
        self.assertEqual(response.status_code, 400)
        response_errors = response.json()['errors']
        print('response_errors', response_errors)
        self.assertEqual(response_errors[0]['message'], 'Invalid country code.')
    
    # Case: invalid pref_code
    def test_create_recruitment_with_invalid_pref_code(self):
        url = f"{BASE_URL}/api/recruitment/create/"
        invalid_data = self.make_invalid_data(pref_code1 = 'VN-11')
        response = post(url, 
                        data=json.dumps(invalid_data),
                        headers=self.headers)
                        
        self.assertEqual(response.status_code, 400)
        response_errors = response.json()['errors']
        print('response_errors', response_errors)
        self.assertEqual(response_errors[0]['message'], 'Invalid pref code.')
        self.assertEqual(response_errors[0]['field'], 'pref_code1')

    # Case: invalid place_code
    def test_create_recruitment_with_invalid_place_code(self):
        url = f"{BASE_URL}/api/recruitment/create/"
        invalid_data = self.make_invalid_data(place_code2 = 'VN-11')
        response = post(url, 
                        data=json.dumps(invalid_data),
                        headers=self.headers)                  

        self.assertEqual(response.status_code, 400)
        response_errors = response.json()['errors']
        print('response_errors', response_errors)
        self.assertEqual(response_errors[0]['message'], 'Invalid address code.')
        self.assertEqual(response_errors[0]['field'], 'place_code2')

    # Case: age_from must be less than age_to
    def test_create_recruitment_with_invalid_age_from_and_age_to(self):
        url = f"{BASE_URL}/api/recruitment/create/"
        invalid_data = self.make_invalid_data(age_from = 30, age_to = 20)
        response = post(url, 
                        data=json.dumps(invalid_data),
                       headers=self.headers)
                        
        self.assertEqual(response.status_code, 400)
        response_errors = response.json()['errors']
        print('response_errors', response_errors)
        self.assertEqual(response_errors[0]['message'], 'age_from must be less than age_to')
       
    # Case: invalid payroll_code
    def test_create_recruitment_with_invalid_payroll_code(self):
        url = f"{BASE_URL}/api/recruitment/create/"
        invalid_data = self.make_invalid_data(payroll_code = 'ABC')
        response = post(url, 
                        data=json.dumps(invalid_data),
                        headers=self.headers)
                        
        self.assertEqual(response.status_code, 400)
        response_errors = response.json()['errors']
        print('response_errors', response_errors)
        self.assertEqual(response_errors[0]['message'], 'Invalid currency code.')

    # Case: delete recruitment
    def test_delete_recruitment(self):
        url = f"{BASE_URL}/api/recruitment/delete/"
        response = delete(url,  data=json.dumps({"recruit_id": "25"}), headers=self.headers)
        
        if(response.status_code == 200):             
            self.assertEqual(response.status_code, 200)
            self.assertEqual(response.json()['message'], 'Success')
        else:
            self.assertEqual(response.status_code, 404)
            self.assertEqual(response.json()['message'], 'Recruitment does not exist')

    # Case: get recruitment explore with param show_old_post
    # def test_get_recruitment_explore_with_param_show_old_post(self):
    #     url = f"{BASE_URL}/api/recruitment/explore/"
    #     params = {
    #         'show_old_post': 'false',
    #     }
    #     response = get(url, headers=self.headers, params=params)
        
    #     self.assertEqual(response.status_code, 200)
    #     response_data = response.json()
        
    #     if params['show_old_post'] == 'false':
    #         current_time = datetime.now(timezone.utc)
    #         for recruit in response_data['data']['results']:
    #             end_date = datetime.fromisoformat(recruit['end_date'].replace("Z", "+00:00"))
    #             self.assertGreater(end_date, current_time)
    
    # Case: get recruitment explore with param page_size
    def test_get_recruitment_explore_with_param_page_size(self):
        url = f"{BASE_URL}/api/recruitment/explore/"
        params = {
            'page_size': 5,
        }
        response = get(url, headers=self.headers, params=params)
        
        self.assertEqual(response.status_code, 200)
        response_data = response.json()
        results = response_data['data']['results']
        self.assertLessEqual(len(results), params['page_size'])

    # Case: get recruitment explore with param ordering
    def test_get_recruitment_explore_with_param_ordering(self):
        url = f"{BASE_URL}/api/recruitment/explore/"
        params = {
            'ordering': 'payroll_price_from',
        }
        response = get(url, headers=self.headers, params=params)
        
        self.assertEqual(response.status_code, 200)
        response_data = response.json()
        results = response_data['data']['results']
        payroll_prices = [float(recruit['payroll_price_from'].replace(',', '.')) for recruit in results]
        self.assertEqual(payroll_prices, sorted(payroll_prices))
    
    # Case: get recruitment explore with param cursor
    def test_get_recruitment_explore_with_param_cursor(self):
        url = f"{BASE_URL}/api/recruitment/explore/"
        params = {
            'page_size': 2,
            'show_old_post': True
        }
        response = get(url, headers=self.headers, params=params)       
        self.assertEqual(response.status_code, 200)        
        response_data = response.json()['data']

        # get cursor from response
        next_url = response_data.get('next')  
        if next_url:
            cursor = next_url.split('cursor=')[1].split('&')[0]
            decoded_cursor = urllib.parse.unquote(cursor)
            print('decoded_cursor', decoded_cursor)
            params['cursor'] = decoded_cursor
            print('params1324432', params)
            response = get(url, headers=self.headers, params=params)
           
            response_data = response.json()
            self.assertEqual(response.status_code, 200)
        else:
            print('No cursor')

    # Case: get recruitment list-recruits-loaded
    def test_get_recruitment_list_recruits_loaded(self):
        url = f"{BASE_URL}/api/recruitment/list-recruits-loaded/" 
        response = get(url, headers=self.headers)
        
        self.assertEqual(response.status_code, 200)

    # Case: get recruitment detail
    def test_get_recruitment_detail(self):
        recruitment_id  = 5
        url = f"{BASE_URL}/api/recruitment/{recruitment_id}/"
        response = get(url, headers=self.headers)
       
        if(response.status_code == 200):             
            self.assertEqual(response.status_code, 200)
            self.assertEqual(response.json()['message'], 'Success')
        else:
            self.assertEqual(response.status_code, 404)
            self.assertEqual(response.json()['message'], 'Recruitment does not exist')

        
        
    
    
        
        
        
        
