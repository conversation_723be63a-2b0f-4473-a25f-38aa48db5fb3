# Python
from inspect import _empty
import os
from django.test import TestCase, Client
import requests
from api.models.black_listed_access_token import UserAccessToken

BASE_URL = 'http://162.43.54.6:8000/'

access_token = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoiYWNjZXNzIiwiZXhwIjoxNzIyMzE1NDA5LCJpYXQiOjE3MjIyMjkwMDksImp0aSI6ImI4YTgyMTBiNDU4NDQ4MjZiNTc1MzNhMTg1MGZkZDc1IiwidXNlcl9pZCI6NH0.YvgwH_cvnKQpnQCY4QQTf-DYaHN8J5LjVkwO2ECZ1Go'

file_sample = 'tests\media\cat.jpeg'
file_error = 'tests/media/test_media.docx'

class URLTest(TestCase):
    def setUp(self):
        self.client = Client()
        self.access_token = access_token
        self.headers = {
            'Authorization': f'Bearer {self.access_token}',
            'Accept-Language': 'en',
            'Accept': 'application/json',
        }

    #case: upload avatar image
    def test_media_upload_avatar_image(self):
        url = f"{BASE_URL}/api/media/upload-avatar/"

        if not os.path.exists(file_sample):
            raise FileNotFoundError(f"File not found: {file_sample}")
        
        with open(file_sample, 'rb') as img:
            files = {'image': ('cat.jpeg', img, 'image/jpeg')}
            headers = self.headers
            response = requests.post(url, files=files, headers=headers)

        self.assertEqual(response.status_code, 200)
    
    #case: invalid image file for upload avatar
    def test_invalid_image_file_for_upload_avatar(self):
        url = f"{BASE_URL}/api/media/upload-avatar/"

        if not os.path.exists(file_error):
            raise FileNotFoundError(f"File not found: {file_error}")
        
        with open(file_error, 'rb') as file:
            files = {'image': ('test_media.docx', file, 'application/vnd.openxmlformats-officedocument.wordprocessingml.document')}
            headers = self.headers
            response = requests.post(url, files=files, headers=headers)
            
        response_errors = response.json()['errors']
        print('response_errors', response_errors)
        self.assertEqual(response.status_code, 400)
        self.assertEqual(response_errors[0]['message'], 'Upload a valid image. The file you uploaded was either not an image or a corrupted image.')
    
    #case: delete avatar
    def test_delete_avatar(self):
        url = f"{BASE_URL}/api/media/upload-avatar/"
        response = requests.delete(url, headers=self.headers)
       
        self.assertEqual(response.status_code, 200)

    #case: upload company logo
    def test_media_upload_company_logo(self):
        url = f"{BASE_URL}/api/media/upload-company-logo/"

        if not os.path.exists(file_sample):
            raise FileNotFoundError(f"File not found: {file_sample}")
        
        with open(file_sample, 'rb') as img:
            files = {'image': ('cat.jpeg', img, 'image/jpeg')}
            headers = self.headers
            response = requests.post(url, files=files, headers=headers)

        self.assertEqual(response.status_code, 200)
    
    #case: invalid image file for upload company logo
    def test_invalid_image_file_for_upload_company_logo(self):
        url = f"{BASE_URL}/api/media/upload-avatar/"

        if not os.path.exists(file_error):
            raise FileNotFoundError(f"File not found: {file_error}")
        
        with open(file_error, 'rb') as file:
            files = {'image': ('test_media.docx', file, 'application/vnd.openxmlformats-officedocument.wordprocessingml.document')}
            headers = self.headers
            response = requests.post(url, files=files, headers=headers)
            
        response_errors = response.json()['errors']
        print('response_errors', response_errors)
        self.assertEqual(response.status_code, 400)
        self.assertEqual(response_errors[0]['message'], 'Upload a valid image. The file you uploaded was either not an image or a corrupted image.')

    #case: upload company pr
    def test_media_upload_company_pr(self):
        url = f"{BASE_URL}/api/media/upload-company-pr/"

        if not os.path.exists(file_sample):
            raise FileNotFoundError(f"File not found: {file_sample}")
        
        with open(file_sample, 'rb') as img:
            files = {'image': ('cat.jpeg', img, 'image/jpeg')}
            data = {'index': 1}
            headers = {
                'Authorization': str('Bearer ' + self.access_token),
                'Accept': 'application/json',
            }     
            response = requests.post(url, files=files, data=data, headers=headers)

        self.assertEqual(response.status_code, 200)
    
    #case: invalid image file for upload company pr
    def test_invalid_image_file_for_upload_company_pr(self):
        url = f"{BASE_URL}/api/media/upload-company-pr/"
    
        if not os.path.exists(file_error):
            raise FileNotFoundError(f"File not found: {file_error}")
        
        with open(file_error, 'rb') as file:
            files = {'image': ('test_media.docx', file, 'application/vnd.openxmlformats-officedocument.wordprocessingml.document')}
            headers = self.headers
            response = requests.post(url, files=files, headers=headers)
            
        response_errors = response.json()['errors']
        print('response_errors', response_errors)
        self.assertEqual(response.status_code, 400)
        self.assertEqual(response_errors[0]['message'], 'Upload a valid image. The file you uploaded was either not an image or a corrupted image.')
    
    #case: invalid index for upload company pr
    def test_media_upload_company_pr_with_invalid_index(self):
        url = f"{BASE_URL}/api/media/upload-company-pr/"

        if not os.path.exists(file_sample):
            raise FileNotFoundError(f"File not found: {file_sample}")
        
        with open(file_sample, 'rb') as img:
            files = {'image': ('cat.jpeg', img, 'image/jpeg')}
            data = {'index': 4}
            headers = self.headers
            response = requests.post(url, files=files, data=data, headers=headers)

        response_errors = response.json()['errors']
        print('response_errors', response_errors)
        self.assertEqual(response.status_code, 400)
        self.assertEqual(response_errors[0]['message'], 'Index must be 0,1,2')
    
    #case: upload passport
    def test_media_upload_passport(self):
        url = f"{BASE_URL}/api/media/upload-passport/"

        if not os.path.exists(file_sample):
            raise FileNotFoundError(f"File not found: {file_sample}")
        
        with open(file_sample, 'rb') as img:
            files = {'image': ('cat.jpeg', img, 'image/jpeg')}
            data = {'index': 1}
            headers = self.headers
            response = requests.post(url, files=files, data=data, headers=headers)

        self.assertEqual(response.status_code, 200)

    #case: invalid image file for upload passport
    def test_invalid_image_file_for_upload_passport(self):
        url = f"{BASE_URL}/api/media/upload-passport/"

        if not os.path.exists(file_error):
            raise FileNotFoundError(f"File not found: {file_error}")
        
        with open(file_error, 'rb') as file:
            files = {'image': ('test_media.docx', file, 'application/vnd.openxmlformats-officedocument.wordprocessingml.document')}
            headers = self.headers
            response = requests.post(url, files=files, headers=headers)

        response_errors = response.json()['errors']
        print('response_errors', response_errors)
        self.assertEqual(response.status_code, 400)
        self.assertEqual(response_errors[0]['message'], 'Upload a valid image. The file you uploaded was either not an image or a corrupted image.')
    
    #case: delete update passport
    def test_delete_passport(self):
        url = f"{BASE_URL}/api/media/upload-passport/"
        response = requests.delete(url, headers=self.headers)
       
        self.assertEqual(response.status_code, 200)
