#!/bin/bash

# Navigate to the project directory
cd /var/www/asiantechlink/code/asiantech.link/

# create logs/update_apply_status.log
touch logs/schedule_email_sender.log

# Activate the virtual environment
source env/bin/activate

export DJANGO_SETTINGS_MODULE=core.settings.prod 
python3.12 manage.py shell -c "from api.services.schedule_send_email_service import schedule_send_email_service; schedule_send_email_service()"

# Optionally, deactivate the virtual environment
deactivate
