#!/bin/bash

# Get the directory of the script
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"

# Set the target directory (relative to the script location)
DB_DIR="$SCRIPT_DIR/../api/static/data"
DB_FILE="$DB_DIR/GeoLite2-Country.mmdb"

# Set the download URLs
PRIMARY_URL="https://git.io/GeoLite2-Country.mmdb"
BACKUP_URL="https://github.com/P3TERX/GeoLite.mmdb/raw/download/GeoLite2-Country.mmdb"

# Create directory if not exists
mkdir -p "$DB_DIR"

# Function to download the database
download_db() {
    echo "Downloading GeoLite2 database from $1..."
    curl -L --fail --silent --output "$DB_FILE" "$1"
    if [[ $? -eq 0 ]]; then
        echo "Download successful!"
        return 0
    else
        echo "Download failed!"
        return 1
    fi
}

# Try downloading from the primary URL, fallback to backup URL if failed
download_db "$PRIMARY_URL" || download_db "$BACKUP_URL"

# Verify if the file exists after download
if [[ -f "$DB_FILE" ]]; then
    echo "GeoLite2 database updated successfully at $DB_FILE!"
else
    echo "Failed to update GeoLite2 database. Check your network or URLs."
fi
