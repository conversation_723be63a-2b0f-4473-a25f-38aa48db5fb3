#!/bin/bash

# Navigate to the project directory
cd /var/www/staging/asiantechlink/code/asiantech.link/

# create logs/update_apply_status.log
touch logs/update_apply_status.log

# Activate the virtual environment
source env/bin/activate

export DJANGO_SETTINGS_MODULE=core.settings.staging 
python3.12 manage.py shell -c "from api.services.update_progress_code_apply import update_progress_code_apply_after_interview; update_progress_code_apply_after_interview()"

# Optionally, deactivate the virtual environment
deactivate
