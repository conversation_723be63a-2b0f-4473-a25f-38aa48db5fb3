# permissions.py

from rest_framework.permissions import BasePermission
from rest_framework.exceptions import PermissionDenied
from django.utils.translation import gettext_lazy as _
from utils.constants import AuthType, UserType
from django.contrib.auth.models import AnonymousUser


class IsEmailVerified(BasePermission):
    """
    Custom permission to allow access only to users whose email is verified.
    """

    def has_permission(self, request, view):
        if isinstance(request.user, AnonymousUser):
            return False
        if request.user.is_authenticated and request.user.auth_type == AuthType.AUTHENTICATED.value:
            return True
        else:
            raise PermissionDenied({
                "message": _("Account has not been verified"),
            }, code=403)


class IsAdminUser(BasePermission):
    """
    Allows access only to admin users.
    """

    def has_permission(self, request, view):
        if isinstance(request.user, AnonymousUser):
            return False
        if request.user.auth_type != AuthType.AUTHENTICATED.value:
            raise PermissionDenied(
                {"message": _("Account has not been verified")}, code=403)

        if not (request.user.is_authenticated and request.user.user_type == UserType.MANAGEMENT_ACCOUNT.value):
            raise PermissionDenied({"message": _("Access denied")}, code=403)

        return True


class IsEngineer(BasePermission):
    """Allows access to engineers and management accounts."""

    def has_permission(self, request, view):
        if isinstance(request.user, AnonymousUser):
            return False
        if not (request.user.is_authenticated and
                (request.user.user_type == UserType.ENGINEER.value or
                 request.user.user_type == UserType.MANAGEMENT_ACCOUNT.value)):
            raise PermissionDenied({"message": _("Access denied")}, code=403)
        return True


class IsStaff(BasePermission):
    """Allows access only to staff."""

    def has_permission(self, request, view):
        if isinstance(request.user, AnonymousUser):
            return False
        if not (request.user.is_authenticated and request.user.user_type in [UserType.MANAGEMENT_ACCOUNT.value, UserType.HOST_COMPANY_STAFF.value, UserType.HOST_SUPPORT_AGENCY_STAFF.value, UserType.REFERRAL_AGENCY_STAFF.value, UserType.OTHER_SUPPORT_AGENCY_STAFF.value]):
            raise PermissionDenied({"message": _("Access denied")}, code=403)

        return True


class IsCompany(BasePermission):
    """Allows access to companies and management accounts."""

    def has_permission(self, request, view):
        if isinstance(request.user, AnonymousUser):
            return False
        if not (request.user.is_authenticated and
                (request.user.user_type in [
                    UserType.HOST_COMPANY_STAFF.value,
                    UserType.HOST_SUPPORT_AGENCY_STAFF.value,
                    UserType.REFERRAL_AGENCY_STAFF.value,
                    UserType.OTHER_SUPPORT_AGENCY_STAFF.value,
                    UserType.MANAGEMENT_ACCOUNT.value
                ])):
            raise PermissionDenied({"message": _("Access denied")}, code=403)
        return True


class IsHostCompany(BasePermission):
    """Allows access to host company users and management accounts."""

    def has_permission(self, request, view):
        if isinstance(request.user, AnonymousUser):
            return False
        if not (request.user.is_authenticated and
                (request.user.user_type == UserType.HOST_COMPANY_STAFF.value or
                 request.user.user_type == UserType.MANAGEMENT_ACCOUNT.value)):
            raise PermissionDenied({"message": _("Access denied")}, code=403)
        return True


class IsHostSupportAgencyCompany(BasePermission):
    """Allows access only to host support agency users."""

    def has_permission(self, request, view):
        if isinstance(request.user, AnonymousUser):
            return False
        if not (request.user.is_authenticated and request.user.user_type == UserType.HOST_SUPPORT_AGENCY_STAFF.value):
            raise PermissionDenied({"message": _("Access denied")}, code=403)

        return True


class IsReferralAgencyCompany(BasePermission):
    """Allows access only to referral agency users."""

    def has_permission(self, request, view):
        if isinstance(request.user, AnonymousUser):
            return False

        if not (request.user.is_authenticated and request.user.user_type == UserType.REFERRAL_AGENCY_STAFF.value):
            raise PermissionDenied({"message": _("Access denied")}, code=403)

        return True


class IsOtherSupportAgencyCompany(BasePermission):
    """Allows access only to other support agency users."""

    def has_permission(self, request, view):
        if isinstance(request.user, AnonymousUser):
            return False
        if not (request.user.is_authenticated and request.user.user_type == UserType.OTHER_SUPPORT_AGENCY_STAFF.value):
            raise PermissionDenied({"message": _("Access denied")}, code=403)

        return True
