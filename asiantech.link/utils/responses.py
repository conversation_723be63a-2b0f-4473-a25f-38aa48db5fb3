
from rest_framework.response import Response
from drf_yasg import openapi
from rest_framework import serializers


class CustomResponse(Response):
    def __init__(self, data=None, status=None, message=None, errors=None, headers=None, **kwargs):
        response_data = {
            'message': message,
            'errors': errors,
            'data': data,

        }
        super().__init__(data=response_data, status=status, **kwargs)
        if headers is not None:
            for key, value in headers.item():
                self.headers[key] = value


class BaseResponse:

    def __init__(self, status_code: int, message: str = None, schema: any = None, data: any = None, errors: any = None, responseModel: any = None):
        self.status_code = status_code
        self.message = message
        self.schema = schema
        self.data = data
        self.errors = errors
        self.responseModel = responseModel

    def to_openapi_response(self):
        if self.responseModel is None:
            return {
                self.status_code: ResponseModel()
            }

        return {
            self.status_code: self.responseModel
        }

    def to_custom_response(self):

        if self.errors is None and self.status_code >= 400:
            return CustomResponse(data=self.data, status=self.status_code, message=self.message, errors=[
                {
                    "message": self.message,
                    "field": None,
                    "code": None,
                }
            ])
        else:
            return CustomResponse(data=self.data, status=self.status_code, message=self.message, errors=self.errors)

    def update(self, status_code: int = None, message: str = None, data: any = None):
        if status_code is not None:
            self.status_code = status_code
        if message is not None:
            self.message = message
        if data is not None:
            self.data = data

    def copy(self, status_code: int = None, message: str = None, data: any = None, errors: any = None):
        return BaseResponse(
            status_code=status_code if status_code is not None else self.status_code,
            message=message if message is not None else self.message,
            schema=self.schema,
            data=data if data is not None else self.data,
            errors=errors if errors is not None else self.errors,
        )


class ErrorDetailSerializer(serializers.Serializer):
    message = serializers.CharField(allow_null=True)
    title = serializers.CharField(allow_null=True)
    field = serializers.CharField(allow_null=True)
    code = serializers.IntegerField(allow_null=True)


class ResponseModel(serializers.Serializer):
    message = serializers.CharField(allow_null=True)
    data = serializers.DictField(default=dict, allow_null=True)
    errors = serializers.ListField(
        child=ErrorDetailSerializer(), allow_null=True)
