"""
AI Recommendation Reporter

This module provides detailed logging and CSV export functionality
for AI recommendation analysis and debugging.
"""

import csv
import json
import logging
import os
from datetime import datetime
from typing import List, Dict, Any

logger = logging.getLogger(__name__)


class AIRecommendationReporter:
    """
    Reporter class for AI recommendation process analysis
    """

    def __init__(self, company_id: int, user_id: int = None):
        self.company_id = company_id
        self.user_id = user_id
        self.session_id = datetime.now().strftime("%Y%m%d_%H%M%S")
        self.should_log_report = os.environ.get(
            "LOG_AI_RECOMMENDATION_REPORT", "False") == "True"
        self.report_data = {
            'session_info': {
                'session_id': self.session_id,
                'company_id': company_id,
                'user_id': user_id,
                'timestamp': datetime.now().isoformat(),
            },
            'recruit_info': {},
            'initial_engineers': [],
            'top_20_engineers': [],
            'ai_filtered_engineers': [],
            'final_results': []
        }

        # Create reports directory if not exists
        self.reports_dir = os.path.join(
            os.path.dirname(__file__), '..', 'reports')
        os.makedirs(self.reports_dir, exist_ok=True)

    def log_recruit_info(self, recruit_data: Dict[str, Any], readable_recruit: str = ""):
        if not self.should_log_report:
            return
        """Log recruit information"""
        self.report_data['recruit_info'] = {
            'recruit_id': recruit_data.get('recruit_id'),
            'title': recruit_data.get('title', ''),
            'skills': [
                recruit_data.get('skill_code1'),
                recruit_data.get('skill_code2'),
                recruit_data.get('skill_code3')
            ],
            'locations': [
                recruit_data.get('place_code1'),
                recruit_data.get('place_code2'),
                recruit_data.get('place_code3')
            ],
            'remote_code': recruit_data.get('remote_code'),
            'employ_code': recruit_data.get('employ_code'),
            'salary_min': recruit_data.get('payroll_price_from_usd'),
            'salary_max': recruit_data.get('payroll_price_to_usd'),
            'readable_profile': readable_recruit,
        }

        logger.info(
            f"[AI_REPORT] Recruit Info: {self.report_data['recruit_info']}")
        if readable_recruit:
            logger.info(
                f"[AI_REPORT] Recruit Readable Profile:\n{readable_recruit}")

    def log_initial_engineers_count(self, count: int):
        if not self.should_log_report:
            return
        """Log initial engineers count before any filtering"""
        self.report_data['initial_engineers_count'] = count
        logger.info(f"[AI_REPORT] Initial engineers in system: {count}")

    def log_top_20_engineers(self, engineers_data: List[Dict[str, Any]]):
        if not self.should_log_report:
            return
        """Log top 20 engineers with detailed scoring"""
        self.report_data['top_20_engineers'] = []

        logger.info(f"[AI_REPORT] === TOP 20 ENGINEERS SELECTION ===")
        logger.info(
            f"[AI_REPORT] Found {len(engineers_data)} engineers after scoring")

        for i, engineer in enumerate(engineers_data, 1):
            # Get additional engineer details
            engineer_details = self._get_engineer_details(
                engineer.get('user_id'))

            engineer_info = {
                'rank': i,
                'user_id': engineer.get('user_id'),
                'name': f"{engineer.get('first_name', '')} {engineer.get('last_name', '')}".strip(),
                'total_advanced_score': engineer.get('total_advanced_score', 0),
                'skill_score': engineer.get('skill_score', 0),
                'age_score': engineer.get('age_score', 0),
                'place_score': engineer.get('place_score', 0),
                'employment_score': engineer.get('employment_score', 0),
                'remote_score': engineer.get('remote_score', 0),
                'salary_score': engineer.get('salary_score', 0),
                # Add weighted scores according to your formula
                'weighted_employ_remote_score': engineer.get('weighted_employ_remote_score', 0),
                'weighted_age_score': engineer.get('weighted_age_score', 0),
                'weighted_skills_score': engineer.get('weighted_skills_score', 0),
                'weighted_salary_score': engineer.get('weighted_salary_score', 0),
                'engineer_employ_code': engineer.get('engineer_employ_code'),
                'engineer_remote_code': engineer.get('engineer_remote_code'),
                'engineer_salary': engineer.get('engineer_salary'),
                'skills': engineer.get('skills', []),
                # Additional details
                'email': engineer_details.get('email', ''),
                'phone': engineer_details.get('phone', ''),
                'age': engineer_details.get('age', ''),
                'gender': engineer_details.get('gender', ''),
                'location': engineer_details.get('location', ''),
                'experience_years': engineer_details.get('experience_years', ''),
                'education': engineer_details.get('education', ''),
                'languages': engineer_details.get('languages', ''),
                'certifications': engineer_details.get('certifications', ''),
                'professional_summary': engineer_details.get('professional_summary', ''),
                'skills_detail': engineer_details.get('skills_detail', ''),
                'employment_type_name': self._get_employment_type_name(engineer.get('engineer_employ_code')),
                'remote_type_name': self._get_remote_type_name(engineer.get('engineer_remote_code')),
                'readable_profile': engineer.get('readable_profile', ''),
            }

            self.report_data['top_20_engineers'].append(engineer_info)

            logger.info(
                f"[AI_REPORT] #{i:2d} Engineer ID: {engineer_info['user_id']:4d} | "
                f"Name: {engineer_info['name']:20s} | "
                f"Total: {engineer_info['total_advanced_score']:5.2f} | "
                f"Skills: {engineer_info['skill_score']:4.1f} | "
                f"Place: {engineer_info['place_score']:3.1f} | "
                f"Employment: {engineer_info['employment_score']:4.1f} | "
                f"Remote: {engineer_info['remote_score']:4.1f} | "
                f"Salary: {engineer_info['salary_score']:4.1f}"
            )

    def log_ai_filtered_engineers(self, ai_engineer_ids: List[int], ai_summary: str = ""):
        if not self.should_log_report:
            return
        """Log engineers selected by AI"""
        self.report_data['ai_filtered_engineers'] = ai_engineer_ids
        self.report_data['ai_summary'] = ai_summary

        logger.info(f"[AI_REPORT] === AI FILTERING RESULTS ===")
        logger.info(
            f"[AI_REPORT] AI selected {len(ai_engineer_ids)} engineers from top 20")
        logger.info(f"[AI_REPORT] Selected Engineer IDs: {ai_engineer_ids}")

        if ai_summary:
            logger.info(f"[AI_REPORT] AI Summary: {ai_summary[:200]}...")

    def log_final_results(self, final_engineers: List[Dict[str, Any]]):
        if not self.should_log_report:
            return
        """Log final results with AI reasons"""
        self.report_data['final_results'] = []

        logger.info(f"[AI_REPORT] === FINAL RESULTS ===")
        logger.info(
            f"[AI_REPORT] Final {len(final_engineers)} engineers returned to user")

        for i, engineer in enumerate(final_engineers, 1):
            engineer_info = {
                'final_rank': i,
                'user_id': engineer.get('user_id'),
                'name': f"{engineer.get('first_name', '')} {engineer.get('last_name', '')}".strip(),
                'match_score': engineer.get('match_score'),
                'reason_ai_recommend': engineer.get('reason_ai_recommend', ''),
                'was_in_top_20': engineer.get('user_id') in [e.get('user_id') for e in self.report_data['top_20_engineers']],
            }

            self.report_data['final_results'].append(engineer_info)

            logger.info(
                f"[AI_REPORT] Final #{i} Engineer ID: {engineer_info['user_id']:4d} | "
                f"Name: {engineer_info['name']:20s} | "
                f"Match Score: {engineer_info['match_score']} | "
                f"In Top 20: {engineer_info['was_in_top_20']}"
            )

    def export_to_csv(self) -> str:
        if not self.should_log_report:
            return None
        """Export detailed report to CSV files"""
        timestamp = self.session_id

        # 1. Export recruit info
        recruit_file = os.path.join(
            self.reports_dir, f"recruit_info_{timestamp}.csv")
        with open(recruit_file, 'w', newline='', encoding='utf-8') as f:
            writer = csv.writer(f)
            writer.writerow(['Field', 'Value'])
            recruit_info = self.report_data['recruit_info']
            for key, value in recruit_info.items():
                writer.writerow([key, value])

        # 2. Export top 20 engineers with detailed information
        top20_file = os.path.join(
            self.reports_dir, f"top_20_engineers_detailed_{timestamp}.csv")
        if self.report_data['top_20_engineers']:
            # Create detailed CSV with custom headers
            with open(top20_file, 'w', newline='', encoding='utf-8') as f:
                writer = csv.writer(f)

                # Write header according to your formula
                writer.writerow([
                    'Rank', 'Engineer ID', 'Name', 'Email', 'Phone', 'Age', 'Gender',
                    'Total Weighted Score',
                    'Skills Raw', 'Skills Weighted (×0.4)',
                    'Age Raw', 'Age Weighted (×0.2)',
                    'Employment Raw', 'Remote Raw', 'Employ+Remote Weighted (×0.2)',
                    'Salary Raw', 'Salary Weighted (×0.2)',
                    'Employment Type', 'Remote Type', 'Expected Salary', 'Experience Years',
                    'Skills Detail', 'Location Preference', 'Education', 'Languages', 'Certifications',
                    'Professional Summary', 'Readable Profile for AI'
                ])

                # Write data according to your formula
                for engineer in self.report_data['top_20_engineers']:
                    writer.writerow([
                        engineer['rank'],
                        engineer['user_id'],
                        engineer['name'],
                        engineer.get('email', ''),
                        engineer.get('phone', ''),
                        engineer.get('age', ''),
                        engineer.get('gender', ''),
                        f"{engineer['total_advanced_score']:.2f}",
                        f"{engineer['skill_score']:.1f}",
                        f"{engineer.get('weighted_skills_score', 0):.2f}",
                        f"{engineer.get('age_score', 0):.1f}",
                        f"{engineer.get('weighted_age_score', 0):.2f}",
                        f"{engineer['employment_score']:.1f}",
                        f"{engineer['remote_score']:.1f}",
                        f"{engineer.get('weighted_employ_remote_score', 0):.2f}",
                        f"{engineer['salary_score']:.1f}",
                        f"{engineer.get('weighted_salary_score', 0):.2f}",
                        engineer.get('employment_type_name', ''),
                        engineer.get('remote_type_name', ''),
                        engineer.get('engineer_salary', ''),
                        engineer.get('experience_years', ''),
                        engineer.get('skills_detail', ''),
                        engineer.get('location', ''),
                        engineer.get('education', ''),
                        engineer.get('languages', ''),
                        engineer.get('certifications', ''),
                        engineer.get('professional_summary', ''),
                        engineer.get('readable_profile', '')
                    ])

        # 3. Export final results
        final_file = os.path.join(
            self.reports_dir, f"final_results_{timestamp}.csv")
        if self.report_data['final_results']:
            with open(final_file, 'w', newline='', encoding='utf-8') as f:
                writer = csv.writer(f)
                if self.report_data['final_results']:
                    # Write header
                    headers = list(self.report_data['final_results'][0].keys())
                    writer.writerow(headers)
                    # Write data
                    for result in self.report_data['final_results']:
                        writer.writerow([result.get(header, '')
                                        for header in headers])

        # 4. Export comprehensive analysis
        analysis_file = os.path.join(
            self.reports_dir, f"ai_recommendation_analysis_{timestamp}.csv")
        self._create_comprehensive_analysis(analysis_file)

        # 5. Export JSON for detailed analysis
        json_file = os.path.join(
            self.reports_dir, f"ai_recommendation_data_{timestamp}.json")
        with open(json_file, 'w', encoding='utf-8') as f:
            json.dump(self.report_data, f, indent=2, ensure_ascii=False)

        logger.info(f"[AI_REPORT] Reports exported to {self.reports_dir}")
        logger.info(f"[AI_REPORT] Files created:")
        logger.info(f"[AI_REPORT] - Recruit Info: {recruit_file}")
        logger.info(f"[AI_REPORT] - Top 20 Engineers: {top20_file}")
        logger.info(f"[AI_REPORT] - Final Results: {final_file}")
        logger.info(f"[AI_REPORT] - Comprehensive Analysis: {analysis_file}")
        logger.info(f"[AI_REPORT] - Raw Data (JSON): {json_file}")

        return analysis_file

    def _create_comprehensive_analysis(self, file_path: str):
        """Create comprehensive analysis CSV"""
        analysis_data = []

        # Add session info
        analysis_data.append(['=== SESSION INFO ==='])
        analysis_data.append(
            ['Session ID', self.report_data['session_info']['session_id']])
        analysis_data.append(
            ['Company ID', self.report_data['session_info']['company_id']])
        analysis_data.append(
            ['User ID', self.report_data['session_info']['user_id']])
        analysis_data.append(
            ['Timestamp', self.report_data['session_info']['timestamp']])
        analysis_data.append([''])

        # Add recruit info
        analysis_data.append(['=== RECRUIT CRITERIA ==='])
        recruit_info = self.report_data['recruit_info']
        analysis_data.append(['Recruit ID', recruit_info.get('recruit_id')])
        analysis_data.append(['Title', recruit_info.get('title')])
        analysis_data.append(['Skills Required', ', '.join(
            filter(None, recruit_info.get('skills', [])))])
        analysis_data.append(['Locations', ', '.join(
            filter(None, recruit_info.get('locations', [])))])
        analysis_data.append(['Remote Code', recruit_info.get('remote_code')])
        analysis_data.append(
            ['Employment Code', recruit_info.get('employ_code')])
        analysis_data.append(
            ['Salary Range', f"{recruit_info.get('salary_min')} - {recruit_info.get('salary_max')}"])
        analysis_data.append([''])

        # Add statistics
        analysis_data.append(['=== STATISTICS ==='])
        analysis_data.append(
            ['Initial Engineers Count', self.report_data.get('initial_engineers_count', 0)])
        analysis_data.append(['Top 20 Engineers Count', len(
            self.report_data['top_20_engineers'])])
        analysis_data.append(['AI Selected Count', len(
            self.report_data['ai_filtered_engineers'])])
        analysis_data.append(
            ['Final Results Count', len(self.report_data['final_results'])])
        analysis_data.append([''])

        # Add detailed engineer comparison
        analysis_data.append(['=== DETAILED ENGINEER ANALYSIS ==='])
        analysis_data.append([
            'Rank', 'Engineer ID', 'Name', 'Email', 'Phone', 'Age', 'Gender',
            'Total Weighted Score',
            'Skills Raw', 'Skills Weighted (×0.4)',
            'Age Raw', 'Age Weighted (×0.2)',
            'Employment Raw', 'Remote Raw', 'Employ+Remote Weighted (×0.2)',
            'Salary Raw', 'Salary Weighted (×0.2)',
            'Employment Type', 'Remote Type', 'Expected Salary', 'Experience Years',
            'Skills Detail', 'Location Preference', 'Education', 'Languages', 'Certifications',
            'Professional Summary', 'AI Selected', 'Final Result', 'AI Reason', 'Readable Profile for AI'
        ])

        # Create lookup for AI selected and final results
        ai_selected_ids = set(self.report_data['ai_filtered_engineers'])
        final_result_ids = {r['user_id']
            : r for r in self.report_data['final_results']}

        for engineer in self.report_data['top_20_engineers']:
            user_id = engineer['user_id']
            is_ai_selected = user_id in ai_selected_ids
            final_result = final_result_ids.get(user_id)

            analysis_data.append([
                engineer['rank'],
                user_id,
                engineer['name'],
                engineer.get('email', ''),
                engineer.get('phone', ''),
                engineer.get('age', ''),
                engineer.get('gender', ''),
                f"{engineer['total_advanced_score']:.2f}",
                f"{engineer['skill_score']:.1f}",
                f"{engineer.get('weighted_skills_score', 0):.2f}",
                f"{engineer.get('age_score', 0):.1f}",
                f"{engineer.get('weighted_age_score', 0):.2f}",
                f"{engineer['employment_score']:.1f}",
                f"{engineer['remote_score']:.1f}",
                f"{engineer.get('weighted_employ_remote_score', 0):.2f}",
                f"{engineer['salary_score']:.1f}",
                f"{engineer.get('weighted_salary_score', 0):.2f}",
                engineer.get('employment_type_name', ''),
                engineer.get('remote_type_name', ''),
                engineer.get('engineer_salary', ''),
                engineer.get('experience_years', ''),
                engineer.get('skills_detail', ''),
                engineer.get('location', ''),
                engineer.get('education', ''),
                engineer.get('languages', ''),
                engineer.get('certifications', ''),
                engineer.get('professional_summary', '')[
                    :100] + '...' if engineer.get('professional_summary') else '',
                'YES' if is_ai_selected else 'NO',
                'YES' if final_result else 'NO',
                final_result['reason_ai_recommend'][:100] if final_result else '',
                engineer.get('readable_profile', '')[
                    :200] + '...' if engineer.get('readable_profile') else ''
            ])

        # Write to CSV
        with open(file_path, 'w', newline='', encoding='utf-8') as f:
            writer = csv.writer(f)
            writer.writerows(analysis_data)

    def get_summary_stats(self) -> Dict[str, Any]:
        """Get summary statistics"""
        top_20_scores = [e['total_advanced_score']
                         for e in self.report_data['top_20_engineers']]

        return {
            'total_engineers_processed': self.report_data.get('initial_engineers_count', 0),
            'top_20_count': len(self.report_data['top_20_engineers']),
            'ai_selected_count': len(self.report_data['ai_filtered_engineers']),
            'final_results_count': len(self.report_data['final_results']),
            'score_stats': {
                'min_score': min(top_20_scores) if top_20_scores else 0,
                'max_score': max(top_20_scores) if top_20_scores else 0,
                'avg_score': sum(top_20_scores) / len(top_20_scores) if top_20_scores else 0,
            },
            'ai_selection_rate': len(self.report_data['ai_filtered_engineers']) / len(self.report_data['top_20_engineers']) if self.report_data['top_20_engineers'] else 0,
        }

    def _get_engineer_details(self, user_id: int) -> Dict[str, Any]:
        """Get detailed engineer information"""
        try:
            from api.models.user import User
            from api.serializers.engineers.user_serializers import UserDetailsSerializers

            engineer = User.objects.get(user_id=user_id)
            engineer_serializer = UserDetailsSerializers(engineer)
            engineer_data = engineer_serializer.data

            # Extract key information
            details = {
                'email': engineer_data.get('email', ''),
                'phone': engineer_data.get('phone', ''),
                'age': self._calculate_age(engineer_data.get('birthday')),
                'gender': self._get_gender_name(engineer_data.get('sex_type')),
                'location': self._get_location_info(engineer_data),
                'experience_years': self._calculate_experience_years(engineer_data),
                'education': self._get_education_info(engineer_data),
                'languages': self._get_languages_info(engineer_data),
                'certifications': self._get_certifications_info(engineer_data),
                'professional_summary': engineer_data.get('professional_summary', ''),
                'skills_detail': self._get_skills_detail(engineer_data),
            }

            return details

        except Exception as e:
            logger.error(f"Error getting engineer details for {user_id}: {e}")
            return {}

    def _calculate_age(self, birthday_str: str) -> str:
        """Calculate age from birthday string"""
        try:
            if not birthday_str:
                return ''
            from datetime import datetime
            birthday = datetime.strptime(birthday_str, '%Y-%m-%d')
            today = datetime.now()
            age = today.year - birthday.year - \
                ((today.month, today.day) < (birthday.month, birthday.day))
            return str(age)
        except:
            return ''

    def _get_gender_name(self, sex_type: int) -> str:
        """Get gender name from sex_type"""
        gender_map = {1: 'Male', 2: 'Female', 3: 'Other'}
        return gender_map.get(sex_type, '')

    def _get_location_info(self, engineer_data: Dict) -> str:
        """Get location information"""
        try:
            requirements = engineer_data.get('requirements', {})
            locations = []
            for i in range(1, 4):
                place_name = requirements.get(f'place_code{i}_name')
                if place_name:
                    locations.append(place_name)
            return ', '.join(locations)
        except:
            return ''

    def _calculate_experience_years(self, engineer_data: Dict) -> str:
        """Calculate total experience years"""
        try:
            experiences = engineer_data.get('experiences', [])
            if not experiences:
                return ''

            total_months = 0
            for exp in experiences:
                entering_date = exp.get('entering_date')
                quitting_date = exp.get('quitting_date')

                if entering_date:
                    from datetime import datetime
                    start = datetime.strptime(entering_date, '%Y-%m-%d')
                    end = datetime.strptime(
                        quitting_date, '%Y-%m-%d') if quitting_date else datetime.now()
                    months = (end.year - start.year) * \
                        12 + (end.month - start.month)
                    total_months += months

            years = total_months / 12
            return f"{years:.1f}"
        except:
            return ''

    def _get_education_info(self, engineer_data: Dict) -> str:
        """Get education information"""
        try:
            educations = engineer_data.get('educations', [])
            if not educations:
                return ''

            edu_list = []
            for edu in educations:
                school = edu.get('school', '')
                faculty = edu.get('faculty', '')
                type_name = edu.get('type_name', '')

                edu_str = school
                if faculty:
                    edu_str += f" - {faculty}"
                if type_name:
                    edu_str += f" ({type_name})"

                if edu_str:
                    edu_list.append(edu_str)

            return '; '.join(edu_list)
        except:
            return ''

    def _get_languages_info(self, engineer_data: Dict) -> str:
        """Get languages information"""
        try:
            languages = engineer_data.get('languages', [])
            if not languages:
                return ''

            lang_list = []
            for lang in languages:
                lang_name = lang.get('language_name', '')
                level_type = lang.get('language_level_type')

                lang_str = lang_name
                if level_type:
                    level_names = {1: 'Basic', 2: 'Intermediate',
                                   3: 'Advanced', 4: 'Native'}
                    level_name = level_names.get(level_type, '')
                    if level_name:
                        lang_str += f" ({level_name})"

                if lang_str:
                    lang_list.append(lang_str)

            return ', '.join(lang_list)
        except:
            return ''

    def _get_certifications_info(self, engineer_data: Dict) -> str:
        """Get certifications information"""
        try:
            qualifications = engineer_data.get('qualifications', [])
            if not qualifications:
                return ''

            cert_list = []
            for qual in qualifications:
                licence_name = qual.get('licence_name', '')
                license_code_name = qual.get('license_code_name', '')
                cert_name = licence_name or license_code_name

                if cert_name:
                    cert_list.append(cert_name)

            return ', '.join(cert_list)
        except:
            return ''

    def _get_skills_detail(self, engineer_data: Dict) -> str:
        """Get detailed skills information"""
        try:
            skills = engineer_data.get('skills', [])
            if not skills:
                return ''

            skill_list = []
            for skill in skills:
                skill_name = skill.get('skill_name', '')
                level_type = skill.get('level_type', 1)
                level_names = {1: 'Fresher', 2: 'Junior',
                               3: 'Middle', 4: 'Senior', 5: 'Expert'}
                level_name = level_names.get(level_type, 'Unknown')

                if skill_name:
                    skill_list.append(f"{skill_name} ({level_name})")

            return ', '.join(skill_list)
        except:
            return ''

    def _get_employment_type_name(self, employ_code: str) -> str:
        """Get employment type name"""
        employment_names = {
            '1': 'Full-time',
            '2': 'Part-time',
            '3': 'Temporary',
            '4': 'Contract',
            '5': 'Freelance'
        }
        return employment_names.get(employ_code, '')

    def _get_remote_type_name(self, remote_code: str) -> str:
        """Get remote work type name"""
        remote_names = {
            '1': 'Onsite',
            '2': 'Hybrid',
            '3': 'Remote'
        }
        return remote_names.get(remote_code, '')
