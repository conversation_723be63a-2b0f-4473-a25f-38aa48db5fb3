from utils.ai_helper import AIHelper
from utils.ai_recommendation import AIRecommendationEngine
import os
from utils.utils import get_employment_match_score, get_remote_work_match_score, get_salary_compatibility_score
from api.serializers.recruitment_serializers import RecruitmentSerializer
import json
from api.models.ai_prompt_history import AiPromptHistory
from django.utils import timezone
from collections import defaultdict

from django.db.models import Q, Count, F, Value, Case, When, IntegerField
import logging
from datetime import date
from django.db.models import *
from django.db.models.functions import Coalesce
from api.models.eng import *
from api.models.rec import RecRecruit
from django.contrib.auth import get_user_model
from utils.constants import *
from utils.ai_recommendation_reporter import AIRecommendationReporter
from utils.utils import get_skill_code_name, get_skill_level_name_en, get_language_level_model_name, get_employ_type_name, get_remote_code_name
from api.serializers.engineers.user_serializers import UserDetailsSerializers
User = get_user_model()
logger = logging.getLogger("api_logger")
ai_engine = AIRecommendationEngine()

ai_helper = AIHelper()


class AISearchEngineerQuery:
    def engineer_filter_by_ai_recommend(self, company_id, query, user=None, recruit_id=None):

        if company_id is None:
            return query.filter(user_id__in=[])

        # Initialize reporter for detailed logging and CSV export

        reporter = AIRecommendationReporter(
            company_id=company_id,
            user_id=None,
        )

        # Get recruit based on recruit_id parameter or fallback to first recruit with skills
        recruit = None

        if recruit_id:
            # Use specific recruit_id provided from web
            try:
                recruit = RecRecruit.objects.get(
                    recruit_id=recruit_id,
                    host_company_id=company_id
                )
                # Verify recruit has at least 1 skill
                recruit_skill_codes = list(
                    filter(None, [recruit.skill_code1, recruit.skill_code2, recruit.skill_code3]))
                if not recruit_skill_codes:
                    logger.info(
                        f"Recruit {recruit_id} has no skills defined")
                    recruit = None
            except RecRecruit.DoesNotExist:
                logger.info(f"Recruit {recruit_id} not found or not active")

        else:
            # Fallback to first recruit with skills
            try:
                recruits = RecRecruit.objects.filter(
                    host_company_id=company_id,
                    display_flag=1,
                    save_type=1,
                    end_date__gte=timezone.now(),
                    skill_code1__isnull=False,
                )
                if (recruits.exists()):

                    for element in recruits:
                        recruit_skill_codes = list(
                            filter(None, [element.skill_code1, element.skill_code2, element.skill_code3]))
                        # check recruit have some engineers matched
                        results = EngSkill.objects.filter(
                            skill_code__in=recruit_skill_codes
                        )
                        if results.exists():
                            recruit = element
                            break
                else:
                    logger.info(
                        f"No active recruits with skills found for company {company_id}")

            except Exception as e:
                # return empty query
                logger.info(
                    f"No active recruits with skills found for company {company_id}, error: ")

        if not recruit:
            logger.info(
                f"No active recruits with skills found for company {company_id}")
            return query.filter(user_id__in=[])

        result, top_engineer_ids = self.calculate_top_20_engineers_with_recruit(
            query, recruit, reporter)

        # Get AI recommendations
        top_ai_engineer_ids, ai_recommendations = self.rag_ai_recommend(
            query, recruit, top_engineer_ids, user)

        # Log AI filtered engineers
        ai_summary = ai_recommendations['summary']
        reporter.log_ai_filtered_engineers(top_ai_engineer_ids, ai_summary)

        reason_cases = [
            When(user_id=engineer["engineer_id"],
                 then=Value(engineer["reason_ai_recommend"]))
            for engineer in ai_recommendations["top_5_engineers"]
        ]
        reason_vi_cases = [
            When(user_id=engineer["engineer_id"],
                 then=Value(engineer["reason_ai_recommend_vi"]))
            for engineer in ai_recommendations["top_5_engineers"]
        ]
        reason_ja_cases = [
            When(user_id=engineer["engineer_id"],
                 then=Value(engineer["reason_ai_recommend_ja"]))
            for engineer in ai_recommendations["top_5_engineers"]
        ]
        final_result = result.filter(user_id__in=top_ai_engineer_ids).annotate(
            ai_summary=Value(
                ai_recommendations["summary"], output_field=CharField()),
            ai_summary_vi=Value(
                ai_recommendations["summary_vi"], output_field=CharField()),
            ai_summary_ja=Value(
                ai_recommendations["summary_ja"], output_field=CharField()),
            reason_ai_recommend=Case(
                *reason_cases,
                default=Value(""),
                output_field=CharField()
            ),
            reason_ai_recommend_vi=Case(
                *reason_vi_cases,
                default=Value(""),
                output_field=CharField()
            ),
            reason_ai_recommend_ja=Case(
                *reason_ja_cases,
                default=Value(""),
                output_field=CharField()
            ),
        )

        # Export to CSV
        try:
            csv_file = reporter.export_to_csv()
            if csv_file:
                logger.info(
                    f"[AI_REPORT] Detailed report exported to: {csv_file}")

                # Log summary statistics
                stats = reporter.get_summary_stats()
                logger.info(f"[AI_REPORT] Summary Statistics: {stats}")
        except Exception as e:
            logger.error(f"[AI_REPORT] Error exporting report: {e}")
        # append recruit_id into final result
        final_result = final_result.annotate(recruit_id=Value(
            recruit.recruit_id, output_field=IntegerField()))

        return final_result

    def calculate_top_20_engineers_with_recruit(self, query, recruit, reporter):
        # Initialize reporter for detailed logging and CSV export

        # Log recruit information with readable profile
        recruit_data = {
            'recruit_id': recruit.recruit_id,
            'title': recruit.title,
            'skill_code1': recruit.skill_code1,
            'skill_code2': recruit.skill_code2,
            'skill_code3': recruit.skill_code3,
            'place_code1': recruit.place_code1,
            'place_code2': recruit.place_code2,
            'place_code3': recruit.place_code3,
            'remote_code': recruit.remote_code,
            'employ_code': recruit.employ_code,
            'payroll_price_from_usd': recruit.payroll_price_from_usd,
            'payroll_price_to_usd': recruit.payroll_price_to_usd,
        }
        # Generate readable recruit profile for AI

        recruit_serializer = RecruitmentSerializer(recruit)
        recruit_serialized_data = recruit_serializer.data
        # readable_recruit = self._convert_recruit_company_to_readable_string(
        #     recruit_serialized_data)

        # reporter.log_recruit_info(recruit_data, readable_recruit)

        user_scores = {}

        # Get recruit criteria
        recruit_skill_codes = list(
            filter(None, [recruit.skill_code1, recruit.skill_code2, recruit.skill_code3]))
        recruit_pref_codes = list(
            filter(None, [recruit.place_code1, recruit.place_code2, recruit.place_code3]))
        recruit_remote_code = recruit.remote_code
        recruit_employ_code = recruit.employ_code
        recruit_salary_min = recruit.payroll_price_from_usd or 0
        recruit_salary_max = recruit.payroll_price_to_usd or 999999999999999

        # Filter engineers who have at least one matching skill
        filtered_query = query.filter(
            engskill__skill_code__in=recruit_skill_codes
        ).distinct()

        # Calculate basic scores using database annotations
        filtered_query = filtered_query.annotate(
            # Count unique matching skills (for filtering only)
            total_match_skill_score=Coalesce(
                Count(
                    'engskill__skill_code',
                    filter=Q(engskill__skill_code__in=recruit_skill_codes),
                    distinct=True
                ),
                Value(0),
                output_field=IntegerField()
            ),
            # Place score: 1 if engineer's preferred location matches recruit's location
            place_score=Case(
                When(Q(enghope_set__place_code1__in=recruit_pref_codes) |
                     Q(enghope_set__place_code2__in=recruit_pref_codes) |
                     Q(enghope_set__place_code3__in=recruit_pref_codes), then=Value(1)),
                default=Value(0),
                output_field=IntegerField()
            ),
            # Get engineer preferences for advanced scoring
            engineer_remote_code=F('enghope_set__remote_code'),
            engineer_employ_code=F('enghope_set__employ_code'),
            engineer_salary=Coalesce(
                F('enghope_set__payroll_price_usd'),
                Value(0),
                output_field=FloatField()
            )
        ).filter(
            total_match_skill_score__gte=1
        )

        # Calculate advanced scores using our new scoring system

        # Collect detailed engineer data for reporting
        detailed_engineers_data = []

        for engineer in filtered_query.values('user_id', 'total_match_skill_score', 'place_score', 'engineer_remote_code', 'engineer_employ_code', 'engineer_salary'):
            user_id = engineer['user_id']
            # Calculate detailed skill score using your formula: (Skill_Code × Skill_Level) for each skill
            skill_score = self._calculate_detailed_skill_score(
                user_id, recruit_skill_codes, recruit)
            place_score = engineer['place_score']
            engineer_remote_code = engineer['engineer_remote_code']
            engineer_employ_code = engineer['engineer_employ_code']
            engineer_salary = engineer['engineer_salary']

            # Calculate advanced employment matching score (0-10)
            employment_score = 0
            if engineer_employ_code and recruit_employ_code:
                employment_score = get_employment_match_score(
                    recruit_employ_code, engineer_employ_code)

            # Calculate advanced remote work matching score (0-10)
            remote_score = 0
            if engineer_remote_code and recruit_remote_code:
                remote_score = get_remote_work_match_score(
                    recruit_remote_code, engineer_remote_code)

            # Calculate advanced salary compatibility score (0-10)
            salary_score = 0
            if engineer_salary:
                salary_score = get_salary_compatibility_score(
                    engineer_salary, recruit_salary_min, recruit_salary_max)

            # Calculate age score
            age_score = self._calculate_age_score(user_id, recruit)

            # Calculate weighted scores according to your formula
            weighted_employ_remote_score = (
                employment_score + remote_score) * 0.2
            weighted_age_score = age_score * 0.2
            # skill_score already calculated with your formula
            weighted_skills_score = skill_score * 0.4
            weighted_salary_score = salary_score * 0.2

            # Total weighted score according to your formula
            total_advanced_score = (
                weighted_employ_remote_score +  # (employ + remote) * 0.2
                weighted_age_score +            # age * 0.2
                weighted_skills_score +         # skills * 0.4
                weighted_salary_score           # salary * 0.2
            )

            user_scores[user_id] = total_advanced_score

            # Collect detailed data for reporting
            detailed_engineers_data.append({
                'user_id': user_id,
                'total_advanced_score': total_advanced_score,
                'skill_score': skill_score,
                'age_score': age_score,
                'place_score': place_score,
                'employment_score': employment_score,
                'remote_score': remote_score,
                'salary_score': salary_score,
                'weighted_employ_remote_score': weighted_employ_remote_score,
                'weighted_age_score': weighted_age_score,
                'weighted_skills_score': weighted_skills_score,
                'weighted_salary_score': weighted_salary_score,
                'engineer_employ_code': engineer_employ_code,
                'engineer_remote_code': engineer_remote_code,
                'engineer_salary': engineer_salary,

            })

            # Log detailed scoring for debugging according to your formula
            logger.info(
                f"Engineer ID: {user_id}, "
                f"total_weighted_score: {total_advanced_score:.2f}, "
                f"employ_remote: ({employment_score}+{remote_score})*0.2={weighted_employ_remote_score:.2f}, "
                f"age: {age_score}*0.2={weighted_age_score:.2f}, "
                f"skills: {skill_score}*0.4={weighted_skills_score:.2f}, "
                f"salary: {salary_score}*0.2={weighted_salary_score:.2f}"
            )

        # Final filtering: only users with the highest scores
        top_user_ids = list(user_scores.keys())
        result = query.filter(user_id__in=top_user_ids).annotate(
            total_score=Case(
                *[When(user_id=uid, then=Value(score))
                  for uid, score in user_scores.items()],
                default=Value(0),
                output_field=IntegerField()
            )
        ).order_by('-total_score')
        result = result.annotate(
            recruit_id=Value(recruit.recruit_id, output_field=IntegerField())
        )

        result = result.filter(
            total_score__gte=2
        )
        top_engineer_ids = list(result.values_list('user_id', flat=True)[:20])

        # Prepare top 20 engineers data for reporting
        top_20_engineers_data = []
        for engineer_data in detailed_engineers_data:
            if engineer_data['user_id'] in top_engineer_ids:
                # Get engineer name and additional info
                try:
                    engineer = User.objects.get(
                        user_id=engineer_data['user_id'])
                    engineer_data['first_name'] = engineer.first_name
                    engineer_data['last_name'] = engineer.last_name
                    engineer_data['skills'] = []

                    # Generate readable profile for AI
                    from api.serializers.engineers.user_serializers import UserDetailsSerializers
                    engineer_serializer = UserDetailsSerializers(engineer)
                    engineer_serialized_data = engineer_serializer.data
                    readable_profile = self._convert_engineer_to_readable_string(
                        engineer_serialized_data)
                    engineer_data['readable_profile'] = readable_profile

                except Exception as e:
                    logger.error(
                        f"Error getting engineer details for {engineer_data['user_id']}: {e}")
                    engineer_data['first_name'] = 'Unknown'
                    engineer_data['last_name'] = ''
                    engineer_data['skills'] = []
                    engineer_data['readable_profile'] = ''

                top_20_engineers_data.append(engineer_data)

        # Sort by score for proper ranking
        top_20_engineers_data.sort(
            key=lambda x: x['total_advanced_score'], reverse=True)

        # Log top 20 engineers with reporter
        reporter.log_top_20_engineers(top_20_engineers_data)

        for engineer_id in top_engineer_ids:
            logger.info(
                f"Top engineer ID: {engineer_id}, total_score: {user_scores[engineer_id]}")
        return result, top_engineer_ids

    def _calculate_detailed_skill_score(self, engineer_id, recruit_skill_codes, recruit):
        """
        Calculate detailed skill score using formula: (Skill_Code_match × Skill_Level_match) for each skill
        Returns the total score for all matching skills
        """
        try:
            # Get engineer's skills (EngSkill already imported from api.models.eng)
            engineer_skills = EngSkill.objects.filter(engineer_id=engineer_id)

            total_skill_score = 0

            # Get recruit skill levels for comparison
            recruit_skill_levels = {}
            for i in range(1, 4):  # skill_code1, skill_code2, skill_code3
                skill_code = getattr(recruit, f'skill_code{i}', None)
                skill_level = getattr(recruit, f'skill_level{i}', None)
                if skill_code and skill_level:
                    recruit_skill_levels[skill_code] = skill_level

            # Calculate score for each engineer skill that matches recruit requirements
            for eng_skill in engineer_skills:
                if eng_skill.skill_code in recruit_skill_codes:
                    # Skill code match = 1 point
                    skill_code_score = 1

                    # Skill level match score (1-10 based on level comparison)
                    skill_level_score = self._calculate_skill_level_score(
                        eng_skill.level_type,
                        recruit_skill_levels.get(eng_skill.skill_code, 1)
                    )

                    # Total score for this skill = Skill_Code × Skill_Level
                    skill_total = skill_code_score * skill_level_score
                    total_skill_score += skill_total

                    logger.info(f"Engineer {engineer_id} - Skill {eng_skill.skill_code}: "
                                f"Code({skill_code_score}) × Level({skill_level_score}) = {skill_total}")

            logger.info(
                f"Engineer {engineer_id} - Total Skill Score: {total_skill_score}")
            return total_skill_score

        except Exception as e:
            logger.error(
                f"Error calculating detailed skill score for engineer {engineer_id}: {e}")
            return 0

    def _calculate_skill_level_score(self, engineer_level, recruit_level):
        """
        Calculate skill level matching score
        Both engineer_level and recruit_level are integers (1-6)
        Returns score from 1-10 based on how well they match
        """
        try:
            engineer_level = int(engineer_level) if engineer_level else 1
            recruit_level = int(recruit_level) if recruit_level else 1

            # Perfect match gets 10 points
            if engineer_level == recruit_level:
                return 10

            # Calculate score based on level difference
            level_diff = abs(engineer_level - recruit_level)

            # Scoring system:
            # Same level: 10 points
            # 1 level difference: 8 points
            # 2 level difference: 6 points
            # 3 level difference: 4 points
            # 4+ level difference: 2 points
            if level_diff == 1:
                return 8
            elif level_diff == 2:
                return 6
            elif level_diff == 3:
                return 4
            else:
                return 2

        except:
            return 5  # Default score if calculation fails

    def _calculate_age_score(self, engineer_id, recruit):
        """
        Calculate age matching score between engineer and recruit requirements
        Returns score from 0-10 based on how well engineer age fits recruit age range
        """
        try:
            from api.models.user import User

            # Get engineer age
            engineer = User.objects.get(user_id=engineer_id)
            engineer_age = engineer.age if engineer.age else 0

            # Get recruit age requirements
            recruit_age_from = recruit.age_from if recruit.age_from else 0
            recruit_age_to = recruit.age_to if recruit.age_to else 100

            # If no age requirements, give neutral score
            if recruit_age_from == 0 and recruit_age_to == 100:
                return 5

            # Perfect match: engineer age within recruit range
            if recruit_age_from <= engineer_age <= recruit_age_to:
                return 10

            # Calculate score based on how far outside the range
            if engineer_age < recruit_age_from:
                age_diff = recruit_age_from - engineer_age
            else:
                age_diff = engineer_age - recruit_age_to

            # Scoring system:
            # Within range: 10 points
            # 1-2 years outside: 8 points
            # 3-5 years outside: 6 points
            # 6-10 years outside: 4 points
            # 11+ years outside: 2 points
            if age_diff <= 2:
                return 8
            elif age_diff <= 5:
                return 6
            elif age_diff <= 10:
                return 4
            else:
                return 2

        except Exception as e:
            logger.error(
                f"Error calculating age score for engineer {engineer_id}: {e}")
            return 5  # Default score if calculation fails

    def rag_ai_recommend(self, query, recruit, engineerIds, user=None):
        """
        Generate AI recommendations and return filtered engineer IDs
        Also stores recommendations for later retrieval in API responses
        """
        logger.info(f"=== RAG AI RECOMMEND STARTED ===")

        # convert user query to string, so AI can understand
        engineer_ids = engineerIds
        # take 20 engineers
        engineer_profiles = []
        engineer_id_to_profile_map = {}  # Map engineer_id to profile for AI matching

        for engineer_id in engineer_ids:
            engineer = User.objects.get(user_id=engineer_id)
            engineer_serializer = UserDetailsSerializers(engineer)
            engineer_data = engineer_serializer.data

            # Convert to readable string for AI understanding
            readable_profile = self._convert_engineer_to_readable_string(
                engineer_data)
            engineer_profiles.append(readable_profile)

            # Store mapping for AI to match engineer_id with profile index
            # AI uses 1-based indexing
            engineer_id_to_profile_map[len(engineer_profiles)] = engineer_id

        # Get company recruits if user is provided

        recruit_serializer = RecruitmentSerializer(recruit)
        recruit_data = recruit_serializer.data

        # Convert to readable string for AI understanding
        readable_recruit = self._convert_recruit_company_to_readable_string(
            recruit_data)

        full_prompt = ai_engine.generate_prompt(
            engineer_profiles, readable_recruit)

        # Check if we have existing AI recommendations for this exact prompt
        existing_recommendations = self._check_existing_ai_prompt(
            user, full_prompt)

        # Generate AI recommendations using RAG system with real AI service
        ai_recommendations = {}
        top_engineer_ids = []

        if existing_recommendations:
            ai_recommendations = existing_recommendations
        else:
            ai_recommendations = ai_helper.get_ai_recruitment_recommendations(
                engineer_profiles=engineer_profiles,
                company_recruits=recruit_data,
                use_gemini=True  # Use Gemini by default
            )

        # Process AI recommendations and map back to actual engineer IDs (for both existing and new)
        if ai_recommendations:
            top_engineers = ai_recommendations.get('top_5_engineers', [])
            logger.info(
                f"Processing {len(top_engineers)} recommended engineers:")

            for i, engineer_rec in enumerate(top_engineers, 1):
                ai_engineer_id = engineer_rec.get('engineer_id', 'Unknown')

                # Map AI engineer_id (1-based index) to actual engineer_id if needed
                if isinstance(ai_engineer_id, int) and ai_engineer_id <= len(engineer_profiles):
                    actual_engineer_id = engineer_id_to_profile_map.get(
                        ai_engineer_id)
                    if actual_engineer_id:
                        top_engineer_ids.append(actual_engineer_id)
                        # Update the recommendation with actual engineer_id
                        engineer_rec['engineer_id'] = actual_engineer_id

                        match_score = engineer_rec.get('match_score', 0)

                        logger.info(
                            f"{i}. Engineer ID: {actual_engineer_id} (Score: {match_score}%)")
                else:
                    # Already has actual engineer_id (from existing recommendations)
                    if ai_engineer_id in engineer_ids:
                        top_engineer_ids.append(ai_engineer_id)
                        match_score = engineer_rec.get('match_score', 0)
                        logger.info(
                            f"{i}. Engineer ID: {ai_engineer_id}, (Score: {match_score}%)")

            logger.info("=== END AI RECOMMENDATIONS ===")

            # Store recommendations with actual engineer IDs for later retrieval in API responses
            if not existing_recommendations:
                self._store_ai_recommendations(
                    user, full_prompt, ai_recommendations)

        return top_engineer_ids, ai_recommendations

    def _store_ai_recommendations(self, user, prompt, ai_recommendations):
        AiPromptHistory.objects.create(
            company_id=user.company_id,
            prompt=prompt,
            result=json.dumps(ai_recommendations, ensure_ascii=False),
            created_at=timezone.now()
        )

    def _check_existing_ai_prompt(self, user, prompt):
        """Check if the same prompt exists in database and return cached result"""
        try:

            if not user or not prompt:
                return None
            # Look for existing prompt for this company
            existing_history = AiPromptHistory.objects.filter(
                company_id=user.company_id,
                prompt=prompt
            ).order_by('-created_at').first()
            if existing_history and existing_history.result:
                return json.loads(existing_history.result)
            return None
        except Exception as e:
            return None

    def _convert_engineer_to_readable_string(self, engineer_data):
        """Convert engineer data to a readable string format for AI understanding"""
        profile_parts = []

        # Basic Information
        name = f"{engineer_data.get('first_name', '')} {engineer_data.get('last_name', '')}".strip(
        )
        if name:
            profile_parts.append(f"Name: {name}")

        if engineer_data.get('nickname'):
            profile_parts.append(f"Nickname: {engineer_data['nickname']}")

        # Professional Summary
        if engineer_data.get('professional_summary'):
            profile_parts.append(
                f"Professional Summary: {engineer_data['professional_summary']}")

        # Skills
        skills = engineer_data.get('skills', [])
        if skills:
            skill_descriptions = []
            for skill in skills:
                skill_name = skill.get('skill_name', '')
                level_type = skill.get('level_type', 1)
                level_name = get_skill_level_name_en(level_type)
                if skill_name:
                    skill_desc = f"{skill_name}"
                    if level_name:
                        skill_desc += f" ({level_name})"
                    skill_descriptions.append(skill_desc)

            if skill_descriptions:
                profile_parts.append(
                    f"Technical Skills: {', '.join(skill_descriptions)}")

        # Skills for CV Display
        skills_for_cv_display = engineer_data.get('skills_for_cv_display', '')
        if skills_for_cv_display:
            profile_parts.append(f"CV Skills: {skills_for_cv_display}")

        # Work Experience
        experiences = engineer_data.get('experiences', [])
        if experiences:
            exp_descriptions = []
            for exp in experiences:
                company = exp.get('company_name', '')
                job_desc = exp.get('job_description', '')
                entering_date = exp.get('entering_date', '')
                quitting_date = exp.get('quitting_date', '')

                if company:
                    exp_desc = f"Worked at {company}"
                    if entering_date:
                        exp_desc += f" from {entering_date}"
                        if quitting_date:
                            exp_desc += f" to {quitting_date}"
                        else:
                            exp_desc += " to present"

                    if job_desc:
                        exp_desc += f". Job description: {job_desc}"

                    exp_descriptions.append(exp_desc)

            if exp_descriptions:
                profile_parts.append(
                    f"Work Experience: {'; '.join(exp_descriptions)}")

        # Desired Conditions
        desired_conditions = engineer_data.get('requirements', None)
        if desired_conditions:
            payroll_price_usd = desired_conditions.get(
                'payroll_price_usd', None)
            if payroll_price_usd:
                profile_parts.append(
                    f"Desired Conditions: Salary: {payroll_price_usd} USD")
            remote_code = desired_conditions.get('remote_code', None)
            if remote_code:
                remote_code_name = get_remote_code_name(remote_code)
                if remote_code_name:
                    profile_parts.append(
                        f"Desired Conditions: Remote Work: {remote_code_name}")
            place_code1_name = desired_conditions.get('place_code1_name', None)
            place_code2_name = desired_conditions.get('place_code2_name', None)
            place_code3_name = desired_conditions.get('place_code3_name', None)
            location_names = []
            if place_code1_name:
                location_names.append(place_code1_name)
            if place_code2_name:
                location_names.append(place_code2_name)
            if place_code3_name:
                location_names.append(place_code3_name)
            if location_names:
                profile_parts.append(
                    f"Desired Conditions: Work Location: {', '.join(location_names)}")

        # Education
        educations = engineer_data.get('educations', [])
        if educations:
            edu_descriptions = []
            for edu in educations:
                school = edu.get('school', '')
                faculty = edu.get('faculty', '')
                type_name = edu.get('type_name', '')

                if school:
                    edu_desc = f"Studied at {school}"
                    if faculty:
                        edu_desc += f", {faculty}"
                    if type_name:
                        edu_desc += f" ({type_name})"
                    edu_descriptions.append(edu_desc)

            if edu_descriptions:
                profile_parts.append(
                    f"Education: {'; '.join(edu_descriptions)}")

        # Languages
        languages = engineer_data.get('languages', [])
        if languages:
            lang_descriptions = []
            for lang in languages:
                lang_name = lang.get('language_name', '')
                language_level_type = lang.get('language_level_type', None)

                if lang_name:
                    lang_desc = f"{lang_name}"
                    if language_level_type:
                        level_name = get_language_level_model_name(
                            language_level_type)
                        lang_desc += f" ({level_name})"
                    lang_descriptions.append(lang_desc)

            if lang_descriptions:
                profile_parts.append(
                    f"Languages: {', '.join(lang_descriptions)}")

        # Qualifications/Certifications
        qualifications = engineer_data.get('qualifications', [])
        if qualifications:
            qual_descriptions = []
            for qual in qualifications:
                licence_name = qual.get('licence_name', '')
                license_code_name = qual.get('license_code_name', '')

                if licence_name or license_code_name:
                    qual_desc = licence_name or license_code_name
                    qual_descriptions.append(qual_desc)

            if qual_descriptions:
                profile_parts.append(
                    f"Certifications: {', '.join(qual_descriptions)}")

        # Self Assessment (if available)
        self_assessment = engineer_data.get('self_assesment')
        if self_assessment:
            assessment_parts = []
            if self_assessment.get('remote_job_description'):
                assessment_parts.append(
                    f"Remote work experience: {self_assessment['remote_job_description']}")

            # Add skill scores if available
            skill_scores = []
            if self_assessment.get('total_remote_skill'):
                skill_scores.append(
                    f"Remote work skills: {self_assessment['total_remote_skill']}/10")
            if self_assessment.get('total_global_skill'):
                skill_scores.append(
                    f"Global work skills: {self_assessment['total_global_skill']}/10")
            if self_assessment.get('total_communication_skill'):
                skill_scores.append(
                    f"Communication skills: {self_assessment['total_communication_skill']}/10")

            if skill_scores:
                assessment_parts.append(
                    f"Self-assessed skills: {', '.join(skill_scores)}")

            if assessment_parts:
                profile_parts.append(
                    f"Self Assessment: {'; '.join(assessment_parts)}")

        # Join all parts with line breaks for readability

        return '\n'.join(profile_parts)

    def _convert_recruit_company_to_readable_string(self, recruit_data):
        """Convert recruit data to a readable string format for AI understanding"""
        from utils.utils import get_city_name, get_academic_type_name
        from utils.constants import SexType
        import json
        import os

        profile_parts = []

        # Basic Information
        title = recruit_data.get('title', '')
        catch_copy = recruit_data.get('catch_copy', '')

        if title:
            profile_parts.append(f"Job Title: {title}")

        if catch_copy:
            profile_parts.append(f"Catch Copy: {catch_copy}")

        # Job Details
        content = recruit_data.get('content', '')
        if content:
            profile_parts.append(f"Job Description: {content}")

        # Employment Type (convert code to name)
        remote_code = recruit_data.get('remote_code', '')
        if remote_code:
            remote_code_name = get_remote_code_name(remote_code)
            if remote_code_name:
                profile_parts.append(f"Remote Work: {remote_code_name}")

        # Salary Information
        payroll_price_from = recruit_data.get('payroll_price_from', '')
        payroll_price_to = recruit_data.get('payroll_price_to', '')
        payroll_code = recruit_data.get('payroll_code', '')

        if payroll_price_from or payroll_price_to:
            salary_info = "Salary: "
            if payroll_price_from:
                salary_info += f"From {payroll_price_from}"
            if payroll_price_to:
                if payroll_price_from:
                    salary_info += f" to {payroll_price_to}"
                else:
                    salary_info += f"Up to {payroll_price_to}"
            if payroll_code:
                salary_info += f" {payroll_code}"
            profile_parts.append(salary_info)

        # Location Information (convert codes to full names)
        place_names = []
        for i in range(1, 4):
            place_code = recruit_data.get(f'place_code{i}', '')
            if place_code:
                try:
                    city_name = get_city_name(place_code, "en")
                    if city_name:
                        place_names.append(city_name)
                    else:
                        place_names.append(place_code)
                except:
                    place_names.append(place_code)

        if place_names:
            profile_parts.append(f"Work Locations: {', '.join(place_names)}")

        # Requirements
        requirements = []

        # Age requirements
        age_from = recruit_data.get('age_from', '')
        age_to = recruit_data.get('age_to', '')
        if age_from or age_to:
            age_req = "Age requirement: "
            if age_from and age_to:
                age_req += f"{age_from} to {age_to} years old"
            elif age_from:
                age_req += f"From {age_from} years old"
            elif age_to:
                age_req += f"Up to {age_to} years old"
            requirements.append(age_req)

        # Gender requirement (convert to readable format)
        sex_type = recruit_data.get('sex_type', '')
        if sex_type is not None and sex_type != '':
            try:
                sex_type_int = int(sex_type)
                if sex_type_int == SexType.MALE.value:
                    requirements.append("Gender: Male")
                elif sex_type_int == SexType.FEMALE.value:
                    requirements.append("Gender: Female")
                elif sex_type_int == SexType.OTHER.value:
                    requirements.append("Gender: Other")
                elif sex_type_int == SexType.UNKNOWN.value:
                    requirements.append("Gender: No preference")
            except:
                requirements.append(f"Gender: {sex_type}")

        # Education requirement (convert to readable format)
        last_academic_code = recruit_data.get('last_academic_code', '')
        if last_academic_code:
            try:
                academic_name = get_academic_type_name(
                    last_academic_code, "en")
                if academic_name:
                    requirements.append(f"Education: {academic_name}")
                else:
                    requirements.append(f"Education: {last_academic_code}")
            except:
                requirements.append(f"Education: {last_academic_code}")

        # Language requirements
        lang_requirements = []
        for i in range(1, 3):
            lang_code = recruit_data.get(f'language_code{i}', '')
            lang_level = recruit_data.get(f'language_level_type{i}', None)

            if lang_code:
                lang_req = f"{lang_code}"
                if lang_level:
                    lang_level_name = get_language_level_model_name(lang_level)
                    lang_req += f" (Level {lang_level_name})"
                lang_requirements.append(lang_req)

        if lang_requirements:
            requirements.append(f"Languages: {', '.join(lang_requirements)}")

        # Skills requirements
        skill_requirements = []
        for i in range(1, 4):
            skill_code = recruit_data.get(f'skill_code{i}', '')
            skill_name = get_skill_code_name(skill_code)
            skill_level = recruit_data.get(f'skill_level_type{i}', '')
            if skill_code:
                skill_req = f"{skill_name}"
                if skill_level:
                    skill_level_name = get_skill_level_name_en(skill_level)
                    skill_req += f" (Level {skill_level_name})"
                skill_requirements.append(skill_req)

        if skill_requirements:
            requirements.append(
                f"Required Skills: {', '.join(skill_requirements)}")

        # License requirements
        license_requirements = []
        for i in range(1, 4):
            license_code = recruit_data.get(f'licence_code{i}', '')
            license_name = recruit_data.get(f'licence_name{i}', '')
            license_point = recruit_data.get(f'licence_point{i}', '')
            if license_code or license_name:
                license_req = license_name or license_code
                if license_point:
                    license_req += f" (Points: {license_point})"
                license_requirements.append(license_req)

        if license_requirements:
            requirements.append(
                f"Required Licenses: {', '.join(license_requirements)}")

        if requirements:
            profile_parts.append("Requirements:")
            for req in requirements:
                profile_parts.append(f"  - {req}")

        # Company Information
        host_company = recruit_data.get('host_company', {})
        if host_company:
            company_name = host_company.get('name', '')
            if company_name:
                profile_parts.append(f"Company: {company_name}")

        # Join all parts with line breaks for readability
        return '\n'.join(profile_parts)
