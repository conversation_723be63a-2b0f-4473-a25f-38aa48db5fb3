# utils/logging_mixins.py

import logging
from django.utils.decorators import method_decorator
from django.views.decorators.csrf import csrf_exempt
from rest_framework import serializers
from functools import wraps
import os
logger = logging.getLogger('api_logger')


def log_method_call(func):
    @wraps(func)
    def wrapper(self, *args, **kwargs):
        logger.info(f"Called {func.__qualname__}, data: {kwargs}")
        return func(self, *args, **kwargs)
    return wrapper


class LoggingSerializerMixin(serializers.Serializer):
    """
    Mixin to log serialization and deserialization processes.
    """

    def to_representation(self, instance):
        data = super().to_representation(instance)
        try:
            show_log = os.getenv("SHOW_LOG_SERIALIZER", "False")
            if bool(show_log):
                logger.info(
                    f"{self.__class__.__name__} serialized data: {data}")
        except Exception as e:
            logger.error("Error in logging serialized data: ", e)

        return data


class BaseModelSerializer(LoggingSerializerMixin, serializers.ModelSerializer):
    pass


class BaseSerializer(LoggingSerializerMixin, serializers.Serializer):
    pass
