
from django.db import models


class MediumIntegerField(models.IntegerField):
    def db_type(self, connection):
        return 'mediumint'


class Bit1Field(models.IntegerField):
    def db_type(self, connection):
        return 'bit(1)'

    def from_db_value(self, value, expression, connection):

        if value is None:
            return value
        return int.from_bytes(value, byteorder='big')

    def get_prep_value(self, value):
        return value


class Bit2Field(models.IntegerField):
    def db_type(self, connection):
        return 'bit(2)'

    def from_db_value(self, value, expression, connection):

        if value is None:
            return value
        return int.from_bytes(value, byteorder='big')

    def get_prep_value(self, value):
        return value


class Bit3Field(models.IntegerField):
    def db_type(self, connection):
        return 'bit(3)'

    def from_db_value(self, value, expression, connection):
        if value is None:
            return value
        return int.from_bytes(value, byteorder='big')

    def get_prep_value(self, value):
        return value


class Bit4Field(models.IntegerField):
    def db_type(self, connection):
        return 'bit(4)'

    def from_db_value(self, value, expression, connection):
        if value is None:
            return value
        return int.from_bytes(value, byteorder='big')

    def get_prep_value(self, value):
        return value


class Bit5Field(models.IntegerField):
    def db_type(self, connection):
        return 'bit(5)'

    def from_db_value(self, value, expression, connection):
        if value is None:
            return value
        return int.from_bytes(value, byteorder='big')

    def get_prep_value(self, value):
        return value


class Bit6Field(models.IntegerField):

    def db_type(self, connection):
        return 'bit(6)'

    def from_db_value(self, value, expression, connection):
        if value is None:
            return value
        return int.from_bytes(value, byteorder='big')

    def get_prep_value(self, value):
        return value


class Bit7Field(models.IntegerField):
    def db_type(self, connection):
        return 'bit(7)'

    def from_db_value(self, value, expression, connection):
        if value is None:
            return value
        return int.from_bytes(value, byteorder='big')

    def get_prep_value(self, value):
        return value


class Bit11Field(models.IntegerField):

    def db_type(self, connection):
        return 'bit(11)'

    def from_db_value(self, value, expression, connection):
        if value is None:
            return value

        if isinstance(value, bytes):
            return int.from_bytes(value, byteorder='big')
        return value

    def get_prep_value(self, value):
        return value


class TinyTextField(models.TextField):
    def db_type(self, connection):
        return 'tinytext'


class DateTimeField(models.DateTimeField):
    def db_type(self, connection):
        return 'datetime'


class TinyInt2Field(models.IntegerField):
    def db_type(self, connection):
        return 'tinyint(2)'


class TinyInt3Field(models.IntegerField):
    def db_type(self, connection):
        return 'tinyint(3)'


class TinyInt4Field(models.IntegerField):
    def db_type(self, connection):
        return 'tinyint(4)'


class TinyInt5Field(models.IntegerField):
    def db_type(self, connection):
        return 'tinyint(5)'
