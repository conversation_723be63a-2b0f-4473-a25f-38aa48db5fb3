import os
import json
from core.settings.common import CURRENCY_RATE_PATH, CURRENT_CURRENCY_RATE_NEW_PATH
from datetime import datetime
import requests
import logging
logger = logging.getLogger('api_logger')
api_current_rate_1 = "https://www.floatrates.com/daily/usd.json"
api_current_rate_2 = "https://cdn.jsdelivr.net/npm/@fawazahmed0/currency-api@latest/v1/currencies/usd.json"
api_current_rate_3 = "https://api.coinmarketcap.com/data-api/v3/cryptocurrency/quote/latest?id=2781,2782,2783,2784,2785,2786,2787,2788,2789,2790,2791,2792,2793,2794,2795,2796,2797,2798,2799,2800,2801,2802,2803,2804,2805,2806,2807,2808,2809,2810,2811,2812,2823,3554,3544,2821,2817,2824,2819,2813,2820,3538,3566,3530,3540,2814,3573,1,1027,2010,1839,6636,52,1975,2,512,1831,7083,74,9023,9022,5824,6783&convertId=2781"


class CurrencyConverter:
    def __init__(self):

        self.rates = self.read_currency_rate()
        first_currency = self.rates['aud']
        update_date = first_currency['date']
        rate_date = datetime.strptime(
            update_date, "%a, %d %b %Y %H:%M:%S GMT")
        now = datetime.now()

        # check older than 1 day
        if (now - rate_date).days > 1 or self.rates.get("usd", None) is None:
            self.update_currency_rate()
            self.rates = self.read_currency_rate()

    def read_currency_rate(self):
        with open(CURRENCY_RATE_PATH, 'r') as file:
            self.rates = json.load(file)
        try:
            # check file exist first
            if os.path.exists(CURRENT_CURRENCY_RATE_NEW_PATH) == False:
                # copy data from CURRENCY_RATE_PATH to CURRENT_CURRENCY_RATE_NEW_PATH
                with open(CURRENCY_RATE_PATH, 'r') as file:
                    jsonData = json.load(file)
                with open(CURRENT_CURRENCY_RATE_NEW_PATH, 'w') as file:
                    json.dump(jsonData, file)
            else:
                with open(CURRENT_CURRENCY_RATE_NEW_PATH, 'r') as file:
                    jsonData = json.load(file)
                    self.rates = jsonData

        except Exception as e:
            logger.log(logging.ERROR, f"Error reading currency rate: {e}")
        return self.rates

    def update_currency_rate(self):
        response = requests.get(api_current_rate_1)
        if response.status_code == 200:
            data = response.json()
            # add one more item
            data["usd"] = {"code": "USD", "alphaCode": "USD", "numericCode": "001", "name": "Dollar",
                           "rate": 1, "date": data['aud']['date'], "inverseRate": 1}
            with open(CURRENT_CURRENCY_RATE_NEW_PATH, 'w') as file:
                json.dump(data, file)
        else:
            response = requests.get(api_current_rate_2)
            if response.status_code == 200:
                data = response.json()
                data_dict = data['usd']
                new_dict = {}
                for key in data_dict:
                    new_dict[key.lower()] = {
                        "code": key,
                        "alphaCode": key,
                        "rate": data_dict[key],
                        "date": datetime.now().strftime("%a, %d %b %Y %H:%M:%S GMT"),
                    }

                with open(CURRENT_CURRENCY_RATE_NEW_PATH, 'w') as file:
                    json.dump(new_dict, file)
            else:
                response = requests.get(api_current_rate_3)
                if response.status_code == 200:
                    data = response.json()
                    list_data = data['data']
                    new_dict = {}

                    for item in list_data:
                        quote = item['quotes'][0]
                        if quote.get("price", None) is not None:
                            rate = 1/quote["price"]
                            new_dict[item['symbol'].lower()] = {
                                "code": item['symbol'],
                                "alphaCode": item['symbol'],
                                "rate": rate,
                                "date": datetime.now().strftime("%a, %d %b %Y %H:%M:%S GMT"),
                            }
                    with open(CURRENT_CURRENCY_RATE_NEW_PATH, 'w') as file:
                        json.dump(new_dict, file)

    def convert(self, amount, from_currency, to_currency):
        try:
            from_currency = from_currency.lower()
            to_currency = to_currency.lower()

            from_rate = self.rates[from_currency]['rate']
            to_rate = self.rates[to_currency]['rate']
            # convert amount from decimal to float
            amount = float(amount)
            converted_amount = (amount / from_rate) * to_rate
            # max 3 decimal
            converted_amount = round(converted_amount, 3)
            return converted_amount

        except Exception as e:
            logger.log(logging.ERROR, f"Error converting currency: {e}")
            return

    def get_conversion_rate(self, from_currency, to_currency):
        try:
            from_currency = from_currency.lower()
            to_currency = to_currency.lower()

            from_rate = self.rates[from_currency]['rate']
            to_rate = self.rates[to_currency]['rate']
            conversion_rate = to_rate / from_rate
            return float(conversion_rate)

        except Exception as e:
            logger.log(logging.ERROR, f"Error getting conversion rate: {e}")
            return 1
