"""
AI Recommendation System for Engineer-Job Matching
"""
import json
import logging
from typing import List, Dict, Any, Optional

logger = logging.getLogger(__name__)


class AIRecommendationEngine:
    """
    AI-powered recommendation engine for matching engineers to job openings
    """

    def __init__(self):
        self.prompt_template = self._get_prompt_template()

    def _get_prompt_template(self) -> str:
        """Get the RAG prompt template for AI recommendation"""
        return """You are an AI recruitment specialist tasked with matching engineers to company job openings. 

**CONTEXT:**
You will receive:
1. A list of engineer profiles with their skills, experience, and background
2. A list of active job openings from a company with specific requirements

**TASK:**
Analyze the engineers and match them against the company's job requirements to provide intelligent recommendations.

**INPUT DATA:**

**ENGINEERS:**
{engineers_profiles}

**COMPANY JOB OPENINGS:**
{company_recruits}

**INSTRUCTIONS:**
1. Carefully analyze each engineer's profile including:
   - Technical skills and proficiency levels
   - Work experience and career history
   - Education background
   - Language abilities
   - Professional summary and CV highlights

2. Evaluate each job opening's requirements:
   - Required technical skills and levels
   - Experience requirements
   - Education requirements
   - Language requirements
   - Employment type and work conditions

3. Match engineers to jobs based on:
   - Skill compatibility and level matching
   - Experience relevance
   - Career progression fit
   - Location and work arrangement preferences
   - Overall profile alignment
```

**OUTPUT FORMAT FOR SUMMARY:**

Please provide the summary in this exact format using markdown bullet points (`•`):

• Technical skills: [e.g., Candidates show strong skills in Power BI, UI/UX, PHP...]
• Missing certifications/languages: [e.g., Many candidates lack language proficiency, especially in Vietnamese...]
• Experience level: [e.g., Wide range from junior to senior engineers...]
• Age mismatch: [e.g., Some job listings ask for unrealistic age ranges...]
• Versatile skills: [e.g., Several candidates can adapt to multiple roles...]
• English proficiency: [e.g., English remains a weakness for many candidates...]

• Top Candidate Recommendations:
• [Candidate Name]: [Key strengths: e.g., MySQL expert, Junior Cloud, past work at Qualcomm, fluent in Japanese; lacks Vietnamese and PHP but trainable.]

Each bullet point must be concise, one idea per line. Do not return paragraphs or merge points. Do not include icons or emojis.


**GUIDELINES:**
- Be specific and data-driven in your recommendations
- Highlight both strengths and potential concerns
- Consider cultural and language fit
- Prioritize engineers who match multiple job openings
- Provide actionable insights for recruiters
- Use a professional, analytical tone
- Include match scores (0-100) based on overall compatibility

**IMPORTANT:**
- Only recommend engineers who have genuine potential for the roles
- Be honest about skill gaps or mismatches
- Focus on value proposition for both engineer and company
- Consider career growth opportunities in your recommendations"""

    def prepare_input_data(self, engineer_profiles: List[str], company_recruits: List[str]) -> Dict[str, Any]:
        """
        Prepare input data for AI recommendation

        Args:
            engineer_profiles: List of readable engineer profile strings
            company_recruits: List of readable company recruit profile strings

        Returns:
            Dictionary containing formatted input data
        """
        try:
            # Format engineers profiles
            engineers_text = ""
            for i, profile in enumerate(engineer_profiles, 1):
                engineers_text += f"\n**ENGINEER {i}:**\n{profile}\n"

            # Format company recruits
            recruits_text = ""
            for i, recruit in enumerate(company_recruits, 1):
                recruits_text += f"\n**JOB OPENING {i}:**\n{recruit}\n"

            return {
                "engineers_profiles": engineers_text,
                "company_recruits": recruits_text,
                "total_engineers": len(engineer_profiles),
                "total_jobs": len(company_recruits)
            }

        except Exception as e:
            logger.error(f"Error preparing input data: {e}")
            return {
                "engineers_profiles": "",
                "company_recruits": "",
                "total_engineers": 0,
                "total_jobs": 0
            }

    def generate_prompt(self, engineer_profiles: List[str], company_recruits: str) -> str:
        """
        Generate the complete prompt for AI recommendation

        Args:
            engineer_profiles: List of readable engineer profile strings
            company_recruits: List of readable company recruit profile strings

        Returns:
            Complete prompt string ready for AI processing
        """
        try:
            input_data = self.prepare_input_data(
                engineer_profiles, company_recruits)

            prompt = self.prompt_template.format(
                engineers_profiles=input_data["engineers_profiles"],
                company_recruits=input_data["company_recruits"]
            )

            logger.info(
                f"Generated prompt for {input_data['total_engineers']} engineers and {input_data['total_jobs']} jobs")
            return prompt

        except Exception as e:
            logger.error(f"Error generating prompt: {e}")
            return ""

    def parse_ai_response(self, ai_response: str) -> Optional[Dict[str, Any]]:
        """
        Parse AI response and validate the format

        Args:
            ai_response: Raw response from AI service

        Returns:
            Parsed and validated response dictionary, or None if invalid
        """
        try:
            # Try to extract JSON from the response
            # AI might return markdown code blocks, so we need to clean it
            response_text = ai_response.strip()

            # Remove markdown code blocks if present
            if response_text.startswith("```json"):
                response_text = response_text[7:]
            if response_text.endswith("```"):
                response_text = response_text[:-3]

            # Parse JSON
            parsed_response = json.loads(response_text.strip())

            # Validate required fields
            if not self._validate_response_format(parsed_response):
                logger.error("AI response format validation failed")
                return None

            logger.info("Successfully parsed and validated AI response")
            return parsed_response

        except json.JSONDecodeError as e:
            logger.error(f"Failed to parse AI response as JSON: {e}")
            return None
        except Exception as e:
            logger.error(f"Error parsing AI response: {e}")
            return None

    def _validate_response_format(self, response: Dict[str, Any]) -> bool:
        """
        Validate that the AI response has the expected format

        Args:
            response: Parsed response dictionary

        Returns:
            True if format is valid, False otherwise
        """
        try:
            # Check required top-level fields
            if "summary" not in response or "top_5_engineers" not in response:
                return False

            # Check that top_5_engineers is a list
            if not isinstance(response["top_5_engineers"], list):
                return False

            # Validate each engineer recommendation
            for engineer in response["top_5_engineers"]:
                required_fields = ["engineer_id",
                                   "match_score", "reason_ai_recommend"]
                if not all(field in engineer for field in required_fields):
                    return False

                # Validate data types
                if not isinstance(engineer["engineer_id"], (int, str)):
                    return False
                if not isinstance(engineer["match_score"], (int, float)):
                    return False
                if not isinstance(engineer["reason_ai_recommend"], str):
                    return False

            return True

        except Exception as e:
            logger.error(f"Error validating response format: {e}")
            return False

    def get_recommendation(self, engineer_profiles: List[str], company_recruits: List[str],
                           ai_service_call: callable = None) -> Dict[str, Any]:
        """
        Get AI recommendation for engineer-job matching

        Args:
            engineer_profiles: List of readable engineer profile strings
            company_recruits: List of readable company recruit profile strings
            ai_service_call: Optional callable to AI service (for future integration)

        Returns:
            AI recommendation response dictionary
        """
        try:
            # Generate prompt
            prompt = self.generate_prompt(engineer_profiles, company_recruits)

            if not prompt:
                logger.error("Failed to generate prompt")
                return None

            # TODO: Call actual AI service here
            # For now, return a mock response for testing
            if ai_service_call:
                try:
                    ai_response = ai_service_call(prompt)
                    parsed_response = self.parse_ai_response(ai_response)
                    if parsed_response:
                        return parsed_response
                except Exception as e:
                    logger.error(f"AI service call failed: {e}")

            # Log the prompt for debugging (can be removed in production)
            logger.info(f"Generated AI prompt (length: {len(prompt)} chars)")
            logger.debug(f"AI Prompt:\n{prompt}")

            return None

        except Exception as e:
            logger.error(f"Error in get_recommendation: {e}")
            return None
