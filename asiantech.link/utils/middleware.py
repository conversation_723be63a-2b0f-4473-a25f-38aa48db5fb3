from rest_framework.views import exception_handler
from core.settings.common import LANGUAGES
from utils.utils import is_access_token_valid
from core.settings.common import LANGUAGE_CODE
from django.utils import translation
import logging
logger = logging.getLogger("api_logger")


class LoggingMiddleware:
    def __init__(self, get_response):
        self.get_response = get_response

    def __call__(self, request):
        try:
            logger.info(f"Start {request.method} {request.path}")
        except Exception as e:
            print(e)
        response = self.get_response(request)
        return response


def custom_exception_handler(exc, context):
    response = exception_handler(exc, context)
    if response is not None:
        logger.error(f"Exception: {exc}")
    return response


class LanguageMiddleware:
    def __init__(self, get_response):
        self.get_response = get_response
        self.supported_languages = [lang[0] for lang in LANGUAGES]

    def __call__(self, request):
        language_header = request.headers.get('Accept-Language')
        if language_header:
            # Extract the primary language preference from the header
            primary_language = language_header.split(',')[0].split('-')[0]
            if primary_language in self.supported_languages:
                translation.activate(primary_language)
                request.LANGUAGE_CODE = primary_language
            else:
                translation.activate(LANGUAGE_CODE)
                request.LANGUAGE_CODE = LANGUAGE_CODE
        else:
            translation.activate(LANGUAGE_CODE)
            request.LANGUAGE_CODE = LANGUAGE_CODE
        response = self.get_response(request)
        translation.deactivate()
        return response


class CheckBlacklistMiddleware:
    def __init__(self, get_response):
        self.get_response = get_response

    def __call__(self, request):
        response = self.process_request(request)
        if response:
            return response
        return self.get_response(request)

    def process_request(self, request):
        auth_header = request.META.get('HTTP_AUTHORIZATION', '')
        if auth_header.startswith('Bearer '):
            try:
                token = auth_header.split(' ')[1]
                is_token_valid = is_access_token_valid(token)
                if not is_token_valid:
                    request.META.pop('HTTP_AUTHORIZATION', None)
            except Exception as e:
                request.META.pop('HTTP_AUTHORIZATION', None)

        return None
