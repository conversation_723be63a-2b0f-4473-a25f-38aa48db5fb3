"""
Employment Type Matching System

This module provides functionality for calculating matching scores between
recruitment employment types and engineer employment preferences.
"""


class EmploymentType:
    """Employment type codes based on employ_code.json"""
    FULL_TIME = "1"
    PART_TIME = "2"
    TEMPORARY = "3"
    CONTRACT = "4"
    FREELANCE = "5"


class EmploymentMatchingScore:
    """
    Employment type matching score matrix.

    This class provides a scoring system for matching between what companies
    need (recruit employment type) and what engineers want (engineer employment type).

    Scoring Logic:
    - Perfect match (same type): 10 points
    - Good compatibility: 6-8 points
    - Moderate compatibility: 5-7 points
    - Low compatibility: 4-5 points

    Usage:
        score = EmploymentMatchingScore.get_employment_match_score("1", "1")  # 10
        score = EmploymentMatchingScore.get_employment_match_score("1", "5")  # 4
    """

    # Employment type matching score matrix
    # Format: {recruit_employ_code: {engineer_employ_code: score}}
    EMPLOYMENT_SCORE_MATRIX = {
        # Company needs Full-time
        EmploymentType.FULL_TIME: {
            # Full-time → Full-time (Perfect match)
            EmploymentType.FULL_TIME: 10,
            # Full-time → Part-time (Low compatibility)
            EmploymentType.PART_TIME: 5,
            EmploymentType.TEMPORARY: 6,     # Full-time → Temporary (Moderate)
            EmploymentType.CONTRACT: 7,      # Full-time → Contract (Good)
            EmploymentType.FREELANCE: 4,     # Full-time → Freelance (Low)
        },
        # Company needs Part-time
        EmploymentType.PART_TIME: {
            EmploymentType.FULL_TIME: 5,     # Part-time → Full-time (Low)
            # Part-time → Part-time (Perfect match)
            EmploymentType.PART_TIME: 10,
            EmploymentType.TEMPORARY: 8,     # Part-time → Temporary (Good)
            EmploymentType.CONTRACT: 6,      # Part-time → Contract (Moderate)
            EmploymentType.FREELANCE: 5,     # Part-time → Freelance (Low)
        },
        # Company needs Temporary
        EmploymentType.TEMPORARY: {
            EmploymentType.FULL_TIME: 6,     # Temporary → Full-time (Moderate)
            EmploymentType.PART_TIME: 8,     # Temporary → Part-time (Good)
            # Temporary → Temporary (Perfect match)
            EmploymentType.TEMPORARY: 10,
            EmploymentType.CONTRACT: 7,      # Temporary → Contract (Good)
            EmploymentType.FREELANCE: 6,     # Temporary → Freelance (Moderate)
        },
        # Company needs Contract
        EmploymentType.CONTRACT: {
            EmploymentType.FULL_TIME: 7,     # Contract → Full-time (Good)
            EmploymentType.PART_TIME: 6,     # Contract → Part-time (Moderate)
            EmploymentType.TEMPORARY: 7,     # Contract → Temporary (Good)
            # Contract → Contract (Perfect match)
            EmploymentType.CONTRACT: 10,
            EmploymentType.FREELANCE: 7,     # Contract → Freelance (Good)
        },
        # Company needs Freelance
        EmploymentType.FREELANCE: {
            EmploymentType.FULL_TIME: 4,     # Freelance → Full-time (Low)
            EmploymentType.PART_TIME: 5,     # Freelance → Part-time (Low)
            EmploymentType.TEMPORARY: 6,     # Freelance → Temporary (Moderate)
            EmploymentType.CONTRACT: 7,      # Freelance → Contract (Good)
            # Freelance → Freelance (Perfect match)
            EmploymentType.FREELANCE: 10,
        },
    }

    @classmethod
    def get_employment_match_score(cls, recruit_employ_code, engineer_employ_code):
        """
        Get employment type matching score between recruit and engineer.

        Args:
            recruit_employ_code (str): Employment type code that company needs
            engineer_employ_code (str): Employment type code that engineer wants

        Returns:
            int: Matching score (0-10), returns 0 if codes are invalid

        Examples:
            >>> EmploymentMatchingScore.get_employment_match_score("1", "1")
            10
            >>> EmploymentMatchingScore.get_employment_match_score("1", "5")
            4
            >>> EmploymentMatchingScore.get_employment_match_score("invalid", "1")
            0
        """
        try:
            return cls.EMPLOYMENT_SCORE_MATRIX.get(recruit_employ_code, {}).get(engineer_employ_code, 0)
        except (KeyError, TypeError):
            return 0

    @classmethod
    def get_all_employment_types(cls):
        """
        Get all available employment type codes.

        Returns:
            list: List of employment type codes
        """
        return [
            EmploymentType.FULL_TIME,
            EmploymentType.PART_TIME,
            EmploymentType.TEMPORARY,
            EmploymentType.CONTRACT,
            EmploymentType.FREELANCE,
        ]

    @classmethod
    def get_employment_type_name(cls, employ_code):
        """
        Get human-readable name for employment type code.

        Args:
            employ_code (str): Employment type code

        Returns:
            str: Human-readable employment type name
        """
        employment_names = {
            EmploymentType.FULL_TIME: "Full-time",
            EmploymentType.PART_TIME: "Part-time",
            EmploymentType.TEMPORARY: "Temporary",
            EmploymentType.CONTRACT: "Contract",
            EmploymentType.FREELANCE: "Freelance",
        }
        return employment_names.get(employ_code, "Unknown")

    @classmethod
    def get_score_matrix_for_recruit(cls, recruit_employ_code):
        """
        Get all matching scores for a specific recruit employment type.

        Args:
            recruit_employ_code (str): Employment type code that company needs

        Returns:
            dict: Dictionary mapping engineer employment codes to scores
        """
        return cls.EMPLOYMENT_SCORE_MATRIX.get(recruit_employ_code, {})


class RemoteWorkType:
    """Remote work type codes"""
    ONSITE = "1"
    HYBRID = "2"
    REMOTE = "3"


class RemoteWorkMatchingScore:
    """
    Remote work type matching score matrix.

    This class provides scoring for matching between company remote work requirements
    and engineer remote work preferences.
    """

    REMOTE_SCORE_MATRIX = {
        # Company needs Onsite
        RemoteWorkType.ONSITE: {
            RemoteWorkType.ONSITE: 10,    # Onsite → Onsite (Perfect match)
            RemoteWorkType.HYBRID: 6,     # Onsite → Hybrid (Moderate)
            RemoteWorkType.REMOTE: 4,     # Onsite → Remote (Low)
        },
        # Company needs Hybrid
        RemoteWorkType.HYBRID: {
            RemoteWorkType.ONSITE: 6,     # Hybrid → Onsite (Moderate)
            RemoteWorkType.HYBRID: 10,    # Hybrid → Hybrid (Perfect match)
            RemoteWorkType.REMOTE: 7,     # Hybrid → Remote (Good)
        },
        # Company needs Remote
        RemoteWorkType.REMOTE: {
            RemoteWorkType.ONSITE: 4,     # Remote → Onsite (Low)
            RemoteWorkType.HYBRID: 7,     # Remote → Hybrid (Good)
            RemoteWorkType.REMOTE: 10,    # Remote → Remote (Perfect match)
        },
    }

    @classmethod
    def get_remote_match_score(cls, recruit_remote_code, engineer_remote_code):
        """
        Get remote work type matching score.

        Args:
            recruit_remote_code (str): Remote work code that company needs
            engineer_remote_code (str): Remote work code that engineer wants

        Returns:
            int: Matching score (0-10), returns 0 if codes are invalid
        """
        try:
            return cls.REMOTE_SCORE_MATRIX.get(recruit_remote_code, {}).get(engineer_remote_code, 0)
        except (KeyError, TypeError):
            return 0


class ExperienceLevel:
    """Experience level codes"""
    LESS_THAN_1_YEAR = "1"
    LESS_THAN_2_YEAR = "2"
    LESS_THAN_3_YEAR = "3"
    LESS_THAN_5_YEAR = "5"
    MORE_THAN_5_YEARS = "6"


class SkillExperienceMatchingScore:
    """
    Skill experience matching score matrix.

    This class provides scoring for matching between required skill experience
    and engineer's actual experience.
    """

    SKILL_EXPERIENCE_MATRIX = {
        # Job requires less than 1 year
        ExperienceLevel.LESS_THAN_1_YEAR: {
            ExperienceLevel.LESS_THAN_1_YEAR: 10,    # Perfect match
            ExperienceLevel.LESS_THAN_2_YEAR: 10,    # Overqualified but good
            ExperienceLevel.LESS_THAN_3_YEAR: 9,     # Overqualified
            ExperienceLevel.LESS_THAN_5_YEAR: 8,     # Overqualified
            ExperienceLevel.MORE_THAN_5_YEARS: 7,    # Highly overqualified
        },
        # Job requires less than 2 years
        ExperienceLevel.LESS_THAN_2_YEAR: {
            ExperienceLevel.LESS_THAN_1_YEAR: 6,     # Underqualified
            ExperienceLevel.LESS_THAN_2_YEAR: 10,    # Perfect match
            ExperienceLevel.LESS_THAN_3_YEAR: 10,    # Good match
            ExperienceLevel.LESS_THAN_5_YEAR: 9,     # Overqualified
            ExperienceLevel.MORE_THAN_5_YEARS: 8,    # Overqualified
        },
        # Job requires less than 3 years
        ExperienceLevel.LESS_THAN_3_YEAR: {
            ExperienceLevel.LESS_THAN_1_YEAR: 3,     # Significantly underqualified
            ExperienceLevel.LESS_THAN_2_YEAR: 6,     # Underqualified
            ExperienceLevel.LESS_THAN_3_YEAR: 10,    # Perfect match
            ExperienceLevel.LESS_THAN_5_YEAR: 10,    # Good match
            ExperienceLevel.MORE_THAN_5_YEARS: 9,    # Overqualified
        },
        # Job requires less than 5 years
        ExperienceLevel.LESS_THAN_5_YEAR: {
            ExperienceLevel.LESS_THAN_1_YEAR: 1,     # Very underqualified
            ExperienceLevel.LESS_THAN_2_YEAR: 4,     # Underqualified
            ExperienceLevel.LESS_THAN_3_YEAR: 7,     # Somewhat underqualified
            ExperienceLevel.LESS_THAN_5_YEAR: 10,    # Perfect match
            ExperienceLevel.MORE_THAN_5_YEARS: 10,   # Good match
        },
        # Job requires more than 5 years
        ExperienceLevel.MORE_THAN_5_YEARS: {
            ExperienceLevel.LESS_THAN_1_YEAR: 0,     # Completely underqualified
            ExperienceLevel.LESS_THAN_2_YEAR: 2,     # Very underqualified
            ExperienceLevel.LESS_THAN_3_YEAR: 5,     # Underqualified
            ExperienceLevel.LESS_THAN_5_YEAR: 8,     # Somewhat underqualified
            ExperienceLevel.MORE_THAN_5_YEARS: 10,   # Perfect match
        },
    }

    @classmethod
    def get_skill_experience_score(cls, required_experience, engineer_experience):
        """
        Get skill experience matching score.

        Args:
            required_experience (str): Experience level required by job
            engineer_experience (str): Experience level of engineer

        Returns:
            int: Matching score (0-10), returns 0 if codes are invalid
        """
        try:
            return cls.SKILL_EXPERIENCE_MATRIX.get(required_experience, {}).get(engineer_experience, 0)
        except (KeyError, TypeError):
            return 0


class SalaryMatchingScore:
    """
    Salary matching score based on salary difference percentage.

    This class provides scoring for salary compatibility between
    engineer expectations and job offers.
    """

    @classmethod
    def get_salary_match_score(cls, engineer_salary, job_min_salary, job_max_salary):
        """
        Get salary matching score based on engineer's salary expectation vs job salary range.

        Args:
            engineer_salary (float): Engineer's expected salary
            job_min_salary (float): Job's minimum salary
            job_max_salary (float): Job's maximum salary

        Returns:
            int: Matching score (0-10)

        Scoring Logic:
            - Engineer salary within job range: 10 points
            - Engineer salary < job min: 10 points (good for company)
            - Engineer salary > job max ≤ 10%: 8 points
            - Engineer salary > job max ≤ 20%: 6 points
            - Engineer salary > job max ≤ 30%: 4 points
            - Engineer salary > job max > 30%: 1 point
        """
        try:
            engineer_salary = float(engineer_salary) if engineer_salary else 0
            job_min_salary = float(job_min_salary) if job_min_salary else 0
            job_max_salary = float(job_max_salary) if job_max_salary else 0

            # If no salary information available
            if engineer_salary == 0 or job_max_salary == 0:
                return 5  # Neutral score when no data

            # Engineer salary is less than job minimum (good for company)
            if engineer_salary < job_min_salary:
                return 10

            # Engineer salary is within job range
            if job_min_salary <= engineer_salary <= job_max_salary:
                return 10

            # Engineer salary exceeds job maximum
            if engineer_salary > job_max_salary:
                excess_percentage = (
                    (engineer_salary - job_max_salary) / job_max_salary) * 100

                if excess_percentage <= 10:
                    return 8
                elif excess_percentage <= 20:
                    return 6
                elif excess_percentage <= 30:
                    return 4
                else:
                    return 1

            return 0

        except (ValueError, TypeError, ZeroDivisionError):
            return 0


def get_employment_match_score(recruit_employ_code, engineer_employ_code):
    """
    Convenience function to get employment type matching score.

    This is a wrapper around EmploymentMatchingScore.get_employment_match_score()
    for easier importing and usage.

    Args:
        recruit_employ_code (str): Employment type code that company needs
        engineer_employ_code (str): Employment type code that engineer wants

    Returns:
        int: Matching score (0-10), returns 0 if codes are invalid
    """
    return EmploymentMatchingScore.get_employment_match_score(recruit_employ_code, engineer_employ_code)


def get_remote_match_score(recruit_remote_code, engineer_remote_code):
    """
    Convenience function to get remote work matching score.

    Args:
        recruit_remote_code (str): Remote work code that company needs
        engineer_remote_code (str): Remote work code that engineer wants

    Returns:
        int: Matching score (0-10), returns 0 if codes are invalid
    """
    return RemoteWorkMatchingScore.get_remote_match_score(recruit_remote_code, engineer_remote_code)


def get_skill_experience_score(required_experience, engineer_experience):
    """
    Convenience function to get skill experience matching score.

    Args:
        required_experience (str): Experience level required by job
        engineer_experience (str): Experience level of engineer

    Returns:
        int: Matching score (0-10), returns 0 if codes are invalid
    """
    return SkillExperienceMatchingScore.get_skill_experience_score(required_experience, engineer_experience)


def get_salary_match_score(engineer_salary, job_min_salary, job_max_salary):
    """
    Convenience function to get salary matching score.

    Args:
        engineer_salary (float): Engineer's expected salary
        job_min_salary (float): Job's minimum salary
        job_max_salary (float): Job's maximum salary

    Returns:
        int: Matching score (0-10)
    """
    return SalaryMatchingScore.get_salary_match_score(engineer_salary, job_min_salary, job_max_salary)
