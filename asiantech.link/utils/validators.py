from utils.responses import *
from rest_framework import status
from django.utils.translation import gettext as _
import re
import string


def validate_serializer(serializer):
    if serializer.is_valid():
        return True, None
    else:
        errors = []

        for field, messages in serializer.errors.items():
            for message in messages:
                errors.append({
                    'message': message,
                    'field': field,
                })
        return False, errors


def check_email_valid(email, max_length=201):
    # Allowed characters  regex
    allowed_chars_regex = r'^[\w\-\.\+]+@([\w\-]+\.)+[\w\-]{2,}$'

    # Check overall length
    if len(email) >= max_length:
        return False, _("Email is too long")

    # Split email into local and domain parts
    try:
        local_part, domain_part = email.split('@')
    except ValueError:
        return False, _("Email must contain a single '@' symbol")

    # Check local part length
    if len(local_part) >= 64:
        return False, _("Local part of the email is too long (>= 64 characters)")

    # Check allowed characters
    if not re.match(allowed_chars_regex, email):
        return False, _("Email contains invalid characters")

    # Check for consecutive periods
    if '..' in email:
        return False, _("Email contains consecutive periods")

    # Check for periods at the start or end of local part or before @
    if local_part.startswith('.') or local_part.endswith('.') or email.endswith('@'):
        return False, _("Email has periods at invalid positions (start/end of local part or end of email)")

    # Check domain part is not an IP address
    if re.match(r'^\d{1,3}(\.\d{1,3}){3}$', domain_part):
        return False, _("Domain part of the email should not be an IP address")

    return True, _("Email is valid")


def check_password_valid(password):
    # Check length requirements
    if not (8 <= len(password) <= 20):
        return False, _("Password length must be between 8 and 20 characters")

    # Check if the password contains any full-width characters
    full_width_regex = r'[\u3000-\u303F\uFF01-\uFF5E\uFF10-\uFF19\uFF21-\uFF3A\uFF41-\uFF5A]'
    if re.search(full_width_regex, password):
        return False, _("Password contains full-width characters")

    # Check for uppercase letter
    if not re.search(r'[A-Z]', password):
        return False, _("Password must contain at least one uppercase letter")

    # Check for lowercase letter
    if not re.search(r'[a-z]', password):
        return False, _("Password must contain at least one lowercase letter")

    # Check for number
    if not re.search(r'[0-9]', password):
        return False, _("Password must contain at least one number")

    # Check for special character (using string.punctuation)
    if not any(char in string.punctuation for char in password):
        return False, _("Password must contain at least one special character")

    return True, _("Password is valid")
