import geoip2.database
from core.settings.common import GEO_LITE2_COUNTRY


def get_country_from_ip(ip):
    reader = geoip2.database.Reader(GEO_LITE2_COUNTRY)
    response = reader.country(ip)
    return response.country.iso_code


def get_country_from_request(request):
    try:
        # get header from request

        ip = request.META.get("HTTP_X_FORWARDED_FOR",
                              request.META.get("REMOTE_ADDR"))
        ip = ip.split(",")[0].strip() if ip else None

        if ip:
            country = get_country_from_ip(ip)
            return country
        else:
            return None
    except Exception as e:
        return None
