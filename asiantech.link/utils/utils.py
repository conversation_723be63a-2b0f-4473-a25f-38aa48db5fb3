from utils.recruitment_scoring import get_employment_match_score as _get_score
from utils.recruitment_scoring import get_remote_match_score
from utils.recruitment_scoring import get_skill_experience_score
from utils.recruitment_scoring import get_salary_match_score
import difflib
import math
import logging
from core.settings.common import CURRENCY_CODE_BY_COUNTRY_CODE
import requests
from api.models.media import MediaType
from api.serializers.media_serializers import ImageSerializer
from urllib.parse import urlparse
import html
from datetime import date
import json
from django.core.files.uploadedfile import InMemoryUploadedFile
import io
import hashlib
from drf_yasg import openapi
from rest_framework_simplejwt.tokens import RefreshToken
from rest_framework_simplejwt.tokens import AccessToken
from rest_framework_simplejwt.exceptions import InvalidToken, TokenError
import string
from django.utils.translation import gettext as _
from api.models.black_listed_access_token import BlacklistedAccessToken
from rest_framework_simplejwt.token_blacklist.models import OutstandingToken
from captcha.models import CaptchaStore
import random
from api.models.black_listed_access_token import BlacklistedAccessToken, UserAccessToken
import pillow_heif
from PIL import Image, ExifTags
from io import BytesIO
from django.core.files.base import ContentFile
from django.core.cache import cache, caches
from datetime import datetime
from django.utils import timezone
import os
from core.settings.common import CACHES
from core.settings.common import COUNTRY_CODE_PATH, CITY_DATA_PATH, CURRENCIES_PATH, DEGREE_CODE_PATH, LANGUAGE_CODE_PATH, JOB_CODE_PATH, LANGUAGE_TYPE_PATH, SKILL_CODE_PATH, QUALIFICATION_PATH, COUNTRY_PATH, CATEGORY_SKILL_PATH
from utils.constants import UserType, UserSkillPointType, UserSkillPointValue
import re
import yt_dlp
import os
from django.conf import settings
from api.models.eng import EngSelfAssesment
from utils.detect_ip_location import *
from django.contrib.auth import get_user_model
from api.models.user import User
logger = logging.getLogger('api_logger')
accept_language_header = openapi.Parameter(
    'Accept-Language',
    openapi.IN_HEADER,
    description="en,ja,etc",
    type=openapi.TYPE_STRING,
    required=False
)


def get_jwt_token(user):
    refresh = RefreshToken.for_user(user)
    return refresh


def get_payload_token_from_request(request):
    try:
        auth_header = request.headers.get('Authorization')
        if not auth_header or not auth_header.startswith('Bearer '):
            raise InvalidToken("No token found")

        token = auth_header.split(' ')[1]
        access_token = AccessToken(token)
        payload = access_token.payload
        return payload
    except TokenError as e:
        raise InvalidToken(e.args[0])
        raise InvalidToken(e.args[0])


def get_access_token_from_request(request):
    try:
        auth_header = request.headers.get('Authorization')
        if not auth_header or not auth_header.startswith('Bearer '):
            return None
        token = auth_header.split(' ')[1]
        return token
    except TokenError as e:
        return None


def blacklist_refresh_token(refreshToken):
    try:
        refresh_token_obj = RefreshToken(refreshToken)
        refresh_token_obj.blacklist()
    except TokenError as e:
        pass


def blacklist_all_refresh_token(user):
    tokens = OutstandingToken.objects.filter(user=user)
    for token in tokens:
        blacklist_refresh_token(token.token)


def invalidate_access_token(user, token):
    try:
        UserAccessToken.objects.filter(user=user, token=token).delete()
        cache.delete(token)
        return True
    except Exception as e:
        return False


def is_access_token_valid(token):
    if not cache.get(token):
        return False
    return True


def invalidate_all_access_token(user):
    try:
        # Delete all UserAccessToken objects for the given user
        accessTokens = UserAccessToken.objects.filter(user=user)
        for token in accessTokens:
            token.delete()
            cache.delete(token.token)
            BlacklistedAccessToken.objects.get_or_create(
                user=user, token=token.token)

        return True
    except Exception as e:
        print(f"Error invalidating access tokens: {e}")
        return False


def store_access_token(access_token, user):
    try:
        expiration_timestamp = access_token.get('exp')
        expiration_naive = datetime.fromtimestamp(expiration_timestamp)
        expiration = timezone.make_aware(expiration_naive)
        token = str(access_token)

        UserAccessToken.objects.create(
            user=user,
            token=token,
            expires_at=expiration
        )

        # Cache the token
        timeout = (expiration - timezone.now()).total_seconds()
        cache.set(token, user.user_id, timeout=timeout)

        return True
    except Exception as e:
        # Log the error or handle it as needed
        return False


def generate_strong_password(length=12):
    # Define character sets
    uppercase_letters = string.ascii_uppercase
    lowercase_letters = string.ascii_lowercase
    digits = string.digits
    special_chars = string.punctuation

    # Ensure at least one of each type
    password_chars = []
    password_chars.append(random.choice(uppercase_letters))
    password_chars.append(random.choice(lowercase_letters))
    password_chars.append(random.choice(digits))
    password_chars.append(random.choice(special_chars))

    # Fill up the remaining password length
    password_chars.extend(random.choice(
        string.ascii_letters + string.digits + string.punctuation) for _ in range(length - 4))

    # Shuffle to randomize the order
    random.shuffle(password_chars)

    # Generate the final password
    password = ''.join(password_chars)
    return password


# Register HEIF support in Pillow
pillow_heif.register_heif_opener()


def compress_image(image):
    try:
        with Image.open(image) as img:
            # Apply EXIF orientation if it exists
            try:
                for orientation in ExifTags.TAGS.keys():
                    if ExifTags.TAGS[orientation] == 'Orientation':
                        break
                exif = img._getexif()
                if exif is not None:
                    orientation = exif.get(orientation)
                    if orientation == 3:
                        img = img.rotate(180, expand=True)
                    elif orientation == 6:
                        img = img.rotate(270, expand=True)
                    elif orientation == 8:
                        img = img.rotate(90, expand=True)
            except (AttributeError, KeyError, IndexError):
                # No EXIF orientation data
                pass

            # Check if the image has transparency (alpha channel)
            if img.mode in ("RGBA", "LA") or (img.mode == "P" and "transparency" in img.info):
                # Convert to PNG to retain transparency
                output_format = "PNG"
            else:
                # Convert to RGB if the image is not in that mode (JPEG needs RGB)
                if img.mode != "RGB":
                    img = img.convert("RGB")
                output_format = "JPEG"

            output_io = BytesIO()
            img.save(output_io, format=output_format,
                     optimize=True, quality=25)
            output_io.seek(0)
    except IOError:
        print("Cannot open or process the image file.")
        return None

    # Return the compressed image as a ContentFile, preserving the original file's name
    return ContentFile(output_io.getvalue(), name=image.name)


def verify_captcha(captcha_key, captcha_value):
    try:
        captcha = CaptchaStore.objects.get(hashkey=captcha_key)
        if captcha.response.lower() == captcha_value.lower():
            captcha.delete()
            return True
        return False
    except CaptchaStore.DoesNotExist:
        return False


def check_language_code_valid(language_code):
    with open(LANGUAGE_CODE_PATH, 'r') as file:
        language_data = json.load(file)
    valid_language_codes = [language['code'] for language in language_data]
    if language_code not in valid_language_codes:
        return False
    return True


def check_degree_code_valid(degree_code):
    with open(DEGREE_CODE_PATH, 'r') as file:
        degree_data = json.load(file)
    valid_degree_codes = [f"{degree['id']}" for degree in degree_data]
    if f"{degree_code}" not in valid_degree_codes:
        return False
    return True


def check_country_code_valid(country_code):
    with open(COUNTRY_PATH, 'r') as file:
        data = json.load(file)
        country_data = data['country']
    valid_country_codes = [country['country_code'] for country in country_data]
    if country_code not in valid_country_codes:
        return False
    return True


def check_currency_code_valid(currency_code):
    with open(CURRENCIES_PATH, 'r') as file:
        currency_data = json.load(file)
    if currency_code not in currency_data:
        return False
    return True


def check_address_code_valid(address_code):
    with open(CITY_DATA_PATH, 'r') as file:
        city_data = json.load(file)
    valid_address_codes = [city['code'] for city in city_data]
    if address_code not in valid_address_codes:
        return False
    return True


def calculate_years_of_experience(start_date, end_date=None):
    if end_date is None:
        end_date = date.today()
    years_of_experience = end_date.year - start_date.year - \
        ((end_date.month, end_date.day) < (start_date.month, start_date.day))
    return years_of_experience


def get_age(birth_date):
    if birth_date is not None:
        today = date.today()
        age = today.year - birth_date.year - \
            ((today.month, today.day) < (birth_date.month, birth_date.day))
        return age
    return None


def escape_special_characters(password):
    return html.escape(password)


def check_url_valid(url):
    try:
        if not url:
            return False
        url_regex = r'^(http://www\.|https://www\.|http://|https://)?[a-z0-9]+([\-\.]{1}[a-z0-9]+)*\.[a-z]{2,5}(:[0-9]{1,5})?(\/.*)?$'
        if not re.match(url_regex, url):
            return False
        return True
    except:
        return False


def get_job_code():
    try:
        with open(JOB_CODE_PATH, 'r') as file:
            data = json.load(file)
            return data['job_code']
    except Exception as e:
        print(f"Error getting job code: {e}")
        return []


def get_saved_file_path(request, path):
    return path


def save_avatar_sns(user_id, image_url):
    try:
        max_tries = 3
        image_data = None
        # download image
        while max_tries > 0:
            try:
                response = requests.get(image_url)
                if response.status_code == 200:
                    image_data = response.content
                    break
            except Exception as e:
                max_tries -= 1
                if max_tries == 0:
                    return None

        # Convert image_data to a file-like object
        image_file = io.BytesIO(image_data)
        image = Image.open(image_file)
        image_file = io.BytesIO()  # Reset buffer
        image.save(image_file, format=image.format)
        image_file.seek(0)

        # Create InMemoryUploadedFile object
        in_memory_image = InMemoryUploadedFile(
            image_file,  # file object
            None,  # field name, we don't need it here
            f"{user_id}_avatar.{image.format.lower()}",  # file name
            f"image/{image.format.lower()}",  # content type
            image_file.getbuffer().nbytes,  # size
            None  # charset, not required for images
        )

        serializer = ImageSerializer(data={
            'user': user_id,
            'image': in_memory_image,
            'type': MediaType.AVATAR.value,
            'updated_at': timezone.now(),
        })

        serializer.is_valid(raise_exception=True)
        serializer.save()
        imagePath = serializer.data['image']
        user = User.objects.get(user_id=user_id)
        user.profile_image_path = imagePath
        user.save()
        return imagePath
    except Exception as e:
        return None


def get_language_code_from_header(request):
    try:
        language_header = request.headers.get('Accept-Language')
        primary_language = language_header.split(',')[0].split('-')[0]
        return primary_language
    except Exception as e:
        return None


def get_list_language_level_type():
    try:
        with open(LANGUAGE_TYPE_PATH, 'r') as file:
            language_type = json.load(file)
            return language_type['language_level']
    except Exception as e:
        return []


def get_language_model_name(code, language_header):
    try:
        with open(LANGUAGE_CODE_PATH, 'r') as file:
            language_data = json.load(file)
        languages = [language
                     for language in language_data if language['code'].lower() == code.lower()]
        language = languages[0] if len(languages) > 0 else None
        if language:
            if language_header in language:
                return language[language_header]
            else:
                return language["en"]
        return None
    except Exception as e:
        return None


def get_language_level_model_name(level):
    try:
        list_language_types = get_list_language_level_type()
        language = [language
                    for language in list_language_types if language['id'] == level]
        level = language[0] if len(language) > 0 else None
        if level:
            return level["level"]
    except Exception as e:
        return None


def get_list_academic_type():
    try:
        with open(DEGREE_CODE_PATH, 'r') as file:
            degree_data = json.load(file)
            return degree_data
    except Exception as e:
        return []


def get_academic_type_name(id, language_header):
    try:

        degrees_data = get_list_academic_type()
        degree = [degree
                  for degree in degrees_data if str(degree['id']) == str(id)]
        degree = degree[0] if len(degree) > 0 else None
        if degree:
            if language_header in degree:
                return degree[language_header]
            else:
                return degree["en"]
        return None
    except Exception as e:
        return None


def get_list_skill_codes():
    try:
        with open(SKILL_CODE_PATH, 'r') as file:
            data = json.load(file)
            return data['skill_code']
    except Exception as e:
        return []


def get_list_category_skills():
    try:
        with open(CATEGORY_SKILL_PATH, 'r') as file:
            data = json.load(file)
            return data['category_skill_code']
    except Exception as e:
        return []


def get_skill_code_name(id):
    try:
        skills = get_list_skill_codes()
        skill = [skill for skill in skills if str(skill['id']) == str(id)]
        skill = skill[0] if len(skill) > 0 else None
        if skill:
            return skill['name']
        return None
    except Exception as e:
        return None


def get_skill_code_by_name(skill_name):
    try:
        skills = get_list_skill_codes()
        for skill in skills:
            if skill.get("name", "").lower().startswith(skill_name.strip().lower()):
                return skill["id"]
        skill_names = [skill.get("name", "") for skill in skills]
        matches = difflib.get_close_matches(
            skill_name.strip(), skill_names, n=1, cutoff=0.6)
        if matches:
            for skill in skills:
                if skill.get("name", "") == matches[0]:
                    return skill["id"]
        return None
    except Exception as e:
        return None


def get_skill_level_name(level):
    if level <= 1:
        return _("Fresher")
    elif level < 3:
        return _("Junior")
    elif level < 5:
        return _("Middle")
    elif level >= 5:
        return _("Senior")
    else:
        return ""


def get_skill_level_name_en(level):
    if level <= 1:
        return "Fresher"
    elif level < 3:
        return "Junior"
    elif level < 5:
        return "Middle"
    elif level >= 5:
        return "Senior"
    else:
        return ""


def get_list_job_codes():
    try:
        with open(JOB_CODE_PATH, 'r') as file:
            job_data = json.load(file)
            return job_data['job_code']
    except Exception as e:
        return []


def get_job_code_name(id, language_header):
    try:
        jobs = get_list_job_codes()
        job = [job for job in jobs if str(job['id']) == str(id)]
        job = job[0] if len(job) > 0 else None
        if job:
            if language_header in job:
                return job["name_" + language_header]
            else:
                return job["name_en"]
        return None
    except Exception as e:
        return None


def get_list_qualification():
    try:
        with open(QUALIFICATION_PATH, 'r') as file:
            data = json.load(file)
            return data['qualification']
    except Exception as e:
        return []


def get_qualification_name(id):
    try:
        qualifications = get_list_qualification()
        qualification = [qualification for qualification in qualifications if str(
            qualification['id']) == str(id)]
        qualification = qualification[0] if len(qualification) > 0 else None
        if qualification:
            return qualification['name']
        return None
    except Exception as e:
        return None


def get_list_city():
    try:
        with open(CITY_DATA_PATH, 'r') as file:
            data = json.load(file)
            return data
    except Exception as e:
        return []


def get_list_country():
    try:
        with open(COUNTRY_PATH, 'r') as file:
            data = json.load(file)
            return data['country']
    except Exception as e:
        return []


def get_city_name(city_code, language_header):
    try:
        cities = get_list_city()
        city = [city for city in cities if str(
            city['code']) == str(city_code)]
        city = city[0] if len(city) > 0 else None
        if city:
            if language_header in city:
                return city["name_" + language_header]
            else:
                return city["name_en"]
        return None
    except Exception as e:
        return None


def get_country_name(country_code, language_header):
    try:
        countries = get_list_country()
        country = [country for country in countries if str(
            country['country_code']).lower() == str(country_code).lower()]
        country = country[0] if len(country) > 0 else None
        if country:
            if language_header in country:
                return country["name_" + language_header]
            else:
                return country["name_en"]
        return None
    except Exception as e:
        return None


def get_country_code_by_name(country_name, language_header):
    try:
        countries = get_list_country()
        key = f"name_{language_header}"
        for country in countries:
            if key in country and country[key].lower() == country_name.lower():
                return country["country_code"]
        return None
    except Exception as e:
        return None


def map_skill_experiences_to_name(year_of_experiences):
    if year_of_experiences is None:
        return ""
    if year_of_experiences <= 1:
        return _("Fresher")
    elif year_of_experiences < 3:
        return _("Junior")
    elif year_of_experiences < 5:
        return _("Middle")
    elif year_of_experiences >= 5:
        return _("Senior")
    else:
        return ""


def get_host_url_from_request(request):
    return request.build_absolute_uri('/').split('/api')[0].strip('/')


def get_skill_point_from_type(type):
    if type == UserSkillPointType.avg:
        return UserSkillPointValue.avg
    elif type == UserSkillPointType.good:
        return UserSkillPointValue.good
    elif type == UserSkillPointType.excellent:
        return UserSkillPointValue.excellent
    return 0


def get_weekday_to_number(weekdayName):
    weekdayName = weekdayName.lower()
    if weekdayName == "sunday":
        return 0
    elif weekdayName == "monday":
        return 1
    elif weekdayName == "tuesday":
        return 2
    elif weekdayName == "wednesday":
        return 3
    elif weekdayName == "thursday":
        return 4
    elif weekdayName == "friday":
        return 5
    elif weekdayName == "saturday":
        return 6
    return -1


def get_video_source(video_url):
    ydl_opts = {
        'format': 'best',  # Change to 'mp4' if needed
    }
    with yt_dlp.YoutubeDL(ydl_opts) as ydl:
        info = ydl.extract_info(video_url, download=False)
        return info.get('url', None)


def get_question_and_answer_assessment(user_id, file_path, score_key):
    try:
        # Fetch the user's self-assessment data
        self_assessment = EngSelfAssesment.objects.get(engineer_id=user_id)

        # Define the path to the JSON file
        file_path = os.path.join(
            settings.BASE_DIR, 'data_question', file_path)

        # Load the JSON data from the file
        with open(file_path, 'r', encoding='utf-8') as json_file:
            json_data = json.load(json_file)

        for index, question in enumerate(json_data['questions']):

            # Add 'is_expanded' field and set it to False
            question['is_expanded'] = False

            # Check if option (index + 1) == score with self_assessment communication_skills_$index
            column_key = f'{score_key}_{index + 1}'
            if hasattr(self_assessment, column_key):
                score = getattr(self_assessment, column_key)
                for option_index, option in enumerate(question['options']):
                    if option_index + 1 == score:
                        option['is_selected'] = True
                    else:
                        option['is_selected'] = False

            question['options']

        return json_data
    except EngSelfAssesment.DoesNotExist:
        return {
            'questions': []
        }


def get_currency_from_country(country):
    try:
        data = None
        with open(CURRENCY_CODE_BY_COUNTRY_CODE, 'r') as file:
            data = json.load(file)
        list_country = data['country']
        for item in list_country:
            if item['countryCode'].lower() == country.lower():
                return item['currencyCode']
        return None

    except Exception as e:
        logger.log(logging.ERROR, f"Error get currency from country: {e}")
        return None


def get_currency_from_request(request):

    access_token = get_access_token_from_request(request)
    header_currency = None
    if access_token is not None:
        decode_access_token = AccessToken(access_token)
        user_type = decode_access_token.payload['user_type']

        if user_type == UserType.ENGINEER.value:
            header_currency = request.headers.get('engineer-currency-key')
        elif user_type == UserType.HOST_COMPANY_STAFF.value:
            header_currency = request.headers.get('host-company-currency-key')
        elif user_type == UserType.HOST_SUPPORT_AGENCY_STAFF.value:
            header_currency = request.headers.get(
                'host-support-agency-currency-key')
        elif user_type == UserType.REFERRAL_AGENCY_STAFF.value:
            header_currency = request.headers.get(
                'referral-agency-currency-key')
        elif user_type == UserType.OTHER_SUPPORT_AGENCY_STAFF.value:
            header_currency = request.headers.get(
                'other-support-agency-currency-key')
        elif user_type == UserType.MANAGEMENT_ACCOUNT.value:
            header_currency = request.headers.get('admin-currency-key')

    if header_currency is None or header_currency == "":
        country = get_country_from_request(request)
        return get_currency_from_country(country)
    else:
        return header_currency


def get_sex_type_name(sex_type):
    if sex_type == 1:
        return "Male"
    elif sex_type == 2:
        return "Female"
    elif sex_type == 3:
        return "Other"
    else:
        return ""


def parse_date_safe(date_str, format="%b-%y"):
    """
    Parse a date string from several formats and return it formatted as 'MMM-yy' (e.g., 'Apr-25').
    """
    if not date_str:
        return None
    for fmt in ("%Y-%m-%d", "%Y/%m/%d", "%m-%Y", "%Y-%m", "%d/%m/%Y"):
        try:
            dt = datetime.strptime(date_str, fmt)
            return dt.strftime(format)
        except ValueError:
            continue
    return None


def round_salary(amount, currency_code):
    try:

        rounding_rules = {
            "VND": 100_000,
            "JPY": 10_000,
            "CNY": 1_000,
            "KHR": 1_000,
            "THB": 100,
            "SGD": 1,
            "PHP": 100,
            "IDR": 100_000,
            "INR": 1_000,
            "BDT": 1_000,
            "MMK": 1_000,
            "NPR": 1_000,
            "USD": 10,
            "AUD": 1_000
        }

        # default rounding to 1000
        unit = rounding_rules.get(currency_code, 1_000)

        if isinstance(amount, float) and currency_code == "SGD":
            # if SGD and is decimal then round to 1.0 (round up 1)
            return math.ceil(amount * 10) / 10  # round up 1 decimal place

        return math.ceil(amount / unit) * unit
    except Exception as e:
        return amount


def get_employ_type_name(employ_code):
    if employ_code == "1":
        return "Full-time"
    elif employ_code == "2":
        return "Part-time"
    elif employ_code == "3":
        return "Temporary"
    elif employ_code == "4":
        return "Contract"
    elif employ_code == "5":
        return "Freelance"
    else:
        return None


def get_remote_code_name(remote_code):
    if remote_code == "1":
        return "On-site"
    elif remote_code == "2":
        return "Hybrid"
    elif remote_code == "3":
        return "Full Remote"
    else:
        return None


def get_employment_match_score(recruit_employ_code, engineer_employ_code):
    """
    Get employment type matching score between recruit and engineer.

    Args:
        recruit_employ_code (str): Employment type code that company needs
        engineer_employ_code (str): Employment type code that engineer wants

    Returns:
        int: Matching score (0-10), returns 0 if codes are invalid
    """
    return _get_score(recruit_employ_code, engineer_employ_code)


def get_remote_work_match_score(recruit_remote_code, engineer_remote_code):
    """
    Get remote work matching score between recruit and engineer.

    Args:
        recruit_remote_code (str): Remote work code that company needs
        engineer_remote_code (str): Remote work code that engineer wants

    Returns:
        int: Matching score (0-10), returns 0 if codes are invalid
    """
    return get_remote_match_score(recruit_remote_code, engineer_remote_code)


def get_skill_experience_match_score(required_experience, engineer_experience):
    """
    Get skill experience matching score.

    Args:
        required_experience (str): Experience level required by job
        engineer_experience (str): Experience level of engineer

    Returns:
        int: Matching score (0-10), returns 0 if codes are invalid
    """
    return get_skill_experience_score(required_experience, engineer_experience)


def get_salary_compatibility_score(engineer_salary, job_min_salary, job_max_salary):
    """
    Get salary compatibility score based on salary ranges.

    Args:
        engineer_salary (float): Engineer's expected salary
        job_min_salary (float): Job's minimum salary
        job_max_salary (float): Job's maximum salary

    Returns:
        int: Matching score (0-10)
    """

    return get_salary_match_score(engineer_salary, job_min_salary, job_max_salary)
