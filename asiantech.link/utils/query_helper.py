from collections import defaultdict
from utils.currency_converter import CurrencyConverter
import logging
from django.db.models import Q, Count, F, Value, Case, When, IntegerField, OuterRef, Subquery
from utils.constants import Constants
from django.db.models import <PERSON><PERSON><PERSON><PERSON>, ExpressionWrapper, Sum
from datetime import date, timedelta
from django.db.models import *
from django.db.models.functions import Coalesce
from api.models.eng import *
from api.models.rec import RecApply, RecRecruit
from django.contrib.auth import get_user_model
import time
from utils.constants import *
from utils.utils import get_skill_point_from_type, get_skill_code_name, get_skill_level_name_en, get_language_level_model_name
from api.serializers.engineers.user_serializers import UserDetailsSerializers
import json
from utils.ai_search_engineer_query import AISearchEngineerQuery
User = get_user_model()
logger = logging.getLogger("api_logger")
logger = logging.getLogger("api_logger")
currencyConverter = CurrencyConverter()
ai_search_engineer_query = AISearchEngineerQuery()


class QueryHelper:
    def engineer_filter_basic(self, data, user):
        filters = {}
        filters["deleted"] = 0
        filters["is_import"] = 0
        filters["user_type"] = UserType.ENGINEER.value
        filters["auth_type"] = AuthType.AUTHENTICATED.value

        # set this for temporary
        if user.is_authenticated and user.company_id == 1091:
            filters.pop("is_import")

        country_code = data.get('country_code', None)
        last_academic_code = data.get('last_academic_code', None)
        sex_type = data.get('sex_type', None)

        if country_code:
            filters["country_code"] = country_code
        if last_academic_code:
            filters["last_academic_code"] = last_academic_code
        if sex_type:
            filters["sex_type"] = sex_type
        query = User.objects.filter(**filters).prefetch_related(
            'engcareer_set', 'englanguage_set', 'englicence_set', 'engselfassesment_set', 'mapengagc_set', 'enghope_set', 'hopejobskill_set', 'engskill_set', 'recapply_set')
        query = query.filter(
            first_name__isnull=False,
            last_name__isnull=False,
            birth_date__isnull=False,
            country_code__isnull=False,
            tel__isnull=False,
            address_code__isnull=False
        )
        if data.get('search_query', None):
            query = query.filter(
                Q(first_name__icontains=data.get('search_query')) |
                Q(last_name__icontains=data.get('search_query'))
            )
        career_type = data.get('career_type', None)
        if career_type is not None:
            latest_career_qs = EngCareer.objects.filter(
                engineer=OuterRef('pk')
            ).order_by('-entering_date')

            # Filter user by career type
            if career_type == CareerType.EMPLOYED.value:
                users_with_jobs = User.objects.annotate(
                    latest_career_type=Subquery(
                        latest_career_qs.values('career_type')[:1])
                ).filter(latest_career_type=0)
                query = query.filter(
                    Q(user_id__in=users_with_jobs)
                )

            if career_type == CareerType.RETIRED.value:
                latest_career_qs = EngCareer.objects.filter(
                    engineer=OuterRef('pk')
                ).order_by('-entering_date')

                users_latest_career_type = User.objects.annotate(
                    latest_career_type=Subquery(
                        latest_career_qs.values('career_type')[:1]
                    )
                )

                # Users with latest career_type = RETIRED (1)
                users_retired = users_latest_career_type.filter(
                    latest_career_type=CareerType.RETIRED.value
                ).values('pk')

                # Users with no EngCareer records
                users_no_career = User.objects.filter(
                    engcareer__isnull=True
                ).values('pk')

                # Combine both conditions
                query = query.filter(
                    Q(user_id__in=users_retired) | Q(
                        user_id__in=users_no_career)
                )

        return query

    def engineer_filter_by_list_address_code(self, query, data):
        address_code1 = data.get('address_code1')
        address_code2 = data.get('address_code2')

        q_objects = Q()

        if address_code1 and address_code1 != "":
            q_objects |= Q(address_code__startswith=address_code1)
        if address_code2 and address_code2 != "":
            q_objects |= Q(address_code__startswith=address_code2)

        if q_objects:
            query = query.filter(q_objects)
            return query
        return query

    def engineer_filter_by_age(self, query, data):
        age_from = data.get('age_from', None)
        age_to = data.get('age_to', None)
        start_time = time.time()
        if age_from is None and age_to is None:
            return query
        # Calculate age of engineer
        if age_from is None:
            age_from = 0
        if age_to is None:
            age_to = 100
        query = query.annotate(
            age=ExpressionWrapper(
                date.today() - F('birth_date'),
                output_field=DurationField()
            )
        ).filter(
            age__gte=timedelta(days=age_from * 365),
            age__lte=timedelta(days=age_to * 365)
        )
        logger.info(f"Time to filter age: {time.time() - start_time}")
        return query

    def engineer_filter_by_list_language_code_and_level(self, query, data):
        start_time = time.time()
        language_code1 = data.get('language_code1', None)
        language_code2 = data.get('language_code2', None)
        language_level_type1 = data.get('language_level_type1', None)
        language_level_type2 = data.get('language_level_type2', None)

        language_codes = [language_code1, language_code2]
        list_language_level_types = [
            language_level_type1, language_level_type2]

        # Filter data != None
        list_language_code = [code for code in language_codes if code]
        if len(list_language_code) > 0:
            for language_code, language_level in zip(list_language_code, list_language_level_types):
                query = query.filter(
                    englanguage__language_code=language_code,
                    englanguage__language_level_type=language_level)
        logger.info(f"Time to filter language: {time.time() - start_time}")
        return query

    def engineer_filter_by_remote_work_skill(self, query, data):
        start_time = time.time()
        remote_work_skill_point_type = data.get(
            'remote_work_skill_point_type', None)
        if remote_work_skill_point_type is not None:
            point = get_skill_point_from_type(remote_work_skill_point_type)
            start_time = time.time()
            # Calculate total score of remote work skill
            query = query.annotate(
                total_score=(
                    Coalesce(F('engselfassesment__remote_skill_1'), Value(0)) +
                    Coalesce(F('engselfassesment__remote_skill_2'), Value(0)) +
                    Coalesce(F('engselfassesment__remote_skill_3'), Value(0))
                )
            ).filter(total_score__gte=point)
            logger.info(f"Time to filter remote work skill: {
                        time.time() - start_time}")

            return query
        else:
            return query

    def engineer_filter_by_global_skill(self, query, data):
        global_skill_point_type = data.get('global_skill_point_type', None)
        if global_skill_point_type is not None:
            point = get_skill_point_from_type(global_skill_point_type)
            start_time = time.time()
            query = query.annotate(
                total_score=(
                    Coalesce(F('engselfassesment__global_skill_1'), Value(0)) +
                    Coalesce(F('engselfassesment__global_skill_2'), Value(0)) +
                    Coalesce(F('engselfassesment__global_skill_3'), Value(0))
                )
            ).filter(total_score__gte=point)
            logger.info(f"Time to filter global skill: {
                        time.time() - start_time}")
            return query
        else:
            return query

    def engineer_filter_by_communication_skill(self, query, data):
        communication_skill_point_type = data.get(
            'communication_skill_point_type', None)
        if communication_skill_point_type is not None:
            point = get_skill_point_from_type(communication_skill_point_type)
            start_time = time.time()
            query = query.annotate(
                total_remote_score=(
                    Coalesce(F('engselfassesment__communication_skill_1'), Value(0)) +
                    Coalesce(F('engselfassesment__communication_skill_2'), Value(0)) +
                    Coalesce(
                        F('engselfassesment__communication_skill_3'), Value(0))
                )
            ).filter(total_remote_score__gte=point)
            logger.info(f"Time to filter communication skill: {
                        time.time() - start_time}")
            return query
        else:
            return query

    def engineer_filter_by_horenso_skill(self, query, data):
        horenso_skill_point_type = data.get('horenso_skill_point_type', None)
        if horenso_skill_point_type is not None:
            point = get_skill_point_from_type(horenso_skill_point_type)
            start_time = time.time()
            query = query.annotate(
                total_score=(
                    Coalesce(F('engselfassesment__communication_skill_1'), Value(0)) +
                    Coalesce(F('engselfassesment__communication_skill_2'), Value(0)) +
                    Coalesce(
                        F('engselfassesment__communication_skill_3'), Value(0))
                )
            ).filter(total_score__gte=point)
            logger.info(f"Time to filter horenso skill: {
                        time.time() - start_time}")
            return query
        return query

    def engineer_filter_by_work_experience_skill(self, query, data):
        global_work_experience = data.get('global_work_exp', None)
        if global_work_experience is not None:
            start_time = time.time()
            query = query.filter(
                engselfassesment__global_work_exp__gte=global_work_experience
            )
            logger.info(f"Time to filter work experience skill: {
                        time.time() - start_time}")
            return query
        return query

    def engineer_filter_by_project_management_skill(self, query, data):
        project_management_skill_point_type = data.get(
            'project_management_skill_point_type', None)
        if project_management_skill_point_type is not None:
            point = get_skill_point_from_type(
                project_management_skill_point_type)
            start_time = time.time()
            query = query.annotate(
                total_score=(
                    Coalesce(F('engselfassesment__management_skill_1'), Value(0)) +
                    Coalesce(F('engselfassesment__management_skill_2'), Value(0)) +
                    Coalesce(F('engselfassesment__management_skill_3'), Value(0))
                )
            ).filter(total_score__gte=point)
            logger.info(f"Time to filter project management skill: {
                        time.time() - start_time}")
            return query
        else:
            return query

    def engineer_filter_by_list_license_code_and_license_point(self, query, data):
        license_code1 = data.get('licence_code1')
        license_point1 = data.get('licence_point1')
        license_code2 = data.get('licence_code2')
        license_point2 = data.get('licence_point2')
        license_code3 = data.get('licence_code3')
        license_point3 = data.get('licence_point3')
        license_codes = [license_code1, license_code2, license_code3]
        license_points = [license_point1, license_point2, license_point3]
        start_time = time.time()
        # Lọc ra dữ liệu != None
        list_license_code = [code for code in license_codes if code]
        if len(list_license_code) > 0:
            for license_code, license_point in zip(list_license_code, license_points):
                if license_point is None:
                    license_point = 0
                query = query.filter(
                    englicence__license_code=license_code,
                    englicence__license_point__gte=license_point)
        logger.info(f"Time to filter license code and license point: {
                    time.time() - start_time}")
        return query

    def engineer_filter_by_agent_fee(self, query, data):
        agent_fee = data.get('agent_fee', None)
        agent_fee_curr_code = data.get('agent_fee_curr_code', None)

        start_tịme = time.time()
        if agent_fee is not None:
            query = query.filter(
                mapengagc__agency_company__agent_fee__lte=agent_fee,
            )
        if agent_fee_curr_code is not None:
            query = query.filter(
                mapengagc__agency_company__agent_fee_curr_code=agent_fee_curr_code
            )
        logger.info(f"Time to filter agent fee: {time.time() - start_tịme}")
        return query

    def engineer_filter_by_favorite(self, query, data, company_id):
        show_favorite = data.get('show_favorite', None)
        if show_favorite == True:
            start_time = time.time()
            query = query.filter(
                recinterestedengineer__host_company__company_id=company_id,
                recinterestedengineer__interested_flag=1
            )
            logger.info(f"Time to filter favorite: {time.time() - start_time}")
        return query

    def engineer_filter_by_enghope(self, query, data):

        employ_code = data.get('recruiting_employ_code', None)
        place_code1 = data.get('work_place_code1', None)
        place_code2 = data.get('work_place_code2', None)
        place_code3 = data.get('work_place_code3', None)
        payroll_price_from = data.get('payroll_price_from', None)
        payroll_price_to = data.get('payroll_price_to', None)
        payroll_code = data.get('payroll_code', None)

        if employ_code is not None:
            query = query.filter(
                enghope_set__employ_code=employ_code
            )
        list_place_code = [place_code1, place_code2, place_code3]
        list_place_code = [code for code in list_place_code if code]
        if len(list_place_code) > 0:
            query = query.filter(
                Q(enghope_set__place_code1__in=list_place_code) |
                Q(enghope_set__place_code2__in=list_place_code) |
                Q(enghope_set__place_code3__in=list_place_code)
            )

        if payroll_code is not None:
            query = query.filter(
                enghope_set__payroll_code__isnull=False,
                enghope_set__payroll_price_usd__isnull=False
            )
            if payroll_price_from is not None:
                query = query.filter(
                    enghope_set__payroll_price_usd__gte=payroll_price_from
                )
            if payroll_price_to is not None:
                query = query.filter(
                    enghope_set__payroll_price_usd__lte=payroll_price_to)

        return query

    def engineer_filter_by_hope_job_skill(self, query, data):
        job_code = data.get('job_code', None)
        start_time = time.time()
        if job_code is not None:
            query = query.filter(
                hopejobskill__job_code=job_code
            )
        logger.info(f"Time to filter hope job skill: {
                    time.time() - start_time}")
        return query

    def engineer_filter_by_list_jobs_and_job_skill(self, query, data):
        experienced_job_code1 = data.get(
            'experienced_job_code1')
        years_of_experience1 = data.get(
            'years_of_experience1')

        list_job_codes = [experienced_job_code1]
        list_years_of_experiences = [
            years_of_experience1]

        start_time = time.time()
        # Filter data != None
        for index, (job_code, years_of_experience) in enumerate(zip(list_job_codes, list_years_of_experiences)):
            query = self.engineer_filter_by_job_code_and_experience(
                query, job_code, years_of_experience)
        logger.info(f"Time to filter jobs and job skill: {
                    time.time() - start_time}")
        return query

    def engineer_filter_by_job_code_and_experience(self, query, job_code, years_of_experience):
        if job_code is None:
            return query
        start_time = time.time()
        if years_of_experience:
            # Calculate total duration of experience of each engineer if they have quitting date == None then use today as quitting date
            query = query.annotate(
                quitting=Coalesce(F('engcareer__quitting_date'), date.today()),
                duration_date=ExpressionWrapper(
                    F('quitting') - F('engcareer__entering_date'),
                    output_field=DurationField()
                )
            ).values('engcareer__engineer').annotate(
                total_duration=Sum('duration_date')
            ).filter(
                total_duration__gte=timedelta(days=years_of_experience * 365)
            )
            user_ids = [item['engcareer__engineer'] for item in query]
            logger.info(f"Time to filter job code and experience: {
                        time.time() - start_time}")
            return User.objects.filter(user_id__in=user_ids, engskill__job_code=job_code).distinct()
        else:
            # If years_of_experience is None then return all engineers have job_code
            query = query.filter(
                engskill__job_code=job_code
            )
            logger.info(f"Time to filter job code and experience: {
                        time.time() - start_time}")
            return query

    def engineer_filter_by_skills(self, query, data):
        skills_list = data.get("skills")
        if not skills_list:
            return query

        parsed_skills = []

        for entry in skills_list:
            if isinstance(entry, dict):
                code = entry.get("code")
                level = entry.get("level")
                if code and level:
                    parsed_skills.append({"code": code, "level": level})
                continue
            if isinstance(entry, str) and "," in entry:
                tokens = [tok.strip()
                          for tok in entry.split(",") if tok.strip()]
                if len(tokens) < 2:
                    continue
                pair_count = len(tokens) // 2
                for i in range(pair_count):
                    code = tokens[2*i]
                    level = tokens[2*i + 1]
                    if code and level:
                        parsed_skills.append({"code": code, "level": level})
                continue
            continue

        if not parsed_skills:
            return query

        for skill in parsed_skills:
            code = skill.get("code")
            level = skill.get("level")
            if code is None or level is None:
                continue
            query = self.engineer_filter_by_skill_code_and_skill_level_type(
                query, code, level
            )

        return query

    def engineer_filter_by_skill_code_and_skill_level_type(self, query, skill_code, skill_level_type):

        start_time = time.time()
        if skill_code is not None and skill_level_type is not None:
            # Calculate total years of experience of each engineer for each skill_code
            query = query.annotate(
                skill_experience=ExpressionWrapper(
                    Coalesce(
                        F('engskill__level_type'), 0),
                    output_field=TinyInt3Field())
            ).values('engskill__engineer', 'engskill__skill_code').annotate(
                total_skill_experiences=Sum('skill_experience')
            ).filter(
                total_skill_experiences__lte=skill_level_type,
                engskill__skill_code=skill_code
            )
            user_ids = [item['engskill__engineer'] for item in query]
            logger.info(f"Time to filter skill code and skill level type: {
                        time.time() - start_time}")
            return User.objects.filter(user_id__in=user_ids).distinct()

        else:
            return query

    def engineer_filter_by_search_type(self, query, search_type, company_id):
        if search_type == SearchType.AI_RECOMMEND.value:
            return self.engineer_filter_by_ai_recommend(company_id, query)

    def engineer_filter_by_search_type(self, query, search_type, company_id, user=None, recruit_id=None):
        query = query.annotate(
            recruit_id=Value(0, output_field=IntegerField()),
            ai_summary=Value("", output_field=CharField()),
            ai_summary_vi=Value("", output_field=CharField()),
            ai_summary_ja=Value("", output_field=CharField()),
            reason_ai_recommend=Value("", output_field=CharField()),
            reason_ai_recommend_vi=Value("", output_field=CharField()),
            reason_ai_recommend_ja=Value("", output_field=CharField()),
        )
        if search_type == SearchType.AI_RECOMMEND.value:
            return ai_search_engineer_query.engineer_filter_by_ai_recommend(
                company_id, query, user, recruit_id)
        else:
            return query

    def engineer_filter_by_ordering(self, query, ordering):
        if ordering == "-total_recruit_progress_code_active":
            return self.engineer_filter_by_hot_engineer(query)
        return query

    def engineer_filter_by_hot_engineer(self, query):
        query = query.prefetch_related('recapply_set')
        apply_statuses = [
            RecruitProgressCode.INTERVIEW_REQUEST.value,
            RecruitProgressCode.INTERVIEW_SCHEDULING.value,
            RecruitProgressCode.INTERVIEW_DATE_CONFIRMED.value,
            RecruitProgressCode.INTERVIEW_COMPLETED.value,
            RecruitProgressCode.JOB_OFFER.value,
            RecruitProgressCode.EMPLOYED.value,
            RecruitProgressCode.NOT_PASSED.value,
            RecruitProgressCode.INTERVIEW_WITHDRAWN.value,
            RecruitProgressCode.OFFER_DECLINED.value
        ]
        query = query.filter(
            recapply__recruit_progress_code__in=apply_statuses
        ).distinct()

        return query

    from collections import defaultdict

    def engineer_filter_by_details(self, query, host_company_id):
        # payroll_price_usd
        query = query.annotate(
            payroll_price_usd=F('enghope_set__payroll_price_usd')
        )
        # total_recruit_progress_code_active
        query = query.annotate(
            total_recruit_progress_code_active=Count(
                Case(
                    When(recapply__recruit_progress_code__in=[
                        RecruitProgressCode.INTERVIEW_REQUEST.value,
                        RecruitProgressCode.INTERVIEW_SCHEDULING.value,
                        RecruitProgressCode.INTERVIEW_DATE_CONFIRMED.value,
                        RecruitProgressCode.INTERVIEW_COMPLETED.value,
                        RecruitProgressCode.JOB_OFFER.value,
                        RecruitProgressCode.OFFER_ACCEPTED.value,
                        RecruitProgressCode.EMPLOYED.value,
                        RecruitProgressCode.NOT_PASSED.value
                    ], then=1),
                    default=0,
                    output_field=IntegerField()
                )
            )
        )
        # total_company_changed_count
        # Subquery to get the latest career ID per engineer
        latest_career_subquery = EngCareer.objects.filter(
            # or 'user_id' depending on your model
            engineer_id=OuterRef('engineer_id'),
            entering_date__isnull=False
        ).order_by('-entering_date').values('career_id')[:1]

        # Subquery to get the job_code from CareerJobSkill based on that career
        career_job_skill_subquery = CareerJobSkill.objects.filter(
            career_id=Subquery(latest_career_subquery)
        ).values('job_code')[:1]
        query = query.annotate(
            job_code_has_worked_recently=Subquery(career_job_skill_subquery)
        )
        # current_recruit_progress_code_with_my_company with newest apply
        if host_company_id is not None:
            query = query.annotate(
                current_recruit_progress_code_with_my_company=Subquery(
                    RecApply.objects.filter(
                        engineer_id=OuterRef('user_id'),
                        host_company_id=host_company_id,
                    ).order_by('-apply_id').values('recruit_progress_code')[:1]
                )
            )
        else:
            # simply return 0 using Value expression
            query = query.annotate(
                current_recruit_progress_code_with_my_company=Value(
                    0, output_field=IntegerField())
            )

        return query
