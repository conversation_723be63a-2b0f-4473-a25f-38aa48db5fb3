from rest_framework.pagination import Cursor<PERSON>agination, PageNumberPagination


class CustomCursorPagination(CursorPagination):
    page_size = 20
    ordering = '-created'

    def paginate_queryset(self, queryset, request, view=None):
        # Customize page size if provided in query parameters
        if 'page_size' in request.query_params:
            try:
                self.page_size = int(request.query_params.get('page_size'))
            except ValueError:
                pass  # Handle invalid page_size gracefully

        # Customize ordering if provided in query parameters
        if 'ordering' in request.query_params:
            ordering_param = request.query_params.get('ordering')
            self.ordering = [field.strip()
                             for field in ordering_param.split(',')]

        return super().paginate_queryset(queryset, request, view)

    def get_paginated_response(self, data):
        data_response = {
            'next': self.get_next_link(),
            'previous': self.get_previous_link(),
            'results': data,

        }
        return data_response


class CustomCursorTotalCountPagination(CursorPagination):
    page_size = 20
    ordering = '-created'

    def paginate_queryset(self, queryset, request, view=None):
        # Customize page size if provided in query parameters
        if 'page_size' in request.query_params:
            try:
                self.page_size = int(request.query_params.get('page_size'))
            except ValueError:
                pass  # Handle invalid page_size gracefully

        # Customize ordering if provided in query parameters
        if 'ordering' in request.query_params:
            ordering_param = request.query_params.get('ordering')
            self.ordering = [field.strip()
                             for field in ordering_param.split(',')]

        # Save the total count for use in the response
        self.total_count = queryset.count()

        return super().paginate_queryset(queryset, request, view)

    def get_paginated_response(self, data):
        data_response = {
            'next': self.get_next_link(),
            'previous': self.get_previous_link(),
            'results': data,
            'total_count': self.total_count  # Add total count here
        }
        return data_response


class CustomPageNumberPagination(PageNumberPagination):
    page_size = 20
    default_ordering = ['-created']  # Always use a list

    def paginate_queryset(self, queryset, request, view=None):
        # Allow dynamic page size
        page_size = request.query_params.get('page_size')
        if page_size:
            try:
                self.page_size = int(page_size)
            except ValueError:
                pass  # Ignore invalid page size

        # Handle ordering
        ordering_param = request.query_params.get('ordering')
        if ordering_param:
            ordering = [field.strip() for field in ordering_param.split(',')]
        else:
            ordering = self.default_ordering

        queryset = queryset.order_by(*ordering)

        return super().paginate_queryset(queryset, request, view)
