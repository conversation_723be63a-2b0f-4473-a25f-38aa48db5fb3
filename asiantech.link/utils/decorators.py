import logging
from functools import wraps

logger = logging.getLogger("api_logger")


def log_exceptions(func):
    @wraps(func)
    def wrapper(*args, **kwargs):
        logger.info(f"   {func.__name__} START")
        try:
            result = func(*args, **kwargs)
            logger.info(f"Function {func.__name__} END")
            return result
        except Exception as e:
            logger.error(f"Exception in {func.__name__}: {e}")
            raise
    return wrapper
