from utils.constants import *
import requests
from google import genai
from google.genai import types
import io
import mimetypes
import json
import logging
from utils.utils import get_list_qualification

logger = logging.getLogger(__name__)


class AIHelper:
    def __init__(self):
        self.gemini_api_key = Constants.GEMINI_API_KEY
        self.openai_api_key = Constants.OPENAI_API_KEY
        self.gemini_model = Constants.GEMINI_MODEL
        self.rule_extraction_prompt_cv = self._get_rule_extraction_prompt_cv()

    def _get_rule_extraction_prompt_cv(self):
        qualifications = get_list_qualification()
        rules = """

I. Primary Task: Evaluate the provided input text.
II. Conditional Logic 1 (If Not a Resume):
    1,  If the input is not a resume, respond with a JSON object.
    2,  JSON format: {"error": 400, "type": "not_a_resume"}
III. Conditional Logic 2 (If It Is a Resume):
    1,  If the input is a resume, proceed to extract candidate profile data.
    2,  Key Fields to Extract:
        -   Contact Information
        -   Work Experience (including company, title, dates, job description)
        -   Education
        -   Skills (both hard and soft)
        -   Certifications and Licenses
        -   Highlight Projects
    3,  Extraction Priority: Focus on keywords that indicate a candidate's professional background.

IV, Extract this resume to json include all fields and give me full result, adhering strictly to the provided schema and rules.

V, Ensure that all list-like descriptions, specifically "experiences.job_description" and "highlight_projects.responsibilities", are formatted as newline-separated bullet points, each prefixed with "- ".
VII, Extract CV data into JSON with these rules:
 
1. FORMAT:
- Dates: YYYY-MM-DD for full dates, YYYY-MM for months
- Phone:
    - **phone_number**: Extract only the local phone number (e.g., *********). Do NOT include the country ISO code.
    - **iso_code_tel**: Extract the country ISO code separately (e.g., +84).
- Vietnamese names: Convert to non-diacritics for nickname
- Empty optional fields: Omit from output
- Required fields: Use null if missing
 
2. ENUMS:
sex_type: 0=Unknown, 1=Male, 2=Female, 3=Other
education.type: 1=High school, 10=Vocational, 20=Junior College, 100=University, 200=Graduate
language_level_type: 1=Beginner, 3=Intermediate, 5=Advanced, 7=Native
skills.level_type: 1-5=lessThanXYears, 6=moreThan5Years
 
3. EXPERIENCES VS HIGHLIGHT PROJECTS CLASSIFICATION:
    a, Objective: Accurately classify professional experience into two distinct categories: "experiences" for general company roles and "highlight_projects" for specific project details.
    b, Experiences
        -   This section should detail a candidate's general employment history at a company.
        -   Content: Include information about the company name, a **GENERAL SUMMARY of the main role and overall responsibilities (avoiding specific project details if they are better suited for 'highlight_projects')**, entering and quitting dates, role name (if available), and career type.
        -   When to Use:
            +   If the CV describes a general role and responsibilities at a company, not tied to a specific project.
            +   If the CV clearly states the employment period at a company.
            +   **Key Clarification:** If there are descriptions of *specific projects* at a company, these should be extracted into `highlight_projects`. The `experiences.job_description` should then be a **high-level, concise summary** of the overall role, separate from project specifics.
    c, Highlight Projects
        -   This section should detail specific projects undertaken by the candidate.
        -   Content: Include the project name, description, team size, specific role within the project, responsibilities (formatted as a single string with \\n- before each responsibility), technologies used, and the project's start and end dates.
        -   When to Use:
            +   If the CV contains information about a specific project, including details like project name, technologies used, role in the project, and specific responsibilities.
            +   If a job description follows directly after a company name and/or has a "Project:" heading, **it MUST be treated as a project description and included here.**
            +   If there are multiple projects at the same company, create separate entries in highlight_projects for each project.
        -   Date Handling:
            +   from_date and to_date are required.
            +   If specific project dates aren't provided, use the candidate's employment period at the corresponding company as a reference for these dates.        
    d,  Prioritization Rule: **CRITICAL: Always prioritize classifying specific, detailed project descriptions into "highlight_projects". Only use "experiences" for general roles not tied to any particular project, or for a very high-level summary if projects are also present for that company.**
    e,  Important Note on Company Dates (for "experiences" when not explicitly stated):
        -   If the CV doesn't explicitly state the working period for a company in the "experiences" section, but there are projects associated with that company in the highlight_projects section, infer the company's employment duration:
            +   entering_date: Use the from_date of the earliest project completed at that company.
            +   quitting_date: Use the to_date of the latest project completed at that company. If the latest project has no to_date, leave quitting_date blank, indicating the candidate is still working there.

# JOB DESCRIPTION AND HIGHLIGHT PROJECT RESPONSIBILITIES SPLITTING:
For each `experiences.job_description` and `highlight_projects.responsibilities` entry:
- Output as a single string with newline-separated bullet items.
- Split the raw CV text for that entry according to:
  1. Bullet markers (“-”, “•”, “*”) if present.
  2. Semicolons (“;”) if present.
  3. Line breaks if present.
  4. Sentence boundaries otherwise, but further split overly long sentences into logical sub-items.
- Trim whitespace; normalize spacing/diacritics.
- Prefix each item with "- " and join with "\\n".
- If no description text exists, set to empty string or omit.

4. QUALIFICATIONS:
   - **Mapping list: {qualifications} ** use the provided list to map exact `licence_name` → `licence_code`; if no match, set `licence_code = null` and keep `licence_name` as extracted.
   - **Known-test maximum scores:** AI must know common certificates’ maximum scores, e.g.:
     - IELTS → 9.0 (format 0.0–9.0 in 0.5 increments)
     - TOEIC (Listening & Reading) → 990 (integer 10–990)
     - TOEFL iBT → 120 (integer 0–120)
     - Duolingo English Test → 160 (integer 10–160)
     - PTE Academic → 90 (integer 10–90)
     - (Add other known tests as needed)
   - **licence_point extraction & default:**
     - If the resume explicitly mentions an achieved score for a known test, and it is within the valid range and correct format (e.g., “IELTS 5.5”, “TOEIC 750”), extract that numeric value as `licence_point` (float for IELTS, integer for TOEIC, etc.).
     - If the resume mentions a test but no score is provided, **set `licence_point` to the null score** for that qualification (e.g., 9.0 for IELTS, 990 for TOEIC).
     - If the extracted score is outside the valid range or in an invalid format (e.g., “IELTS 12”, “TOEIC 2000”, “N2”, “B1”), disregard the invalid value and instead set `licence_point` to null score.
     - If the qualification is unrecognized (not in known-test mapping), and no numeric score is mentioned, omit the `licence_point` to null value.
   - `get_date`: `"YYYY-MM"` if available; otherwise omit the field.

5. FIX BROKEN LINES & CHARACTER SPACING:
    This resume was extracted from a PDF, leading to potential issues with line breaks and incorrect spacing, both *extra spaces* and *missing spaces*.
    •	Merge lines that belong to the same sentence or paragraph.
    •	**Crucially, normalize Vietnamese character spacing:**
        * **Remove any extraneous spaces inserted *within* or *around* Vietnamese characters with diacritics.** For instance, "H ọ c vi ệ n" must be corrected to "Học viện".
        * **Insert missing spaces between words that have been incorrectly merged, especially in place names or compound words.** For instance, "QuỳnhPhụ" should be "Quỳnh Phụ" and "TháiBình" should be "Thái Bình".
    •	Ensure the output is clean, grammatically correct, and easy to read.

6. Nickname Rules:
    - If the CV contains nickname information: use the provided nickname
    - If the CV does NOT contain nickname information:
    - For Vietnamese/English names: use full name without diacritics (accents)
    - For Japanese names: use full name in Katakana format
    - For other languages: use romanized version of full name
    - Examples: 
        Nguyễn Văn An → Nickname: Nguyen Van An
        田中太郎 (Tanaka Taro) → Nickname: タナカタロウ
        John Smith → Nickname: John Smith

7. Language Consistency Rule
    - **ALWAYS respond in the SAME language as the user's input**
    - **DO NOT automatically translate or switch languages**
"""
        rules = rules.replace("{qualifications}", str(qualifications))
        return rules

    def call_gemini(self, prompt):
        client = genai.Client(api_key=self.gemini_api_key)

        response = client.models.generate_content(
            model=self.gemini_model,
            contents=prompt,
            config=types.GenerateContentConfig(
                temperature=0.0
            )
        )
        return response.text

    def call_gemini_with_file(self, uploaded_file):
        import tempfile
        import os

        client = genai.Client(api_key=self.gemini_api_key)
        uploaded_file.seek(0)

        # Get the file content and mime type
        file_content = uploaded_file.read()

        # Create a temporary file
        with tempfile.NamedTemporaryFile(delete=False, suffix=os.path.splitext(uploaded_file.name)[1]) as temp_file:
            temp_file.write(file_content)
            temp_file_path = temp_file.name

        try:
            files = [
                client.files.upload(file=temp_file_path),
            ]

            contents = [
                types.Content(
                    role="user",
                    parts=[
                        types.Part.from_uri(
                            file_uri=files[0].uri,
                            mime_type=files[0].mime_type,
                        ),
                        types.Part.from_text(
                            text=f"""
                        Extract this resume to json include all fields and give me full result\n
                        Rules: 
                        {self.rule_extraction_prompt_cv}
                        """),
                    ],
                ),
            ]

            generate_content_config = types.GenerateContentConfig(
                response_mime_type="application/json",
                temperature=0.0,
                response_schema=types.Schema(
                    type=types.Type.OBJECT,
                    properties={
                        "is_valid_resume": types.Schema(type=types.Type.BOOLEAN, description="true if the input is a resume/cv, false otherwise"),
                        "email": types.Schema(type=types.Type.STRING, description="email of the candidate"),
                        "first_name": types.Schema(type=types.Type.STRING, description="first name of the candidate"),
                        "last_name": types.Schema(type=types.Type.STRING, description="last name of the candidate"),
                        "nickname": types.Schema(type=types.Type.STRING, description=" optional, converted to non-diacritics if Vietnamese"),
                        "sex_type": types.Schema(type=types.Type.INTEGER, description="0: Unknown, 1: Male, 2: Female, 3: Other"),
                        "birth_date": types.Schema(type=types.Type.STRING, description="YYYY-MM-DD"),
                        "country_code": types.Schema(type=types.Type.STRING, description="country code of the candidate"),
                        "phone_number": types.Schema(type=types.Type.STRING, description="sample: ********* not include iso code"),
                        "iso_code_tel": types.Schema(type=types.Type.STRING, description="this is iso code, sample: +84"),
                        "address_details": types.Schema(type=types.Type.STRING, description="address details of the candidate"),
                        "address_country_code": types.Schema(type=types.Type.STRING, description="country code of the candidate's address"),
                        "address_city_name": types.Schema(type=types.Type.STRING, description=" extract from address_details, sample: Hà Nội, Hồ Chí Minh, Đà Nẵng, Tokyo, Osaka, etc."),
                        "educations": types.Schema(
                            type=types.Type.ARRAY,
                            items=types.Schema(
                                type=types.Type.OBJECT,
                                properties={
                                    "school": types.Schema(type=types.Type.STRING),
                                    "type": types.Schema(type=types.Type.INTEGER, description="1: High school, 10: Vocational school, 20: Junior College, 100: University, 200: Graduate School"),
                                    "in_date": types.Schema(type=types.Type.STRING, description="YYYY-MM-DD"),
                                    "out_date": types.Schema(type=types.Type.STRING, description="YYYY-MM-DD, optional"),
                                    "faculty": types.Schema(type=types.Type.STRING),
                                },
                                required=["school", "type",
                                          "in_date", "faculty", "out_date"]
                            ),
                        ),
                        "languages": types.Schema(
                            type=types.Type.ARRAY,
                            items=types.Schema(
                                type=types.Type.OBJECT,
                                properties={
                                    "language_level_type": types.Schema(type=types.Type.INTEGER, description="1: Beginner, 3: Intermediate, 5: Advanced, 7: Native"),
                                    "language_code": types.Schema(type=types.Type.STRING, description="sample: EN,VI,JA"),
                                },
                                required=["language_level_type",
                                          "language_code"]
                            ),
                        ),
                        "qualifications": types.Schema(
                            type=types.Type.ARRAY,
                            items=types.Schema(
                                type=types.Type.OBJECT,
                                properties={
                                    "licence_code": types.Schema(type=types.Type.INTEGER, description="Map with the sample list"),
                                    "licence_name": types.Schema(type=types.Type.STRING),
                                    "get_date": types.Schema(type=types.Type.STRING, description="YYYY-MM"),
                                    "licence_point": types.Schema(type=types.Type.INTEGER, description="sample: 9.0 for IELTS, 990 for TOEIC"),
                                },
                                required=["licence_code", "licence_name",
                                          "get_date", "licence_point"]
                            ),
                        ),
                        "skills": types.Schema(
                            type=types.Type.ARRAY,
                            items=types.Schema(
                                type=types.Type.OBJECT,
                                properties={
                                    "skill": types.Schema(type=types.Type.STRING, description=" Skill Name like Java, Python, React, etc."),
                                    "level_type": types.Schema(type=types.Type.INTEGER, description="1: lessThan1Year, 2: lessThan2Years, 3: lessThan3Years, 4: lessThan4Years, 5: lessThan5Years, 6: moreThan5Years"),
                                    "category_id": types.Schema(type=types.Type.INTEGER, description="100: Programming language, 101: Framework, 102: Database, 103: Project management tool, 104: Source control, 105: IDE (Integrated Development Environment), 106: OS, 999: Other skills and tools"),
                                },
                                required=["skill", "level_type", "category_id"]
                            ),
                        ),
                        "experiences": types.Schema(
                            type=types.Type.ARRAY,
                            items=types.Schema(
                                type=types.Type.OBJECT,
                                properties={
                                    "company_name": types.Schema(type=types.Type.STRING, description="company name of the candidate"),
                                    "job_description": types.Schema(type=types.Type.STRING, description="Description of main role at that company. MUST BE EXTRACTED VERBATIM."),
                                    "entering_date": types.Schema(type=types.Type.STRING, description="YYYY-MM-DD"),
                                    "quitting_date": types.Schema(type=types.Type.STRING, description="YYYY-MM-DD"),
                                    "role_name": types.Schema(type=types.Type.STRING, description="role name of the candidate, optional"),
                                    "career_type": types.Schema(type=types.Type.INTEGER, description="0: EMPLOYEE, 1: RETIRED"),
                                },
                                required=["company_name", "job_description",
                                          "entering_date", "career_type"]
                            ),
                        ),
                        "skills_for_cv_display": types.Schema(type=types.Type.STRING, description=""
                                                              "Analyze the candidate’s list of skills, tools, and technologies, and present them as a clean, readable CV section. Group skills into relevant categories (e.g., Programming Languages, Frameworks, Project Management Tools, Design, Automation, etc.). "
                                                              "For each category, use the format: Category: item1, item2, item3."
                                                              "Separate each category with a new line. Avoid repetition and keep the content concise yet informative."
                                                              "Example: Programming Languages: Java, Python, JavaScript, TypeScript, etc."
                                                              "Example: Frameworks: React, Angular, Vue, etc."
                                                              "Example: Project Management Tools: Jira, Trello, etc."
                                                              "Example: Design: Figma, Adobe XD, etc."
                                                              "Example: Automation: Selenium, Playwright, etc."
                                                              ),
                        "professional_summary": types.Schema(type=types.Type.STRING, description="professional summary of the candidate"),
                        "highlight_projects": types.Schema(
                            type=types.Type.ARRAY,
                            items=types.Schema(type=types.Type.OBJECT,
                                               properties={
                                                   "name": types.Schema(type=types.Type.STRING, description="name of the project"),
                                                   "description": types.Schema(type=types.Type.STRING, description="description of the project"),
                                                   "size": types.Schema(type=types.Type.STRING, description="size of the project"),
                                                   "role_name": types.Schema(type=types.Type.STRING, description="role name of the candidate in the project, optional"),
                                                   "responsibilities": types.Schema(type=types.Type.STRING, description=" As a single string: include newline + '- ' before each responsibility. MUST BE EXTRACTED VERBATIM."),
                                                   "technology_used": types.Schema(type=types.Type.STRING, description="technology used in the project"),
                                                   "from_date": types.Schema(type=types.Type.STRING, description="YYYY-MM-DD"),
                                                   "to_date": types.Schema(type=types.Type.STRING, description="YYYY-MM-DD"),
                                               },
                                               required=[
                                                   "name", "description", "size", "responsibilities", "technology_used", "from_date", "to_date"]
                                               ),
                        ),
                    },
                    required=["email", "first_name", "last_name", "sex_type", "birth_date", "country_code", "phone_number", "iso_code_tel", "address_details", "address_country_code",
                              "address_city_name", "educations", "languages", "qualifications", "skills", "experiences", "skills_for_cv_display", "professional_summary", "highlight_projects", "is_valid_resume"]
                ),
            )

            response = client.models.generate_content(
                model=self.gemini_model,
                contents=contents,
                config=generate_content_config,
            )
            data = response.model_dump()
            data_parsed = data.get("parsed")
            return data_parsed
        finally:
            # Clean up temporary file
            if os.path.exists(temp_file_path):
                os.remove(temp_file_path)

    def call_gemini_for_recommendation(self, prompt):
        """
        Call Gemini API specifically for AI recruitment recommendations
        Returns structured JSON response
        """
        try:
            client = genai.Client(api_key=self.gemini_api_key)

            # Define the response schema for recruitment recommendations
            recommendation_schema = types.Schema(
                type=types.Type.OBJECT,
                properties={
                    "summary": types.Schema(
                        type=types.Type.STRING,
                        description=(
                            "Return the talent pool summary in the following format:\n\n"
                            "• **Technical skills**: Bullet-point insights on key technical strengths.\n"
                            "• **Missing certifications/languages**: Note missing certificates or language barriers.\n"
                            "• **Experience levels**: Indicate junior/senior balance.\n"
                            "• **Mismatch with job requirements**: Such as unrealistic age or experience demands.\n"
                            "• **Flexibility/versatility**: Show adaptability across roles.\n"
                            "• **Language proficiency issues**: Note specific weaknesses (e.g., English or Vietnamese).\n\n"
                            "• **Top Candidate Recommendations**:\n"
                            "  • Highlight 1 to 5 candidates by name.\n"
                            "  • Describe their key strengths (skills, experience, employers, languages, certifications).\n"
                            "  • Mention any training needs if applicable.\n\n"
                            "Format text using markdown bullet points (`•`) and support your analysis with actual data."
                        )
                    ),
                    "summary_vi": types.Schema(
                        type=types.Type.STRING,
                        description=(
                            "Trả về bản tóm tắt nhóm ứng viên kỹ sư với định dạng sau:\n\n"
                            "• **Kỹ năng chuyên môn**: Các điểm nổi bật về kỹ năng kỹ thuật.\n"
                            "• **Thiếu chứng chỉ/ngôn ngữ**: Ghi chú về việc thiếu chứng chỉ hoặc rào cản ngôn ngữ.\n"
                            "• **Mức độ kinh nghiệm**: Cho biết sự phân bố giữa ứng viên mới và có kinh nghiệm.\n"
                            "• **Không phù hợp với yêu cầu công việc**: Ví dụ như yêu cầu độ tuổi hoặc kinh nghiệm không thực tế.\n"
                            "• **Khả năng linh hoạt/đa nhiệm**: Ứng viên có thể đảm nhận nhiều vai trò.\n"
                            "• **Vấn đề về trình độ ngoại ngữ**: Ghi rõ điểm yếu về ngôn ngữ (như tiếng Anh hoặc tiếng Việt).\n\n"
                            "• **Đề xuất ứng viên nổi bật**:\n"
                            "  • Nêu từ 1 đến 5 ứng viên kèm tên.\n"
                            "  • Mô tả ngắn gọn các điểm mạnh (kỹ năng, kinh nghiệm, công ty, ngôn ngữ, chứng chỉ).\n"
                            "  • Nếu cần, đề cập đến điểm cần đào tạo.\n\n"
                            "Định dạng kết quả bằng các gạch đầu dòng markdown (`•`) và phân tích dựa trên dữ liệu thực tế."
                        )
                    ),
                    "summary_ja": types.Schema(
                        type=types.Type.STRING,
                        description=(
                            "エンジニア候補者の要約を次の形式で返してください：\n\n"
                            "• **技術スキル**: 主な技術的な強みを箇条書きで示してください。\n"
                            "• **資格や言語の欠如**: 資格不足や言語障壁について言及。\n"
                            "• **経験レベル**: 初級から上級までの幅広さを示す。\n"
                            "• **求人要件との不一致**: 非現実的な年齢や経験年数の要求など。\n"
                            "• **柔軟性・多用途性**: 複数の役割に適応できる候補者。\n"
                            "• **語学能力の問題**: 英語やベトナム語などの言語に関する弱点。\n\n"
                            "• **注目候補者の推薦**:\n"
                            "  • 氏名付きで1〜5名の候補者を挙げる。\n"
                            "  • スキル・経験・勤務先・言語・資格などの強みを簡潔に説明。\n"
                            "  • 必要であれば、トレーニングが必要な点も記載。\n\n"
                            "出力形式は markdown の箇条書き（`•`）を使い、実際のデータに基づいた分析を行ってください。"
                        )
                    ),
                    "top_5_engineers": types.Schema(
                        type=types.Type.ARRAY,
                        items=types.Schema(
                            type=types.Type.OBJECT,
                            properties={
                                "engineer_id": types.Schema(
                                    type=types.Type.INTEGER,
                                    description="ID of the engineer"
                                ),
                                "match_score": types.Schema(
                                    type=types.Type.INTEGER,
                                    description="Match score from 0-100 based on overall compatibility"
                                ),
                                "primary_job_match": types.Schema(
                                    type=types.Type.STRING,
                                    description="The job title that best matches this engineer"
                                ),
                                "reason_ai_recommend": types.Schema(
                                    type=types.Type.STRING,
                                    description=(
                                        "Provide a thorough explanation of why this engineer is recommended for the job using the following markdown format:\n\n"
                                        "• **Job Fit**: How the engineer meets job requirements:\n"
                                        "  - Skills (e.g., Java, SQL, AWS)\n"
                                        "  - Years of experience\n"
                                        "  - Educational background\n"
                                        "  - Language skills (e.g., English, Japanese)\n"
                                        "  - Work arrangement preference (e.g., onsite, remote)\n\n"
                                        "• **Relevant Experience**:\n"
                                        "  - Past projects or roles aligned with the job's responsibilities\n"
                                        "  - Industries or companies relevant to the job\n\n"
                                        "• **Notable Achievements**:\n"
                                        "  - Certifications (e.g., AWS Certified, JLPT N2)\n"
                                        "  - Key accomplishments or awards\n\n"
                                        "• **Versatility**:\n"
                                        "  - Skills that allow adaptability across different roles\n\n"
                                        "• **Language and Culture Fit**:\n"
                                        "  - Communication skills and cultural compatibility\n\n"
                                        "• **Long-Term Potential**:\n"
                                        "  - Growth trajectory, leadership potential, or innovation mindset\n\n"
                                        "• **Gaps or Risks**:\n"
                                        "  - Any missing areas and possible solutions (e.g., training)\n\n"
                                        "Support your explanation with concrete examples based on the engineer’s actual profile."
                                    )
                                ),
                                "reason_ai_recommend_vi": types.Schema(
                                    type=types.Type.STRING,
                                    description=(
                                        "Cung cấp lời giải thích chi tiết về lý do vì sao kỹ sư này được đề xuất cho công việc, theo định dạng markdown như sau:\n\n"
                                        "• **Phù hợp với công việc**: Kỹ sư đáp ứng các yêu cầu như:\n"
                                        "  - Kỹ năng chuyên môn (ví dụ: Java, SQL, AWS)\n"
                                        "  - Số năm kinh nghiệm\n"
                                        "  - Trình độ học vấn\n"
                                        "  - Khả năng ngôn ngữ (ví dụ: tiếng Anh, tiếng Nhật)\n"
                                        "  - Hình thức làm việc mong muốn (trực tiếp, từ xa,...)\n\n"
                                        "• **Kinh nghiệm liên quan**:\n"
                                        "  - Các dự án hoặc vị trí đã làm có liên quan đến công việc\n"
                                        "  - Ngành nghề hoặc công ty từng làm phù hợp với yêu cầu\n\n"
                                        "• **Thành tích đáng chú ý**:\n"
                                        "  - Các chứng chỉ (ví dụ: AWS, JLPT N2)\n"
                                        "  - Giải thưởng hoặc thành tựu nổi bật\n\n"
                                        "• **Tính linh hoạt**:\n"
                                        "  - Kỹ năng có thể áp dụng cho nhiều vai trò khác nhau\n\n"
                                        "• **Khả năng ngôn ngữ và phù hợp văn hóa**:\n"
                                        "  - Khả năng giao tiếp và sự tương thích văn hóa\n\n"
                                        "• **Tiềm năng dài hạn**:\n"
                                        "  - Khả năng phát triển, lãnh đạo hoặc đổi mới trong tương lai\n\n"
                                        "• **Khoảng trống hoặc rủi ro**:\n"
                                        "  - Các điểm thiếu sót và giải pháp khắc phục (như đào tạo, cố vấn)\n\n"
                                        "Phân tích nên dựa vào dữ liệu thực tế trong hồ sơ của kỹ sư."
                                    )
                                ),
                                "reason_ai_recommend_ja": types.Schema(
                                    type=types.Type.STRING,
                                    description=(
                                        "このエンジニアをなぜ推薦するのかについて、以下のMarkdown形式で詳細に説明してください：\n\n"
                                        "• **職務適合性**：エンジニアが職務要件をどのように満たしているか：\n"
                                        "  - スキル（例：Java、SQL、AWS）\n"
                                        "  - 経験年数\n"
                                        "  - 学歴\n"
                                        "  - 語学力（例：英語、日本語）\n"
                                        "  - 希望する働き方（例：出社、リモート）\n\n"
                                        "• **関連経験**：\n"
                                        "  - 関連するプロジェクトや職務経歴\n"
                                        "  - 関連する業界や企業での勤務経験\n\n"
                                        "• **顕著な実績**：\n"
                                        "  - 認定資格（例：AWS認定、JLPT N2）\n"
                                        "  - 主な業績や受賞歴\n\n"
                                        "• **柔軟性**：\n"
                                        "  - 複数の職務に適応できるスキル\n\n"
                                        "• **言語と文化の適合性**：\n"
                                        "  - コミュニケーション能力や文化的な適合性\n\n"
                                        "• **長期的な可能性**：\n"
                                        "  - 成長性、リーダーシップの素質、革新性\n\n"
                                        "• **懸念点またはリスク**：\n"
                                        "  - 不足点や課題とその対処方法（例：研修）\n\n"
                                        "実際のプロフィールに基づいた具体的な情報で裏付けてください。"
                                    )
                                ),
                                "strengths": types.Schema(
                                    type=types.Type.ARRAY,
                                    items=types.Schema(
                                        type=types.Type.STRING),
                                    description="List of engineer's key strengths relevant to the job requirements"
                                ),
                                "potential_concerns": types.Schema(
                                    type=types.Type.ARRAY,
                                    items=types.Schema(
                                        type=types.Type.STRING),
                                    description="List of potential concerns or areas that might need attention"
                                )
                            },
                            required=["engineer_id", "match_score",
                                      "reason_ai_recommend", "reason_ai_recommend_vi", "reason_ai_recommend_ja"]
                        )
                    )
                },
                required=["summary", "top_5_engineers",
                          "summary_ja", "summary_vi"]
            )

            response = client.models.generate_content(
                model=self.gemini_model,
                contents=prompt,
                config=types.GenerateContentConfig(
                    temperature=0.0,
                    response_mime_type="application/json",
                    response_schema=recommendation_schema
                )
            )

            logger.info(
                f"Gemini API response received: {len(response.text)} characters")
            return response.text

        except Exception as e:
            logger.error(f"Error calling Gemini API for recommendations: {e}")
            raise

    def get_ai_recruitment_recommendations(self, engineer_profiles, company_recruits, use_gemini=True):
        """
        Get AI-powered recruitment recommendations

        Args:
            engineer_profiles: List of readable engineer profile strings
            company_recruits: List of readable company recruit profile strings
            use_gemini: Whether to use Gemini (True) or OpenAI (False)

        Returns:
            Parsed JSON response with recommendations
        """
        try:
            from utils.ai_recommendation import AIRecommendationEngine

            # Generate the prompt using our recommendation engine
            ai_engine = AIRecommendationEngine()
            prompt = ai_engine.generate_prompt(
                engineer_profiles, company_recruits)

            logger.info(
                f"Generated prompt for AI service: {len(prompt)} characters")

            # Call the appropriate AI service
            if use_gemini and self.gemini_api_key:
                logger.info("Using Gemini API for recommendations")
                ai_response = self.call_gemini_for_recommendation(prompt)
            elif self.openai_api_key:
                pass
            else:
                raise Exception("No AI API keys available")

            # Parse and validate the response
            parsed_response = ai_engine.parse_ai_response(ai_response)

            if parsed_response:
                logger.info("Successfully generated AI recommendations")
                return parsed_response
            else:
                logger.warning("Failed to parse AI response, using fallback")
                return ai_engine.create_fallback_response(engineer_profiles)

        except Exception as e:
            logger.error(f"Error in get_ai_recruitment_recommendations: {e}")
            # Return fallback response
            from utils.ai_recommendation import AIRecommendationEngine
            ai_engine = AIRecommendationEngine()
            return ai_engine.create_fallback_response(engineer_profiles)
