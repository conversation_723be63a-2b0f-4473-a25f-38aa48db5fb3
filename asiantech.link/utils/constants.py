from enum import Enum


class Constants:
    WEB_LINK = "https://asiantech.link"

    MINIMUM_SCORE_OTHER_SKILL_CONDITION = 8

    COMPANY_ID_REFERRAL_AGENCY_TEMPLE = 1097

    # SNS
    LINKEDIN_CLIENT_ID = "869ze5ivkt11br"
    LINKEDIN_CLIENT_SECRET = "WPL_AP1.sxxgGubzzn49MGgo.qNYXmA=="
    ZALO_APP_ID = "4318851121227495354"
    ZALO_SECRET_KEY = "WU5YoESKppURhP37JYnF"
    WHATSAPP_BUSINESS_PHONE_NUMBER_ID = "479159985279555"
    WHATSAPP_ACCESS_TOKEN = "EAAHoklhgRM8BO4MFIBtwSDSPMLbPoGYfEFxfPp1iFcrwgtF4X6OIkJpdLIFis4I3CWq8ofVFTpuGuI8vMedwphty0GuZAhdagWZA6cpOCiXy9XtjaPzpA13mxyHAMps9pdueOjVhTF9CMItJpSzZBe5ZBBe8KIwwTEve69dy8J7NrBsMogFApCkFt1yIt41dFwZDZD"
    FACEBOOK_APP_ID = "2090694394684658"
    FACEBOOK_APP_SECRET = "********************************"
    # Email
    EMAIL_PRIVATE_DOMAIN = "private.asiantech.link"

    # AI API KEY
    GEMINI_API_KEY = "AIzaSyCoK8VjREZ11HU3QllxwZUbFSO_WbMwbpE"
    GEMINI_MODEL = "gemini-2.5-flash"
    OPENAI_API_KEY = "sk-proj-00000000000000000000000000000000"

    DEFAULT_JOB_CODE = "100"
    DEFAULT_CUSTOM_JOB_CODE = "999"
    DEFAULT_CUSTOM_SKILL_CODE = "999999"
    DEFAULT_OTHER_CATEGORY_ID = "999"

    # Max Size CV
    MAX_SIZE_CV = 5 * 1024 * 1024


class ErrorConstants:
    EMAIL_NOT_VERIFIED = 0
    CAPTCHA_VERIFICATION_FAILED = 1
    INVALID_SEX_TYPE = 2


class UserSkillPointType:
    avg = 1
    good = 2
    excellent = 3


class UserSkillPointValue:
    avg = 6
    good = 9
    excellent = 12


class UserType(Enum):
    ENGINEER = 0
    REFERRAL_AGENCY_STAFF = 1
    HOST_COMPANY_STAFF = 2
    HOST_SUPPORT_AGENCY_STAFF = 3
    OTHER_SUPPORT_AGENCY_STAFF = 4
    MANAGEMENT_ACCOUNT = 5


class AuthType(Enum):
    IN_PROGRESS = 0
    AUTHENTICATED = 1


class SexType(Enum):
    UNKNOWN = 0
    MALE = 1
    FEMALE = 2
    OTHER = 3


class JobStatus(Enum):
    PREPARING = 0
    LOOKING_FOR_WORK = 1
    RECEIVED_JOB_OFFER = 2
    CURRENTLY_EMPLOYED = 3


class DeletedStatus(Enum):
    NORMAL = 0
    CANCELLED = 1


class AcademicType(Enum):
    CURRENT_ENROLLED = 0
    GRADUATED = 1
    DROPPED_OUT = 2


class HopeSaveType(Enum):
    DRAFT = 0
    SAVED = 1


class CareerType(Enum):
    EMPLOYED = 0
    RETIRED = 1


class CompanySizeType(Enum):
    FROM_1_TO_9 = 0
    FROM_10_TO_29 = 1
    FROM_30_TO_49 = 2
    FROM_50_TO_99 = 3
    FROM_100_TO_299 = 4
    FROM_300_TO_499 = 5
    FROM_500_TO_999 = 6
    OVER_1000 = 7


class MediaType(Enum):
    AVATAR = 0
    PASSPORT = 1
    COMPANY_LOGO = 2
    COMPANY_PR = 3
    CONTRACT = 4
    COVER_RECRUIT = 5
    CONTRACT_COMPANY = 6


class RecruitDisplayFlag(Enum):
    HIDE = 0
    SHOW = 1


class WaitingFlag(Enum):
    CANCELLED = "0"
    UNDER_CONSIDERATION = "1"
    APPLIED = "2"


class CompanyStatus(Enum):
    PRIVATE = 0
    PUBLIC = 1
    DELETED = 2


class RecruitProgressCode (Enum):
    REQUESTING_AN_AGENT = 10  # エージェント依頼中
    APPLICATION = 20  # 応募
    INTERVIEW_REQUEST = 30  # 面接依頼
    INTERVIEW_SCHEDULING = 31  # 面接日時調整中
    INTERVIEW_DATE_CONFIRMED = 32  # 面接日時確定
    INTERVIEW_COMPLETED = 40  # 面接完了
    JOB_OFFER = 50  # 内定
    OFFER_ACCEPTED = 60  # 内定受理
    EMPLOYED = 80  # 在職中
    NOT_PASSED = 90  # 不通過
    APPLICATION_WITHDRAWN = 91  # 応募辞退
    INTERVIEW_WITHDRAWN = 92  # 面接辞退
    OFFER_DECLINED = 93  # 内定辞退
    OTHER_COMPANY_OFFER = 94  # 他社内定


class ApplyStatusFilterCode (Enum):
    ALL = 0  # すべて
    OFFERED = 1  # 内定
    INTERVIEW_SCHEDULING = 2  # 面接調整中
    APPLIED = 3  # 応募済
    UNDER_REVIEW = 4  # 検討中
    NOT_PASSED = 5  # 不通過


class EmailScheduleType(Enum):
    ADMIN_NOTIFY = 1
    AGREE_EMAIL = 2


class RepeatType(Enum):
    NO_REPEAT = 0
    WEEKLY = 1
    MONTHLY = 2
    YEARLY = 3


class SearchType(Enum):
    AI_RECOMMEND = "ai_recommend"


class SNSType(Enum):
    ZALO = "zalo"
    WHATSAPP = "whatsapp"
    FACEBOOK = "facebook"
    LINKEDIN = "linkedin"
