from django.urls import path

from api.views.notify.get_list_notify_view import GetListNotifyView
from api.views.notify.count_unread_notify_view import CountUnreadNotifyView
from api.views.notify.set_read_notify_view import SetReadNotifyView
from api.views.notify.set_read_all_notify_view import SetReadAllNotifyView
notify_patterns = [
    path('get-count-unread-notify',
         CountUnreadNotifyView.get_count_unread_notify, name='get-count-unread-notify'),
    path('get-list-notify',
         GetListNotifyView.get_list_notify, name='get-list-notify'),
    path('set-read-notify',
         SetReadNotifyView.set_read_notify, name='set-read-notify'),
    path('set-read-all-notify',
         SetReadAllNotifyView.set_read_all_notify, name='set-read-all-notify'),
]
