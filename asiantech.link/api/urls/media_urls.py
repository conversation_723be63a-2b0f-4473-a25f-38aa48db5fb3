from django.urls import path
from api.views.media.avatar_views import Avatar<PERSON>iew
from api.views.media.passport_views import PassportViews
from api.views.media.company_media_views import CompanyLogoViews
from api.views.media.company_media_views import CompanyPRViews
from api.views.media.contract_media_view import ContractMediaViews, ContractCompanyMediaViews
from api.views.media.recruit_media_views import Recruit<PERSON>ogoViews
from api.views.media.admin_upload_avatar_engineer_view import AdminUploadAvatarViewEngineerView


media_patterns = [
    path("upload-avatar/", AvatarView.as_view(), name="upload-avatar"),
    path("upload-passport/", PassportViews.as_view(), name="upload-passport"),
    path("upload-company-logo/",
         CompanyLogoViews.as_view(), name="upload-company-logo"),
    path("upload-company-pr/",
         CompanyPRViews.as_view(), name="upload-company-pr"),
    path("upload-contract-signature/", ContractMediaViews.as_view(),
         name="upload-contract-signature"),
    path("upload-recruit-cover/", RecruitLogoViews.as_view()),
    path("upload-contract-company-signature/",
         ContractCompanyMediaViews.as_view()),
    path("upload-avatar-engineer-by-admin",
         AdminUploadAvatarViewEngineerView.as_view())
]
