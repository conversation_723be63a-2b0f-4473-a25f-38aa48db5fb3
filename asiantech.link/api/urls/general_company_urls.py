from django.urls import path, include
from api.views.general.general_company_views import GeneralCompanyView

general_company_patterns = [
    # register
    path('register/', GeneralCompanyView.register_company, name='register_company'),

    # engineers
    path('details/', GeneralCompanyView.company_details, name='company_details'),
    path("explore/", GeneralCompanyView.explore_users, name="explore_users"),
    path("get-explore-user-count",
         GeneralCompanyView.get_explore_user_count, name="get_explore_user_count"),
    path("save-filter/", GeneralCompanyView.save_filter, name="save_filter"),
    path("saved-filters/", GeneralCompanyView.get_saved_filters,
         name="get_saved_filters"),
    path("saved-filter/<int:filter_id>/",
         GeneralCompanyView.delete_filter, name="delete_saved_filter"),
    path("update-favorite",
         GeneralCompanyView.updateFavoriteUser, name="update_favorite"),
    path("engineer-details/<int:user_id>/", GeneralCompanyView.get_user_details,
         name="get_user_details"),
    path("engineer-details/get-remote-work-skills-engineer/<int:user_id>/",
         GeneralCompanyView.get_remote_work_skills_engineer, name="get-remote-work-skills-engineer"),
    path("engineer-details/get-communication-skills-self-assessment-engineer/<int:user_id>/",
         GeneralCompanyView.get_communication_skills_self_assessment_engineer, name="get-communication-skills-self-assessment-engineer"),
    path("engineer-details/get-global-responsiveness-skills-engineer/<int:user_id>/",
         GeneralCompanyView.get_global_responsiveness_skills_engineer, name="get-global-responsiveness-skills-engineer"),
    path("engineer-details/get-reporting-consultation-skills-self-evaluation-engineer/<int:user_id>/",
         GeneralCompanyView.get_reporting_consultation_skills_self_evaluation_engineer, name="get-reporting-consultation-skills-self-evaluation-engineer"),
    path("engineer-details/get-project-management-skills-self-evaluation-engineer/<int:user_id>/",
         GeneralCompanyView.get_project_management_skills_self_evaluation_engineer, name="get-project-management-skills-self-evaluation-engineer"),

    # apply
    path("list-applied-engineers/",
         GeneralCompanyView.list_applied_engineers, name="list_applied_engineers"),
    path("apply/details/",
         GeneralCompanyView.get_apply_details, name="apply-details"),
    path("apply/reject/", GeneralCompanyView.reject_apply, name="reject-apply"),
    path("apply/request-interview",
         GeneralCompanyView.request_interview_apply, name="accept-apply"),
    path("apply/get-calendar-interview",
         GeneralCompanyView.get_calendar_interview, name="get-calendar-interview"),
    path("apply/update-interview-datetime",
         GeneralCompanyView.update_interview_datetime, name="update-interview-datetime"),
    path("apply/get-contract-details",
         GeneralCompanyView.get_contract_details, name="get_contract_details"),
    path("apply/download-employment-contract",
         GeneralCompanyView.download_employment_contract, name="download_contract"),

    # group chat
    path("get-list-group-chat/",
         GeneralCompanyView.get_list_group_chat, name="get_list_group_chat"),

]
