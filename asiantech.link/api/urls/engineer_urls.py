from django.urls import path, include
from api.views.engineer.engineer_views import <PERSON><PERSON>ie<PERSON>
from .recruitment_urls import recruitment_engineer_patterns
engineer_patterns = [
    path("list-agency-company/",
         EngineerViews.get_list_agency_company, name="list-agency-company"),
    path("add-agency-company/",
         EngineerViews.add_agency_company, name="add-agency-company"),
    path("get-list-of-select-agency-companies/",
         EngineerViews.get_list_of_select_agency_companies, name="get-list-of-select-agency-companies"),
    path("delete-agency-company/",
         EngineerViews.delete_agency_company, name="delete-agency-company"),
    path("list-apply-company/",
         EngineerViews.get_list_apply_company, name="list-apply-company"),
    path("get-list-best-companies",
         EngineerViews.get_best_companies, name="get_list_best_companies"),
    path("update-data-policy/", name="update_data_policy",
         view=EngineerViews.update_data_policy),
    path("<int:user_id>/agency-company/",
         EngineerViews.get_user_agency_company, name="get_user_agency_company"),
    path("recruit/", include(recruitment_engineer_patterns))
]
