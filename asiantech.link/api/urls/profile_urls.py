from django.urls import path
from api.views.profile.profile_views import ProfileView
from api.views.profile.cv_views import *
profile_patterns = [
    path("", ProfileView.profile_details, name="user_profile"),
    path("update-password/",
         ProfileView.update_password, name='update-password'),
    path("get-remote-work-skills/",
         ProfileView.get_remote_work_skills, name='get-remote-work-skills'),
    path("save-reporting-communication-skills/",
         ProfileView.save_remote_work_skills, name='save-reporting-communication-skills'),
    path("get-communication-skills-self-assessment/",
         ProfileView.get_communication_skills_self_assessment, name='get-communication-skills-self-assessment'),
    path("save-communication-skills-self-assessment/",
         ProfileView.save_communication_skills_self_assessment, name='save-communication-skills-self-assessment'),
    path("get-global-responsiveness-skills/",
         ProfileView.get_global_responsiveness_skills, name='get-global-responsiveness-skills'),
    path("save-global-responsiveness-skills/",
         ProfileView.save_global_responsiveness_skills, name='save-global-responsiveness-skills'),
    path("get-reporting-consultation-skills-self-evaluation/",
         ProfileView.get_reporting_consultation_skills_self_evaluation, name='get-reporting-consultation-skills-self-evaluation'),
    path("save-reporting-consultation-skills-self-evaluation/",
         ProfileView.save_reporting_consultation_skills_self_evaluation, name='save-reporting-consultation-skills-self-evaluation'),
    path("get-project-management-skills-self-evaluation/",
         ProfileView.get_project_management_skills_self_evaluation, name='get-project-management-skills-self-evaluation'),
    path("save-project-management-skills-self-evaluation/",
         ProfileView.save_project_management_skills_self_evaluation, name='save-project-management-skills-self-evaluation'),
    path("upload-cv-ai/",
         CVView.as_view(), name='upload-cv'),
    path("save-cv-uploaded/",
         SaveCVUploadedView.save_cv_uploaded, name='save-cv-uploaded'),
    path("download-cv/",
         ProfileView.download_cv, name='download-cv'),
    path("upload-cv-v2/",
         CVViewV2.as_view(), name='upload-cv-v2'),
    path("upload-cv-test/",
         CVViewTest.as_view(), name='upload-cv-test'),
]
