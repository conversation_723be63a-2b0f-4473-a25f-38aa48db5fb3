from django.urls import path
from api.views.admin.admin_views import AdminViews

admin_patterns = [
    path("get-list-engineer/",
         AdminViews.get_engineer_list, name="get_engineer_list"),
    path("get-list-registrar",
         AdminViews.get_list_registrar, name="get_list_registrar"),
    path("delete-engineers/",
         AdminViews.delete_engineers, name="delete_engineers"),
    path("get-list-of-email-schedules",
         AdminViews.get_list_of_email_schedules, name="get_list_of_email_schedules"),
    path("create-email-schedule",
         AdminViews.create_email_schedule, name="create_email_schedule"),
    path('update-email-schedule/<int:pk>/',
         AdminViews.update_email_schedule, name='update-email-schedule'),
    path('delete-email-schedule/<int:pk>/',
         AdminViews.delete_email_schedule, name='delete-email-schedule'),
    path('delete-email-schedule-list',
         AdminViews.delete_email_schedule_list, name='delete-email-schedule-list'),
    path('get-email-schedule-by-id/<int:pk>/',
         AdminViews.get_email_schedule_by_id, name='get-email-schedule-by-id'),
    path('update-engineer/',
         AdminViews.update_engineer, name='update-engineer'),
    path('export-user-data',
         AdminViews.export_user_data, name='export-user-data'),

]
