from django.urls import path
from api.views.host_support_agency_company.host_support_agency_company_views import HostSupportAgencyCompanyView

support_company_patterns = [
    path('get-list-registered-company/',
         HostSupportAgencyCompanyView.get_list_registered_company, name='get_list_registered_company'),
    path('request_interview/',
         HostSupportAgencyCompanyView.request_interview, name='support_company_request_interview'),
    path('get-list-recruit-host-company/',
         HostSupportAgencyCompanyView.get_list_recruit_of_host_company, name='get_list_recruit_of_host_company'),
    path('support-company/get-list-manage-host-company/',
         HostSupportAgencyCompanyView.get_list_manage_host_company, name='get-list-manage-host-company'),

]
