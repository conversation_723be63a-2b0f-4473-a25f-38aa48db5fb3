from django.urls import path, include
from django.conf.urls.static import static
from django.conf import settings

from .swagger_urls import swagger_patterns
from .auth_urls import auth_patterns
from .profile_urls import profile_patterns
from .media_urls import media_patterns

from .admin_urls import admin_patterns

from .engineer_urls import engineer_patterns
from .host_company_urls import host_company_patterns
from .host_support_agency_company_urls import support_company_patterns
from .general_company_urls import general_company_patterns
from .general_urls import general_patterns
from .notify_urls import notify_patterns
from .chat_urls import chat_patterns
from django.urls import re_path
from django.views.static import serve
from .token_urls import token_patterns
urlpatterns = [
    path('', include(swagger_patterns)),
    path('token/', include(token_patterns)),
    path('authentication/', include(auth_patterns)),
    path('profile/', include(profile_patterns)),
    path('image/', include(media_patterns)),
    path('host-companies/', include(host_company_patterns)),
    path('support-agencies/host/', include(support_company_patterns)),
    path('general-company/', include(general_company_patterns)),
    path('engineers/', include(engineer_patterns)),
    path('admin/', include(admin_patterns)),
    path('captcha/', include('captcha.urls')),
    path('general/', include(general_patterns)),
    path('notify/', include(notify_patterns)),
    path('chat/', include(chat_patterns)),

]

urlpatterns += [
    re_path(r'^media/(?P<path>.*)$', serve,
            {'document_root': settings.MEDIA_ROOT}),
]
