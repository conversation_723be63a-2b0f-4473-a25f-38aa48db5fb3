from django.urls import path
from api.views.recruit.recruitment_views import RecruitmentView

recruitment_company_patterns = [
    path('create/', RecruitmentView.create_recruitment,
         name='create_recruitment'),
    path('company-recruit/<int:recruitment_id>/',
         RecruitmentView.get_company_recruitment_details, name='get_my_recruitment_details'),
    path('<int:recruitment_id>/',
         RecruitmentView.get_recruitment_details, name='get_recruitment_details'),
    path('delete/', RecruitmentView.delete_recruitment,
         name='delete_recruitment'),
    path('list-recruits-uploaded/',
         RecruitmentView.list_recruits_uploaded, name='list_recruits_uploaded'),
]
recruitment_engineer_patterns = [
    path('explore/', RecruitmentView.list_recruits_explore,
         name='explore_recruits'),
    path('count-filter/', RecruitmentView.count_filter,
         name='count_filter'),
    path('change-status-interested/<int:recruit_id>', RecruitmentView.change_status_interested,
         name='change_status_interested'),
    path('apply-recruit/<int:recruit_id>/<int:company_id>', RecruitmentView.apply_recruit,
         name='apply_recruit'),
    path('recruitment-management-detail/<int:recruitment_id>', RecruitmentView.get_recruitment_management_detail,
         name='get_recruitment_management_detail'),
    path('cancel-recruit/', RecruitmentView.cancel_recruit,
         name='cancel_recruit'),
    path('update-interview-datetime/', RecruitmentView.update_interview_datetime,
         name='update_interview_datetime'),
    path("sign-contract/<int:recruitment_id>",
         RecruitmentView.sign_contract, name="sign_contract"),
    path("contract-details/<int:recruitment_id>",
         RecruitmentView.get_contract_details, name="contract_details"),
    path("get-list-featured-job",
         RecruitmentView.get_list_featured_jobs, name="get_list_featured_job"),
]
