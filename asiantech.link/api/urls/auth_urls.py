from django.urls import path
from api.views.authentication.authentication_views import AuthenticationViews
auth_patterns = [
    path("send-email-verify/",
         AuthenticationViews.send_email_verify, name='send-email-verify'),
    path("verify-email/",
         AuthenticationViews.verify_email, name="verify-email"),
    path("register/", AuthenticationViews.register, name="register"),
    path("login/", AuthenticationViews.login, name="login"),
    path("reset-password/",
         AuthenticationViews.resetPassword, name="reset-password"),
    path("confirm-login/",
         AuthenticationViews.confirmLogin, name="confirm-login"),
    path("logout/", AuthenticationViews.logout, name="logout"),
    path("captcha/",
         AuthenticationViews.generate_captcha, name="generate-captcha"),
    path("check-captcha-required-in-login/",
         AuthenticationViews.check_captcha_required_in_login, name="check-captcha-required-in-login"),
    path("reset-password-confirm/",
         AuthenticationViews.reset_password_confirm, name="reset-password-confirm"),
    path("resend-verification-email/",
         AuthenticationViews.resend_verification_email, name="resend-verification-email"),
    path("send-whatsapp-code/",
         AuthenticationViews.send_whatsapp_code, name="send-whatsapp-code"),
    path("confirm-whatsapp-code/",
         AuthenticationViews.confirm_whatsapp_code, name="confirm-whatsapp-code"),

    path("delete-account/",
         AuthenticationViews.delete_account, name="delete-account"),
    path("login-with-sns/",
         AuthenticationViews.login_with_sns, name="login-with-sns"),

    path('data-deletion-request/', AuthenticationViews.data_deletion_request,
         name='data_deletion_request'),
]
