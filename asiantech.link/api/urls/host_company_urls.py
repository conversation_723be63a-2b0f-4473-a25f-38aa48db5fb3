from django.urls import path, include
from api.views.host_company.host_company_views import HostCompanyView
from .recruitment_urls import recruitment_company_patterns
host_company_patterns = [

    path('list-support-company/',
         HostCompanyView.get_list_support_company, name='get_list_support_company'),
    path("subscribe-support-company",
         HostCompanyView.subscribe_support_company, name="subscribe-support-company"),
    path("unsubscribe-support-company",
         HostCompanyView.unsubscribe_support_company, name="unsubscribe-support-company"),
    path("list-support-company-subscribed",
         HostCompanyView.get_support_company_subscribed, name="list-support-company-subscribed"),

    # engineer applied for company
    path("list-recruitment-title/",
         HostCompanyView.list_recruitment_title, name="list_recruitment_title"),
    path("count-unresolved-issues/",
         HostCompanyView.count_unresolved_issues, name="count_unresolved_issues"),
    path("request-interview/",
         HostCompanyView.request_interview, name="request-interview"),
    path("update-apply-admission",
         HostCompanyView.update_apply_admission, name="update-apply-admission"),

    path("recruit/", include(recruitment_company_patterns))

]
