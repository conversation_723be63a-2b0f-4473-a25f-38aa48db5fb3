from api.services.notify_service.company_notify_service import CompanyNotifyService
from api.models.rec import *
from api.models.chat import *
from datetime import timezone, timedelta, datetime


from api.models.chat import MapChatGroup, RecChat
from api.models.map_hst_sup import MapHstSup
from api.models.rec import RecApply, RecRecruit
from api.serializers.host_company.host_company_serializers import *
from django.contrib.auth.models import User
from rest_framework.views import APIView
from django.contrib.auth import get_user_model
from rest_framework.decorators import api_view
from rest_framework import status
from api.serializers.engineers.user_serializers import *

from utils.responses import BaseResponse, CustomResponse
from rest_framework.decorators import api_view, permission_classes
from drf_yasg.utils import swagger_auto_schema
from django.utils.translation import gettext as _
from utils.permissions import *
from django.db.models import Q
from utils.validators import validate_serializer
from api.models.rec import *
from api.services.notify_service.engineer_notify_service import EngineerNotifyService
from api.serializers.general_company.general_company_serializers import GeneralCompanyRequestInterviewApplyResponseModel
User = get_user_model()


class HostCompanyView(APIView):

    @swagger_auto_schema(
        method='get',
        operation_id='get_list_support_company',
        query_serializer=HostCompanyParamsSupportCompanySerializer,
        responses=BaseResponse(status_code=status.HTTP_200_OK, message=_(
            "Success"), responseModel=HostCompanyListSupportCompanyResponseModel).to_openapi_response()
    )
    @permission_classes([IsHostCompany, IsEmailVerified])
    @api_view(['GET'])
    def get_list_support_company(request):
        email = request.query_params.get('email')
        company_queryset = ComCompany.objects.filter(
            user_type=UserType.HOST_SUPPORT_AGENCY_STAFF.value)

        if email:
            company_queryset = company_queryset.filter(
                Q(company_id__in=User.objects.filter(
                    email=email).values_list('company_id', flat=True))
            )

        serializer = HostCompanyGetSupportCompanySerializer(
            company_queryset, many=True)
        return CustomResponse(data=serializer.data, status=status.HTTP_200_OK, message=_("Success"))

    @swagger_auto_schema(
        method='post',
        operation_id='subscribe_support_company',
        request_body=HostCompanySubscribeSupportCompanySerializer,
        responses=BaseResponse(status_code=status.HTTP_200_OK, message=_(
            'Success')).to_openapi_response()
    )
    @api_view(['POST'])
    @permission_classes([IsHostCompany, IsEmailVerified])
    def subscribe_support_company(request):
        user = request.user
        if user.company_id is None and user.user_type is not UserType.HOST_COMPANY_STAFF.value:
            return CustomResponse(errors=[{'message': _('User does not have company')}], status=status.HTTP_400_BAD_REQUEST)
        company_id = request.data.get('support_company_id')
        try:
            support_company = ComCompany.objects.get(
                company_id=company_id, user_type=UserType.HOST_SUPPORT_AGENCY_STAFF.value)
        except ComCompany.DoesNotExist:
            return CustomResponse(errors=[{'message': _('Company not found')}], status=status.HTTP_404_NOT_FOUND)

        agent_user = User.objects.get(company_id=support_company.company_id)

        # check if user already subscribed
        try:
            data = MapHstSup.objects.get(host_company_id=user.company_id,
                                         support_company_id=company_id, support_agent_id=agent_user.user_id)
            if data.deleted == 1:
                data.deleted = 0
                data.save()
                return CustomResponse(message=_('Success'), status=status.HTTP_200_OK)
            else:
                return CustomResponse(message=_("User already subscribed"), status=status.HTTP_400_BAD_REQUEST)
        except MapHstSup.DoesNotExist:
            MapHstSup.objects.create(host_company_id=user.company_id,
                                     support_company_id=company_id, support_agent_id=agent_user.user_id, deleted=0)
            return CustomResponse(message=_('Success'), status=status.HTTP_200_OK)

    @swagger_auto_schema(
        method='post',
        operation_id='unsubscribe_support_company',
        request_body=HostCompanyUnsubscribeSupportCompanySerializer,
        responses=BaseResponse(status_code=status.HTTP_200_OK, message=_(
            'Success')).to_openapi_response()
    )
    @api_view(['POST'])
    @permission_classes([IsHostCompany, IsEmailVerified])
    def unsubscribe_support_company(request):
        user = request.user
        if user.company_id is None and user.user_type is not UserType.HOST_COMPANY_STAFF.value:
            return CustomResponse(errors=[{'message': _('User does not have company')}], status=status.HTTP_400_BAD_REQUEST)
        company_id = request.data.get('support_company_id')
        try:
            support_company = ComCompany.objects.get(
                company_id=company_id, user_type=UserType.HOST_SUPPORT_AGENCY_STAFF.value)
        except ComCompany.DoesNotExist:
            return CustomResponse(message=_('Company not found'), status=status.HTTP_404_NOT_FOUND)

        agent_user = User.objects.get(company_id=support_company.company_id)

        # check if user already subscribed
        exist = MapHstSup.objects.filter(host_company_id=user.company_id,
                                         support_company_id=int(company_id), support_agent_id=agent_user.user_id).exists()
        if not exist:
            return CustomResponse(message=_("User not subscribed"), status=status.HTTP_400_BAD_REQUEST)
        else:
            try:
                data = MapHstSup.objects.get(host_company_id=user.company_id,
                                             support_company_id=company_id, support_agent_id=agent_user.user_id)
                data.deleted = 1
                data.save()
            except MapHstSup.DoesNotExist:
                return CustomResponse(message=_('Data not found'), status=status.HTTP_404_NOT_FOUND)

        return CustomResponse(message=_('Success'), status=status.HTTP_200_OK)

    @swagger_auto_schema(
        method='get',
        operation_id='get_support_company_subscribed',
        responses=BaseResponse(status_code=status.HTTP_200_OK, message=_(
            'Success'), responseModel=HostCompanyListSupportCompanyResponseModel).to_openapi_response()
    )
    @api_view(['GET'])
    @permission_classes([IsHostCompany, IsEmailVerified])
    def get_support_company_subscribed(request):
        user = request.user
        if user.company_id is None and user.user_type is not UserType.HOST_COMPANY_STAFF.value:
            return CustomResponse(data=[], status=status.HTTP_200_OK, message=_('User does not have company.'))
        support_company_ids = MapHstSup.objects.filter(
            host_company_id=user.company_id, deleted=0).values_list('support_company_id', flat=True)
        company_queryset = ComCompany.objects.filter(
            company_id__in=support_company_ids)
        serializer = HostCompanyGetSupportCompanySerializer(
            company_queryset, many=True, context={"request": request})
        return CustomResponse(data=serializer.data, status=status.HTTP_200_OK, message=_('Success'))

    @swagger_auto_schema(
        method='post',
        operation_id='request_interview',
        request_body=HostCompanyRequestInterviewSerializer,
        responses=BaseResponse(status_code=status.HTTP_200_OK, message=_(
            'Success'), responseModel=GeneralCompanyRequestInterviewApplyResponseModel).to_openapi_response()
    )
    @permission_classes([IsHostCompany, IsEmailVerified])
    @api_view(['POST'])
    def request_interview(request):
        recruit_id = request.data.get('recruit_id')
        user_id = request.data.get('user_id')
        token = get_access_token_from_request(request)
        agency_company_id = Constants.COMPANY_ID_REFERRAL_AGENCY_TEMPLE

        existApply = RecApply.objects.filter(
            engineer_id=user_id,
            host_company_id=request.user.company_id,
        ).exists()
        if existApply:
            return CustomResponse(message=_("You have already make a request interview"), status=status.HTTP_400_BAD_REQUEST)
        agency_agent = User.objects.filter(
            company_id=agency_company_id, user_type=UserType.REFERRAL_AGENCY_STAFF.value).first()
        company_user_id = request.user.user_id

        message = request.data.get('message')
        group = RecChatGroup.objects.create()
        recruit = RecRecruit.objects.get(recruit_id=recruit_id)
        chat = RecChat.objects.create(
            group=group,
            user_id=company_user_id,
            text=message,
            send=timezone.now(),
        )

        MapChatGroup.objects.create(
            group=group,
            user_id=company_user_id,
            chat=chat
        )
        MapChatGroup.objects.create(
            group=group,
            user_id=user_id
        )
        expire_date = timezone.now() + timezone.timedelta(days=365*100)
        try:
            existApply = RecApply.objects.get(
                engineer_id=user_id,
                recruit_id=recruit_id,
            )
            if existApply.recruit_progress_code <= RecruitProgressCode.APPLICATION.value:
                existApply.recruit_progress_code = RecruitProgressCode.INTERVIEW_REQUEST.value
                existApply.updated = timezone.now()
                existApply.save()

                # EngineerNotifyService().send_notify_company_request_interview(
                #     token, existApply.host_agent, existApply.apply_id)
                CompanyNotifyService().send_notify_company_request_interview(
                    token, existApply.host_agent, existApply.apply_id)
                return CustomResponse(message=_("Success"), status=status.HTTP_200_OK)
            else:
                return CustomResponse(message=_("You have requested an interview for this engineer."), status=status.HTTP_400_BAD_REQUEST)
        except RecApply.DoesNotExist:
            pass
        support_company = recruit.support_company
        support_agent = None
        if support_company:
            support_agent = User.objects.get(
                company_id=support_company.company_id, user_type=UserType.HOST_SUPPORT_AGENCY_STAFF.value)
        rec_apply = RecApply.objects.create(
            recruit=recruit,
            group=group,
            engineer_id=user_id,
            agency_company_id=agency_company_id,
            agency_agent_id=agency_agent.user_id,
            host_company=recruit.host_company,
            host_agent=recruit.host_agent,
            recruit_progress_code=RecruitProgressCode.INTERVIEW_REQUEST.value,
            progress_update_datetime=timezone.now(),
            job_code=recruit.job_code,
            employ_code=recruit.employ_code,
            payroll_code=recruit.payroll_code,
            expiry_date=expire_date,
            support_company=support_company,
            support_agent=support_agent,
        )

        EngineerNotifyService().send_notify_company_request_interview(
            token, rec_apply.host_agent, rec_apply.apply_id)
        response_success_data = {
            'apply_id': rec_apply.apply_id
        }
        return CustomResponse(data=response_success_data, status=status.HTTP_200_OK, message=_("Success"))

    # List recruit title

    @swagger_auto_schema(
        method='get',
        operation_id='list_recruitment_title',
        responses=BaseResponse(status_code=status.HTTP_200_OK, message=_(
            'Success'), responseModel=HostCompanyListUserAppliedCompanyResponseModel).to_openapi_response()
    )
    @api_view(['GET'])
    @permission_classes([IsHostCompany, IsEmailVerified])
    def list_recruitment_title(request):
        user = request.user

        rec_queryset = RecRecruit.objects.filter(
            host_company_id=user.company_id)
        serializer = HostCompanyListRecruitmentTitleSerializer(
            rec_queryset, many=True)
        return CustomResponse(data=serializer.data, status=status.HTTP_200_OK, message=_("Success"))

    # count_unresolved_issues
    @swagger_auto_schema(
        method='get',
        operation_id='count_unresolved_issues',
        responses=BaseResponse(status_code=status.HTTP_200_OK, message=_(
            'Success'), responseModel=HostCompanyListUserAppliedCompanyResponseModel).to_openapi_response()
    )
    @api_view(['GET'])
    @permission_classes([IsHostCompany, IsEmailVerified])
    def count_unresolved_issues(request):
        user = request.user
        condition1 = Q(recruit_progress_code__in=[
            RecruitProgressCode.APPLICATION.value,
            RecruitProgressCode.INTERVIEW_SCHEDULING.value,
            RecruitProgressCode.INTERVIEW_COMPLETED.value,
        ])
        condition2 = Q(recruit_progress_code__in=[
            RecruitProgressCode.JOB_OFFER.value,
            RecruitProgressCode.OFFER_ACCEPTED.value]) & Q(engineer_accept_sign_id__isnull=True)

        count = RecApply.objects.filter(
            Q(host_company_id=user.company_id) & (condition1 | condition2)
        ).count()

        return CustomResponse(data={'count': count}, status=status.HTTP_200_OK, message=_("Success"))

    @swagger_auto_schema(
        method="POST",
        operation_id="update_apply_admission",
        request_body=HostCompanyUpdateInterviewAdmissionSerializer,
        responses=BaseResponse(
            status_code=status.HTTP_200_OK,
            message=_('Success')
        ).to_openapi_response()
    )
    @permission_classes([IsHostCompany, IsEmailVerified])
    @api_view(['POST'])
    def update_apply_admission(request):
        user = request.user
        host_company_id = user.company_id
        apply_id = request.data.get('apply_id')

        try:
            apply = RecApply.objects.get(
                apply_id=apply_id, host_company_id=host_company_id)

            if apply.recruit_progress_code <= RecruitProgressCode.EMPLOYED.value:
                if apply.recruit_progress_code < RecruitProgressCode.JOB_OFFER.value:
                    apply.recruit_progress_code = RecruitProgressCode.JOB_OFFER.value
                apply.progress_update_datetime = timezone.now()
                apply.job_code = request.data.get('job_code')
                apply.employ_code = request.data.get('employ_code')
                apply.place_code = request.data.get('place_code')
                apply.payroll_code = request.data.get('payroll_code')
                apply.payroll_price = request.data.get('payroll_price')
                apply.joing_date = request.data.get('joining_date')
                apply.updated = timezone.now()
                apply.save()
                token = get_access_token_from_request(request)
                EngineerNotifyService().send_notify_company_job_offer(
                    token, apply.host_agent, apply.apply_id)

                # create rec_chat
                RecChat.objects.create(
                    group=apply.group,
                    user=apply.host_agent,
                    text=_('Notification of acceptance has been sent.'),
                    send=timezone.now(),
                    created=timezone.now()
                )

                return CustomResponse(message=_('Success'), status=status.HTTP_200_OK)
            else:
                return CustomResponse(errors=[{'message': _('Invalid status')}], status=status.HTTP_400_BAD_REQUEST)
        except RecApply.DoesNotExist:
            return CustomResponse(errors=[{'message': _('Apply not found')}], status=status.HTTP_404_NOT_FOUND)
