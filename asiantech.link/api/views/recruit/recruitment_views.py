from api.services.notify_service.support_company_notify_service import SupportCompanyNotifyService
from api.services.notify_service.company_notify_service import CompanyNotifyService
from django.core.exceptions import ValidationError
from django.shortcuts import get_object_or_404
from django.db.models import Q
from decimal import Decimal
from api.serializers.common_serializers import BoolResponseModel
from api.serializers.recruitment_serializers import *
from utils.custom_pagination import CustomCursorPagination, CustomCursorTotalCountPagination
from django.contrib.auth.models import User
from rest_framework.permissions import IsAuthenticated
from rest_framework.views import APIView
from django.contrib.auth import get_user_model
from rest_framework.decorators import api_view
from rest_framework import status
from api.serializers.engineers.user_serializers import *
from utils.responses import BaseResponse, CustomResponse
from rest_framework.decorators import api_view, permission_classes
from drf_yasg.utils import swagger_auto_schema
from django.utils.translation import gettext as _
from utils.permissions import IsEmailVerified
from utils.validators import validate_serializer
from api.models.rec import *
from datetime import datetime, timezone
from django.db.models import Subquery, OuterRef
from api.models.rec_waiting_company import RecWaitingCompany
from core.settings.common import BASE_DIR
from utils.utils import get_access_token_from_request
User = get_user_model()


class RecruitmentView(APIView):

    @swagger_auto_schema(
        method='post',
        operation_id='create_recruitment',
        request_body=CreateRecruitmentSerializer,
        responses=BaseResponse(status_code=status.HTTP_201_CREATED, message=_(
            "Success")).to_openapi_response()

    )
    @api_view(['POST'])
    @permission_classes([IsAuthenticated, IsEmailVerified])
    def create_recruitment(request):
        user = request.user
        company_id = user.company_id

        if company_id is None:
            return CustomResponse(errors=_("You do not own the company"), status=status.HTTP_400_BAD_REQUEST)

        serializer = CreateRecruitmentSerializer(data=request.data)
        is_valid, error = validate_serializer(serializer)
        if not is_valid:
            return CustomResponse(errors=error, status=status.HTTP_400_BAD_REQUEST)
        serializer.is_valid(raise_exception=True)
        serializer.validated_data['host_company_id'] = company_id
        serializer.validated_data['host_agent_id'] = user.user_id
        serializer.save()

        return CustomResponse(status=status.HTTP_201_CREATED, message=_("Success"))

    @swagger_auto_schema(
        method='delete',
        operation_id='delete_recruitment',
        request_body=DeleteRecruitmentSerializer,
        responses=BaseResponse(status_code=status.HTTP_200_OK, message=_(
            "Success")).to_openapi_response()
    )
    @api_view(['DELETE'])
    @permission_classes([IsAuthenticated, IsEmailVerified])
    def delete_recruitment(request):
        user_id = request.user.user_id
        serializer = DeleteRecruitmentSerializer(data=request.data)
        is_valid, error = validate_serializer(serializer)
        if not is_valid:
            return CustomResponse(errors=error, status=status.HTTP_400_BAD_REQUEST)
        try:
            recruitment_id = serializer.validated_data['recruit_id']
            recruit = RecRecruit.objects.get(
                pk=recruitment_id, host_agent_id=user_id)
        except RecRecruit.DoesNotExist:
            return CustomResponse(message=_("Recruitment does not exist"), status=status.HTTP_404_NOT_FOUND)
        recruit.delete()
        return CustomResponse(status=status.HTTP_200_OK, data={}, message=_("Success"))

    @swagger_auto_schema(
        method='patch',
        operation_id='update_company_recruitment_details',
        request_body=UpdateRecruitmentSerializer,
        responses=BaseResponse(status_code=status.HTTP_200_OK, message=_(
            "Success")).to_openapi_response()
    )
    @swagger_auto_schema(
        method='get',
        operation_id='get_company_recruitment_details',
        responses=BaseResponse(status_code=status.HTTP_200_OK, message=_(
            "Success"), responseModel=CompanyMyRecruitmentDetailsResponseModel).to_openapi_response()
    )
    @api_view(['GET', 'PATCH'])
    def get_company_recruitment_details(request, recruitment_id):
        if request.method == 'GET':
            try:
                recruit = RecRecruit.objects.select_related('host_company').prefetch_related(
                    'waiting_company_set').get(pk=recruitment_id, host_company=request.user.company_id)
            except RecRecruit.DoesNotExist:
                return CustomResponse(message=_("Recruitment does not exist"), status=status.HTTP_404_NOT_FOUND)
            serializer = CompanyRecruitDetailSerializer(
                recruit, context={'request': request})
            return CustomResponse(data=serializer.data, status=status.HTTP_200_OK, message=_("Success"))
        if request.method == 'PATCH':
            try:
                recruit = RecRecruit.objects.select_related('host_company').prefetch_related(
                    'waiting_company_set').get(pk=recruitment_id, host_company=request.user.company_id)
            except RecRecruit.DoesNotExist:
                return CustomResponse(message=_("Recruitment does not exist"), status=status.HTTP_404_NOT_FOUND)
            serializer = UpdateRecruitmentSerializer(
                recruit, data=request.data, partial=True)
            is_valid, error = validate_serializer(serializer)
            if not is_valid:
                return CustomResponse(errors=error, status=status.HTTP_400_BAD_REQUEST)
            serializer.save()
            return CustomResponse(status=status.HTTP_200_OK, message=_("Success"))

    @swagger_auto_schema(
        method='get',
        operation_id='get_recruitment_details',
        responses=BaseResponse(status_code=status.HTTP_200_OK, message=_(
            "Success"), responseModel=RecruitmentDetailsResponseModel).to_openapi_response()
    )
    @api_view(['GET'])
    def get_recruitment_details(request, recruitment_id):
        try:
            recruit = RecRecruit.objects.select_related('host_company').prefetch_related(
                'waiting_company_set').get(pk=recruitment_id)
        except RecRecruit.DoesNotExist:
            return CustomResponse(message=_("Recruitment does not exist"), status=status.HTTP_404_NOT_FOUND)
        serializer = RecruitDetailSerializer(
            recruit, context={'request': request})
        return CustomResponse(data=serializer.data, status=status.HTTP_200_OK, message=_("Success"))

    @swagger_auto_schema(
        method='get',
        operation_id='list_recruits_uploaded',
        query_serializer=RecruitUploadedParamsSerializer,
        responses=BaseResponse(status_code=status.HTTP_200_OK, message=_(
            "Success"), responseModel=ListRecruitUploadedResponseModel).to_openapi_response()
    )
    @api_view(['GET'])
    @permission_classes([IsAuthenticated, IsEmailVerified])
    def list_recruits_uploaded(request):
        paginator = CustomCursorTotalCountPagination()
        user = request.user
        company_id = user.company_id
        if company_id is None:
            return CustomResponse(errors=_("You do not own the company"), status=status.HTTP_400_BAD_REQUEST)
        show_old_post = request.query_params.get('show_old_post')
        display_flag = request.query_params.get('display_flag')
        filter = {
            'host_company_id': company_id,
            'host_agent': user,
        }
        if show_old_post == "false":
            filter['end_date__gte'] = datetime.now(timezone.utc)

        if display_flag:
            filter['display_flag'] = display_flag

        recruits = RecRecruit.objects.filter(
            **filter)
        paginated_recruits = paginator.paginate_queryset(recruits, request)
        serializer = RecruitUploadedSerializers(
            paginated_recruits, many=True, context={'request': request})

        return CustomResponse(data=paginator.get_paginated_response(serializer.data), status=status.HTTP_200_OK, message=_("Success"))

    @swagger_auto_schema(
        method='get',
        operation_id='list_recruits_explore',
        query_serializer=RecruitExploreParamsSerializer,
        responses=BaseResponse(status_code=status.HTTP_200_OK, message=_(
            "Success"), responseModel=RecruitExploreResponseModel).to_openapi_response()
    )
    @api_view(['GET'])
    def list_recruits_explore(request):
        paginator = CustomCursorPagination()
        name = request.query_params.get('name', None)
        places = request.query_params.getlist('places', [])
        employ_code = request.query_params.get('employ_code', None)
        job_code = request.query_params.get('job_code', None)
        skill_codes_1 = request.query_params.getlist('skill_codes_1', [])
        skill_codes_2 = request.query_params.getlist('skill_codes_2', [])
        skill_codes_3 = request.query_params.getlist('skill_codes_3', [])
        payroll_code = request.query_params.getlist('payroll_code', [])
        payroll_price_from = request.query_params.get(
            'payroll_price_from', None)
        payroll_price_to = request.query_params.get(
            'payroll_price_to', None)
        recruits = RecRecruit.objects.none()
        language_codes = request.query_params.getlist('language_codes', [])
        # Combine all skill codes into a single list
        all_skill_codes = list(
            set(skill_codes_1 + skill_codes_2 + skill_codes_3))

        company_id = request.query_params.get('company_id', None)
        job_codes = request.query_params.getlist('job_codes', [])
        waiting_flag = request.query_params.getlist('waiting_flag', None)
        remote_code = request.query_params.get('remote_code', None)
        # Initialize an empty Q object
        query = Q()
        if company_id:
            query &= Q(host_company_id=company_id)
        if job_codes:
            query &= Q(job_code__in=job_codes)
        query &= Q(display_flag=1) & Q(start_date__lte=datetime.now(
            timezone.utc)) & Q(end_date__gte=datetime.now(timezone.utc))
        if remote_code:
            query &= Q(remote_code=remote_code)
        if name:
            query &= (
                Q(host_company__name__icontains=name) |
                Q(host_company__about_us__icontains=name) |
                Q(host_company__business_details__icontains=name) |
                Q(catch_copy__icontains=name) |
                Q(content__icontains=name)
            )
        # If places contains a single string with commas, split it into a list
        if len(places) == 1 and isinstance(places[0], str) and ',' in places[0]:
            places = places[0].split(',')
            # Strip any whitespace around the place codes
            places = [place.strip() for place in places]
        if places:
            place_query = Q()
            for place in places:
                place_query |= Q(host_company__address_code__icontains=place)
                place_query |= Q(place_code1__icontains=place)
                place_query |= Q(place_code2__icontains=place)
                place_query |= Q(place_code3__icontains=place)
            query &= place_query

        if employ_code:
            query &= Q(employ_code=employ_code)

        if job_code:
            query &= Q(job_code=job_code)

        # If all_skill_codes contains a single string with commas, split it into a list
        if len(all_skill_codes) == 1 and isinstance(all_skill_codes[0], str) and ',' in all_skill_codes[0]:
            all_skill_codes = all_skill_codes[0].split(',')
            # Strip any whitespace around the skill codes
            all_skill_codes = [code.strip() for code in all_skill_codes]

        if all_skill_codes:
            query &= (
                Q(skill_code1__in=all_skill_codes) |
                Q(skill_code2__in=all_skill_codes) |
                Q(skill_code3__in=all_skill_codes)
            )

        if payroll_code:
            query &= Q(payroll_code__in=payroll_code)

            try:
                if payroll_price_from:
                    payroll_price_from = Decimal(payroll_price_from)
                if payroll_price_to:
                    payroll_price_to = Decimal(payroll_price_to)
            except (ValueError, TypeError) as e:
                raise ValidationError(f"Invalid payroll price value: {e}")

            if payroll_price_from and not payroll_price_to:
                query &= Q(payroll_price_to__gte=payroll_price_from)
            elif payroll_price_to and not payroll_price_from:
                query &= Q(payroll_price_from__lte=payroll_price_to)
            elif payroll_price_from and payroll_price_to:
                query &= Q(payroll_price_from__gte=payroll_price_from) & Q(
                    payroll_price_to__lte=payroll_price_to)

        # If language_codes contains a single string with commas, split it into a list
        if len(language_codes) == 1 and isinstance(language_codes[0], str) and ',' in language_codes[0]:
            language_codes = language_codes[0].split(',')
            # Strip any whitespace around the language_codes
            language_codes = [code.strip() for code in language_codes]
        if language_codes:
            query &= (
                Q(language_code1__in=language_codes) |
                Q(language_code2__in=language_codes)
            )
        # Apply the combined Q object to filter recruits
        recruits = RecRecruit.objects.filter(query).select_related(
            'host_company').prefetch_related('waiting_company_set')

        # Filter by waiting_flag on RecWaitingCompany
        if waiting_flag:
            user = request.user
            if user.is_authenticated:
                recruits = recruits.filter(
                    waiting_company_set__waiting_flag__in=waiting_flag, waiting_company_set__engineer_id=user.user_id)
            else:
                return CustomResponse(
                    status=status.HTTP_401_UNAUTHORIZED, message=_("Error"))

        paginated_recruits = paginator.paginate_queryset(recruits, request)
        serializer = RecruitExploreSerializer(
            paginated_recruits, many=True, context={'request': request})
        return CustomResponse(data=paginator.get_paginated_response(serializer.data), status=status.HTTP_200_OK, message=_("Success"))

    @swagger_auto_schema(
        method='get',
        operation_id='count_filter',
        query_serializer=RecruitCountFilterParamsSerializer,
        responses=BaseResponse(status_code=status.HTTP_200_OK, message=_(
            "Success"), responseModel=RecruitCountFilterResponseModel).to_openapi_response()
    )
    @api_view(['GET'])
    def count_filter(request):

        name = request.query_params.get('name', None)
        places = request.query_params.getlist('places', [])
        employ_code = request.query_params.get('employ_code', None)
        job_code = request.query_params.get('job_code', None)
        skill_codes_1 = request.query_params.getlist('skill_codes_1', [])
        skill_codes_2 = request.query_params.getlist('skill_codes_2', [])
        skill_codes_3 = request.query_params.getlist('skill_codes_3', [])
        payroll_code = request.query_params.getlist('payroll_code', [])
        payroll_price_from = request.query_params.get(
            'payroll_price_from', None)
        payroll_price_to = request.query_params.get(
            'payroll_price_to', None)
        recruits = RecRecruit.objects.none()
        language_codes = request.query_params.getlist('language_codes', [])
        # Combine all skill codes into a single list
        all_skill_codes = list(
            set(skill_codes_1 + skill_codes_2 + skill_codes_3))
        company_id = request.query_params.get('company_id', None)
        job_codes = request.query_params.getlist('job_codes', [])
        waiting_flag = request.query_params.getlist('waiting_flag', None)
        remote_code = request.query_params.get('remote_code', None)
        # Initialize an empty Q object
        query = Q()

        if company_id:
            query &= Q(host_company_id=company_id)
        if job_codes:
            query &= Q(job_code__in=job_codes)
        query &= Q(display_flag=1) & Q(start_date__lte=datetime.now(
            timezone.utc)) & Q(end_date__gte=datetime.now(timezone.utc))
        if remote_code:
            query &= Q(remote_code=remote_code)

        query &= Q(display_flag=1) & Q(start_date__lte=datetime.now(
            timezone.utc)) & Q(end_date__gte=datetime.now(timezone.utc))

        if name:
            query &= (
                Q(host_company__name__icontains=name) |
                Q(host_company__about_us__icontains=name) |
                Q(host_company__business_details__icontains=name) |
                Q(catch_copy__icontains=name) |
                Q(content__icontains=name)
            )

        # If places contains a single string with commas, split it into a list
        if len(places) == 1 and isinstance(places[0], str) and ',' in places[0]:
            places = places[0].split(',')
            # Strip any whitespace around the place codes
            places = [place.strip() for place in places]
        if places:
            place_query = Q()
            for place in places:
                place_query |= Q(host_company__address_code__icontains=place)
                place_query |= Q(place_code1__icontains=place)
                place_query |= Q(place_code2__icontains=place)
                place_query |= Q(place_code3__icontains=place)
            query &= place_query

        if employ_code:
            query &= Q(employ_code=employ_code)

        if job_code:
            query &= Q(job_code=job_code)

        # If all_skill_codes contains a single string with commas, split it into a list
        if len(all_skill_codes) == 1 and isinstance(all_skill_codes[0], str) and ',' in all_skill_codes[0]:
            all_skill_codes = all_skill_codes[0].split(',')
            # Strip any whitespace around the skill codes
            all_skill_codes = [code.strip() for code in all_skill_codes]

        if all_skill_codes:
            query &= (
                Q(skill_code1__in=all_skill_codes) |
                Q(skill_code2__in=all_skill_codes) |
                Q(skill_code3__in=all_skill_codes)
            )

        if payroll_code:
            query &= Q(payroll_code__in=payroll_code)

            try:
                if payroll_price_from:
                    payroll_price_from = Decimal(payroll_price_from)
                if payroll_price_to:
                    payroll_price_to = Decimal(payroll_price_to)
            except (ValueError, TypeError) as e:
                raise ValidationError(f"Invalid payroll price value: {e}")

            if payroll_price_from and not payroll_price_to:
                query &= Q(payroll_price_to__gte=payroll_price_from)
            elif payroll_price_to and not payroll_price_from:
                query &= Q(payroll_price_from__lte=payroll_price_to)
            elif payroll_price_from and payroll_price_to:
                query &= Q(payroll_price_from__gte=payroll_price_from) & Q(
                    payroll_price_to__lte=payroll_price_to)

        # If language_codes contains a single string with commas, split it into a list
        if len(language_codes) == 1 and isinstance(language_codes[0], str) and ',' in language_codes[0]:
            language_codes = language_codes[0].split(',')
            # Strip any whitespace around the language_codes
            language_codes = [code.strip() for code in language_codes]
        if language_codes:
            query &= (
                Q(language_code1__in=language_codes) |
                Q(language_code2__in=language_codes)
            )
        # Apply the combined Q object to filter recruits
        recruits = RecRecruit.objects.select_related(
            'host_company').filter(query)

        # Filter by waiting_flag on RecWaitingCompany
        if waiting_flag:
            user = request.user
            if user.is_authenticated:
                recruits = recruits.filter(
                    waiting_company_set__waiting_flag__in=waiting_flag, waiting_company_set__engineer_id=user.user_id)
            else:
                return CustomResponse(
                    status=status.HTTP_401_UNAUTHORIZED, message=_("Error"))
        total_recruits = recruits.count()
        return CustomResponse(status=status.HTTP_200_OK, message=_("Success"), data=total_recruits)

    @swagger_auto_schema(
        method='post',
        operation_id='change_status_interested',
        responses=BaseResponse(status_code=status.HTTP_200_OK, message=_(
            "Success"), responseModel=BoolResponseModel).to_openapi_response()
    )
    @api_view(['POST'])
    @permission_classes([IsAuthenticated, IsEmailVerified])
    def change_status_interested(request, recruit_id):
        user = request.user

        recruit = get_object_or_404(RecRecruit, recruit_id=recruit_id)
        # Check if a RecWaitingCompany object exists for the given recruit
        waiting_company = recruit.waiting_company_set.filter(
            engineer_id=user.user_id, waiting_flag__isnull=False).first()

        if waiting_company:
            # If waiting_flag = APPLIED, return failed response
            if waiting_company.waiting_flag == WaitingFlag.APPLIED.value:
                data = False
                return CustomResponse(data=data, status=status.HTTP_200_OK, message=_("Failed"))
            # If waiting_flag = 1, change to waiting_flag = 0
            if waiting_company.waiting_flag == WaitingFlag.UNDER_CONSIDERATION.value:
                waiting_company.waiting_flag = WaitingFlag.CANCELLED.value
            else:
                waiting_company.waiting_flag = WaitingFlag.UNDER_CONSIDERATION.value

            waiting_company.updated = datetime.now(timezone.utc)
            waiting_company.save()
            data = True
        else:
            # If waiting_flag does not exist, create a new RecWaitingCompany with waiting_flag = 1
            RecWaitingCompany.objects.create(
                recruit=recruit, waiting_flag=WaitingFlag.UNDER_CONSIDERATION.value, engineer_id=user.user_id, created=datetime.now(timezone.utc), updated=datetime.now(timezone.utc))
            data = True
        return CustomResponse(data=data, status=status.HTTP_200_OK, message=_("Success"))

    @swagger_auto_schema(
        method='post',
        operation_id='apply_recruit',
        responses=BaseResponse(status_code=status.HTTP_200_OK, message=_(
            "Success"), responseModel=BoolResponseModel).to_openapi_response()
    )
    @api_view(['POST'])
    @permission_classes([IsAuthenticated, IsEmailVerified])
    def apply_recruit(request, recruit_id, company_id):
        user = request.user

        recruit = get_object_or_404(RecRecruit, recruit_id=recruit_id)

        # Check ComCompany is available
        choose_company = ComCompany.objects.filter(
            company_id=company_id).first()
        if choose_company is None:
            data = False
            return CustomResponse(data=data, status=status.HTTP_200_OK, message=_("ComCompany not found"))

        # Check MapEngAgc is available
        map_eng_agc = MapEngAgc.objects.filter(
            engineer=user, agency_company=choose_company).first()
        if map_eng_agc is None:
            data = False
            return CustomResponse(data=data, status=status.HTTP_200_OK, message=_("MapEngAgc not found"))

        # Check if a RecWaitingCompany object exists for the given recruit
        waiting_company = recruit.waiting_company_set.filter(
            engineer_id=user.user_id, waiting_flag__isnull=False).first()

        if waiting_company:
            # If waiting_flag != 2, change to waiting_flag = 2
            if waiting_company.waiting_flag != WaitingFlag.APPLIED.value:
                waiting_company.waiting_flag = WaitingFlag.APPLIED.value
                waiting_company.updated = datetime.now(timezone.utc)
            # If waiting_flag == 2, return not updated
            else:
                data = False
                return CustomResponse(data=data, status=status.HTTP_200_OK, message=_("Already applied"))
            waiting_company.save()
            data = True
        else:
            # If waiting_flag does not exist, create a new RecWaitingCompany with waiting_flag = 2
            RecWaitingCompany.objects.create(
                recruit=recruit, waiting_flag=WaitingFlag.APPLIED.value, engineer_id=user.user_id, created=datetime.now(timezone.utc), updated=datetime.now(timezone.utc))
            data = True

        # Check RecApply record exist
        try:
            existApply = RecApply.objects.get(
                recruit=recruit, engineer=user
            )
            if existApply is not None:
                return CustomResponse(
                    status=status.HTTP_400_BAD_REQUEST,
                    message=_("You have already applied for this recruitment")
                )
        except RecApply.DoesNotExist:
            pass
        # Create RecApply
        rec_apply = RecApply.objects.create(recruit=recruit, group=None, engineer=user, agency_company=map_eng_agc.agency_company,
                                            agency_agent=map_eng_agc.agency_agent, host_company=recruit.host_company, host_agent=recruit.host_agent,
                                            support_company=recruit.support_company, support_agent=recruit.support_agent,
                                            recruit_progress_code=RecruitProgressCode.APPLICATION.value,
                                            progress_update_datetime=datetime.now(timezone.utc), job_code=recruit.job_code, employ_code=recruit.employ_code,
                                            payroll_code=recruit.payroll_code, expiry_date=datetime(
                                                9999, 12, 31),
                                            created=datetime.now(timezone.utc))

        # Create RecApplyRead
        RecApplyRead.objects.create(
            apply_id=rec_apply.apply_id, user_id=user.user_id, created=datetime.now(timezone.utc))

        # Check if a RecInterestedEngineer object exists for the given host_company_id and considering_engineer_id then change interested_flag = 2
        login_id = user.user_id
        interested_engineer = RecInterestedEngineer.objects.filter(
            host_company_id=company_id, engineer_id=login_id).first()
        if interested_engineer:
            interested_engineer.interested_flag = 2
            interested_engineer.save()

        # Send notify to host_agent
        access_token = get_access_token_from_request(request)
        CompanyNotifyService().send_notify_engineer_apply_recruitment(
            access_token, user, rec_apply.apply_id)
        if rec_apply.support_agent:
            SupportCompanyNotifyService().send_notify_engineer_apply_recruitment(
                access_token, user, rec_apply.apply_id)

        return CustomResponse(data=data, status=status.HTTP_200_OK, message=_("Success"))

    # Recruitment management detail
    @swagger_auto_schema(
        method='get',
        operation_id='get_recruitment_management_detail',
        responses=BaseResponse(status_code=status.HTTP_200_OK, message=_(
            "Success"), responseModel=RecruitmentManagementDetailsResponseModel).to_openapi_response()
    )
    @api_view(['GET'])
    @permission_classes([IsAuthenticated, IsEmailVerified])
    def get_recruitment_management_detail(request, recruitment_id):
        user = request.user

        try:
            if not RecRecruit.objects.filter(pk=recruitment_id).exists():
                return CustomResponse(message=_("Recruitment does not exist"), status=status.HTTP_404_NOT_FOUND)

            recruit = RecRecruit.objects.filter(
                Q(recapply__engineer_id=user.user_id) |
                Q(waiting_company_set__engineer_id=user.user_id),
                pk=recruitment_id
            ).annotate(
                recruit_progress_code=Subquery(
                    RecApply.objects.filter(
                        recruit_id=OuterRef('pk'),
                        engineer_id=user.user_id,
                        recruit_progress_code__isnull=False,
                    ).values('recruit_progress_code')[:1]
                ),
                interview_datetime=Subquery(
                    RecApply.objects.filter(
                        recruit_id=OuterRef('pk'),
                        engineer_id=user.user_id,
                        interview_datetime__isnull=False,
                    ).values('interview_datetime')[:1]
                ),
                payroll_price=Subquery(
                    RecApply.objects.filter(
                        recruit_id=OuterRef('pk'),
                        engineer_id=user.user_id,
                        payroll_price__isnull=False,
                    ).values('payroll_price')[:1]
                ),
                place_code=Subquery(
                    RecApply.objects.filter(
                        recruit_id=OuterRef('pk'),
                        engineer_id=user.user_id,
                        place_code__isnull=False,
                    ).values('place_code')[:1]
                ),
                joing_date=Subquery(
                    RecApply.objects.filter(
                        recruit_id=OuterRef('pk'),
                        engineer_id=user.user_id,
                        joing_date__isnull=False,
                    ).values('joing_date')[:1]
                ),
                group_id=Subquery(
                    RecApply.objects.filter(
                        recruit_id=OuterRef('pk'),
                        engineer_id=user.user_id,
                        group_id__isnull=False,
                    ).values('group_id')[:1]
                ),
                waiting_flag=Subquery(
                    RecWaitingCompany.objects.filter(
                        recruit_id=OuterRef('pk'),
                        engineer_id=user.user_id,
                        waiting_flag__isnull=False
                    ).values('waiting_flag')[:1]
                ),


            ).select_related('host_company').first()

            try:
                user = request.user
                rec_apply = RecApply.objects.get(
                    recruit=recruit, engineer=user)
                if rec_apply is not None:
                    rec_record = RecApplyRead.objects.filter(
                        apply_id=rec_apply.apply_id, user_id=user.user_id).first()
                    if rec_record is not None:
                        rec_record.delete()
            except RecApply.DoesNotExist:
                pass

            if recruit is None:
                return CustomResponse(message=_("Recruitment does not exist or you do not have access to it"), status=status.HTTP_404_NOT_FOUND)
        except RecRecruit.DoesNotExist:
            return CustomResponse(message=_("Recruitment does not exist"), status=status.HTTP_404_NOT_FOUND)

        serializer = RecruitmentManagementDetailSerializer(
            recruit, context={'request': request})
        return CustomResponse(data=serializer.data, status=status.HTTP_200_OK, message=_("Success"))

    # Update recruit progress
    @swagger_auto_schema(
        method='patch',
        query_serializer=RecruitmentParamsSerializer,
        operation_id='cancel_recruit',
        responses=BaseResponse(status_code=status.HTTP_200_OK, message=_(
            "Success")).to_openapi_response()
    )
    @api_view(['PATCH'])
    @permission_classes([IsAuthenticated, IsEmailVerified])
    def cancel_recruit(request):
        user = request.user
        recruit_id = request.query_params.get('recruit_id')

        if recruit_id is None:
            return CustomResponse(errors=_("Recruit ID is required"), status=status.HTTP_400_BAD_REQUEST)

        try:
            recruit = RecRecruit.objects.get(pk=recruit_id)
            data = RecApply.objects.filter(
                recruit=recruit, engineer=user).first()

            if data is None:
                return CustomResponse(message=_("No application found"), status=status.HTTP_404_NOT_FOUND)

            if data.recruit_progress_code == RecruitProgressCode.APPLICATION.value:
                data.recruit_progress_code = RecruitProgressCode.APPLICATION_WITHDRAWN.value
            elif data.recruit_progress_code == RecruitProgressCode.INTERVIEW_REQUEST.value:
                data.recruit_progress_code = RecruitProgressCode.INTERVIEW_WITHDRAWN.value
            elif data.recruit_progress_code == RecruitProgressCode.INTERVIEW_DATE_CONFIRMED.value or data.recruit_progress_code == RecruitProgressCode.INTERVIEW_COMPLETED.value:
                data.recruit_progress_code = RecruitProgressCode.INTERVIEW_WITHDRAWN.value
            elif data.recruit_progress_code == RecruitProgressCode.JOB_OFFER.value or data.recruit_progress_code == RecruitProgressCode.OFFER_ACCEPTED.value:
                data.recruit_progress_code = RecruitProgressCode.OFFER_DECLINED.value
            else:
                return CustomResponse(message=_("Invalid recruit progress code to cancel"), status=status.HTTP_400_BAD_REQUEST)

            data.progress_update_datetime = datetime.now(timezone.utc)
            data.save()

            return CustomResponse(status=status.HTTP_200_OK, message=_("Success"))
        except RecRecruit.DoesNotExist:
            return CustomResponse(message=_("Recruitment does not exist"), status=status.HTTP_404_NOT_FOUND)

    # Update interview datetime
    @swagger_auto_schema(
        method='patch',
        query_serializer=RecruitmentParamsSerializer,
        operation_id='update_interview_datetime',
        request_body=UpdateInterviewDatetimeSerializer,
        responses=BaseResponse(status_code=status.HTTP_200_OK, message=_(
            "Success")).to_openapi_response()
    )
    @api_view(['PATCH'])
    @permission_classes([IsAuthenticated, IsEmailVerified])
    def update_interview_datetime(request):
        user = request.user
        recruit_id = request.query_params.get('recruit_id')
        interview_datetime = request.data.get('interview_datetime')

        if recruit_id is None:
            return CustomResponse(errors=_("Recruit ID is required"), status=status.HTTP_400_BAD_REQUEST)

        try:
            recruit = RecRecruit.objects.get(pk=recruit_id)
            data = RecApply.objects.filter(
                recruit=recruit, engineer=user).first()

            if data.recruit_progress_code == RecruitProgressCode.INTERVIEW_REQUEST.value or data.recruit_progress_code == RecruitProgressCode.INTERVIEW_SCHEDULING.value:
                if interview_datetime is not None:
                    data.interview_datetime = interview_datetime
                    data.recruit_progress_code = RecruitProgressCode.INTERVIEW_SCHEDULING.value

                else:
                    return CustomResponse(errors=_("Interview datetime is required"), status=status.HTTP_400_BAD_REQUEST)
            else:
                return CustomResponse(message=_("Invalid recruit progress code to cancel"), status=status.HTTP_400_BAD_REQUEST)

            data.progress_update_datetime = datetime.now(timezone.utc)
            data.save()

            # Send notify to host_agent
            token = get_access_token_from_request(request)
            CompanyNotifyService().send_notify_engineer_request_time_interview(
                token, user,  data.apply_id)
            SupportCompanyNotifyService().send_notify_engineer_request_time_interview(
                token, user, data.apply_id)

            return CustomResponse(status=status.HTTP_200_OK, message=_("Success"))
        except RecRecruit.DoesNotExist:
            return CustomResponse(message=_("Recruitment does not exist"), status=status.HTTP_404_NOT_FOUND)

    # Sign contract
    @swagger_auto_schema(
        method='get',
        operation_id='get_work_conditions',
        responses=BaseResponse(status_code=status.HTTP_200_OK, message=_(
            "Success"), responseModel=SignContractResponseModel).to_openapi_response()
    )
    @swagger_auto_schema(
        method='patch',
        operation_id='update_recruit_progress',
        responses=BaseResponse(status_code=status.HTTP_200_OK, message=_(
            "Success")).to_openapi_response()
    )
    @api_view(['GET', 'PATCH'])
    @permission_classes([IsAuthenticated, IsEmailVerified])
    def sign_contract(request, recruitment_id):
        user = request.user
        try:
            rec_apply = RecApply.objects.get(
                recruit_id=recruitment_id, engineer=user)
        except RecApply.DoesNotExist:
            return CustomResponse(message=_("No application found"), status=status.HTTP_404_NOT_FOUND)

        if rec_apply.recruit_progress_code == RecruitProgressCode.JOB_OFFER.value:
            if request.method == 'GET':
                serializer = SignContractSerializer(
                    rec_apply, context={'request': request})

                return CustomResponse(data=serializer.data, status=status.HTTP_200_OK, message=_("Success"))
            elif request.method == 'PATCH':
                rec_apply.recruit_progress_code = RecruitProgressCode.OFFER_ACCEPTED.value
                rec_apply.progress_update_datetime = datetime.now(timezone.utc)
                rec_apply.save()
                # Update progress another application of engineer to OTHER_COMPANY_OFFER
                RecApply.objects.filter(engineer=rec_apply.engineer, recruit_progress_code__in=[
                    RecruitProgressCode.REQUESTING_AN_AGENT.value, RecruitProgressCode.APPLICATION.value,
                    RecruitProgressCode.INTERVIEW_REQUEST.value, RecruitProgressCode.INTERVIEW_SCHEDULING.value,
                    RecruitProgressCode.INTERVIEW_DATE_CONFIRMED.value, RecruitProgressCode.INTERVIEW_COMPLETED.value,
                    RecruitProgressCode.JOB_OFFER.value
                ]).exclude(recruit_id=recruitment_id).update(
                    recruit_progress_code=RecruitProgressCode.OTHER_COMPANY_OFFER.value, progress_update_datetime=datetime.now(timezone.utc))
                return CustomResponse(status=status.HTTP_200_OK, message=_("Success"))
        else:
            return CustomResponse(message=_("Invalid recruit progress code to sign contract"), status=status.HTTP_400_BAD_REQUEST)

    @swagger_auto_schema(
        method='GET',
        operation_id='get_contract_details',
        responses=BaseResponse(
            status_code=status.HTTP_200_OK,
            message=_('Success'),
            responseModel=RecruitContractDetailsResponseModel
        ).to_openapi_response()
    )
    @api_view(['GET'])
    @permission_classes([IsAuthenticated, IsEmailVerified])
    def get_contract_details(request, recruitment_id):
        try:
            user_id = request.user.user_id

            rec_apply = RecApply.objects.get(
                recruit_id=recruitment_id, engineer_id=user_id)

            if rec_apply:
                serializer = RecruitGetContractDetailsSerializer(rec_apply)
                return CustomResponse(data=serializer.data, status=status.HTTP_200_OK)

            else:
                return CustomResponse(
                    message=_("No application found"),
                    status=status.HTTP_404_NOT_FOUND
                )

        except RecApply.DoesNotExist:
            return CustomResponse(
                message=_(
                    "No application found for the provided recruitment ID"),
                status=status.HTTP_404_NOT_FOUND
            )

        except RecAcceptSign.DoesNotExist:
            return CustomResponse(
                message=_("No accept sign found for the application"),
                status=status.HTTP_404_NOT_FOUND
            )

        except Exception as e:
            return CustomResponse(
                message=_(str(e)),
                status=status.HTTP_400_BAD_REQUEST
            )

    @swagger_auto_schema(
        method='get',
        operation_id='get_list_featured_jobs',
        responses=BaseResponse(
            status_code=status.HTTP_200_OK,
            message=_("Success"),
            responseModel=ListTopFeaturedJobsResponseModel
        ).to_openapi_response()
    )
    @api_view(['GET'])
    def get_list_featured_jobs(request):
        list_job_codes = get_job_code()
        featured_jobs_list = []

        for job_code in list_job_codes:
            job_id = job_code['id']
            # count recruit use job_id
            count_recruit = RecRecruit.objects.filter(job_code=job_id,
                                                      start_date__lte=datetime.now(
                                                          timezone.utc),
                                                      end_date__gte=datetime.now(
                                                          timezone.utc),
                                                      ).count()

            # Generate image path and check if file exists
            host_url = request.build_absolute_uri('/')
            image_path = host_url + f"media/assets/job-images/{job_id}.png"

            # Prepare data for serializer
            serializer = FeaturedJobSerializer({
                "id": job_id,
                "name_jp": job_code['name_jp'],
                "name_en": job_code['name_en'],
                "image": image_path,
                "total_jobs": count_recruit,
            })

            featured_jobs_list.append(serializer.data)

        return CustomResponse(
            data=featured_jobs_list,
            status=status.HTTP_200_OK,
            message=_("Success")
        )
