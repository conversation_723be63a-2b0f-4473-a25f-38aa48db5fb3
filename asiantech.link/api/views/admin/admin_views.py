from datetime import date
from django.utils import translation
from cryptography.fernet import Fernet
from api.services import mail_sender_service


from utils.decorators import log_exceptions
from utils.utils import *


from django.contrib.auth.models import User
from rest_framework.views import APIView
from django.contrib.auth import get_user_model
from rest_framework.decorators import api_view
from rest_framework import status
from api.serializers.engineers.user_serializers import *
from utils.responses import BaseResponse, CustomResponse
from rest_framework.decorators import api_view, permission_classes
from drf_yasg.utils import swagger_auto_schema
from django.utils.translation import gettext as _
from utils.permissions import IsEmailVerified, IsAdminUser
from utils.validators import validate_serializer
from utils.validators import validate_serializer
from utils.custom_pagination import CustomCursorTotalCountPagination
from utils.validators import validate_serializer
from django.db.models import Q, F, Max
from api.serializers.admin.admin_serializers import *
from api.serializers.admin.email_schedule_serializers import *
from django.http import HttpResponse
from docx import Document
from html4docx import HtmlToDocx
from docx.shared import Pt
from docx.oxml.ns import qn
from docx.shared import RGBColor, Inches
from docx.enum.table import WD_ALIGN_VERTICAL
from docx.enum.text import WD_PARAGRAPH_ALIGNMENT
from docx.enum.text import WD_UNDERLINE
import zipfile


from io import BytesIO
User = get_user_model()


class AdminViews(APIView):

    @swagger_auto_schema(
        method='get',
        operation_description='Get list of engineers',
        operation_id='Get Engineer List',
        responses=BaseResponse(status_code=status.HTTP_200_OK, message=_(
            'List of engineers'), responseModel=GetListEngineerResponseModel).to_openapi_response(),
        query_serializer=GetListEngineerParamsSerializer
    )
    @api_view(['GET'])
    @permission_classes([IsAdminUser, IsEmailVerified])
    def get_engineer_list(request):
        query = User.objects.filter(
            user_type=UserType.ENGINEER.value, deleted=0)
        register_date_from = request.query_params.get('register_date_from')
        register_date_to = request.query_params.get('register_date_to')
        search = request.query_params.get('query_search')
        created_user = request.query_params.get('created_user')
        is_data_policy_accept = request.query_params.get(
            'is_data_policy_accept')
        # if engineer contain "mailsale" in email -> only display engineer have first name and last name
        query = query.filter(
            Q(
                email__icontains='mailsale',
                first_name__isnull=False,
                last_name__isnull=False
            ) | ~Q(email__icontains='mailsale')
        )
        if is_data_policy_accept is not None and is_data_policy_accept != '':
            is_data_policy_accept = int(is_data_policy_accept)
            if is_data_policy_accept == -1:
                is_data_policy_accept = None
            query = query.filter(
                is_data_policy_accept=is_data_policy_accept
            )
        if register_date_from is not None or register_date_to is not None:
            query = query.filter(
                updated__isnull=False
            )
        if register_date_from is not None:
            query = query.filter(
                updated__gte=register_date_from
            )
        if register_date_to is not None:
            query = query.filter(
                updated__lte=register_date_to
            )
        if search is not None and search.strip() != "":
            language = get_language_code_from_header(request) or 'en'
            search_norm = search.strip()

            # Split the input into terms
            terms = search_norm.lower().split()

            # Build the query for multi-word matching
            name_query = Q()
            if len(terms) > 1:
                # If multiple words, search for any combination of terms in first and last name
                for term in terms:
                    name_query |= Q(first_name__icontains=term) | Q(
                        last_name__icontains=term)
            else:
                # Single term search
                name_query = Q(first_name__icontains=search) | Q(
                    last_name__icontains=search)

            # Filter by country code
            country_query = Q()
            country_code = get_country_code_by_name(search_norm, language)
            if country_code:
                country_query = Q(country_code=country_code)

            # Filter by age
            age_query = Q()
            if search_norm.isdigit():
                age = int(search_norm)
                today = date.today()
                year_target = today.year - age
                age_query = Q(birth_date__year=year_target)
            else:
                age_query = Q()

            # Filter by skill
            skill_query = Q()
            skill_code = get_skill_code_by_name(search_norm)
            if isinstance(skill_code, tuple):
                skill_code = skill_code[0]
            if skill_code:
                skill_query = Q(engskill__skill_code=skill_code)
            else:
                skill_query = Q()

            academic_query = Q()
            if search_norm:
                query = query.annotate(
                    max_academic_type=Max('engacademic__type')
                )

                academic_query = Q(
                    engacademic__type=F('max_academic_type'),
                    engacademic__school__icontains=search_norm
                )

            final_search_q = (
                name_query |
                country_query |
                age_query |
                skill_query |
                academic_query
            )
            # Apply the query filter
            query = query.filter(final_search_q).distinct()
        if created_user is not None and created_user != '':
            query = query.filter(
                created_user=created_user
            )

        paginator = CustomCursorTotalCountPagination()
        paginated_recruits = paginator.paginate_queryset(query, request)
        serializer = GetListEngineerSerializer(
            paginated_recruits, many=True, context={'request': request})
        return CustomResponse(
            data=paginator.get_paginated_response(serializer.data),
            message=_("Success"),
            status=status.HTTP_200_OK,
        )

    @swagger_auto_schema(
        method='get',
        operation_description='Get list of email schedules',
        operation_id='get_list_of_email_schedules',
        responses=BaseResponse(status_code=status.HTTP_200_OK, message=_(
            'List of email schedules'), responseModel=GetEmailScheduleResponseModel).to_openapi_response(),
        query_serializer=GetEmailScheduleParamsSerializer
    )
    @api_view(['GET'])
    @permission_classes([IsAdminUser, IsEmailVerified])
    def get_list_of_email_schedules(request):
        query = EmailSchedules.objects.all()
        date_from = request.query_params.get('date_from')
        date_to = request.query_params.get('date_to')
        search = request.query_params.get('query_search')
        created_user = request.query_params.get('created_user')
        if date_from is not None:
            query = query.filter(
                created__gte=date_from
            )
        if date_to is not None:
            query = query.filter(
                created__lte=date_to
            )
        if search is not None and search != '':
            search = search.strip().lower()

            # Split the input into terms
            terms = search.split()

            # Build the query for multi-word matching
            name_query = Q()
            if len(terms) > 1:
                # If multiple words, search for any combination of terms in first and last name
                for term in terms:
                    name_query |= Q(first_name__icontains=term) | Q(
                        last_name__icontains=term)
            else:
                # Single term search
                name_query = Q(first_name__icontains=search) | Q(
                    last_name__icontains=search)

            # Apply the query filter
            query = query.filter(name_query)
        if created_user is not None and created_user != '':
            query = query.filter(
                created_user=created_user
            )

        paginator = CustomCursorTotalCountPagination()
        paginated_recruits = paginator.paginate_queryset(query, request)
        serializer = EmailScheduleSerializer(paginated_recruits, many=True)
        return CustomResponse(
            data=paginator.get_paginated_response(serializer.data),
            message=_("Success"),
            status=status.HTTP_200_OK,
        )

    @swagger_auto_schema(
        method='get',
        operation_description='Get list of registrars',
        operation_id='Get List Registrar',
        responses=BaseResponse(
            status_code=status.HTTP_200_OK,
            message=_('List of registrars'),
            responseModel=GetListRegistrarResponseModel
        ).to_openapi_response(),
    )
    @api_view(['GET'])
    @permission_classes([IsAdminUser, IsEmailVerified])
    def get_list_registrar(request):
        query = (
            User.objects.filter(
                user_type=UserType.ENGINEER.value,
                deleted=0,
                created_user__isnull=False
            )
            .values_list('created_user', flat=True)
            .distinct()
        )

        # Convert to list to make the response serializable
        created_user_list = list(query)

        return CustomResponse(
            data=created_user_list,
            message=_("Success"),
            status=status.HTTP_200_OK,
        )

    @swagger_auto_schema(
        method='DELETE',
        operation_description='Delete engineers',
        operation_id='Delete Engineers',
        request_body=DeleteEngineersSerializer,
        responses=BaseResponse(
            status_code=status.HTTP_200_OK,
            message=_('Success'),
        ).to_openapi_response(),
    )
    @api_view(['DELETE'])
    @permission_classes([IsAdminUser, IsEmailVerified])
    def delete_engineers(request):
        serializer = DeleteEngineersSerializer(data=request.data)
        validate_serializer(serializer)

        engineer_ids = serializer.validated_data['engineer_ids']
        User.objects.filter(user_id__in=engineer_ids,
                            user_type=UserType.ENGINEER.value,
                            ).update(deleted=1)

        return CustomResponse(
            message=_("Success"),
            status=status.HTTP_200_OK,
        )

    # Create a new email schedule
    @swagger_auto_schema(
        method='post',
        operation_description='Create a new email schedule',
        operation_id='Create Email Schedule',
        responses=BaseResponse(
            status_code=status.HTTP_201_CREATED,
            message=_('Email schedule created successfully'),
            responseModel=EmailScheduleSerializer
        ).to_openapi_response(),
        request_body=CreateEmailScheduleSerializer
    )
    @api_view(['POST'])
    @permission_classes([IsAdminUser, IsEmailVerified])
    @log_exceptions
    def create_email_schedule(request):

        serializer = CreateEmailScheduleSerializer(data=request.data)
        validate_serializer(serializer)
        target_email_email = serializer.validated_data.get('target_email')
        if target_email_email:
            target_email = User.objects.filter(
                email=target_email_email).first()
            if not target_email:
                return CustomResponse(
                    message=_('Target email not found'),
                    status=status.HTTP_400_BAD_REQUEST
                )
            email_sender = mail_sender_service.EmailSender()
            if serializer.validated_data.get('type') == EmailScheduleType.ADMIN_NOTIFY.value:

                email_sender.send_email(
                    receiver_email=target_email.email,
                    subject=f"{serializer.validated_data.get('subject')}",
                    body=f"{serializer.validated_data.get('body')}"
                )
            else:
                receiver_email = target_email.email
                print("email to send", receiver_email)
                user_id = target_email.user_id
                key = os.getenv('CRYPTOGRAPHY_KEY')
                f = Fernet(key)
                content = f"user_id:{user_id}".encode('utf-8')
                encrypt_data = f.encrypt(content)
                encrypted_code = encrypt_data.decode('utf-8')
                link = f"https://asiantech.link/update-data-policy?code={
                    encrypted_code}"
                email_sender.send_email(
                    receiver_email=receiver_email,
                    subject=f"{serializer.validated_data.get('subject')}",
                    body=f"""
                <!DOCTYPE html>
                    <html lang="en">
                    <head>
                        <meta charset="UTF-8">
                        <meta name="viewport" content="width=device-width, initial-scale=1.0">
                        <title>{serializer.validated_data.get('subject')}</title>
                    </head>
                    <body style="font-family: Arial, sans-serif; background-color: #ffffff; margin: 0; padding: 0; line-height: 1.6;">
                        <div style="max-width: 100%; margin: 20px auto; background: #ffffff; border: 0px solid #ffffff; border-radius: 0px; overflow: hidden;">
                            <!-- Content -->
                            <div style="padding: 0px; color: #333;">
                                <div style="background-color: #ffffff; border: 0px solid #ddd; border-radius: 5px; padding: 15px; margin-bottom: 10px; font-family: monospace; overflow: auto;">
                                    {serializer.validated_data.get('body')}
                                </div>
                            </div>

                            <!-- Button -->
                            <div style="text-align: center; margin-top: 10px; margin-bottom: 10px">
                                <a href="{link}" style="display: inline-block; background-color: #28a745; color: white; text-decoration: none; padding: 10px 20px; font-size: 1em; border-radius: 5px; cursor: pointer;">Review</a>
                            </div>
                        </div>
                    </body>
                    </html>
                    """
                )
                return CustomResponse(
                    data=serializer.data,
                    message=_("Send test email successfully"),
                    status=status.HTTP_201_CREATED,
                )
        else:
            # Check request type is 1 and have this EmailSchedules in DB already will show error
            if serializer.validated_data.get('type') == 2:
                if EmailSchedules.objects.filter(type=2).exists():
                    return CustomResponse(
                        message=_(
                            'Email personal information already exists'),
                        status=status.HTTP_400_BAD_REQUEST
                    )

            serializer.save()

        return CustomResponse(
            data=serializer.data,
            message=_("Email schedule created successfully"),
            status=status.HTTP_201_CREATED,
        )

    # Update an existing email schedule
    @swagger_auto_schema(
        method='put',
        operation_description='Update an existing email schedule',
        operation_id='Update Email Schedule',
        responses=BaseResponse(
            status_code=status.HTTP_200_OK,
            message=_('Email schedule updated successfully'),
            responseModel=EmailScheduleSerializer
        ).to_openapi_response(),
        request_body=CreateEmailScheduleSerializer
    )
    @api_view(['PUT'])
    @permission_classes([IsAdminUser, IsEmailVerified])
    @log_exceptions
    def update_email_schedule(request, pk):
        try:
            email_schedule = EmailSchedules.objects.get(pk=pk)
        except EmailSchedules.DoesNotExist:
            return CustomResponse(message=_('Email schedule not found'), status=status.HTTP_404_NOT_FOUND)
        serializer = CreateEmailScheduleSerializer(
            email_schedule, data=request.data, partial=True)
        validate_serializer(serializer)
        target_email_email = serializer.validated_data.get('target_email')
        if target_email_email:
            target_email = User.objects.filter(
                email=target_email_email).first()
            if not target_email:
                return CustomResponse(
                    message=_('Target email not found'),
                    status=status.HTTP_400_BAD_REQUEST
                )
            email_sender = mail_sender_service.EmailSender()
            if serializer.validated_data.get('type') == EmailScheduleType.ADMIN_NOTIFY.value:

                email_sender.send_email(
                    receiver_email=target_email.email,
                    subject=f"{serializer.validated_data.get('subject')}",
                    body=f"{serializer.validated_data.get('body')}"
                )
            else:
                receiver_email = target_email.email
                print("email to send", receiver_email)
                user_id = target_email.user_id
                key = os.getenv('CRYPTOGRAPHY_KEY')
                f = Fernet(key)
                content = f"user_id:{user_id}".encode('utf-8')
                encrypt_data = f.encrypt(content)
                encrypted_code = encrypt_data.decode('utf-8')
                link = f"https://asiantech.link/update-data-policy?code={
                    encrypted_code}"
                email_sender.send_email(
                    receiver_email=receiver_email,
                    subject=f"{serializer.validated_data.get('subject')}",
                    body=f"""
                <!DOCTYPE html>
                    <html lang="en">
                    <head>
                        <meta charset="UTF-8">
                        <meta name="viewport" content="width=device-width, initial-scale=1.0">
                        <title>{serializer.validated_data.get('subject')}</title>
                    </head>
                    <body style="font-family: Arial, sans-serif; background-color: #ffffff; margin: 0; padding: 0; line-height: 1.6;">
                        <div style="max-width: 100%; margin: 20px auto; background: #ffffff; border: 0px solid #ffffff; border-radius: 0px; overflow: hidden;">
                            <!-- Content -->
                            <div style="padding: 0px; color: #333;">
                                <div style="background-color: #ffffff; border: 0px solid #ddd; border-radius: 5px; padding: 15px; margin-bottom: 10px; font-family: monospace; overflow: auto;">
                                    {serializer.validated_data.get('body')}
                                </div>
                            </div>

                            <!-- Button -->
                            <div style="text-align: center; margin-top: 10px; margin-bottom: 10px">
                                <a href="{link}" style="display: inline-block; background-color: #28a745; color: white; text-decoration: none; padding: 10px 20px; font-size: 1em; border-radius: 5px; cursor: pointer;">Review</a>
                            </div>
                        </div>
                    </body>
                    </html>
                    """
                )
                return CustomResponse(
                    data=serializer.data,
                    message=_("Send test email successfully"),
                    status=status.HTTP_201_CREATED,
                )
        else:
            # Check request type is 1 and have this EmailSchedules in DB already will show error
            if serializer.validated_data.get('type') == 2:
                if EmailSchedules.objects.filter(type=2).exclude(pk=pk).exists():
                    return CustomResponse(
                        message=_('Email personal information already exists'),
                        status=status.HTTP_400_BAD_REQUEST
                    )

            serializer.save()

        return CustomResponse(
            data=serializer.data,
            message=_("Email schedule updated successfully"),
            status=status.HTTP_200_OK,
        )

    # Delete an existing email schedule
    @swagger_auto_schema(
        method='delete',
        operation_description='Delete an existing email schedule',
        operation_id='Delete Email Schedule',
        responses=BaseResponse(
            status_code=status.HTTP_204_NO_CONTENT,
            message=_('Email schedule deleted successfully')
        ).to_openapi_response()
    )
    @api_view(['DELETE'])
    @permission_classes([IsAdminUser, IsEmailVerified])
    @log_exceptions
    def delete_email_schedule(request, pk):
        try:
            email_schedule = EmailSchedules.objects.get(pk=pk)
        except EmailSchedules.DoesNotExist:
            return CustomResponse(message=_('Email schedule not found'), status=status.HTTP_404_NOT_FOUND)

        email_schedule.delete()

        return CustomResponse(
            message=_("Email schedule deleted successfully"),
            status=status.HTTP_204_NO_CONTENT,
        )

    # Delete a list of email schedules by list id
    @swagger_auto_schema(
        method='delete',
        operation_description='Delete a list of email schedules by list id',
        operation_id='delete_email_schedule_list',
        query_serializer=DeleteEmailScheduleListParamsSerializer,
        responses=BaseResponse(
            status_code=status.HTTP_204_NO_CONTENT,
            message=_('Email schedule list deleted successfully')
        ).to_openapi_response()
    )
    @api_view(['DELETE'])
    @permission_classes([IsAdminUser, IsEmailVerified])
    @log_exceptions
    def delete_email_schedule_list(request):
        # Create a mutable copy of the query parameters
        query_params = request.query_params.copy()
        # Convert the comma-separated string to a list of integers
        if 'email_schedule_ids' in query_params:
            query_params.setlist('email_schedule_ids', [int(
                x) for x in query_params.get('email_schedule_ids').split(',')])

        serializer = DeleteEmailScheduleListParamsSerializer(data=query_params)
        if not serializer.is_valid():
            return CustomResponse(message=_('Invalid data'), data=serializer.errors, status=status.HTTP_400_BAD_REQUEST)

        email_schedule_ids = serializer.validated_data.get(
            'email_schedule_ids', [])
        if not email_schedule_ids:
            return CustomResponse(message=_('No email schedule ids provided'), status=status.HTTP_400_BAD_REQUEST)

        EmailSchedules.objects.filter(pk__in=email_schedule_ids).delete()

        return CustomResponse(
            message=_("Email schedule list deleted successfully"),
            status=status.HTTP_204_NO_CONTENT,
        )

    # Get email schedules by id
    @swagger_auto_schema(
        method='get',
        operation_description='Get email schedules by id',
        operation_id='get_email_schedule_by_id',
        responses=BaseResponse(
            status_code=status.HTTP_200_OK,
            message=_('Email schedule details'),
            responseModel=GetEmailDetailResponseModel
        ).to_openapi_response()
    )
    @api_view(['GET'])
    @permission_classes([IsAdminUser, IsEmailVerified])
    @log_exceptions
    def get_email_schedule_by_id(request, pk):
        try:
            email_schedule = EmailSchedules.objects.get(pk=pk)
        except EmailSchedules.DoesNotExist:
            return CustomResponse(message=_('Email schedule not found'), status=status.HTTP_404_NOT_FOUND)
        print(email_schedule)
        serializer = EmailScheduleSerializer(email_schedule)

        return CustomResponse(
            data=serializer.data,
            message=_("Success"),
            status=status.HTTP_200_OK,
        )

    @swagger_auto_schema(
        method='put',
        operation_description='Update engineer',
        operation_id='update_engineer',
        request_body=UpdateEngineerSerializer,
        query_serializer=UpdateEngineerParamsSerializer,
        responses=BaseResponse(status_code=status.HTTP_200_OK, message=_(
            'Success')).to_openapi_response(),
    )
    @api_view(['PUT'])
    @permission_classes([IsAdminUser, IsEmailVerified])
    def update_engineer(request):
        try:
            params = request.query_params
            engineer = User.objects.get(user_id=params.get('user_id'))
            serializer = UpdateEngineerSerializer(
                engineer, data=request.data, partial=True)
            validate_serializer(serializer)
            serializer.save()
            return CustomResponse(message=_('Success'), status=status.HTTP_200_OK)
        except User.DoesNotExist:
            return CustomResponse(message=_('Engineer not found'), status=status.HTTP_404_NOT_FOUND)

    def generate_cv_docx(cv_template, engineer_id):
        file_name = f"cv_engineer_{engineer_id}.docx"
        document = Document()
        new_parser = HtmlToDocx()
        new_parser.table_style = 'TableGrid'
        new_parser.include_tables = True
        new_parser.add_html_to_document(cv_template, document)

        # ==== Set Styles ====
        def set_style(document, style_name, font_name='Times New Roman', font_size=12, bold=False):
            style = document.styles[style_name]
            font = style.font
            font.name = font_name
            font.size = Pt(font_size)
            font.bold = bold
            font.color.rgb = RGBColor(0, 0, 0)

            # For East Asian compatibility
            rFonts = style.element.rPr.rFonts
            rFonts.set(qn('w:eastAsia'), font_name)

        def convert_heading_to_normal(paragraph):
            # Set paragraph style to 'Normal'
            paragraph.style = 'Normal'

            # Remove outline level (so it doesn't appear in nav pane)
            pPr = paragraph._element.get_or_add_pPr()
            outline = pPr.find(qn('w:outlineLvl'))
            if outline is not None:
                pPr.remove(outline)

            # Format all runs in the paragraph
            for run in paragraph.runs:
                run.font.name = 'Times New Roman'
                run.font.size = Pt(14)
                run.font.bold = True
                run.font.underline = WD_UNDERLINE.SINGLE

        set_style(document, 'Normal', font_size=12)
        for para in document.paragraphs:
            if para.style.name in ['Heading 1', 'Heading 2', 'Heading 3']:
                convert_heading_to_normal(para)

        # Paragraph spacing
        for para in document.paragraphs:
            para.paragraph_format.space_before = Pt(6)
            para.paragraph_format.space_after = Pt(6)
            # line spacing = 1.5
            para.paragraph_format.line_spacing = 1.5
            for run in para.runs:
                run.font.name = 'Times New Roman'
                run._element.rPr.rFonts.set(
                    qn('w:eastAsia'), 'Times New Roman')
                run.font.color.rgb = RGBColor(0, 0, 0)

        # ==== Page and Section Setup ====
        content_width_inch = 0
        left_margin = 0
        right_margin = 0
        sesion_page_width = 0
        section = document.sections[0]
        left_margin = section.left_margin
        right_margin = section.right_margin
        sesion_page_width = section.page_width
        section.top_margin = Pt(50)
        content_width = sesion_page_width - left_margin - right_margin

        content_width_inch = content_width / 914400
        content_width_inch = content_width_inch
        # ==== Table Formatting ====
        for table in document.tables:
            table.width = Inches(content_width_inch)
            table.columns[0].width = Inches(content_width_inch * 0.25)
            table.columns[1].width = Inches(content_width_inch * 0.75)

            table.autofit = False
            for row in table.rows:
                row.height = Inches(0.283)
                for cell in row.cells:
                    cell.vertical_alignment = WD_ALIGN_VERTICAL.CENTER
                    for para in cell.paragraphs:
                        para.alignment = WD_PARAGRAPH_ALIGNMENT.LEFT
                        para.paragraph_format.space_before = Pt(0)
                        para.paragraph_format.space_after = Pt(0)
                        for run in para.runs:
                            run.font.name = 'Times New Roman'
                            run.font.size = Pt(12)
                            run.font.bold = False

                # Set widths for first two cells only
                if len(row.cells) >= 2:
                    row.cells[0].width = Inches(content_width_inch * 0.25)
                    row.cells[1].width = Inches(content_width_inch * 0.75)

        # ==== Personal Details Special Styling ====
        personal_details_table = document.tables[0]
        header_row = personal_details_table.rows[0]
        merged_cell = header_row.cells[0].merge(header_row.cells[1])
        merged_cell.text = "PERSONAL DETAILS"
        para = merged_cell.paragraphs[0]
        para.alignment = WD_PARAGRAPH_ALIGNMENT.LEFT
        if para.runs:
            para.runs[0].font.bold = True

        # Special styling for full name
        if len(personal_details_table.rows) > 1 and len(personal_details_table.rows[1].cells) > 1:
            full_name_cell = personal_details_table.rows[1].cells[1]
            for para in full_name_cell.paragraphs:
                para.alignment = WD_PARAGRAPH_ALIGNMENT.LEFT
                for run in para.runs:
                    run.font.name = 'Times New Roman'
                    run.font.size = Pt(14)
                    run.font.bold = True

        # Highlight project table from 1 to remain
        for i in range(1, len(document.tables)):
            highlight_project_table = document.tables[i]

            highlight_project_table.autofit = False

            for row in highlight_project_table.rows:

                for cell in row.cells:
                    cell.vertical_alignment = WD_ALIGN_VERTICAL.CENTER
                    for para in cell.paragraphs:
                        para.alignment = WD_PARAGRAPH_ALIGNMENT.LEFT
                        para.paragraph_format.line_spacing = 1.5
                        para.paragraph_format.space_before = Pt(0)
                        para.paragraph_format.space_after = Pt(0)

                        for run in para.runs:
                            run.font.name = 'Times New Roman'
                            run._element.rPr.rFonts.set(
                                qn('w:eastAsia'), 'Times New Roman')
                            run.font.size = Pt(12)
                            run.font.bold = False

        # ==== return as bytes
        docx_io = BytesIO()
        document.save(docx_io)
        docx_io.seek(0)  # rewind to beginning
        docx_bytes = docx_io.getvalue()
        return docx_bytes

    def format_job_description(description):
        return description.replace('\n', '<br>')

    def export_engineer_to_cv(engineer_id, request):
        engineer = User.objects.get(
            user_id=engineer_id, user_type=UserType.ENGINEER.value, deleted=0)
        engineer_serializer = UserDetailsSerializers(engineer, context={
            'request': request
        })
        # read cv template
        with open(settings.CV_TEMPLATE_PATH, 'r') as file:
            cv_template = file.read()
            # full name
            first_name = engineer_serializer.data.get('first_name', "")
            last_name = engineer_serializer.data.get('last_name', "")
            full_name = f"{first_name} {last_name}"
            full_name = full_name.upper()
            cv_template = cv_template.replace('{engineerName}', full_name)

            # dob
            dob = engineer_serializer.data.get('birth_date')
            if dob is not None and dob != "":
                dob = parse_date_safe(dob, "%d/%m/%Y")
            else:
                dob = ""
            cv_template = cv_template.replace('{dateOfBirthValue}', dob)

            # gender
            sex_type = engineer_serializer.data.get('sex_type')
            sex_type_name = get_sex_type_name(sex_type)
            cv_template = cv_template.replace(
                '{genderValue}', sex_type_name)

            # address
            address_code = engineer_serializer.data.get('address_code')
            address_name = ""
            if address_code is not None:
                city_name = get_city_name(address_code, 'en')
                country_name = get_country_name(
                    address_code.split('-')[0], 'en')
                address_name = f"{city_name}, {country_name}"
            cv_template = cv_template.replace(
                '{addressValue}', address_name)

            # professional summary
            professional_summary = engineer_serializer.data.get(
                'professional_summary')
            professional_summary_html_tag = ""
            if professional_summary is not None and professional_summary != "":
                professional_summary_html_tag = f"""
                        <p class="c2 c11"><span class="c0">{professional_summary}</span></p>"""
            if professional_summary_html_tag != "":
                cv_template += f"""
                
                <h2>        
                    <u>PROFESSIONAL SUMMARY</u>
                </h2>
                {professional_summary_html_tag}
                """

            # education
            education_html_tag = ""
            educations = engineer_serializer.data.get('educations')
            # sort by type
            educations = sorted(educations, key=lambda x: x.get('type'))
            for education in educations:
                type_name = get_academic_type_name(
                    education.get('type'), "en")
                school_name = education.get('school')
                out_date = education.get('out_date')
                faculty = education.get('faculty')
                education_title = ""
                # sample: Degree of Software Engineering - Hanoi University of Industry Class of 2017
                if faculty is not None and faculty != "":
                    education_title = f"Degree of {faculty} - {school_name} {type_name}"
                elif school_name is not None and school_name != "":
                    education_title = f"{school_name} {type_name}"
                if out_date is not None and out_date != "":
                    # format yyyy
                    # convert string to date
                    out_date = parse_date_safe(out_date, "%Y")
                    education_title += f" of {out_date}"
                education_html_tag += f"""
                    <p class="c2 c11"><span class="c0">&#9679; {education_title}</span></p>
                    """

            if education_html_tag != "":
                cv_template += f"""

                <h2>        
                    <u>EDUCATION </u>
                </h2>
                {education_html_tag}
                """

            # technical skills

            skill_cv_for_display = engineer_serializer.data.get(
                'skills_for_cv_display', None)
            if skill_cv_for_display != None and skill_cv_for_display != "":

                skill_cv_for_display = skill_cv_for_display.replace(
                    "\n\n", "\n")
                lines = skill_cv_for_display.split('\n')
                formatted_lines = []

                for line in lines:
                    if ':' in line:
                        category, items = line.split(':', 1)
                        formatted_line = f"<strong>{category.strip()}:</strong>{items}"
                    else:
                        formatted_line = line
                    formatted_lines.append(formatted_line)

                skill_html = "" + "<br>&#9679; ".join(formatted_lines)

                category_skill_html_tag = f"""
                <p class="c2 c11"><span class="c0">&#9679; {skill_html}</span></p>
                """

                cv_template += f"""

                <h2>        
                    <u>SKILLS </u>
                </h2>
                {category_skill_html_tag}
                """

            # professional experience
            professional_experience_html_tag = ""
            experiences = engineer_serializer.data.get('experiences', [])
            for experience in experiences:
                entering_date_raw = experience.get('entering_date')
                quitting_date_raw = experience.get('quitting_date')
                role_name = experience.get('role_name') or ""
                job_description = experience.get(
                    'job_description') or ""
                job_description = AdminViews.format_job_description(
                    job_description)

                # Format dates to MMMM-yyyy
                entering_date = parse_date_safe(
                    entering_date_raw, "%B %Y")
                quitting_date = parse_date_safe(
                    quitting_date_raw, "%B %Y")

                if entering_date is None:
                    entering_str = ""
                else:
                    entering_str = entering_date

                if quitting_date is None:
                    quitting_str = "Now"
                else:
                    quitting_str = quitting_date
                time_duration = ""
                if entering_str == "" and quitting_str == "Now":
                    time_duration = "Present"
                else:
                    time_duration = f"{entering_str} - {quitting_str}"
                    professional_experience_html_tag += f"""
                <p style="margin-bottom: 6pt; font-size: 12pt; font-family: 'Times New Roman';">
                    <b>{time_duration}</b>
                </p>
                <p style="margin: 6pt 0; font-size: 12pt; font-family: 'Times New Roman';">
                    <b>Role: {role_name}</b>
                </p>
                <p style="margin: 6pt 0; font-size: 12pt; font-family: 'Times New Roman';">
                    <b>Responsibilities:</b>
                </p>
                <p style="margin: 6pt 0;margin-left: 20pt; font-size: 12pt; font-family: 'Times New Roman'; padding-left: 20pt; white-space: pre-wrap;">
                    {job_description}
                </p>
            """
            if professional_experience_html_tag != "":
                cv_template += f"""
                
                <h2>        
                    <u>PROFESSIONAL EXPERIENCE</u>
                </h2>
                {professional_experience_html_tag}

                """

            highlight_project_html_tag = ""
            highlight_projects = engineer_serializer.data.get(
                'highlight_projects', [])
            for highlight_project in highlight_projects:
                project_index = highlight_projects.index(highlight_project) + 1
                project_name = highlight_project.get('name') or ""
                project_description = highlight_project.get(
                    'description', None) or ""
                project_description = AdminViews.format_job_description(
                    project_description) or ""
                project_size = highlight_project.get('size') or ""
                project_role_name = highlight_project.get('role_name') or ""
                project_responsibilities = highlight_project.get(
                    'responsibilities', None) or ""
                project_responsibilities = AdminViews.format_job_description(
                    project_responsibilities) or ""
                project_technology_used = highlight_project.get(
                    'technology_used') or ""
                project_from_date = highlight_project.get('from_date')
                project_to_date = highlight_project.get('to_date')
                date_project = ""
                try:
                    project_from_date = parse_date_safe(
                        project_from_date, "%m/%Y")
                except:
                    project_from_date = project_from_date
                try:
                    project_to_date = parse_date_safe(
                        project_to_date, "%m/%Y")
                except:
                    project_to_date = project_to_date
                list_date_range = [project_from_date, project_to_date]
                list_date_range = [
                    date for date in list_date_range if date is not None]
                # join date range with '-'
                if len(list_date_range) > 0:
                    date_project = ' - '.join(list_date_range)
                else:
                    date_project = ""

                project_header = f"""<p class="c2 c11"><span class="c0"><strong>Project {project_index}: {project_name}</strong></span></p>"""

                project_table = f"""
                    <table class="highlight-project-table">
                        <tbody>
                            <tr>
                                <th>Project Description</th>
                                <td>{project_description}</td>
                            </tr>
                            <tr>
                                <th>Team Size</th>
                                <td>{project_size}</td>
                            </tr>
                            <tr>
                                <th>Position</th>
                                <td>{project_role_name}</td>
                            </tr>
                            <tr>
                                <th>Responsibilities</th>
                                <td>{project_responsibilities}</td>
                            </tr>
                            <tr>
                                <th>Technology Used</th>
                                <td>{project_technology_used}</td>
                            </tr>
                        </tbody>
                    </table>
                """

                if date_project:
                    date_html = f"""<p class="c2 c11"><span class="c0">{date_project}</span></p>"""
                    highlight_project_html_tag += f"{date_html}\n{project_header}\n{project_table}"
                else:
                    highlight_project_html_tag += f"{project_header}\n{project_table}"
            if highlight_project_html_tag != "":
                cv_template += f"""

                <h2>        
                    <u>HIGHLIGHT PROJECTS</u>
                </h2>
                {highlight_project_html_tag}
                """
            return cv_template

    @swagger_auto_schema(
        method='post',
        operation_description='Export user data',
        operation_id='export_user_data',
        responses={
            status.HTTP_200_OK: openapi.Schema(
                type=openapi.TYPE_FILE,
                format=openapi.FORMAT_BINARY
            ),
        },
        request_body=ExportUserDataBodySerializer
    )
    @api_view(['POST'])
    @permission_classes([IsAdminUser, IsEmailVerified])
    def export_user_data(request):
        try:
            translation.activate('en')

            serializer = ExportUserDataBodySerializer(data=request.data)
            validate_serializer(serializer)
            engineer_ids = serializer.validated_data.get('engineer_ids')
            export_type = serializer.validated_data.get('export_type')

            in_memory_zip = io.BytesIO()

            with zipfile.ZipFile(in_memory_zip, 'w', zipfile.ZIP_DEFLATED) as zip_file:
                for idx, engineer_id in enumerate(engineer_ids, start=1):
                    cv_template = AdminViews.export_engineer_to_cv(
                        engineer_id, request)
                    docx_bytes = AdminViews.generate_cv_docx(
                        cv_template, engineer_id)

                    engineer = User.objects.get(user_id=engineer_id)
                    full_name = f"{engineer.first_name or ''}_{engineer.last_name or ''}".strip(
                    ).replace(" ", "_")
                    filename_base = f"{full_name}_{idx}"

                    if export_type.lower() == "pdf":
                        pass
                    else:
                        zip_file.writestr(f"{filename_base}.docx", docx_bytes)

            in_memory_zip.seek(0)
            now = datetime.now().strftime("%Y%m%d%H%M%S")
            zip_filename = f"cv_engineer_{now}.zip"
            response = HttpResponse(
                in_memory_zip.getvalue(), content_type='application/zip')
            response['Content-Disposition'] = f'attachment; filename="{zip_filename}"'
            return response

        except Exception as e:
            return CustomResponse(message=f"Error exporting user data: {str(e)}", status=status.HTTP_400_BAD_REQUEST)
