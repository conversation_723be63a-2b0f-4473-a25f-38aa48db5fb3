from api.serializers.engineers.user_serializers import GetUserProfileParamsSerializer
from rest_framework.permissions import IsAuthenticated
from django.db.models import Count
from django.forms import IntegerField
from api.models.rec import *
from django.db.models import OuterRef, Exists, <PERSON><PERSON><PERSON><PERSON>ield, Value, Case, When
from api.models.chat import *
from datetime import timezone, datetime

from django.contrib.auth.models import User
from rest_framework.views import APIView
from django.contrib.auth import get_user_model
from rest_framework.decorators import api_view
from rest_framework import status
from api.serializers.engineers.engineer_serializers import *
from utils.custom_pagination import CustomCursorPagination
from utils.responses import BaseResponse, CustomResponse
from rest_framework.decorators import api_view, permission_classes
from drf_yasg.utils import swagger_auto_schema
from django.utils.translation import gettext as _
from utils.permissions import *
from django.db.models import Q
from django.db.models import F, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>ield, Value, Case, When
from api.models.rec import *
from itertools import chain
from django.db.models import IntegerField, BooleanField, DateTimeField, Value
from datetime import datetime
import pytz
from django.utils import timezone as tz
from django.db.models.functions import Coalesce
from api.models.rec_waiting_company import RecWaitingCompany
from cryptography.fernet import Fernet
User = get_user_model()


class EngineerViews(APIView):
    # List agency company
    @swagger_auto_schema(
        method='get',
        operation_id='get_list_agency_company',
        query_serializer=EngineerParamListAgencyCompany,
        responses=BaseResponse(status_code=status.HTTP_200_OK, message=_(
            'Success'), responseModel=EngineerListAgencyCompanyResponseModel).to_openapi_response()
    )
    @api_view(['GET'])
    @permission_classes([IsEngineer, IsEmailVerified])
    def get_list_agency_company(request):
        contact_mail = request.query_params.get('contact_mail')
        company_queryset = ComCompany.objects.filter(
            user_type=UserType.REFERRAL_AGENCY_STAFF.value)

        if contact_mail:
            company_queryset = company_queryset.filter(
                contact_mail=contact_mail)

        serializer = EngineerListAgencyCompanySerializer(
            company_queryset, many=True)
        return CustomResponse(data=serializer.data, status=status.HTTP_200_OK, message=_("Success"))

    # Create selected agency company
    @swagger_auto_schema(
        method='post',
        operation_id='add_agency_company',
        request_body=EngineerUpdateAgencyCompanySerializer,
        responses=BaseResponse(status_code=status.HTTP_200_OK, message=_(
            'Success')).to_openapi_response()
    )
    @api_view(['POST'])
    @permission_classes([IsEngineer, IsEmailVerified])
    def add_agency_company(request):
        user = request.user
        contact_mail = request.data.get('contact_mail')

        optional_engineer_id = request.data.get(
            'optional_engineer_id', None)
        if user.user_type == UserType.MANAGEMENT_ACCOUNT.value and optional_engineer_id is not None:
            user = User.objects.get(pk=optional_engineer_id)

        try:
            company_id = User.objects.filter(
                email=contact_mail, user_type=UserType.REFERRAL_AGENCY_STAFF.value).first().company_id
            if user:
                company = ComCompany.objects.filter(
                    company_id=company_id).first()

        except:
            return CustomResponse(message=_('Agency company not found.'), status=status.HTTP_404_NOT_FOUND)

        existing_link = MapEngAgc.objects.filter(
            engineer=user, agency_company=company).first()

        if existing_link:
            if existing_link.deleted == 0:
                return CustomResponse(message=_('This agency company is already linked with the engineer.'), status=status.HTTP_400_BAD_REQUEST)
            else:
                existing_link.deleted = 0
                existing_link.create_datetime = tz.now()
                existing_link.save()
        else:
            MapEngAgc.objects.create(
                engineer_id=user.user_id,
                agency_company_id=company.company_id,
                create_user_id=user.user_id,
                deleted=0,
                create_datetime=tz.now())

        return CustomResponse(status=status.HTTP_200_OK, message=_("Success"))

    # List of selected agency companies
    @swagger_auto_schema(
        method='get',
        operation_id='get_list_of_select_agency_companies',
        query_serializer=GetUserProfileParamsSerializer,
        responses=BaseResponse(status_code=status.HTTP_200_OK, message=_(
            'Success'), responseModel=EngineerListAgencyCompanyResponseModel).to_openapi_response()
    )
    @api_view(['GET'])
    @permission_classes([IsAuthenticated, IsEmailVerified])
    def get_list_of_select_agency_companies(request):
        user = request.user

        optional_engineer_id = request.query_params.get(
            'optional_engineer_id', None)
        if user.user_type == UserType.MANAGEMENT_ACCOUNT.value and optional_engineer_id is not None:
            user = User.objects.get(pk=optional_engineer_id)

        company_queryset = ComCompany.objects.filter(
            company_id__in=MapEngAgc.objects.filter(engineer_id=user.user_id, deleted=0).values_list('agency_company_id', flat=True))

        serializer = EngineerListAgencyCompanySerializer(
            company_queryset, many=True)
        return CustomResponse(data=serializer.data, status=status.HTTP_200_OK, message=_("Success"))

    # Delete selected agency company
    @swagger_auto_schema(
        method='delete',
        operation_id='delete-agency-company',
        request_body=EngineerRemoveAgencyCompanySerializer,
        responses=BaseResponse(status_code=status.HTTP_200_OK,
                               message=_('Success')).to_openapi_response()
    )
    @api_view(['DELETE'])
    @permission_classes([IsAuthenticated, IsEmailVerified])
    def delete_agency_company(request):
        user = request.user
        serializer = EngineerRemoveAgencyCompanySerializer(data=request.data)

        optional_engineer_id = request.data.get(
            'optional_engineer_id', None)
        if user.user_type == UserType.MANAGEMENT_ACCOUNT.value and optional_engineer_id is not None:
            user = User.objects.get(pk=optional_engineer_id)

        if serializer.is_valid():
            company_id = serializer.validated_data['company_id']

            try:
                company = ComCompany.objects.get(
                    company_id=company_id, user_type=UserType.REFERRAL_AGENCY_STAFF.value)
            except ComCompany.DoesNotExist:
                return CustomResponse(message=_('Agency company not found.'), status=status.HTTP_404_NOT_FOUND)

            try:
                # Fetch the MapEngAgc instance
                map_eng_agc = MapEngAgc.objects.get(
                    engineer=user, agency_company=company)

                # Check if it is already deleted
                if map_eng_agc.deleted == 1:
                    return CustomResponse(message=_('This agency company has already been deleted.'), status=status.HTTP_409_CONFLICT)

                # Update the record
                map_eng_agc.deleted = 1
                map_eng_agc.update_datetime = tz.now()
                map_eng_agc.save()  # Save changes

                return CustomResponse(message=_('Agency company successfully deleted.'), status=status.HTTP_200_OK)

            except MapEngAgc.DoesNotExist:
                return CustomResponse(message=_('This agency company is not linked with the engineer.'), status=status.HTTP_400_BAD_REQUEST)

        return CustomResponse(errors=serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    @swagger_auto_schema(
        method="GET",
        operation_id="get-best-companies",
        query_serializer=EngineerGetListBestCompanyParamsSerializer,
        responses=BaseResponse(
            status_code=status.HTTP_200_OK,
            message=_('Success'),
            responseModel=EngineerGetListBestCompaniesResponseModel
        ).to_openapi_response()
    )
    @api_view(['GET'])
    def get_best_companies(request):
        paginator = CustomCursorPagination()
        # Query to annotate companies with the number of related recruits and order by the count
        companies = ComCompany.objects.annotate(
            # Count related RecRecruit objects
            num_recruits=Count('recrecruit')
            # Sort by the number of recruits in descending order
        ).filter(num_recruits__gt=0).order_by('num_recruits')
        paginated_recruits = paginator.paginate_queryset(companies, request)
        # Serialize the paginated results
        serializer = EngineerBestCompanySerializer(
            paginated_recruits, many=True)

        # Return paginated response
        return CustomResponse(data=paginator.get_paginated_response(serializer.data), status=status.HTTP_200_OK, message=_("Success"))

    # List apply company
    @swagger_auto_schema(
        method='get',
        operation_id='get_list_apply_company',
        query_serializer=EngineerParamListApplyCompanySerializer,
        responses=BaseResponse(status_code=status.HTTP_200_OK, message=_(
            'Success'), responseModel=EngineerListApplyCompanyResponseModel).to_openapi_response()
    )
    @permission_classes([IsEngineer, IsEmailVerified])
    @api_view(['GET'])
    def get_list_apply_company(request):
        user = request.user
        apply_status_filter_code = request.query_params.get(
            'apply_status_filter_code')
        paginator = CustomCursorPagination()
        now = timezone.now()
        ordering = request.query_params.get('ordering')

        valid_ordering_fields = [
            'progress_update_datetime', '-progress_update_datetime']
        if ordering and ordering not in valid_ordering_fields:
            return CustomResponse(data=[], status=status.HTTP_200_OK, message=_("Invalid ordering field."))

        if apply_status_filter_code is None:
            apply_status_filter_code = ApplyStatusFilterCode.ALL.value
        else:
            try:
                apply_status_filter_code = int(apply_status_filter_code)
            except ValueError:
                return CustomResponse(errors=[{'message': _('Invalid recruit_progress_code')}], status=status.HTTP_400_BAD_REQUEST)
         # Convert to FakeQuerySet

        class FakeQuerySet:
            def __init__(self, data):
                self.data = data

            def __iter__(self):
                return iter(self.data)

            def count(self):
                return len(self.data)

            def order_by(self, *args, **kwargs):
                return self.data

        company_queryset = RecApply.objects.filter(engineer_id=user.user_id).exclude(
            recruit_progress_code=RecruitProgressCode.REQUESTING_AN_AGENT.value
        ).select_related('recruit', 'host_company').only(
            'apply_id', 'engineer_id', 'recruit_id', 'host_company_id', 'recruit_progress_code',
            'recruit__end_date', 'expiry_date', 'recruit__waiting_company_set__waiting_flag',
            'recruit__payroll_price_from', 'recruit__payroll_price_to', 'recruit__payroll_code'
        ).annotate(
            # Case: apply company is closed
            is_closed=Case(
                When(
                    Exists(
                        RecWaitingCompany.objects.filter(
                            recruit_id=OuterRef('recruit_id'),
                            engineer_id=user.user_id,
                            waiting_flag=WaitingFlag.UNDER_CONSIDERATION.value,
                            recruit__end_date__lte=now,
                        )
                    ) |
                    (Q(recruit_progress_code__lte=RecruitProgressCode.APPLICATION.value) &
                     Q(recruit__end_date__lte=now)) |
                    (Q(recruit_progress_code__gt=RecruitProgressCode.APPLICATION.value) &
                     Q(expiry_date__lte=now.date())),
                    then=Value(True)
                ),
                default=Value(False),
                output_field=BooleanField()
            ),
            # case: user already read
            is_read=~Exists(
                RecApplyRead.objects.filter(
                    apply_id=OuterRef('pk'),
                    user_id=user.user_id
                )
            ),
            waiting_flag=Coalesce(
                'recruit__waiting_company_set__waiting_flag', Value('')),
        ).distinct().order_by('-progress_update_datetime')

        under_consideration_company_queryset = RecWaitingCompany.objects.filter(
            engineer_id=user.user_id, waiting_flag=WaitingFlag.UNDER_CONSIDERATION.value
        ).select_related('recruit__host_company').annotate(
            apply_id=Value(None, output_field=IntegerField()),
            is_closed=Value(None, output_field=BooleanField()),
            is_read=Value(None, output_field=BooleanField()),
            recruit_progress_code=Value(None, output_field=IntegerField()),
            progress_update_datetime=Value(None, output_field=DateTimeField()),
            host_company_id=F('recruit__host_company_id'),
            host_company_name=F('recruit__host_company__name'),
            host_company_logo_image_path=F(
                'recruit__host_company__logo_image_path'),

        ).order_by('-updated')
        company_recruit_ids = set(
            company_queryset.values_list('recruit_id', flat=True))
        # Manually construct the host_company JSON object
        under_consideration_company_list = []
        for item in under_consideration_company_queryset:
            item.host_company = {
                'name': item.host_company_name,
                'logo_image_path': item.host_company_logo_image_path
            }
            if item.recruit_id not in company_recruit_ids:
                under_consideration_company_list.append(item)

        # Case すべて (all): rec_apply all
        if apply_status_filter_code == ApplyStatusFilterCode.ALL.value or apply_status_filter_code is None:
            #
            company_queryset = company_queryset.exclude(
                recruit_progress_code=RecruitProgressCode.REQUESTING_AN_AGENT.value)

            combined_queryset = sorted(
                chain(company_queryset, under_consideration_company_list),
                key=lambda instance: (
                    instance.progress_update_datetime if hasattr(
                        instance, 'progress_update_datetime') else instance.updated
                ) or tz.make_aware(datetime.min, pytz.UTC),
                reverse=(ordering != 'progress_update_datetime')
            )
        # Case 内定 (offered): rec_apply.recruit_progress_code in ('50','60')
        elif apply_status_filter_code == ApplyStatusFilterCode.OFFERED.value:
            combined_queryset = company_queryset.filter(
                recruit_progress_code__in=[
                    RecruitProgressCode.JOB_OFFER.value,
                    RecruitProgressCode.OFFER_ACCEPTED.value,
                    RecruitProgressCode.EMPLOYED.value
                ], expiry_date__gte=now)
        # Case 面接調整中 (interview in progress): rec_apply.recruit_progress_code in ('30','31','32','40')
        elif apply_status_filter_code == ApplyStatusFilterCode.INTERVIEW_SCHEDULING.value:
            combined_queryset = company_queryset.filter(
                recruit_progress_code__in=[
                    RecruitProgressCode.INTERVIEW_REQUEST.value,
                    RecruitProgressCode.INTERVIEW_SCHEDULING.value,
                    RecruitProgressCode.INTERVIEW_DATE_CONFIRMED.value,
                    RecruitProgressCode.INTERVIEW_COMPLETED.value,
                ], expiry_date__gte=now)
        # Case 応募済 (applied): rec_apply.recruit_progress_code in ('20')
        elif apply_status_filter_code == ApplyStatusFilterCode.APPLIED.value:
            combined_queryset = company_queryset.filter(
                recruit_progress_code__in=[RecruitProgressCode.APPLICATION.value], expiry_date__gte=now)
        # Case 検討中 (under consideration): rec_waiting_company.waiting_flag in ('1')
        elif apply_status_filter_code == ApplyStatusFilterCode.UNDER_REVIEW.value:
            combined_queryset = under_consideration_company_list
        # Case 不通過 (rejected): rec_apply.recruit_progress_code in ('90','91','92')
        elif apply_status_filter_code == ApplyStatusFilterCode.NOT_PASSED.value:
            combined_queryset = company_queryset.filter(
                recruit_progress_code__in=[
                    RecruitProgressCode.NOT_PASSED.value,
                    RecruitProgressCode.APPLICATION_WITHDRAWN.value,
                    RecruitProgressCode.INTERVIEW_WITHDRAWN.value,
                    RecruitProgressCode.OFFER_DECLINED.value,
                    RecruitProgressCode.OTHER_COMPANY_OFFER.value
                ], expiry_date__gte=now)
        else:
            return CustomResponse(errors=[{'message': _('Invalid recruit_progress_code')}], status=status.HTTP_400_BAD_REQUEST)

        fake_queryset = FakeQuerySet(combined_queryset)
        paginator = CustomCursorPagination()
        paginated_queryset = paginator.paginate_queryset(
            fake_queryset, request)
        serializer = EngineerListApplyCompanySerializer(
            paginated_queryset, many=True, context={'request': request})

        return CustomResponse(paginator.get_paginated_response(serializer.data), status=status.HTTP_200_OK)

    @swagger_auto_schema(
        method='get',
        operation_id='get_user_agency_company',
        responses=BaseResponse(status_code=status.HTTP_200_OK, message=_(
            'Success'), responseModel=EngineerAgencyCompanyResponseModel).to_openapi_response()
    )
    @api_view(['GET'])
    @permission_classes([IsEngineer, IsEmailVerified])
    def get_user_agency_company(request, user_id):
        try:
            user = User.objects.get(pk=user_id)
            map_entries = MapEngAgc.objects.filter(
                engineer=user, deleted=0).select_related('agency_company')
            companies = [entry.agency_company for entry in map_entries]
            serializer = EngineerAgencyCompanySerializer(companies, many=True)
            return CustomResponse(data=serializer.data, status=status.HTTP_200_OK)
        except User.DoesNotExist:
            return CustomResponse(message=_('User not found'), status=status.HTTP_404_NOT_FOUND)

    @swagger_auto_schema(
        method='PATCH',
        operation_id='update_data_policy',
        request_body=EngineerUpdateDataPolicySerializer,
        responses=BaseResponse(status_code=status.HTTP_200_OK, message=_(
            'Success')).to_openapi_response()
    )
    @api_view(['PATCH'])
    def update_data_policy(request):
        try:
            body_data = EngineerUpdateDataPolicySerializer(data=request.data)
            if not body_data.is_valid():
                return CustomResponse(errors=body_data.errors, status=status.HTTP_400_BAD_REQUEST)
            is_data_policy_accept = request.data.get('is_data_policy_accept')
            code = request.data.get("code")
            key = os.getenv('CRYPTOGRAPHY_KEY')
            f = Fernet(key)
            decrypt_data = f.decrypt(code)
            decrypt_data = decrypt_data.decode('utf-8')
            user_id = decrypt_data.split(":")[1]
            user = User.objects.get(pk=user_id)
            user.is_data_policy_accept = is_data_policy_accept
            user.data_policy_accept_date = timezone.now()
            user.save()
            return CustomResponse(data={'message': 'Success'}, status=status.HTTP_200_OK)
        except Exception as e:
            return CustomResponse(message=str(e), status=status.HTTP_400_BAD_REQUEST)
