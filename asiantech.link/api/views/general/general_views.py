
from utils.utils import *
from rest_framework.views import APIView
from rest_framework.decorators import api_view
from rest_framework import status
from api.serializers.engineers.user_serializers import *
from utils.responses import BaseResponse, CustomResponse
from rest_framework.decorators import api_view
from drf_yasg.utils import swagger_auto_schema
from django.utils.translation import gettext as _
from rest_framework.decorators import api_view
from utils.utils import accept_language_header
from django.utils import translation
from core.settings.common import LANGUAGES
from rest_framework.response import Response
from api.serializers.engineers.user_chat_serializers import UserChatSerializer
from utils.currency_converter import CurrencyConverter
from api.serializers.general.general_serializers import *
from utils.detect_ip_location import *


class GeneralViews(APIView):
    @swagger_auto_schema(
        method='GET',
        operation_id='get_video_social_source',
        query_serializer=GetVideoSocialSourceSerializer,
        responses=BaseResponse(status_code=status.HTTP_200_OK, message=_(
            'Success'), responseModel=GetVideoSocialSourceResponseModel).to_openapi_response()
    )
    @api_view(['GET'])
    def get_video_social_source(request):
        try:
            link = request.query_params.get('link')
            source = get_video_source(link)

            return CustomResponse(data=source, status=status.HTTP_200_OK)
        except Exception as e:
            return CustomResponse(message="", status=status.HTTP_200_OK)

    @api_view(['GET'])
    def current_language(request):
        language = request.LANGUAGE_CODE if hasattr(
            request, 'LANGUAGE_CODE') else translation.get_language()
        return CustomResponse(
            status=status.HTTP_200_OK,
            data={"message": _("Current language is: ") + language}
        )

    @swagger_auto_schema(
        method='get',
        operation_id='support_language',
        manual_parameters=[accept_language_header],
        responses={
            status.HTTP_200_OK: openapi.Schema(
                type=openapi.TYPE_OBJECT,
                properties={"support": openapi.Schema(type=openapi.TYPE_ARRAY, items=openapi.Schema(type=openapi.TYPE_STRING))})
        }
    )
    @api_view(['GET'])
    def support_language(request):
        supported_languages = [lang[0] for lang in LANGUAGES]
        return CustomResponse(status=status.HTTP_200_OK, data={"support": supported_languages})

    @swagger_auto_schema(method='get', responses={200: UserChatSerializer(many=True)})
    @api_view(['GET'])
    def dummy_user_chat_view(request):
        """
        Dummy view to display UserChatSerializer in Swagger UI
        """
        return Response(status=200)

    @swagger_auto_schema(
        method='POST',
        operation_id='convert_currency',
        request_body=ConvertCurrencySerializer,
        responses=BaseResponse(status_code=status.HTTP_200_OK, message=_(
            'Success')).to_openapi_response()
    )
    @api_view(['POST'])
    def convert_currency(request):
        currencyConverter = CurrencyConverter()
        serializer = ConvertCurrencySerializer(data=request.data)
        if serializer.is_valid():
            amount = serializer.validated_data['amount']
            from_currency = serializer.validated_data['from_currency']
            to_currency = serializer.validated_data['to_currency']
            converted_amount = currencyConverter.convert(
                amount, from_currency, to_currency)
            return CustomResponse(data={"converted_amount": converted_amount}, status=status.HTTP_200_OK)
        else:
            return CustomResponse(errors=serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    @swagger_auto_schema(
        method='GET',
        operation_id='get_current_location',
    )
    @api_view(['GET'])
    def get_current_location(request):
        pass
        # country = get_country_from_request(request)
        # return CustomResponse(data=location, status=status.HTTP_200_OK)
