from utils.custom_pagination import CustomCursorPagination, CustomCursorTotalCountPagination
from api.models.map_hst_sup import MapHstSup
import pdfkit
from django.http import HttpResponse
from api.models.rec import RecFilter
from utils.query_helper import QueryHelper
from utils.custom_pagination import CustomCursorPagination, CustomPageNumberPagination
from utils.permissions import IsEmailVerified
from django.contrib.auth.models import User
from utils.utils import *
from api.serializers.engineers.self_assessment_serializer import AssessmentQuestionDataSerializer, AssessmentQuestionsResponseModel, SelfAssessmentAnswerSerializer, SelfAssessmentResponseModel
import logging
import time
from api.models.rec import RecInterestedEngineer
from api.models.rec import *

from rest_framework.views import APIView
from django.contrib.auth import get_user_model
from rest_framework.decorators import api_view
from rest_framework import status
from api.serializers.engineers.user_serializers import *

from utils.responses import BaseResponse, CustomResponse
from rest_framework.decorators import api_view, permission_classes
from drf_yasg.utils import swagger_auto_schema
from django.utils.translation import gettext as _
from utils.permissions import *
from utils.validators import validate_serializer
from api.models.rec import *
from api.views.authentication.authentication_views import AuthenticationViews
from core.settings.common import EMPLOYEE_CONTRACT_TEMPLATE_PATH
from api.serializers.general_company.general_company_serializers import *
from datetime import timedelta
from django.db.models import Q, F
from api.services.notify_service.engineer_notify_service import EngineerNotifyService
from rest_framework_simplejwt.authentication import JWTAuthentication
from rest_framework.exceptions import AuthenticationFailed
User = get_user_model()
logger = logging.getLogger("api_logger")


class GeneralCompanyView(APIView):

    @swagger_auto_schema(
        method='post',
        operation_id='register_company',
        request_body=GeneralCompanyRegisterSerializer,
        responses=BaseResponse(status_code=status.HTTP_201_CREATED, message=_(
            "Success"),
        ).to_openapi_response()
    )
    @api_view(['POST'])
    def register_company(request):
        serializer = GeneralCompanyRegisterSerializer(data=request.data)
        is_valid, error = validate_serializer(serializer)
        if not is_valid:
            return CustomResponse(errors=error, status=status.HTTP_400_BAD_REQUEST)
        serializer.is_valid(raise_exception=True)
        email = serializer.validated_data['user']['email']
        if User.objects.filter(email=email).exists():
            return CustomResponse(errors=[
                {
                    'message': _('Email already exists'),
                    'field': 'email'
                }
            ], status=status.HTTP_400_BAD_REQUEST)
        serializer.save()
        web_link = get_host_url_from_request(request)
        AuthenticationViews.send_verification_email(email, web_link)

        return CustomResponse(status=status.HTTP_201_CREATED, message=_("Success"))

    @swagger_auto_schema(
        method='PATCH',
        operation_id='update_company',
        request_body=GeneralCompanyUpdateSerializer,
        responses=BaseResponse(status_code=status.HTTP_200_OK, message=_(
            'Success')).to_openapi_response()
    )
    @swagger_auto_schema(
        method='get',
        operation_id='company_details',
        responses=BaseResponse(status_code=status.HTTP_200_OK, message=_(
            'Success'), responseModel=GeneralCompanyResponseModel).to_openapi_response()
    )
    @api_view(['GET', 'PATCH'])
    @permission_classes([IsStaff, IsEmailVerified])
    def company_details(request):
        user = request.user
        # check user has company
        company_id = user.company_id
        if not company_id:
            return CustomResponse(errors=[{'message': _('User does not have company')}], status=status.HTTP_400_BAD_REQUEST)
        if request.method == 'GET':
            try:
                company = ComCompany.objects.get(company_id=company_id)
                serializer = GeneralCompanyDetailsSerializer(company)
                return CustomResponse(data=serializer.data, status=status.HTTP_200_OK, message=_('Success'))
            except ComCompany.DoesNotExist:
                return CustomResponse(errors=[{'message': _('Company not found')}], status=status.HTTP_404_NOT_FOUND)

        elif request.method == 'PATCH':
            try:
                company = ComCompany.objects.get(company_id=company_id)
                if company.user_type != UserType.HOST_SUPPORT_AGENCY_STAFF.value:
                    if 'accepting_fee' in request.data:
                        request.data.pop('accepting_fee')
                    if 'accepting_fee_curr_code' in request.data:
                        request.data.pop('accepting_fee_curr_code')
                    if 'support_outsourcing_fee' in request.data:
                        request.data.pop('support_outsourcing_fee')
                    if 'support_outsourcing_fee_curr_code' in request.data:
                        request.data.pop('support_outsourcing_fee_curr_code')
                    if 'support' in request.data:
                        request.data.pop('support')

                serializer = GeneralCompanyUpdateSerializer(
                    company, data=request.data, partial=True)
                is_valid, error = validate_serializer(serializer)
                if not is_valid:
                    return CustomResponse(errors=error, status=status.HTTP_400_BAD_REQUEST)
                serializer.save()
                return CustomResponse(status=status.HTTP_200_OK, message=_('Success'))
            except ComCompany.DoesNotExist:
                return CustomResponse(errors=[{'message': _('Company not found')}], status=status.HTTP_404_NOT_FOUND)

    def get_explore_user_data(request):
        params = ExploreUserParamsSerializer(data=request.query_params)

        if not params.is_valid():
            return CustomResponse(errors=params.errors, status=status.HTTP_400_BAD_REQUEST)

        filter_id = params.validated_data.get("filter_id")
        if filter_id:
            try:
                filter = RecFilter.objects.filter(pk=filter_id).get()

                params = ExploreUserParamsSerializer(
                    data=filter.__dict__)
                if not params.is_valid():
                    return CustomResponse(errors=params.errors, status=status.HTTP_400_BAD_REQUEST)
            except RecFilter.DoesNotExist:
                return CustomResponse(errors={"filter_id": "Filter not found"}, status=status.HTTP_400_BAD_REQUEST)

        queryHelper = QueryHelper()
        params_data = params.validated_data
        company_id = None
        if request.user.is_authenticated == True and request.user.company_id is not None:
            company_id = request.user.company_id
        # replace all value '' to None
        for key, value in params_data.items():
            if value == '':
                params_data[key] = None

        query = queryHelper.engineer_filter_basic(params_data, request.user)
        query = queryHelper.engineer_filter_by_list_address_code(
            query, params_data)
        query = queryHelper.engineer_filter_by_age(
            query, params_data)
        query = queryHelper.engineer_filter_by_list_language_code_and_level(
            query, params_data)
        query = queryHelper.engineer_filter_by_remote_work_skill(
            query, params_data)
        query = queryHelper.engineer_filter_by_global_skill(query, params_data)
        query = queryHelper.engineer_filter_by_communication_skill(
            query, params_data)
        query = queryHelper.engineer_filter_by_horenso_skill(
            query, params_data)
        query = queryHelper.engineer_filter_by_project_management_skill(
            query, params_data)
        query = queryHelper.engineer_filter_by_work_experience_skill(
            query, params_data)
        query = queryHelper.engineer_filter_by_list_license_code_and_license_point(
            query, params_data)
        query = queryHelper.engineer_filter_by_agent_fee(
            query, params_data)
        if request.user.is_authenticated == True and request.user.company_id is not None:
            query = queryHelper.engineer_filter_by_favorite(
                query, params_data, request.user.company_id)
        query = queryHelper.engineer_filter_by_enghope(
            query, params_data)
        query = queryHelper.engineer_filter_by_list_jobs_and_job_skill(
            query, params_data)
        query = queryHelper.engineer_filter_by_skills(query, params_data)
        query = queryHelper.engineer_filter_by_ordering(
            query, params_data.get('ordering', None))
        query = queryHelper.engineer_filter_by_search_type(
            query, params_data.get("search_type", None), company_id, request.user, params_data.get("recruit_id", None))
        query = queryHelper.engineer_filter_by_details(
            query, company_id)

        return query.distinct()

    @swagger_auto_schema(
        method='get',
        operation_id='explore_users',
        responses=BaseResponse(status_code=status.HTTP_200_OK, message=_(
            "Success"), responseModel=GeneralCompanyListUserExploreResponseModel).to_openapi_response(),
        query_serializer=ExploreUserParamsSerializer(),
    )
    @api_view(['GET'])
    def explore_users(request):
        query = GeneralCompanyView.get_explore_user_data(request)
        ai_summary = None
        ai_summary_vi = None
        ai_summary_ja = None
        recruit_id = None

        # Add AI summary for AI recommend search type
        search_type = request.query_params.get("search_type")
        authenticated_user = request.user

        if search_type == SearchType.AI_RECOMMEND.value and authenticated_user.is_authenticated:
            try:
                first_engineer = query.first()

                recruit_id = first_engineer.recruit_id
                ai_summary = first_engineer.ai_summary
                ai_summary_vi = first_engineer.ai_summary_vi
                ai_summary_ja = first_engineer.ai_summary_ja

            except Exception as e:
                pass

        paginator = CustomPageNumberPagination()
        paginated_recruits = paginator.paginate_queryset(query, request)

        serializer = GeneralCompanyExploreUserSerializer(
            paginated_recruits, many=True, context={'request': request})
        paginated_response = paginator.get_paginated_response(serializer.data)
        paginated_response.data['recruit_id'] = recruit_id
        paginated_response.data['ai_summary'] = ai_summary
        paginated_response.data['ai_summary_ja'] = ai_summary_ja
        paginated_response.data['ai_summary_vi'] = ai_summary_vi

        return CustomResponse(
            data=paginated_response.data,
            status=status.HTTP_200_OK,
            message=_("Success")
        )

    @swagger_auto_schema(
        method='get',
        operation_id='get_explore_user_count',
        responses=BaseResponse(status_code=status.HTTP_200_OK, message=_(
            "Success"), responseModel=GeneralCompanyGetUserExploreCountResponseModel).to_openapi_response(),
        query_serializer=ExploreUserParamsSerializer(),
    )
    @api_view(['GET'])
    def get_explore_user_count(request):
        response = GeneralCompanyView.get_explore_user_data(request)
        if isinstance(response, CustomResponse):
            return response
        count = response.count()
        return CustomResponse(data=count, status=status.HTTP_200_OK)

    @swagger_auto_schema(
        method='post',
        operation_id='save_filter',
        request_body=GeneralCompanySaveFilterSerializer,
        responses=BaseResponse(status_code=status.HTTP_200_OK, message=_(
            "Success"),).to_openapi_response()
    )
    @api_view(['POST'])
    @permission_classes([IsStaff, IsEmailVerified])
    def save_filter(request):
        serializer = GeneralCompanySaveFilterSerializer(data=request.data)
        request.data["host_agent"] = request.user.user_id
        if not serializer.is_valid():
            return CustomResponse(errors=serializer.errors, status=status.HTTP_400_BAD_REQUEST)

        filter_data = serializer.validated_data
        serializer.save()
        return CustomResponse(message=_("Filter saved successfully"), status=status.HTTP_200_OK)

    @swagger_auto_schema(
        method='get',
        operation_id='get_saved_filters',
        responses=BaseResponse(status_code=status.HTTP_200_OK, message=_(
            "Success"), responseModel=GeneralCompanyListFilterResponseModel).to_openapi_response()
    )
    @api_view(['GET'])
    @permission_classes([IsStaff, IsEmailVerified])
    def get_saved_filters(request):
        user = request.user
        filters = RecFilter.objects.filter(
            host_agent=request.user
        )
        serializer = GeneralCompanyFilterSerializer(filters, many=True)
        return CustomResponse(data=serializer.data, status=status.HTTP_200_OK)

    @swagger_auto_schema(
        method='delete',
        operation_id='delete_filter',
        responses=BaseResponse(status_code=status.HTTP_200_OK, message=_(
            "Success"),).to_openapi_response()
    )
    @api_view(['DELETE'])
    @permission_classes([IsStaff, IsEmailVerified])
    def delete_filter(request, filter_id):
        try:
            filter = RecFilter.objects.filter(pk=filter_id).get()
            filter.delete()
            return CustomResponse(message=_("Filter deleted successfully"), status=status.HTTP_200_OK)
        except RecFilter.DoesNotExist:
            return CustomResponse(errors={"filter_id": "Filter not found"}, status=status.HTTP_400_BAD_REQUEST)

    @swagger_auto_schema(
        method='post',
        operation_id='add-favorite-user',
        request_body=GeneralCompanyUpdateFavoriteUserSerializer,
        responses=BaseResponse(status_code=status.HTTP_200_OK, message=_(
            "Success")).to_openapi_response()
    )
    @api_view(['POST'])
    @permission_classes([IsStaff, IsEmailVerified])
    def updateFavoriteUser(request):
        try:
            company_id = request.user.company_id
            user_id = request.data.get("user_id")
            flag = 0
            if request.data.get("is_favorite"):
                flag = 1
            interested, created = RecInterestedEngineer.objects.get_or_create(
                host_company_id=company_id, engineer_id=user_id)
            if interested.interested_flag == 3:
                return CustomResponse(message=_("You can't update this status!"), status=status.HTTP_400_BAD_REQUEST)
            interested.interested_flag = flag
            interested.updated = timezone.now()
            interested.save()
            return CustomResponse(message=_("Success"), status=status.HTTP_200_OK)
        except Exception as e:
            return CustomResponse(message=str(e), status=status.HTTP_400_BAD_REQUEST)

    @swagger_auto_schema(
        method='get',
        operation_id='get_user_details',
        responses=BaseResponse(status_code=status.HTTP_200_OK, message=_(
            'Success'), responseModel=UserExploreDetailsResponseModel).to_openapi_response()
    )
    @api_view(['GET'])
    def get_user_details(request, user_id):
        try:
            if request.user.is_authenticated and request.user.company_id == 1091:
                user = User.objects.get(pk=user_id)
            else:
                user = User.objects.get(pk=user_id, is_import=0)
            serializer = UserExploreDetailsSerializers(
                user, context={"request": request})
            return CustomResponse(data=serializer.data, status=status.HTTP_200_OK)
        except User.DoesNotExist:
            return CustomResponse(message=_('User not found'), status=status.HTTP_404_NOT_FOUND)

    @swagger_auto_schema(
        method='get',
        operation_id='get_remote_work_skills_engineer',
        responses=BaseResponse(status_code=status.HTTP_200_OK, message=_(
            'Success'), responseModel=AssessmentQuestionsResponseModel).to_openapi_response()
    )
    @api_view(['GET'])
    @permission_classes([IsStaff, IsEmailVerified])
    def get_remote_work_skills_engineer(request, user_id):

        try:
            json_data = get_question_and_answer_assessment(
                user_id, 'remote_work_skills.json', 'remote_skill')

            serializer = AssessmentQuestionDataSerializer(
                json_data['questions'], many=True, context={'request': request})
            return CustomResponse(data=serializer.data, status=status.HTTP_200_OK)
        except Exception as e:
            return CustomResponse(message=str(e), status=status.HTTP_400_BAD_REQUEST)

    @swagger_auto_schema(
        method='get',
        operation_id='get_communication_skills_self_assessment_engineer',
        responses=BaseResponse(status_code=status.HTTP_200_OK, message=_(
            'Success'), responseModel=AssessmentQuestionsResponseModel).to_openapi_response()
    )
    @api_view(['GET'])
    @permission_classes([IsStaff, IsEmailVerified])
    def get_communication_skills_self_assessment_engineer(request, user_id):

        try:
            json_data = get_question_and_answer_assessment(
                user_id, 'communication_skills_self_assessment.json', 'communication_skill')
            serializer = AssessmentQuestionDataSerializer(
                json_data['questions'], many=True, context={'request': request})
            return CustomResponse(data=serializer.data, status=status.HTTP_200_OK)
        except Exception as e:
            return CustomResponse(message=str(e), status=status.HTTP_400_BAD_REQUEST)

    @swagger_auto_schema(
        method='get',
        operation_id='get_global_responsiveness_skills_engineer',
        responses=BaseResponse(status_code=status.HTTP_200_OK, message=_(
            'Success'), responseModel=AssessmentQuestionsResponseModel).to_openapi_response()
    )
    @api_view(['GET'])
    @permission_classes([IsStaff, IsEmailVerified])
    def get_global_responsiveness_skills_engineer(request, user_id):

        try:
            json_data = get_question_and_answer_assessment(
                user_id, 'global_responsiveness_skills.json', 'global_skill')
            serializer = AssessmentQuestionDataSerializer(
                json_data['questions'], many=True, context={'request': request})
            return CustomResponse(data=serializer.data, status=status.HTTP_200_OK)
        except Exception as e:
            return CustomResponse(message=str(e), status=status.HTTP_400_BAD_REQUEST)

    @swagger_auto_schema(
        method='get',
        operation_id='get_reporting_consultation_skills_self_evaluation_engineer',
        responses=BaseResponse(status_code=status.HTTP_200_OK, message=_(
            'Success'), responseModel=AssessmentQuestionsResponseModel).to_openapi_response()
    )
    @api_view(['GET'])
    @permission_classes([IsStaff, IsEmailVerified])
    def get_reporting_consultation_skills_self_evaluation_engineer(request, user_id):
        try:
            json_data = get_question_and_answer_assessment(
                user_id, 'reporting_consultation_skills_self_evaluation.json', 'report_skill')
            serializer = AssessmentQuestionDataSerializer(
                json_data['questions'], many=True, context={'request': request})
            return CustomResponse(data=serializer.data, status=status.HTTP_200_OK)
        except Exception as e:
            return CustomResponse(message=str(e), status=status.HTTP_400_BAD_REQUEST)

    @swagger_auto_schema(
        method='get',
        operation_id='get_project_management_skills_self_evaluation_engineer',
        responses=BaseResponse(status_code=status.HTTP_200_OK, message=_(
            'Success'), responseModel=AssessmentQuestionsResponseModel).to_openapi_response()
    )
    @api_view(['GET'])
    @permission_classes([IsStaff, IsEmailVerified])
    def get_project_management_skills_self_evaluation_engineer(request, user_id):
        try:
            json_data = get_question_and_answer_assessment(
                user_id, 'project_management_skills_self_evaluation.json', 'management_skill')
            serializer = AssessmentQuestionDataSerializer(
                json_data['questions'], many=True, context={'request': request})
            return CustomResponse(data=serializer.data, status=status.HTTP_200_OK)
        except Exception as e:
            return CustomResponse(message=str(e), status=status.HTTP_400_BAD_REQUEST)

    # List of applied engineers
    @swagger_auto_schema(
        method='get',
        operation_id='list_applied_engineers',
        query_serializer=GeneralCompanyParamListUserAppliedCompanySerializer,
        responses=BaseResponse(
            status_code=status.HTTP_200_OK,
            message=_('Success'),
            responseModel=GeneralCompanyListUserAppliedCompanyResponseModel
        ).to_openapi_response()
    )
    @api_view(['GET'])
    @permission_classes([IsCompany, IsEmailVerified])
    def list_applied_engineers(request):
        user = request.user
        recruit_ids = request.query_params.get('recruit_ids')
        apply_status_filter_codes = request.query_params.get(
            'apply_status_filter_codes')
        ordering = request.query_params.get('ordering')
        search = request.query_params.get("search", None)

        # Define valid ordering fields - keep simple for now
        valid_ordering_fields = [
            'created', 'total_recruit_progress_code_active', 'age', 'payroll_price'
        ]

        # Check if the ordering is valid
        if ordering and ordering not in valid_ordering_fields:
            return CustomResponse(data=[], status=status.HTTP_400_BAD_REQUEST, message=_("Invalid ordering field."))

        # Queryset for RecApply
        host_company_id = user.company_id
        if user.user_type == UserType.HOST_SUPPORT_AGENCY_STAFF.value:
            host_company_id = request.query_params.get('host_company_id')
            # check exist relationship between host company and support company
            if not MapHstSup.objects.filter(host_company_id=host_company_id, support_company_id=user.company_id).exists():
                return CustomResponse(errors=[{'message': _("You don't have permission to access this company")}], status=status.HTTP_400_BAD_REQUEST)
            if host_company_id is None or host_company_id == "":
                return CustomResponse(errors=[{'message': _('Host company not found')}], status=status.HTTP_400_BAD_REQUEST)

        rec_apply_queryset = RecApply.objects.filter(
            host_company_id=host_company_id)

        # Filter by support company if user is support company staff
        if user.user_type == UserType.HOST_SUPPORT_AGENCY_STAFF.value:
            rec_apply_queryset = rec_apply_queryset.filter(
                support_company_id=user.company_id
            )

        # Use QueryHelper to properly annotate engineer fields
        from django.db.models import Prefetch
        query_helper = QueryHelper()

        # Create annotated engineer queryset
        engineer_queryset = User.objects.all()
        engineer_queryset = query_helper.engineer_filter_by_details(engineer_queryset, host_company_id)

        if recruit_ids is not None and recruit_ids != "":
            rec_apply_queryset = rec_apply_queryset.filter(
                recruit_id__in=recruit_ids.split(','))

        if apply_status_filter_codes is not None and apply_status_filter_codes != "":
            rec_apply_queryset = rec_apply_queryset.filter(
                recruit_progress_code__in=apply_status_filter_codes.split(',')
            )
        # Apply search functionality for recruit title or engineer first name/last name
        if search:
            rec_apply_queryset = rec_apply_queryset.filter(
                # Searching by recruit title
                Q(recruit__title__icontains=search) |
                # Searching by engineer's first name
                Q(engineer__first_name__icontains=search) |
                # Searching by engineer's last name
                Q(engineer__last_name__icontains=search)
            )

        # Apply ordering if provided, else default to '-created'
        if ordering:
            rec_apply_queryset = rec_apply_queryset.order_by(ordering)
        else:
            rec_apply_queryset = rec_apply_queryset.order_by('-created')

        # Add select_related and prefetch_related for better performance
        rec_apply_queryset = rec_apply_queryset.select_related(
            'recruit'
        ).prefetch_related(
            Prefetch('engineer', queryset=engineer_queryset),
            'engineer__engskill_set',
            'engineer__engacademic_set',
            'engineer__englanguage_set',
            'engineer__englicence_set',
            'engineer__engcareer_set',
            'engineer__enghope_set',
            'engineer__hopejobskill_set'
        )

        # Add pagination
        paginator = CustomPageNumberPagination()
        paginated_applies = paginator.paginate_queryset(rec_apply_queryset, request)

        # Use the enhanced serializer with all required fields
        serializer = GeneralCompanyAppliedEngineersSerializer(
            paginated_applies, many=True, context={'request': request}
        )

        paginated_response = paginator.get_paginated_response(serializer.data)

        return CustomResponse(
            data=paginated_response.data,
            status=status.HTTP_200_OK,
            message=_("Success")
        )

    def check_host_company_of_support_company(support_company_id, host_company_id):
        exist = MapHstSup.objects.filter(
            support_company_id=support_company_id, host_company_id=host_company_id).exists()
        return exist

    @swagger_auto_schema(
        method='get',
        operation_id='get-apply-details',
        query_serializer=GeneralCompanyParamGetApplyDetailsSerializer,
        manual_parameters=[accept_language_header],
        responses=BaseResponse(
            status_code=status.HTTP_200_OK,
            message=_('Success'),
            responseModel=GeneralCompanyApplyDetailsResponseModel,
        ).to_openapi_response()
    )
    @api_view(['GET'])
    @permission_classes([IsCompany, IsEmailVerified])
    def get_apply_details(request):
        user = request.user
        host_company_id = None
        support_company_id = None

        if user.user_type == UserType.HOST_SUPPORT_AGENCY_STAFF.value:
            support_company_id = user.company_id
        if user.user_type == UserType.HOST_COMPANY_STAFF.value:
            host_company_id = user.company_id

        apply_id = request.query_params.get('apply_id')
        filters = {'apply_id': apply_id}

        try:
            apply = RecApply.objects.get(**filters)
            if support_company_id is not None:
                if apply.support_company_id != support_company_id:
                    return CustomResponse(errors=[{'message': _("You don't have permission to access this apply")}], status=status.HTTP_400_BAD_REQUEST)
            elif host_company_id is not None:
                if apply.host_company_id != host_company_id:
                    return CustomResponse(errors=[{'message': _("You don't have permission to access this apply")}], status=status.HTTP_400_BAD_REQUEST)
            elif host_company_id is None and support_company_id is None:
                return CustomResponse(errors=[{'message': _("You don't have permission to access this apply")}], status=status.HTTP_400_BAD_REQUEST)

            recruit = apply.recruit
            engineer = apply.engineer
            pr_comments = MapEngAgc.objects.filter(
                agency_company_id=apply.agency_company_id, engineer_id=engineer.user_id)
            serializer = GeneralCompanyGetApplyDetailsSerializer({
                "apply": apply,
                "recruit": recruit,
                "engineer": engineer,
                "pr_comments": pr_comments
            }, context={'request': request})
            return CustomResponse(data=serializer.data, status=status.HTTP_200_OK, message=_("Success"))
        except RecApply.DoesNotExist:
            return CustomResponse(errors=[{'message': _('Apply not found')}], status=status.HTTP_404_NOT_FOUND)

    @swagger_auto_schema(
        method='post',
        operation_id='reject_apply',
        request_body=GeneralCompanyRejectApplySerializer,
        responses=BaseResponse(
            status_code=status.HTTP_200_OK,
            message=_('Success')
        ).to_openapi_response()
    )
    @api_view(['POST'])
    @permission_classes([IsCompany, IsEmailVerified])
    def reject_apply(request):
        user = request.user
        host_company_id = None
        support_company_id = None

        if user.user_type == UserType.HOST_SUPPORT_AGENCY_STAFF.value:
            support_company_id = user.company_id
        if user.user_type == UserType.HOST_COMPANY_STAFF.value:
            host_company_id = user.company_id

        serializer = GeneralCompanyRejectApplySerializer(data=request.data)

        if serializer.is_valid():

            apply_id = serializer.validated_data['apply_id']

            filters = {'apply_id': apply_id}

            try:
                apply = RecApply.objects.get(**filters)
                if support_company_id is not None:
                    if apply.support_company_id != support_company_id:
                        return CustomResponse(errors=[{'message': _("You don't have permission to access this apply")}], status=status.HTTP_400_BAD_REQUEST)
                elif host_company_id is not None:
                    if apply.host_company_id != host_company_id:
                        return CustomResponse(errors=[{'message': _("You don't have permission to access this apply")}], status=status.HTTP_400_BAD_REQUEST)
                elif host_company_id is None and support_company_id is None:
                    return CustomResponse(errors=[{'message': _("You don't have permission to access this apply")}], status=status.HTTP_400_BAD_REQUEST)

                apply.recruit_progress_code = RecruitProgressCode.NOT_PASSED.value
                apply.progress_update_datetime = timezone.now()
                apply.expiry_date = timezone.now() + timezone.timedelta(days=30)
                apply.updated = timezone.now()
                apply.save()
                return CustomResponse(message=_('Success'), status=status.HTTP_200_OK)
            except RecApply.DoesNotExist:
                return CustomResponse(errors=[{'message': _('Apply not found')}], status=status.HTTP_404_NOT_FOUND)
        return CustomResponse(errors=serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    @swagger_auto_schema(
        method='post',
        operation_id='accept_apply',
        request_body=GeneralCompanyRequestInterviewApplySerializer,
        responses=BaseResponse(
            status_code=status.HTTP_200_OK,
            message=_('Success')
        ).to_openapi_response()
    )
    @api_view(['POST'])
    @permission_classes([IsCompany, IsEmailVerified])
    def request_interview_apply(request):
        user = request.user
        host_company_id = user.company_id
        support_company_id = None
        serializer = GeneralCompanyRequestInterviewApplySerializer(
            data=request.data)
        if serializer.is_valid():
            apply_id = serializer.validated_data['apply_id']

            if user.user_type == UserType.HOST_SUPPORT_AGENCY_STAFF.value:
                host_company_id = request.data['host_company_id']
                support_company_id = user.company_id
                if host_company_id is None or host_company_id == "":
                    return CustomResponse(errors=[{'message': _('Host company not found')}], status=status.HTTP_400_BAD_REQUEST)
                if not GeneralCompanyView.check_host_company_of_support_company(user.company_id, host_company_id):
                    return CustomResponse(errors=[{'message': _("You don't have permission to access this company")}], status=status.HTTP_400_BAD_REQUEST)

            filters = {'apply_id': apply_id,
                       'host_company_id': host_company_id}
            if support_company_id:
                filters['support_company_id'] = support_company_id
            try:
                apply = RecApply.objects.get(**filters)
                apply.recruit_progress_code = RecruitProgressCode.INTERVIEW_REQUEST.value
                apply.progress_update_datetime = timezone.now()
                apply.updated = timezone.now()
                apply.save()

                if apply.group is None:
                    group = RecChatGroup.objects.create()
                    apply.group = group
                    apply.save()

                    MapChatGroup.objects.create(
                        group=group,
                        user_id=apply.engineer.user_id,
                    )
                    MapChatGroup.objects.create(
                        group=group,
                        user_id=apply.host_agent.user_id,
                    )

                token = get_access_token_from_request(request)
                EngineerNotifyService().send_notify_company_request_interview(
                    token,  sender=user, apply_id=apply_id)
                return CustomResponse(message=_('Success'), status=status.HTTP_200_OK)
            except RecApply.DoesNotExist:
                return CustomResponse(errors=[{'message': _('Apply not found')}], status=status.HTTP_404_NOT_FOUND)
        return CustomResponse(errors=serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    @swagger_auto_schema(
        method='get',
        operation_id='get_calendar_interview',
        query_serializer=GeneralCompanyCalendarInterviewParamsSerializer,
        responses=BaseResponse(
            status_code=status.HTTP_200_OK,
            message=_('Success'),
            responseModel=GeneralCompanyCalendarInterviewResponseModel
        ).to_openapi_response()
    )
    @api_view(['GET'])
    @permission_classes([IsCompany, IsEmailVerified])
    def get_calendar_interview(request):
        user = request.user
        host_company_id = None
        support_company_id = None

        if user.user_type == UserType.HOST_SUPPORT_AGENCY_STAFF.value:
            support_company_id = user.company_id
        elif user.user_type == UserType.HOST_COMPANY_STAFF.value:
            host_company_id = user.company_id

        date = request.query_params.get('date')
        input_date = datetime.strptime(date, '%Y-%m-%d')

        # Get first day and last day of the month
        # Example:
        # input_date = 2021-08-15
        # first_day = 2021-07-31
        # last_day = 2021-09-01

        first_date = input_date.replace(
            day=1) - timedelta(days=1)  # Last day of previous month
        next_month = input_date.replace(
            day=28) + timedelta(days=4)  # Approximate next month
        last_date = next_month.replace(day=1)  # First day of next month

        # Truy vấn tất cả trong khoảng tháng
        filter_condition = Q(interview_datetime__gte=first_date,
                             interview_datetime__lte=last_date)

        if support_company_id:
            filter_condition &= Q(support_company_id=support_company_id)
        else:
            filter_condition &= Q(
                host_company_id=host_company_id, host_agent=user)

        apply_queryset = RecApply.objects.filter(
            filter_condition).order_by('interview_datetime')

        serializer = GeneralCompanyInterviewDataSerializer(
            apply_queryset, many=True, context={'request': request}
        )

        return CustomResponse(data=serializer.data, status=status.HTTP_200_OK)

    @swagger_auto_schema(
        method="POST",
        operation_id="update_interview_date",
        request_body=GeneralCompanyUpdateInterviewDateTimeSerializer,
        responses=BaseResponse(
            status_code=status.HTTP_200_OK,
            message=_('Success')
        ).to_openapi_response()
    )
    @permission_classes([IsCompany, IsEmailVerified])
    @api_view(['POST'])
    def update_interview_datetime(request):
        user = request.user
        host_company_id = None
        support_company_id = None

        if user.user_type == UserType.HOST_SUPPORT_AGENCY_STAFF.value:
            support_company_id = user.company_id
        if user.user_type == UserType.HOST_COMPANY_STAFF.value:
            host_company_id = user.company_id

        apply_id = request.data.get('apply_id')
        interview_datetime = request.data.get('interview_datetime')

        filters = {'apply_id': apply_id}

        try:
            apply = RecApply.objects.get(**filters)
            apply = RecApply.objects.get(**filters)
            if support_company_id is not None:
                if apply.support_company_id != support_company_id:
                    return CustomResponse(errors=[{'message': _("You don't have permission to access this apply")}], status=status.HTTP_400_BAD_REQUEST)
            elif host_company_id is not None:
                if apply.host_company_id != host_company_id:
                    return CustomResponse(errors=[{'message': _("You don't have permission to access this apply")}], status=status.HTTP_400_BAD_REQUEST)
            elif host_company_id is None and support_company_id is None:
                return CustomResponse(errors=[{'message': _("You don't have permission to access this apply")}], status=status.HTTP_400_BAD_REQUEST)

            apply.interview_datetime = interview_datetime
            apply.recruit_progress_code = RecruitProgressCode.INTERVIEW_DATE_CONFIRMED.value
            apply.progress_update_datetime = timezone.now()
            apply.updated = timezone.now()
            apply.save()
            return CustomResponse(message=_('Success'), status=status.HTTP_200_OK)
        except RecApply.DoesNotExist:
            return CustomResponse(errors=[{'message': _('Apply not found')}], status=status.HTTP_404_NOT_FOUND)

    @swagger_auto_schema(
        method="GET",
        operation_id="get_company_contract_details",
        query_serializer=GeneralCompanyParamGetContractDetailsSerializer,
        responses=BaseResponse(
            status_code=status.HTTP_200_OK,
            message=_('Success'),
            responseModel=GeneralCompanyContractDetailsResponseModel
        ).to_openapi_response()
    )
    @api_view(['GET'])
    @permission_classes([IsCompany, IsEmailVerified])
    def get_contract_details(request):
        user = request.user
        host_company_id = user.company_id
        support_company_id = None

        if user.user_type == UserType.HOST_SUPPORT_AGENCY_STAFF.value:
            host_company_id = request.query_params.get('host_company_id')
            support_company_id = user.company_id
            if host_company_id is None or host_company_id == "":
                return CustomResponse(errors=[{'message': _('Host company not found')}], status=status.HTTP_400_BAD_REQUEST)
            if not GeneralCompanyView.check_host_company_of_support_company(user.company_id, host_company_id):
                return CustomResponse(errors=[{'message': _("You don't have permission to access this company")}], status=status.HTTP_400_BAD_REQUEST)

        apply_id = request.query_params.get('apply_id')
        filters = {'apply_id': apply_id,
                   'host_company_id': host_company_id}
        if support_company_id:
            filters['support_company_id'] = support_company_id
        try:
            apply = RecApply.objects.get(**filters)
            serializer = GeneralCompanyGetContractDetailsSerializer(apply)
            return CustomResponse(data=serializer.data, status=status.HTTP_200_OK)
        except RecApply.DoesNotExist:
            return CustomResponse(errors=[{'message': _('Apply not found')}], status=status.HTTP_404_NOT_FOUND)

    @swagger_auto_schema(
        method="POST",
        operation_id="download_employee_contract",
        request_body=GeneralCompanyParamGetContractDetailsSerializer,
        responses={
            status.HTTP_200_OK: openapi.Schema(
                type=openapi.TYPE_FILE,
                format=openapi.FORMAT_BINARY
            ),
        }
    )
    @api_view(['POST'])
    @permission_classes([IsCompany, IsEmailVerified])
    def download_employment_contract(request):
        user = request.user
        apply_id = request.data.get('apply_id')
        host_company_id = None
        support_company_id = None

        if user.user_type == UserType.HOST_SUPPORT_AGENCY_STAFF.value:
            support_company_id = user.company_id
        if user.user_type == UserType.HOST_COMPANY_STAFF.value:
            host_company_id = user.company_id
        try:

            apply = RecApply.objects.get(apply_id=apply_id)
            if support_company_id is not None:
                if apply.support_company_id != support_company_id:
                    return CustomResponse(errors=[{'message': _("You don't have permission to access this apply")}], status=status.HTTP_400_BAD_REQUEST)
            elif host_company_id is not None:
                if apply.host_company_id != host_company_id:
                    return CustomResponse(errors=[{'message': _("You don't have permission to access this apply")}], status=status.HTTP_400_BAD_REQUEST)
            elif host_company_id is None and support_company_id is None:
                return CustomResponse(errors=[{'message': _("You don't have permission to access this apply")}], status=status.HTTP_400_BAD_REQUEST)

            # Serialize the object to get signature paths
            serializer = GeneralCompanyGetContractDetailsSerializer(apply)
            host_agent_accept_sign_path = serializer.data.get(
                'host_agent_accept_sign_path')
            support_agent_accept_sign_path = serializer.data.get(
                'support_agent_accept_sign_path')
            agency_agent_accept_sign_path = serializer.data.get(
                'agency_agent_accept_sign_path')
            engineer_accept_sign_path = serializer.data.get(
                'engineer_accept_sign_path')
            host_url = request.build_absolute_uri('/')
            # remove last slash
            host_url = host_url+"api"

            if host_agent_accept_sign_path:

                host_agent_accept_sign_path = host_url + host_agent_accept_sign_path + \
                    "?"+str(timezone.now().timestamp())
            if support_agent_accept_sign_path:
                support_agent_accept_sign_path = host_url + support_agent_accept_sign_path + \
                    "?"+str(timezone.now().timestamp())
            if agency_agent_accept_sign_path:
                agency_agent_accept_sign_path = host_url + agency_agent_accept_sign_path + \
                    "?"+str(timezone.now().timestamp())
            if engineer_accept_sign_path:
                engineer_accept_sign_path = host_url + engineer_accept_sign_path + \
                    "?"+str(timezone.now().timestamp())

            if apply:
                # Load HTML file path
                employee_contract_path = EMPLOYEE_CONTRACT_TEMPLATE_PATH

                # Load HTML file content
                with open(employee_contract_path, 'r') as file:
                    html_content = file.read()

                # # Replace placeholders with actual data (signature base64 strings)
                html_content = html_content.replace(
                    '{{ hostCompanySignature }}', host_agent_accept_sign_path or '')
                html_content = html_content.replace(
                    '{{ engineerSignature }}', engineer_accept_sign_path or '')
                html_content = html_content.replace(
                    '{{ supportCompanySignature }}', support_agent_accept_sign_path or '')
                html_content = html_content.replace(
                    '{{ agencyCompanySignature }}', agency_agent_accept_sign_path or '')

                # replace name
                html_content = html_content.replace(
                    '{{ hostCompanyName }}', f"{serializer.data.get("host_company_name")}")
                html_content = html_content.replace(
                    '{{ engineerName }}', f"{serializer.data.get("engineer_first_name")} {serializer.data.get("engineer_last_name")}")
                html_content = html_content.replace(
                    '{{ supportAgentName }}', f"{serializer.data.get("support_company_name")}")
                html_content = html_content.replace(
                    '{{ agencyAgentName }}', f"{serializer.data.get("agency_company_name")}")

                # Convert HTML content to PDF
                pdf_file = pdfkit.from_string(html_content, False)

                # Create a response with the PDF file
                response = HttpResponse(
                    pdf_file, content_type='application/pdf')
                response['Content-Disposition'] = f'attachment; filename="employment_contract_{
                    apply_id}.pdf"'
                return response

            return CustomResponse(errors=[{'message': _('Something went wrong!')}], status=404)

        except RecApply.DoesNotExist:
            return CustomResponse(errors=[{'message': _('Apply not found')}], status=404)

    @swagger_auto_schema(
        method="GET",
        operation_id="get_list_group_chat",
        query_serializer=GeneralCompanyParamGroupChatParamsSerializer,
        responses=BaseResponse(
            status_code=status.HTTP_200_OK,
            message=_('Success'),
            responseModel=GeneralCompanyListGroupChatResponseModel
        ).to_openapi_response()
    )
    @api_view(['GET'])
    @permission_classes([IsCompany, IsEmailVerified])
    def get_list_group_chat(request):
        paginator = CustomCursorTotalCountPagination()
        user = request.user
        company_id = user.company_id
        if company_id is None:
            return CustomResponse(errors=_("You do not own the company"), status=status.HTTP_400_BAD_REQUEST)

        recruits = MapChatGroup.objects.filter(
            user=user
        ).select_related('user', 'chat').distinct()

        paginated_recruits = paginator.paginate_queryset(recruits, request)
        serializer = GroupChatSerializer(
            paginated_recruits, many=True, context={'request': request})

        return CustomResponse(data=paginator.get_paginated_response(serializer.data), status=status.HTTP_200_OK, message=_("Success"))
