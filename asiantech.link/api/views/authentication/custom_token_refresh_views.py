from rest_framework_simplejwt.tokens import RefreshToken, AccessToken
from rest_framework_simplejwt.views import TokenRefreshView

from utils.utils import store_access_token
from django.contrib.auth.models import User
from django.contrib.auth import get_user_model
User = get_user_model()


class CustomTokenRefreshView(TokenRefreshView):
    def post(self, request, *args, **kwargs):

        response = super().post(request, *args, **kwargs)
        access_token = response.data.get('access')
        decode_access_token = AccessToken(access_token)
        user_id = decode_access_token.payload['user_id']
        user = User.objects.get(user_id=user_id)

        # Add user_type to token payload
        response.data['user_type'] = str(user.user_type)
        store_access_token(decode_access_token, user)

        return response
