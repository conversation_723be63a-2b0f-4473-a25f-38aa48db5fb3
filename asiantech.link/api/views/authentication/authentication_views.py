from django.contrib.auth.models import User


import requests
from rest_framework.views import APIView
from django.contrib.auth import get_user_model
from rest_framework.decorators import api_view
from rest_framework import status
from api.serializers.authentications.authentications_serializers import *

from utils.responses import *
from drf_yasg.utils import swagger_auto_schema
from api.services import mail_sender_service
from utils.validators import validate_serializer, check_password_valid, check_email_valid
from django.contrib.auth.base_user import BaseUserManager
from django.utils import timezone as tz
from django.contrib.auth import authenticate
from utils.utils import accept_language_header, get_jwt_token, store_access_token, get_host_url_from_request
from django.utils.crypto import get_random_string
from django.core.cache import cache
from core.settings.common import EMAIL_TEMPLATE_PATH, RESET_PASSWORD_TEMPLATE_PATH, VERIFICATION_LOGIN_PATH, RESET_PASSWORD_REQUEST_TEMPLATE_PATH, EMAIL_REGISTER_SUCCESS_TEMPLATE_PATH
from utils.utils import generate_strong_password, verify_captcha
from core.settings import common
from datetime import datetime, timezone, timedelta
import jwt
import os
from rest_framework.decorators import permission_classes
from rest_framework.permissions import IsAuthenticated
from utils.utils import get_access_token_from_request, invalidate_access_token, blacklist_refresh_token, escape_special_characters, save_avatar_sns
from utils.constants import *
from django.utils.translation import gettext as _
from captcha.models import CaptchaStore
from captcha.helpers import captcha_image_url
import json
import base64
import hmac
import hashlib
from api.serializers.media_serializers import UserMediaSerializer
import logging
logger = logging.getLogger("api_logger")

send_code_response = BaseResponse(
    status_code=status.HTTP_200_OK, message=_("Verification email sent"))

send_whatsapp_response = BaseResponse(
    status_code=status.HTTP_200_OK, message=_("Verification whatsapp sent"))

verify_email_response = BaseResponse(
    status_code=status.HTTP_200_OK, message=_("Email verified successfully"))

register_response = BaseResponse(
    status_code=status.HTTP_201_CREATED, message=_('User created successfully'))

login_response = BaseResponse(status_code=status.HTTP_200_OK, message=_(
    'Login successfully'), schema=TokenSerializer)
reset_password_response = BaseResponse(
    status_code=status.HTTP_200_OK, message=_('Reset password successfully'))
User = get_user_model()

otp_timeout = 60 * 5


class AuthenticationViews(APIView):

    @staticmethod
    def generate_code(key):
        code = get_random_string(length=6, allowed_chars='0123456789')
        cache.set(key, code, timeout=otp_timeout)
        return code

    @staticmethod
    def get_code(key):
        code = cache.get(key)
        return code

    @staticmethod
    def generate_verification_token(email, additional_data=None):
        exp = datetime.now(timezone.utc) + common.MAX_EXPIRED_CODE
        payload = {
            'email': email,
            'exp': exp
        }
        if additional_data:
            payload.update(additional_data)
        token = jwt.encode(payload, os.getenv('SECRET_KEY'), algorithm='HS256')
        return token

    @staticmethod
    def send_verification_email(email, web_link, sns_email=None):
        email_sender = mail_sender_service.EmailSender()
        # send email
        token = AuthenticationViews.generate_verification_token(
            email, {"sns_email": sns_email})
        user = None
        if sns_email:
            user = User.objects.get(email=sns_email)
        else:
            user = User.objects.get(email=email)
        user_type = user.user_type

        link = web_link + f'/verify?token={token}&user_type={user_type}'
        subject = _('emailRegisterVerificationTitle')
        receiver = email
        logoUrl = web_link + '/assets/assets/images/fois-logo.png'
        # Path to your HTML file
        html_template_path = EMAIL_TEMPLATE_PATH
        with open(html_template_path, 'r') as file:
            html_template = file.read()
        content1 = _('emailConfirmContent1')
        content2 = _('emailConfirmContent2')
        content3 = _('emailConfirmContent3')
        content4 = _('emailConfirmContent4')
        replacements = {
            '{{ emailVerification }}': _('Verification Email'),
            '{{ logoUrl }}': logoUrl,
            '{{ verificationLink }}': link,
            '{{ emailConfirmContent1 }}': content1+"\n",
            '{{ emailConfirmContent2 }}':  content2,
            '{{ verifyYourAccount }}': _('verifyYourAccount'),
            '{{ emailConfirmContent3 }}': content3,
            '{{ emailConfirmContent4 }}': content4,
        }

        for key, value in replacements.items():
            html_template = html_template.replace(key, value)
        html_content = html_template
        # Replace placeholder with actual code
        email_sender.send_email(receiver_email=receiver,
                                subject=subject, body=html_content)

    @swagger_auto_schema(
        method='post',
        operation_id='send_code',
        request_body=SendCodeRequestSerializer,
        manual_parameters=[accept_language_header],
        responses=send_code_response.to_openapi_response(),
    )
    @api_view(['POST'])
    def send_email_verify(request):
        serializer = SendCodeRequestSerializer(data=request.data)
        is_valid, error = validate_serializer(serializer)
        if not is_valid:
            return send_code_response.copy(errors=error, status_code=status.HTTP_400_BAD_REQUEST).to_custom_response()

        email = request.data['email']
        captcha_key = request.data.get('captcha_key', '')
        captcha_value = request.data.get('captcha_value', '')
        sns_email = request.data.get('sns_email', None)
        # Check user is verified
        try:
            user = None
            if sns_email:
                user = User.objects.get(email=sns_email)
                if User.objects.filter(email=email).exists():
                    return send_code_response.copy(message=_('Email is already linked to an another account'), status_code=status.HTTP_400_BAD_REQUEST, errors=[
                        {
                            "message": _('Email is already linked to an another account'),
                            "code": None,
                            "field": "email"
                        }
                    ]).to_custom_response()

            else:
                user = User.objects.get(email=email)
        except User.DoesNotExist:
            return send_code_response.copy(message=_('User not found!'), status_code=status.HTTP_404_NOT_FOUND).to_custom_response()
        if sns_email == None:
            if user.auth_type == AuthType.AUTHENTICATED.value:
                return send_code_response.copy(message=_('User is already verified!'), status_code=status.HTTP_400_BAD_REQUEST).to_custom_response()
              # Validate CAPTCHA
        is_valid_captcha = verify_captcha(captcha_key, captcha_value)
        if not is_valid_captcha:
            return CustomResponse(message=_('CAPTCHA verification failed'), status=status.HTTP_400_BAD_REQUEST)
        web_link = get_host_url_from_request(request)
        AuthenticationViews.send_verification_email(email, web_link, sns_email)
        return send_code_response.to_custom_response()

    @swagger_auto_schema(
        method='post',
        operation_id='send_whatsapp_code',
        request_body=SendWhatsappCodeSerializer,
        manual_parameters=[accept_language_header],
        responses=send_code_response.to_openapi_response(),
    )
    @api_view(['POST'])
    def send_whatsapp_code(request):
        serializer = SendWhatsappCodeSerializer(data=request.data)
        is_valid, error = validate_serializer(serializer)
        if not is_valid:
            return send_code_response.copy(errors=error, status_code=status.HTTP_400_BAD_REQUEST).to_custom_response()

        phone_number = request.data['phone_number']
        code = AuthenticationViews.generate_code(phone_number)

        url = f"https://graph.facebook.com/v21.0/{
            Constants.WHATSAPP_BUSINESS_PHONE_NUMBER_ID}/messages"
        headers = {
            "Authorization": f"Bearer {Constants.WHATSAPP_ACCESS_TOKEN}",
            "Content-Type": "application/json"
        }
        payload = {
            "messaging_product": "whatsapp",
            "to": phone_number,
            "type": "template",
            "template":
            {
                "name": "auth",
                "language":
                {
                    "code": "en_US"
                },
                "components": [
                    {
                        "type": "body",
                        "parameters": [
                            {
                                "type": "text",
                                "text": code
                            }
                        ]
                    },
                    {
                        "type": "button",
                        "sub_type": "url",
                        "index": "0",
                        "parameters": [
                            {
                                "type": "text",
                                "text": code
                            }
                        ]
                    }
                ]


            }
        }

        response = requests.post(url, headers=headers, json=payload)
        if response.status_code == 200:
            print("Message sent successfully")
        else:
            print(f"Failed to send message: {
                response.status_code} - {response.text}")
        return send_whatsapp_response.to_custom_response()

    @swagger_auto_schema(
        method='post',
        operation_id='confirm_whatsapp_code',
        request_body=ConfirmWhatsappCodeSerializer,
        manual_parameters=[accept_language_header],
        responses=send_code_response.to_openapi_response(),
    )
    @api_view(['POST'])
    def confirm_whatsapp_code(request):
        serializer = SendWhatsappCodeSerializer(data=request.data)
        is_valid, error = validate_serializer(serializer)
        if not is_valid:
            return send_code_response.copy(errors=error, status_code=status.HTTP_400_BAD_REQUEST).to_custom_response()
        phone_number = request.data['phone_number']
        code = request.data['code']
        codeLocal = AuthenticationViews.get_code(phone_number)
        if codeLocal == code:
            return CustomResponse(message=_('Success'), status=status.HTTP_200_OK)
        else:
            return CustomResponse(message=_('Invalid code!'), status=status.HTTP_400_BAD_REQUEST)

    @swagger_auto_schema(
        method='post',
        operation_id='verify_email',
        request_body=VerifyEmailSerializer,
        responses=verify_email_response.to_openapi_response()
    )
    @api_view(['POST'])
    def verify_email(request):
        token = request.data.get('token')
        # get host api from request

        web_link = get_host_url_from_request(request)

        if not token:
            return verify_email_response.copy(message=_('Token is required!'), status_code=status.HTTP_400_BAD_REQUEST).to_custom_response()
        try:
            decoded = jwt.decode(token, os.getenv(
                'SECRET_KEY'), algorithms=['HS256'])
            email = decoded['email']
            sns_email = decoded.get('sns_email', None)
            exp = datetime.fromtimestamp(decoded['exp'], timezone.utc)
            try:
                user = None
                if sns_email:
                    user = User.objects.get(email=sns_email)
                else:
                    user = User.objects.get(email=email)
            except User.DoesNotExist:
                return verify_email_response.copy(message=_('User not found!'), status_code=status.HTTP_404_NOT_FOUND).to_custom_response()
            if user.auth_type == AuthType.AUTHENTICATED.value and sns_email == None:
                return verify_email_response.copy(message=_('User is already verified!'), status_code=status.HTTP_400_BAD_REQUEST).to_custom_response()

            if datetime.now(timezone.utc) < exp:
                user.auth_type = AuthType.AUTHENTICATED.value
                user.email = email
                user.save()
                email_sender = mail_sender_service.EmailSender()
                # send email
                subject = _('emailRegisterSuccessTitle')
                receiver = email
                logoUrl = web_link + '/assets/assets/images/fois-logo.png'
                # Path to your HTML file
                html_template_path = EMAIL_REGISTER_SUCCESS_TEMPLATE_PATH
                with open(html_template_path, 'r') as file:
                    html_template = file.read()
                content1 = _('emailRegisterSuccessContent1')
                content2 = _('emailRegisterSuccessContent2')
                content4 = _('emailConfirmContent4')
                replacements = {
                    '{{ logoUrl }}': logoUrl,
                    '{{ emailRegisterSuccessContent1 }}': content1,
                    '{{ emailRegisterSuccessContent2 }}':  content2,
                    '{{ emailConfirmContent4 }}': content4,
                }

                for key, value in replacements.items():
                    html_template = html_template.replace(key, value)
                html_content = html_template
                # Replace placeholder with actual code
                email_sender.send_email(receiver_email=receiver,
                                        subject=subject, body=html_content)
                return verify_email_response.to_custom_response()
            else:
                return verify_email_response.copy(message=_('Verification token has expired!'), status_code=status.HTTP_400_BAD_REQUEST).to_custom_response()
        except jwt.ExpiredSignatureError:
            return verify_email_response.copy(message=_('Verification token has expired!'), status_code=status.HTTP_400_BAD_REQUEST).to_custom_response()
        except jwt.InvalidTokenError:
            return verify_email_response.copy(message=_('Invalid verification token!'), status_code=status.HTTP_400_BAD_REQUEST).to_custom_response()

    @swagger_auto_schema(
        method='post',
        operation_id='register',
        request_body=RegisterSerializer,
        manual_parameters=[accept_language_header],
        responses=register_response.to_openapi_response()
    )
    @api_view(['POST'])
    def register(request):

        is_valid, error = validate_serializer(
            RegisterSerializer(data=request.data))
        if not is_valid:
            return register_response.copy(errors=error, status_code=status.HTTP_400_BAD_REQUEST, message=_("Validation errors in your request")).to_custom_response()

        email = request.data['email']
        password = request.data['password']
        email = BaseUserManager.normalize_email(email)
        extra_fields = {
            'is_staff': False,
            'is_superuser': False,
            'is_active': True,
            'user_type': UserType.ENGINEER.value,
            'auth_type': AuthType.IN_PROGRESS.value,
            'job_status': JobStatus.PREPARING.value,
            "updated": tz.now(),
        }

        is_valid_password, error_password = check_password_valid(password)
        if not is_valid_password:
            return register_response.copy(message=error_password, errors=[
                {
                    "message": error_password,
                    "code": None,
                    "field": "password"
                }
            ], status_code=status.HTTP_400_BAD_REQUEST).to_custom_response()

        is_valid_email, error_email = check_email_valid(email)
        if not is_valid_email:
            return register_response.copy(message=error_email,
                                          errors=[
                                              {
                                                  "message": error_email,
                                                  "code": None,
                                                  "field": "email"
                                              }
                                          ], status_code=status.HTTP_400_BAD_REQUEST).to_custom_response()

        userTest = User.objects.filter(email=email).first()
        print(userTest)
        if User.objects.filter(email=email).exists():
            user = User.objects.get(email=email)
            if user.user_type == UserType.ENGINEER.value:
                return register_response.copy(message=_('The email address is already registered with engineer'), status_code=status.HTTP_400_BAD_REQUEST).to_custom_response()
            if user.user_type == UserType.HOST_COMPANY_STAFF.value:
                return register_response.copy(message=_('The email address is already registered with company'), status_code=status.HTTP_400_BAD_REQUEST).to_custom_response()
            return register_response.copy(message=_('Email is already registered!'), status_code=status.HTTP_400_BAD_REQUEST).to_custom_response()
        else:
            new_user = User(email=email, username=email, **extra_fields)
            new_user.set_password(password)
            new_user.is_data_policy_accept = 1,
            new_user.data_policy_accept_date = tz.now()
            new_user.deleted = 0
            new_user.created = tz.now()
            new_user.updated = tz.now()
            new_user.save()
            web_link = get_host_url_from_request(request)
            AuthenticationViews.send_verification_email(email, web_link)
            return register_response.to_custom_response()

    @swagger_auto_schema(
        method='post',
        operation_id='login',
        request_body=LoginSerializer,
        manual_parameters=[accept_language_header],
        responses=BaseResponse(
            status_code=status.HTTP_200_OK,
            message=_('Login successfully'),
            responseModel=LoginSuccessResponseModel
        ).to_openapi_response()
    )
    @api_view(['POST'])
    def login(request):
        login_serializer = LoginSerializer(data=request.data)
        is_valid, error = validate_serializer(login_serializer)
        user_type = login_serializer.validated_data.get('user_type', None)
        if not is_valid:
            return login_response.copy(
                errors=error,
                status_code=status.HTTP_400_BAD_REQUEST,
                message=_("Validation errors in your request")
            ).to_custom_response()

        email = BaseUserManager.normalize_email(request.data['email'])
        password = request.data['password']

        if not User.objects.filter(email=email).exists():
            return login_response.copy(
                message=_('Email address or password does not match'),
                errors=[
                    {
                        "title": _('Login failed.'),
                        "message": _('Email address or password does not match'),
                        "field": None,
                        "code": None
                    }
                ],
                status_code=status.HTTP_404_NOT_FOUND
            ).to_custom_response()

        user = authenticate(request, email=email, password=password)
        if user is None:
            return login_response.copy(
                message=_('The user ID or password is incorrect.'),
                errors=[
                    {
                        "title": _('Login failed.'),
                        "message": _('The user ID or password is incorrect.'),
                        "field": None,
                        "code": None
                    }
                ],
                status_code=status.HTTP_400_BAD_REQUEST
            ).to_custom_response()

        if user.deleted == 1:
            return login_response.copy(
                message=_('Email address or password does not match'),
                errors=[
                    {
                        "title": _('Login failed.'),
                        "message": _('Email address or password does not match'),
                        "field": None,
                        "code": None
                    }
                ],
                status_code=status.HTTP_404_NOT_FOUND
            ).to_custom_response()

        if user.auth_type != AuthType.AUTHENTICATED.value:
            loginSuccessSerializer = LoginSuccessSerializer(
                data={
                    "captcha_image_url": None,
                    "captcha_key": None,
                    "is_email_verified": user.auth_type == AuthType.AUTHENTICATED.value
                }
            )
            loginSuccessSerializer.is_valid()
            return CustomResponse(
                data=loginSuccessSerializer.data,
                status=status.HTTP_200_OK,
                message=_('CAPTCHA verification required')
            )
        if user_type is not None and user.user_type != user_type:
            if user.user_type == UserType.HOST_COMPANY_STAFF.value:
                return CustomResponse(
                    message=_(
                        'The email address is already registered with company'),
                    status=status.HTTP_400_BAD_REQUEST
                )
            if user.user_type == UserType.ENGINEER.value:
                return CustomResponse(
                    message=_(
                        'The email address is already registered with engineer'),
                    status=status.HTTP_400_BAD_REQUEST
                )
            if user.user_type == UserType.HOST_SUPPORT_AGENCY_STAFF.value:
                return CustomResponse(
                    message=_(
                        'The account with this email has been registered with the receiving support organization.'),
                    status=status.HTTP_400_BAD_REQUEST
                )

        # Check email contain mailsale will return access token for this user
        if 'mailsale' in user.email:
            refresh = get_jwt_token(user)
            access_token = refresh.access_token
            store_access_token(access_token, user)
            return CustomResponse(
                status=status.HTTP_200_OK,
                message=str(access_token),
            )

          # If last login is less than 24 hours, will return access token for this user
        if user.last_login is not None and (tz.now() - user.last_login) < timedelta(hours=24):
            refresh = get_jwt_token(user)
            access_token = refresh.access_token
            access_token['user_type'] = user.user_type
            store_access_token(access_token, user)
            loginSuccessSerializer = LoginSuccessSerializer(
                data={
                    "captcha_image_url": None,
                    "captcha_key": None,
                    "is_email_verified": user.auth_type == AuthType.AUTHENTICATED.value,
                    "refresh": str(refresh),
                    "access": str(access_token)
                }
            )
            loginSuccessSerializer.is_valid()
            return CustomResponse(
                data=loginSuccessSerializer.data,
                status=status.HTTP_200_OK,
                message=_('Login successfully'),
            )

        require_captcha = False
        # TODO: Remove Captcha for production
        # if user.last_login is not None and (tz.now() - user.last_login) < timedelta(minutes=5):
        #     require_captcha = True

        if require_captcha:
            if login_serializer.validated_data['captcha_key'] and login_serializer.validated_data['captcha_value']:
                if not verify_captcha(login_serializer.validated_data['captcha_key'], login_serializer.validated_data['captcha_value']):
                    return CustomResponse(
                        message=_('CAPTCHA verification failed'),
                        status=status.HTTP_400_BAD_REQUEST
                    )
                user.last_login = tz.now()
                user.save(update_fields=['last_login'])
                return AuthenticationViews.send_verification_code_login(email, user, request)

            captcha = CaptchaStore.generate_key()
            image_url = request.build_absolute_uri(captcha_image_url(captcha))
            loginSuccessSerializer = LoginSuccessSerializer(
                data={
                    "captcha_image_url": image_url,
                    "captcha_key": captcha,
                    "is_email_verified": user.auth_type == AuthType.AUTHENTICATED.value
                }
            )
            loginSuccessSerializer.is_valid()
            return CustomResponse(
                data=loginSuccessSerializer.data,
                status=status.HTTP_200_OK,
                message=_('CAPTCHA verification required')
            )

        return AuthenticationViews.send_verification_code_login(email, user, request)

    @swagger_auto_schema(
        method='post',
        operation_id='login_with_sns',
        request_body=LoginWithSNSSerializer,
        manual_parameters=[accept_language_header],
        responses=BaseResponse(
            status_code=status.HTTP_200_OK,
            message=_('Login successfully'),
            responseModel=LoginWithSNSResponseModel
        ).to_openapi_response()
    )
    @api_view(['POST'])
    def login_with_sns(request):
        login_serializer = LoginWithSNSSerializer(data=request.data)
        is_valid, error = validate_serializer(login_serializer)
        if not is_valid:
            return login_response.copy(errors=error, status_code=status.HTTP_400_BAD_REQUEST).to_custom_response()
        sns_type = login_serializer.validated_data.get('sns_type', None)
        code = login_serializer.validated_data.get('code', None)
        code_verifier = login_serializer.validated_data.get(
            'code_verifier', None)
        redirect_uri = login_serializer.validated_data.get(
            'redirect_uri', None)
        whatsapp_number = login_serializer.validated_data.get(
            'whatsapp_number', None)
        user = None
        if sns_type == SNSType.ZALO.value:
            response = requests.post("https://oauth.zaloapp.com/v4/access_token", data={
                "app_id": Constants.ZALO_APP_ID,
                "code": code,
                'grant_type': 'authorization_code',
                'code_verifier': code_verifier
            }, headers={
                'Content-Type': 'application/x-www-form-urlencoded',
                'secret_key': Constants.ZALO_SECRET_KEY
            })
            if response.status_code == 200:
                data = response.json()
                access_token = data.get('access_token', None)
                params = {

                    'fields': 'id,name'
                }
                logger.info(f"zalo accessToken : {access_token}")
                response = requests.get(
                    "https://graph.zalo.me/v2.0/me", params=params, headers={
                        'access_token': f'{access_token}'
                    })
                if response.status_code == 200:
                    data = response.json()
                    logger.info(f"body data zalo : {data}")
                    email = data.get('email', None)
                    name = data.get('name', "")
                    if len(name.split(' ')) > 1:
                        first_name = name.split(' ')[0]
                        last_name = name.split(' ')[1]
                    else:
                        first_name = name
                        last_name = ''
                    image_url = None
                    try:
                        image_url = data['picture']['data']['url']
                    except:
                        image_url = None
                    zalo_id = data['id']

                    if User.objects.filter(zalo_id=zalo_id).exists():
                        user = User.objects.get(zalo_id=zalo_id)
                    else:
                        user = AuthenticationViews.update_or_create_user_from_sns(
                            data={
                                'email': email,
                                'first_name': first_name,
                                'last_name': last_name,
                                'image_url': image_url,
                                'sns_type': sns_type,
                                'zalo_id': zalo_id
                            }
                        )
        elif sns_type == SNSType.FACEBOOK.value:
            redirect_uri = f"{redirect_uri}?sns_type=facebook"
            token_url = "https://graph.facebook.com/v12.0/oauth/access_token"
            params = {
                "client_id": Constants.FACEBOOK_APP_ID,
                "redirect_uri": redirect_uri,
                "client_secret": Constants.FACEBOOK_APP_SECRET,
                "code": code,
            }

            token_response = requests.get(token_url, params=params)
            token_data = token_response.json()
            if token_data.get('access_token', None):
                access_token = token_data.get('access_token', None)
                params = {
                    'access_token': access_token,
                    'fields': 'id,name,picture,email'
                }
                response = requests.get(
                    "https://graph.facebook.com/v2.0/me", params=params)
                if response.status_code == 200:
                    data = response.json()
                    email = data.get('email', None)
                    name = data.get('name', "")
                    if len(name.split(' ')) > 1:
                        first_name = name.split(' ')[0]
                        last_name = name.split(' ')[1]
                    else:
                        first_name = name
                        last_name = ''
                    image_url = data.get('picture', None)
                    facebook_id = data.get('id', None)
                    user = AuthenticationViews.update_or_create_user_from_sns(
                        data={
                            'email': email,
                            'first_name': first_name,
                            'last_name': last_name,
                            'image_url': image_url,
                            'sns_type': sns_type,
                            'facebook_id': facebook_id,
                        }
                    )
                else:
                    return CustomResponse(message=_('Login failed!'), status=status.HTTP_400_BAD_REQUEST)
            else:
                return CustomResponse(message=_('Login failed!'), status=status.HTTP_400_BAD_REQUEST)

        elif sns_type == SNSType.LINKEDIN.value:

            url = "https://www.linkedin.com/oauth/v2/accessToken"
            redirect_uri = f"{redirect_uri}?sns_type=linkedin"
            data = {
                "grant_type": "authorization_code",
                "code": code,
                "redirect_uri": redirect_uri,
                "client_id": Constants.LINKEDIN_CLIENT_ID,
                "client_secret": Constants.LINKEDIN_CLIENT_SECRET
            }
            response = requests.post(url, data=data)

            if response.status_code == 200:
                data = response.json()
                access_token = data.get('access_token', None)
                response = requests.get(
                    f"https://api.linkedin.com/v2/userinfo", headers={"Authorization": f"Bearer {access_token}"})
                if response.status_code == 200:
                    data = response.json()
                    email = data.get('email', None)
                    first_name = data.get('given_name', None)
                    last_name = data.get('family_name', None)
                    image_url = data.get('picture', None)
                    linkedin_id = data.get('sub', None)
                    user = AuthenticationViews.update_or_create_user_from_sns(
                        data={
                            'email': email,
                            'first_name': first_name,
                            'last_name': last_name,
                            'image_url': image_url,
                            'sns_type': sns_type,
                            'linkedin_id': linkedin_id,
                        }
                    )
                else:
                    return CustomResponse(message=_('Login failed!'), status=status.HTTP_400_BAD_REQUEST)
            else:
                return CustomResponse(message=_('Login failed!'), status=status.HTTP_400_BAD_REQUEST)
        elif sns_type == SNSType.WHATSAPP.value:
            url = f"https://graph.facebook.com/v21.0/{
                Constants.WHATSAPP_BUSINESS_PHONE_NUMBER_ID}/messages"
            headers = {
                "Authorization": f"Bearer {Constants.WHATSAPP_ACCESS_TOKEN}",
                "Content-Type": "application/json"
            }
            email_whatsapp = f"whatsapp_{whatsapp_number.replace('+', '')}@{Constants.EMAIL_PRIVATE_DOMAIN}"

            otp_code = AuthenticationViews.generate_code(
                f"confirm_login_{email_whatsapp}")

            payload = {
                "messaging_product": "whatsapp",
                "to": whatsapp_number,
                "type": "template",
                "template":
                {
                    "name": "auth",
                    "language":
                    {
                        "code": "en_US"
                    },
                    "components": [
                        {
                            "type": "body",
                            "parameters": [
                                {
                                    "type": "text",
                                    "text": otp_code
                                }
                            ]
                        },
                        {
                            "type": "button",
                            "sub_type": "url",
                            "index": "0",
                            "parameters": [
                                {
                                    "type": "text",
                                    "text": otp_code
                                }
                            ]
                        }
                    ]
                }
            }

            response = requests.post(url, headers=headers, json=payload)
            if response.status_code == 200:

                whatsapp_id = whatsapp_number.replace('+', '')
                user = AuthenticationViews.update_or_create_user_from_sns(
                    data={
                        'whatsapp_id': whatsapp_id,
                        'sns_type': sns_type,
                    }
                )
                cache.set(f"confirm_login_{user.email}", otp_code, otp_timeout)

                return CustomResponse(message=_('Login successfully'), status=status.HTTP_200_OK, data={
                    "refresh_token": None,
                    "access_token": None,
                    "user_type": None,
                    'email': user.email
                })

            else:
                return CustomResponse(message=_('Login failed!'), status=status.HTTP_400_BAD_REQUEST)
        else:
            return CustomResponse(message=_('Invalid SNS type!'), status=status.HTTP_400_BAD_REQUEST)
        if user is not None:
            refresh, access_token = AuthenticationViews.get_refresh_and_access_token(
                user)

            return CustomResponse(
                message=_('Login successfully'),
                data={
                    'refresh_token': str(refresh),
                    'access_token': str(access_token),
                    'user_type': str(user.user_type),
                    'email': user.email

                }
            )
        else:
            return CustomResponse(message=_('Login failed!'), status=status.HTTP_400_BAD_REQUEST)

    def update_or_create_user_from_sns(data):
        email = data.get('email', None)
        first_name = data.get('first_name', None)
        last_name = data.get('last_name', None)
        image_url = data.get('image_url', None)

        sns_type = data.get('sns_type', None)
        username = email
        zalo_id = data.get('zalo_id', None)
        whatsapp_id = data.get('whatsapp_id', None)
        facebook_id = data.get('facebook_id', None)
        linkedin_id = data.get('linkedin_id', None)

        if sns_type == SNSType.ZALO.value:
            username = f"zalo_{zalo_id}@{Constants.EMAIL_PRIVATE_DOMAIN}"
            email = username
        elif sns_type == SNSType.WHATSAPP.value:
            username = f"whatsapp_{whatsapp_id}@{Constants.EMAIL_PRIVATE_DOMAIN}"
            email = username
        elif sns_type == SNSType.FACEBOOK.value:
            if email is None:
                exist = User.objects.filter(facebook_id=facebook_id).exists()
                if exist:
                    return User.objects.get(facebook_id=facebook_id)
                else:
                    email = f"facebook_{facebook_id}@{Constants.EMAIL_PRIVATE_DOMAIN}"
                    username = email
        elif sns_type == SNSType.LINKEDIN.value:
            if email is None:
                exist = User.objects.filter(linkedin_id=linkedin_id).exists()
                if exist:
                    return User.objects.get(linkedin_id=linkedin_id)
                else:
                    email = f"linkedin_{linkedin_id}@{Constants.EMAIL_PRIVATE_DOMAIN}"
                    username = email
        user = None
        if User.objects.filter(username=username).exists():
            user = User.objects.get(username=username)

        elif User.objects.filter(email=email).exists():
            user = User.objects.get(email=email)

        if user:
            if sns_type == SNSType.ZALO.value:
                user.zalo_id = zalo_id

            elif sns_type == SNSType.WHATSAPP.value:
                user.whatsapp_id = whatsapp_id
            elif sns_type == SNSType.FACEBOOK.value:
                user.facebook_id = facebook_id

            elif sns_type == SNSType.LINKEDIN.value:
                user.linkedin_id = linkedin_id

            if user.auth_type != AuthType.AUTHENTICATED.value:
                user.auth_type = AuthType.AUTHENTICATED.value

            user.save()
            return user
        else:
            extra_fields = {
                'last_name': last_name,
                'first_name': first_name,
                'is_staff': False,
                'is_superuser': False,
                'is_active': True,
                'user_type': UserType.ENGINEER.value,
                'auth_type': AuthType.AUTHENTICATED.value,
                'job_status': JobStatus.PREPARING.value,
                "updated": tz.now(),
                'last_login': tz.now(),
                'email': email,
                'username': username,
                'whatsapp_id': whatsapp_id,
                'zalo_id': zalo_id,
                'facebook_id': facebook_id,
                'linkedin_id': linkedin_id,

            }

            new_user = User(**extra_fields)
            new_user.save()
            if image_url:
                save_avatar_sns(
                    new_user.user_id, image_url)
            return new_user

    @staticmethod
    def send_verification_code_login(email, user, request):
        email_sender = mail_sender_service.EmailSender()
        key = "confirm_login_" + email
        code = AuthenticationViews.generate_code(key)
        subject = _('Your Verification Code')
        receiver = email
        with open(VERIFICATION_LOGIN_PATH, 'r') as file:
            html_template = file.read()
        html_content = html_template.replace(
            '{{ emailVerification }}', _('Email Verification')
        ).replace(
            '{{ emailConfirmLoginContent }}', _(
                'Please use the verification code below to complete your login:')
        ).replace(
            '{{ emailConfirmLoginContentNote }}', _(
                'Note: This code is valid for a limited time.')
        ).replace(
            '{{ code }}', code
        )
        email_sender.send_email(receiver_email=receiver,
                                subject=subject, body=html_content)
        loginSuccessSerializer = LoginSuccessSerializer(data={
            "captcha_image_url": None,
            "captcha_key": None,
            "is_email_verified": user.auth_type == AuthType.AUTHENTICATED.value
        })
        loginSuccessSerializer.is_valid()
        return CustomResponse(
            data=loginSuccessSerializer.data,
            message=_("We sent you a code to verify your login"),
            status=status.HTTP_200_OK
        )

    @swagger_auto_schema(
        method='post',
        operation_id='reset-password',
        request_body=ResetPasswordSerializer,
        manual_parameters=[accept_language_header],
        responses=reset_password_response.to_openapi_response()
    )
    @api_view(['POST'])
    def resetPassword(request):
        is_valid, error = validate_serializer(
            ResetPasswordSerializer(data=request.data))
        if not is_valid:
            return login_response.copy(errors=error, status_code=status.HTTP_400_BAD_REQUEST).to_custom_response()
        email = request.data['email']
        email = BaseUserManager.normalize_email(email)
        if User.objects.filter(email=email).exists():
            email_sender = mail_sender_service.EmailSender()
            # send email
            subject = _('Password Reset Request')
            receiver = email
            # Path to your HTML file
            html_template_path = RESET_PASSWORD_REQUEST_TEMPLATE_PATH
            with open(html_template_path, 'r') as file:
                html_template = file.read()
            token = AuthenticationViews.generate_verification_token(
                email, additional_data={'type': 'reset_password'})
            password_reset_link = Constants.WEB_LINK + \
                f'/reset-password-confirm?token={token}'
            html_content = html_template.replace("{{ Password Reset Request }}", _('Password Reset Request')).replace('{{ Dear }}', _('Dear')).replace('{{ user.username }}', email).replace('{{ password_request_reset_content_1 }}', _('password_request_reset_content_1')).replace(
                '{{ password_request_reset_content_2 }}', _('password_request_reset_content_2')).replace('{{ passwordResetContent2 }}', _("passwordResetContent2")).replace('{{ Thank you }}', _('Thank you')).replace('{{ password_reset_link }}', password_reset_link)

            # Replace placeholder with actual code
            email_sender.send_email(
                receiver_email=receiver, subject=subject, body=html_content)
            return CustomResponse(message=_('Password reset email sent!'), status=status.HTTP_200_OK)
        else:
            return CustomResponse(message=_('User not found!'), status=status.HTTP_404_NOT_FOUND)

    @swagger_auto_schema(
        method='post',
        operation_id='reset-password-confirm',
        request_body=ResetPasswordConfirmSerializer,
        manual_parameters=[accept_language_header],
        responses=reset_password_response.to_openapi_response()
    )
    @api_view(['POST'])
    def reset_password_confirm(request):
        is_valid, error = validate_serializer(
            ResetPasswordConfirmSerializer(data=request.data))
        if not is_valid:
            return login_response.copy(errors=error, status_code=status.HTTP_400_BAD_REQUEST).to_custom_response()
        token = request.data['token']
        try:
            decoded = jwt.decode(token, os.getenv(
                'SECRET_KEY'), algorithms=['HS256'])
        except jwt.ExpiredSignatureError:
            return CustomResponse(message=_('Verification token has expired!'), status=status.HTTP_400_BAD_REQUEST)

        email = decoded['email']
        exp = datetime.fromtimestamp(decoded['exp'], timezone.utc)
        email = BaseUserManager.normalize_email(email)

        # check if token already used
        if cache.get('reset_password_confirm_token_'+email+"_"+token):
            return CustomResponse(message=_('Verification token has expired!'), status=status.HTTP_400_BAD_REQUEST)

        if User.objects.filter(email=email).exists():
            type = decoded['type']
            if type != 'reset_password':
                return reset_password_response.copy(message=_('Invalid token type!'), status_code=status.HTTP_400_BAD_REQUEST).to_custom_response()
            if datetime.now(timezone.utc) < exp:
                user = User.objects.get(email=email)
                new_password = generate_strong_password()
                user.set_password(new_password)
                email_sender = mail_sender_service.EmailSender()
                # send email
                subject = _('Your Password Reset Request')
                receiver = email
                # Path to your HTML file
                html_template_path = RESET_PASSWORD_TEMPLATE_PATH
                with open(html_template_path, 'r') as file:
                    html_template = file.read()
                email_title = _('Password Reset')
                escape_password = escape_special_characters(new_password)
                html_content = html_template.replace('{{ passwordResetRequest }}', _('Password Reset Request')).replace('{{ hello }}', _('Hello') + ",").replace('{{ passwordResetContent1 }}', _('passwordResetContent1')).replace(
                    '{{ password }}', escape_password).replace('{{ passwordResetContent2 }}', _("passwordResetContent2")).replace('{{ passwordResetContent3 }}', _('passwordResetContent3')).replace('{{ passwordReset }}', email_title)

                # Replace placeholder with actual code
                email_sender.send_email(
                    receiver_email=receiver, subject=subject, body=html_content)
                # save password
                user.save()
                cache.set('reset_password_confirm_token_' +
                          email+"_"+token, token, timeout=60*5)
                return CustomResponse(message=_('Password reset successfully!'), status=status.HTTP_200_OK)
            else:
                return CustomResponse(message=_('Verification token has expired!'), status=status.HTTP_400_BAD_REQUEST)

        else:
            return reset_password_response.copy(message=_('User not found!'), status_code=status.HTTP_404_NOT_FOUND).to_custom_response()

    def get_refresh_and_access_token(user):
        refresh = get_jwt_token(user)
        access_token = refresh.access_token
        access_token['user_type'] = user.user_type
        access_token['email'] = user.email
        store_access_token(access_token, user)
        return refresh, access_token

    @swagger_auto_schema(
        method='post',
        operation_id='confirm-login',
        request_body=ConfirmLoginSerializer,
        manual_parameters=[accept_language_header],
        responses=BaseResponse(message=_(
            "Login successfully"), status_code=status.HTTP_200_OK).to_openapi_response()
    )
    @api_view(['POST'])
    def confirmLogin(request):
        is_valid, error = validate_serializer(
            ConfirmLoginSerializer(data=request.data))
        if not is_valid:
            return CustomResponse(errors=error, status=status.HTTP_400_BAD_REQUEST)
        email = request.data['email']
        code = request.data['code']
        password = request.data['password']
        email = BaseUserManager.normalize_email(email)
        if password is not None or password != "":
            user = authenticate(request, email=email, password=password)
        if user is None:
            user = User.objects.get(email=email)
        if user is not None:
            if User.objects.filter(email=email).exists():
                user = User.objects.get(email=email)
                key = "confirm_login_"+email
                cached_code = cache.get(key)
                if cached_code and cached_code == code:
                    user.last_login = tz.now()
                    user.save(update_fields=['last_login'])
                    refresh, access_token = AuthenticationViews.get_refresh_and_access_token(
                        user)
                    return CustomResponse(data={
                        'refresh': str(refresh),
                        'access': str(access_token),
                        'user_type': str(user.user_type)
                    },
                        status=status.HTTP_200_OK,
                    )
                else:
                    return CustomResponse(message=_('Invalid code or email!'), status=status.HTTP_400_BAD_REQUEST)
            else:
                return CustomResponse(message=_('User not found!'), status=status.HTTP_404_NOT_FOUND)
        else:
            return CustomResponse(message=_('Invalid email or password!'), status=status.HTTP_400_BAD_REQUEST)

    @swagger_auto_schema(
        method='post',
        operation_id='logout',
        request_body=LogoutSerializer,
        manual_parameters=[accept_language_header],
        responses=BaseResponse(message=_(
            "Logout successfully"), status_code=status.HTTP_200_OK).to_openapi_response()
    )
    @permission_classes([IsAuthenticated])
    @api_view(['POST'])
    def logout(request):
        is_valid, error = validate_serializer(
            LogoutSerializer(data=request.data))
        if not is_valid:
            return CustomResponse(errors=error, status=status.HTTP_400_BAD_REQUEST)
        user = request.user
        accessToken = get_access_token_from_request(request)
        refreshToken = request.data['refresh']
        invalidate_access_token(user, accessToken)
        blacklist_refresh_token(refreshToken)
        return CustomResponse(message=_('Logout successfully'), status=status.HTTP_200_OK)

    @swagger_auto_schema(
        method='get',
        operation_id='generate-captcha',
        manual_parameters=[accept_language_header],
        responses=BaseResponse(message=_("Captcha generated successfully"),
                               status_code=status.HTTP_200_OK, responseModel=CaptchaResponseModel).to_openapi_response()
    )
    @api_view(['GET'])
    def generate_captcha(request):
        captcha = CaptchaStore.generate_key()
        image_url = captcha_image_url(captcha)
        # Constructing full URL
        full_url = request.build_absolute_uri(image_url)
        serializers = CaptchaSerializer(
            data={'key': captcha, 'image_url': full_url})
        if not serializers.is_valid():
            return CustomResponse(errors=serializers.errors, status=status.HTTP_400_BAD_REQUEST)
        return CustomResponse(data=serializers.data, status=status.HTTP_200_OK, message=_('Captcha generated successfully'))

    @swagger_auto_schema(
        method='post',
        operation_id='check_captcha_required_in_login',
        request_body=CheckCaptchaRequireInLoginSerializer,
        manual_parameters=[accept_language_header],
        responses=BaseResponse(message=_("Check captcha required in login"), status_code=status.HTTP_200_OK,
                               responseModel=CheckCaptchaRequireInLoginResponseModel).to_openapi_response()
    )
    @api_view(['POST'])
    def check_captcha_required_in_login(request):
        serializer = CheckCaptchaRequireInLoginSerializer(data=request.data)
        is_valid, error = validate_serializer(serializer)
        if not is_valid:
            return CustomResponse(errors=error, status=status.HTTP_400_BAD_REQUEST)
        email = request.data['email']
        try:
            user = User.objects.get(email=email)
            require_captcha = False
            if user.last_login is not None:
                last_login_time = user.last_login
                current_time = tz.now()
                elapsed_time = current_time - last_login_time
                if elapsed_time < timedelta(minutes=5):
                    require_captcha = True
            responseSerializer = CaptchaAndUserStatusSerializer(
                data={'require_captcha': require_captcha, 'is_user_verified': user.auth_type == AuthType.AUTHENTICATED.value})
            if not responseSerializer.is_valid():
                return CustomResponse(errors=responseSerializer.errors, status=status.HTTP_400_BAD_REQUEST)
            return CustomResponse(data=responseSerializer.data, status=status.HTTP_200_OK)
        except User.DoesNotExist:
            return CustomResponse(message=_('User not found!'), status=status.HTTP_404_NOT_FOUND)

    # Resend verification email for Facebook user

    @swagger_auto_schema(
        method='post',
        operation_id='resend_verification_email',
        request_body=SendCodeRequestSerializer,
        manual_parameters=[accept_language_header],
        responses=send_code_response.to_openapi_response(),
    )
    @api_view(['POST'])
    def resend_verification_email(request):
        serializer = SendCodeRequestSerializer(data=request.data)
        is_valid, error = validate_serializer(serializer)
        if not is_valid:
            return send_code_response.copy(errors=error, status_code=status.HTTP_400_BAD_REQUEST).to_custom_response()

        email = request.data['email']
        email = BaseUserManager.normalize_email(email)

        user = User.objects.get(email=email)

        AuthenticationViews.send_verification_code_login(
            email, user, request)
        return send_code_response.to_custom_response()

    # Delete account

    @swagger_auto_schema(
        method='delete',
        operation_id='delete_account',
        manual_parameters=[accept_language_header],
        responses=BaseResponse(
            status_code=status.HTTP_200_OK,
            message=_('Account deleted successfully')
        ).to_openapi_response()
    )
    @permission_classes([IsAuthenticated])
    @api_view(['DELETE'])
    def delete_account(request):
        user = request.user
        user.deleted = True
        user.username = user.username + '_deleted'
        user.email = user.email + '_deleted'
        user.facebook_id = None
        user.linkedin_id = None
        user.whatsapp_id = None
        user.zalo_id = None
        user.save(update_fields=['deleted', 'email', 'username',
                                 'facebook_id', 'linkedin_id', 'whatsapp_id', 'zalo_id'])

        return CustomResponse(message=_('Account deleted successfully'), status=status.HTTP_200_OK)

    # Delete account
    @swagger_auto_schema(
        method='post',
        operation_id='data_deletion_request',
        manual_parameters=[accept_language_header],
        responses=BaseResponse(
            status_code=status.HTTP_200_OK,
            message=_('Account deleted successfully')
        ).to_openapi_response()
    )
    @api_view(['POST'])
    def data_deletion_request(request):
        if request.method == 'POST':
            signed_request = request.POST.get('signed_request')
            data = AuthenticationViews.parse_signed_request(signed_request)
            user_id = data.get('user_id')
            user = User.objects.get(id=user_id)
            user.deleted = True
            user.username = user.username + '_deleted'
            user.email = user.email + '_deleted'
            user.facebook_id = None
            user.linkedin_id = None
            user.whatsapp_id = None
            user.zalo_id = None
            user.save(update_fields=['deleted', 'email', 'username',
                                     'facebook_id', 'linkedin_id', 'whatsapp_id', 'zalo_id'])
            return CustomResponse(message=_('Account deleted successfully'), status=status.HTTP_200_OK)

        return CustomResponse(message=_('error'), status=status.HTTP_400_BAD_REQUEST)

    def parse_signed_request(signed_request):
        encoded_sig, payload = signed_request.split('.', 1)

        secret = "998e09cb442e85b2c04d350ef2df30be"  # Use your app secret here

        # decode the data
        sig = AuthenticationViews.base64_url_decode(encoded_sig)
        data = json.loads(AuthenticationViews.base64_url_decode(payload))

        # confirm the signature
        expected_sig = hmac.new(
            secret.encode(), payload.encode(), hashlib.sha256).digest()
        if sig != expected_sig:
            raise ValueError('Bad Signed JSON signature!')

        return data

    def base64_url_decode(input):
        input += '=' * (4 - len(input) % 4)  # Pad with '=' characters
        return base64.b64decode(input.translate(str.maketrans('-_', '+/')))
