from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import IsAuthenticated
from rest_framework import status
from django.utils.translation import gettext as _
from django.utils import timezone as tz
from drf_yasg.utils import swagger_auto_schema
from utils.permissions import IsEmailVerified
from utils.responses import CustomResponse, BaseResponse
from api.models.chat import RecChatGroup, RecChat, MapChatGroup
from api.serializers.chat.chat_serializers import (
    MarkReadAllMessagesResponseModel,
)


class ChatViews:

    @swagger_auto_schema(
        method='POST',
        operation_id='mark_read_all_messages',
        responses=BaseResponse(
            status_code=status.HTTP_200_OK,
            message=_('All messages marked as read'),
            responseModel=MarkReadAllMessagesResponseModel
        ).to_openapi_response()
    )
    @api_view(['POST'])
    @permission_classes([IsAuthenticated, IsEmailVerified])
    def mark_read_all_messages(request, group_id):
        """
        Mark all messages in a chat group as read for the current user
        """
        user = request.user

        # Check if group exists
        try:
            group = RecChatGroup.objects.get(group_id=group_id)
        except RecChatGroup.DoesNotExist:
            return CustomResponse(message=_('Group does not exist.'), status=status.HTTP_404_NOT_FOUND)

        # Check if user is a member of this group
        try:
            map_chat_group = MapChatGroup.objects.get(group=group, user=user)
        except MapChatGroup.DoesNotExist:
            return CustomResponse(message=_('User is not a member of this group.'), status=status.HTTP_403_FORBIDDEN)

        # Get the latest message in the group
        latest_chat = RecChat.objects.filter(
            group=group).order_by('created').last()

        if latest_chat is not None:
            # Update the user's read status to the latest message
            map_chat_group.chat_id = latest_chat.chat_id
            map_chat_group.updated = tz.now()
            map_chat_group.save()

            return CustomResponse(status=status.HTTP_200_OK, message=_('All messages marked as read'))
        else:
            # No messages in the group
            return CustomResponse(status=status.HTTP_200_OK, message=_('No messages to mark as read'))
