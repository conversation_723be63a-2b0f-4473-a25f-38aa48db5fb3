
from utils.constants import *
from api.serializers.host_support_agency_company.host_support_agency_company_serializers import *


from django.contrib.auth.models import User
from rest_framework.permissions import IsAuthenticated
from rest_framework.views import APIView
from django.contrib.auth import get_user_model
from rest_framework.decorators import api_view
from rest_framework import status
from api.serializers.engineers.user_serializers import *
from utils.responses import BaseResponse, CustomResponse
from rest_framework.decorators import api_view, permission_classes
from drf_yasg.utils import swagger_auto_schema
from django.utils.translation import gettext as _
from api.models.rec import *
from api.serializers.recruitment_serializers import ListRecruitUploadedResponseModel, RecruitUploadedSerializers
from utils.custom_pagination import CustomCursorPagination, CustomCursorTotalCountPagination
from django.utils import timezone
from api.models.map_hst_sup import MapHstSup
from utils.permissions import IsHostSupportAgencyCompany, IsEmailVerified
from api.models.chat import RecChat, MapChatGroup
from api.services.notify_service.engineer_notify_service import EngineerNotifyService
from api.serializers.general_company.general_company_serializers import GeneralCompanyRequestInterviewApplyResponseModel
User = get_user_model()


class HostSupportAgencyCompanyView(APIView):
    @swagger_auto_schema(
        method='get',
        operation_id='get_list_registered_company',
        manual_parameters=[accept_language_header],
        responses=BaseResponse(status_code=status.HTTP_200_OK, message=_(
            "Success"), responseModel=SPListRegisteredCompanyResponseModel).to_openapi_response()
    )
    @api_view(['GET'])
    @permission_classes([IsHostSupportAgencyCompany, IsEmailVerified])
    def get_list_registered_company(request):
        try:
            sp_company_id = request.user.company_id
            if not sp_company_id:
                return CustomResponse(
                    status=status.HTTP_400_BAD_REQUEST,
                    message=_("Company not found")
                )
            sp_company = ComCompany.objects.get(
                company_id=sp_company_id,
                user_type=UserType.HOST_SUPPORT_AGENCY_STAFF.value
            )
            # Get distinct host_company IDs
            host_company_ids = MapHstSup.objects.filter(
                support_company=sp_company,
                deleted=0
            ).values_list('host_company', flat=True).distinct()

            # Fetch corresponding ComCompany objects
            list_company_registered = ComCompany.objects.filter(
                company_id__in=host_company_ids
            )

            # Serialize the ComCompany objects
            return CustomResponse(
                data=SPCompanyRegisteredSerializer(
                    list_company_registered, many=True
                ).data
            )

        except ComCompany.DoesNotExist:
            return CustomResponse(
                status=status.HTTP_400_BAD_REQUEST,
                message=_("Company not found")
            )

    @swagger_auto_schema(
        method='post',
        operation_id='support_request_interview',
        manual_parameters=[accept_language_header],
        request_body=SPRequestInterviewBodySerializer,
        responses=BaseResponse(status_code=status.HTTP_200_OK, message=_(
            "Success"), responseModel=GeneralCompanyRequestInterviewApplyResponseModel).to_openapi_response()
    )
    @api_view(['POST'])
    @permission_classes([IsHostSupportAgencyCompany, IsEmailVerified])
    def request_interview(request):

        body_data = request.data
        token = get_access_token_from_request(request)
        recruit_id = body_data['host_company_recruit_id']
        engineer_id = body_data['engineer_id']
        support_company_id = request.user.company_id
        host_company_id = body_data['host_company_id']
        agency_company_id = Constants.COMPANY_ID_REFERRAL_AGENCY_TEMPLE
        support_company = ComCompany.objects.get(company_id=support_company_id)
        support_agent = request.user
        exist = MapHstSup.objects.filter(
            host_company_id=host_company_id,
            support_company_id=support_company_id,
            deleted=0
        ).exists()
        if not exist:
            return CustomResponse(message=_("Host company is not belong to you"), status=status.HTTP_400_BAD_REQUEST)

        existApply = RecApply.objects.filter(
            engineer_id=engineer_id,
            host_company_id=host_company_id,
        ).exists()
        if existApply:
            return CustomResponse(message=_("This engineer already make a interview to host company"), status=status.HTTP_400_BAD_REQUEST)

        agency_agent = User.objects.filter(
            company_id=agency_company_id, user_type=UserType.REFERRAL_AGENCY_STAFF.value).first()
        host_company_user_id = User.objects.get(
            user_type=UserType.HOST_COMPANY_STAFF.value,
            company_id=host_company_id
        ).user_id

        message = request.data.get('message')
        group = RecChatGroup.objects.create()
        recruit = RecRecruit.objects.get(recruit_id=recruit_id)
        chat = RecChat.objects.create(
            group=group,
            user_id=support_agent.user_id,
            text=message,
            send=timezone.now(),
        )

        # Create MapChatGroup for host company
        MapChatGroup.objects.create(
            group=group,
            user_id=host_company_user_id,
            chat=chat
        )
        # Create MapChatGroup for engineer
        MapChatGroup.objects.create(
            group=group,
            user_id=engineer_id
        )
        # Create MapChatGroup for host support agency
        MapChatGroup.objects.create(
            group=group,
            user_id=support_agent.user_id
        )
        expire_date = timezone.now() + timezone.timedelta(days=365*100)
        try:
            existApply = RecApply.objects.get(
                engineer_id=engineer_id,
                recruit_id=recruit_id,
            )
            if existApply.recruit_progress_code <= RecruitProgressCode.APPLICATION.value:
                existApply.recruit_progress_code = RecruitProgressCode.INTERVIEW_REQUEST.value
                existApply.updated = timezone.now()
                existApply.save()
                EngineerNotifyService().send_notify_company_request_interview(
                    token,  existApply.host_agent, existApply.apply_id)
                response_success_data = {
                    'apply_id': existApply.apply_id
                }
                return CustomResponse(data=response_success_data, status=status.HTTP_200_OK, message=_("Success"))
            else:
                return CustomResponse(message=_("You have requested an interview for this engineer."), status=status.HTTP_400_BAD_REQUEST)
        except RecApply.DoesNotExist:
            pass

        rec_apply = RecApply.objects.create(
            recruit=recruit,
            group=group,
            engineer_id=engineer_id,
            agency_company_id=agency_company_id,
            agency_agent_id=agency_agent.user_id,
            host_company=recruit.host_company,
            host_agent=recruit.host_agent,
            recruit_progress_code=RecruitProgressCode.INTERVIEW_REQUEST.value,
            progress_update_datetime=timezone.now(),
            job_code=recruit.job_code,
            employ_code=recruit.employ_code,
            payroll_code=recruit.payroll_code,
            expiry_date=expire_date,
            support_company=support_company,
            support_agent=support_agent,
            is_from_support=1,

        )

        EngineerNotifyService().send_notify_company_request_interview(
            token, rec_apply.host_agent, rec_apply.apply_id)

        return CustomResponse(message=_("Success"), status=status.HTTP_200_OK)

    @swagger_auto_schema(
        method='get',
        operation_id='get_list_recruit_of_host_company',
        manual_parameters=[accept_language_header],
        query_serializer=SPListRecruitOfHostCompanyParams,
        responses=BaseResponse(status_code=status.HTTP_200_OK, message=_(
            "Success"), responseModel=ListRecruitUploadedResponseModel).to_openapi_response()
    )
    @api_view(['GET'])
    @permission_classes([IsHostSupportAgencyCompany, IsEmailVerified])
    def get_list_recruit_of_host_company(request):
        paginator = CustomCursorTotalCountPagination()
        user = request.user
        company_id = user.company_id
        if company_id is None:
            return CustomResponse(errors=_("You do not own the company"), status=status.HTTP_400_BAD_REQUEST)
        host_company_id = request.query_params.get('host_company_id')
        exist = MapHstSup.objects.filter(
            host_company_id=host_company_id,
            support_company_id=company_id,
            deleted=0
        ).exists()
        if not exist:
            return CustomResponse(message=_("Host company is not belong to you"), status=status.HTTP_400_BAD_REQUEST)
        display_flag = 1
        filter = {
            'host_company_id': host_company_id,
        }

        filter['end_date__gte'] = timezone.now()

        if display_flag:
            filter['display_flag'] = display_flag

        recruits = RecRecruit.objects.filter(
            **filter)
        paginated_recruits = paginator.paginate_queryset(recruits, request)
        serializer = RecruitUploadedSerializers(
            paginated_recruits, many=True, context={'request': request})

        return CustomResponse(data=paginator.get_paginated_response(serializer.data), status=status.HTTP_200_OK, message=_("Success"))

    @swagger_auto_schema(
        method='get',
        operation_id='get_list_manage_host_company',
        manual_parameters=[accept_language_header],
        query_serializer=ListManageHostCompanyParams,
        responses=BaseResponse(status_code=status.HTTP_200_OK, message=_(
            "Success"), responseModel=ListManageHostCompanyResponseModel).to_openapi_response()
    )
    @api_view(['GET'])
    @permission_classes([IsAuthenticated])
    def get_list_manage_host_company(request):
        paginator = CustomCursorPagination()
        if request.user.user_type != UserType.HOST_SUPPORT_AGENCY_STAFF.value:
            return CustomResponse(
                status=status.HTTP_400_BAD_REQUEST,
                message=_("You are not authorized to access this endpoint")
            )
        params = request.query_params
        search = params.get('search', None)
        progress_status = params.get('progress_status', None)
        sort_by = params.get('sort_by', None)
        list_companies = MapHstSup.objects.filter(
            support_company=request.user.company_id,
            deleted=0
        ).values_list('host_company', flat=True)

        companies = ComCompany.objects.filter(
            company_id__in=list_companies
        )
        if search is not None and search != '':
            # lowercase search
            search = search.lower()
            companies = companies.filter(
                name__icontains=search
            )
        if progress_status is not None and progress_status != '':
            applies = RecApply.objects.filter(
                recruit_progress_code=int(progress_status),
                host_company_id__in=list_companies,
                support_company_id=request.user.company_id,
                support_agent=request.user
            )
            companies = companies.filter(
                company_id__in=applies.values_list(
                    'host_company_id', flat=True)
            )
            # remove duplicate
            companies = companies.distinct()

        paginated_recruits = paginator.paginate_queryset(companies, request)
        serialized_companies = ManageHostCompanySerializer(
            paginated_recruits, many=True, context={'request': request}).data
        # Apply sorting manually
        if sort_by:
            reverse = sort_by.startswith('-')
            key = 'message_count' if sort_by.lstrip(
                '-') == 'message_count' else None

            if key:
                serialized_companies = sorted(
                    serialized_companies, key=lambda x: x[key], reverse=reverse
                )

        return CustomResponse(data=paginator.get_paginated_response(serialized_companies), status=status.HTTP_200_OK, message=_("Success"))
