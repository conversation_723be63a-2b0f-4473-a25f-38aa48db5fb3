

import logging
from api.serializers.engineers.self_assessment_serializer import SelfAssessmentAnswerSerializer, SelfAssessmentResponseModel
from utils.utils import *

from django.db.models.functions import Now

from utils.utils import blacklist_all_refresh_token
from django.contrib.auth.models import User
from rest_framework.permissions import IsAuthenticated
from rest_framework.views import APIView
from django.contrib.auth import get_user_model
from rest_framework.decorators import api_view
from rest_framework import status
from api.serializers.engineers.user_serializers import *
from utils.responses import BaseResponse, CustomResponse
from rest_framework.decorators import api_view, permission_classes
from drf_yasg.utils import swagger_auto_schema
from django.utils.translation import gettext as _
from utils.utils import invalidate_all_access_token
from utils.permissions import IsEmailVerified
from utils.validators import validate_serializer
from django.contrib.auth import authenticate
from utils.validators import validate_serializer, check_password_valid
from api.serializers.admin.admin_serializers import *
from utils.validators import validate_serializer
from django.db.models import Func
import zipfile
from api.views.admin.admin_views import AdminViews
from datetime import datetime
from django.http import HttpResponse
import io

User = get_user_model()
logger = logging.getLogger("api_logger")
profile_response = BaseResponse(status_code=status.HTTP_200_OK, message=_(
    'Success'), responseModel=UserDetailsResponseModel)


class ExtractYear(Func):
    function = 'YEAR'
    template = '%(function)s(%(expressions)s)'


class ProfileView(APIView):

    @swagger_auto_schema(
        method='PATCH',
        operation_id='update_profile',
        request_body=UpdateUserSerializer,
        responses=BaseResponse(status_code=status.HTTP_200_OK, message=_(
            'Success'), responseModel=UpdateUserResponseModel).to_openapi_response()
    )
    @swagger_auto_schema(
        method='get',
        operation_id='get_profile',
        query_serializer=GetUserProfileParamsSerializer,
        responses=profile_response.to_openapi_response()
    )
    @api_view(['GET', 'PATCH'])
    @permission_classes([IsAuthenticated, IsEmailVerified])
    def profile_details(request):
        user = request.user
        if request.method == 'GET':

            optional_engineer_id = request.query_params.get(
                'optional_engineer_id', None)
            if user.user_type == UserType.MANAGEMENT_ACCOUNT.value and optional_engineer_id is not None:
                user = User.objects.get(pk=optional_engineer_id)

            serializer = UserDetailsSerializers(user)
            return profile_response.copy(data=serializer.data).to_custom_response()
        elif request.method in ["PATCH"]:
            experiences = request.data.pop('experiences', None)
            requirement = request.data.pop('requirements', None)

            optional_engineer_id = request.data.pop(
                'optional_engineer_id', None)
            if user.user_type == UserType.MANAGEMENT_ACCOUNT.value and optional_engineer_id is not None:
                user = User.objects.get(pk=optional_engineer_id)

            # Check if the email is different from the current email and temp_email
            new_email = request.data.get('email_temp', None)
            if user.email_temp is not None and new_email is not None and user.email_temp != new_email and (User.objects.filter(email=new_email).exists() or User.objects.filter(email_temp=new_email).exists()):
                return CustomResponse(message=_('This email is already in use.'), status=status.HTTP_400_BAD_REQUEST)

            serializer = UpdateUserSerializer(
                user, data=request.data, partial=True)
            is_valid, error = validate_serializer(serializer)
            if not is_valid:
                return CustomResponse(errors=error, status=status.HTTP_400_BAD_REQUEST)
            user_data = serializer.save()
            if experiences is not None:
                EngCareer.objects.filter(engineer=user).delete()
                CareerJobSkill.objects.filter(engineer=user).delete()

            # Iterate through the new experiences and create new EngCareer and CareerJobSkill objects
            if experiences:
                experiences = sorted(
                    experiences, key=lambda x: x['entering_date'], reverse=True)
                for experience in experiences:

                    career_job_skills = experience.pop(
                        'career_job_skills', None)
                    # Create new career
                    if experience.get('job_code', None) is None:
                        experience['job_code'] = Constants.DEFAULT_JOB_CODE
                    career = EngCareer.objects.create(
                        engineer=user, **experience)
                    if career_job_skills is not None:
                        for career_job_skill in career_job_skills:
                            career_job_skill['career'] = career
                            career_job_skill['engineer'] = user
                            CareerJobSkill.objects.create(**career_job_skill)
            if requirement:
                hope_job_skills = requirement.pop('job_skills', None)
                payroll_price = requirement.get('payroll_price', None)
                payroll_code = requirement.get('payroll_code', None)
                if payroll_price and payroll_code:
                    payroll_price_usd = currency_converter.convert(
                        payroll_price, payroll_code, 'USD')
                    requirement['payroll_price_usd'] = payroll_price_usd
                EngHope.objects.filter(engineer=user).delete()
                EngHope.objects.create(
                    engineer=user, **requirement)
                if hope_job_skills is not None:
                    HopeJobSkill.objects.filter(
                        engineer=user).delete()
                for hope_job_skill in hope_job_skills:
                    hope_job_skill['engineer'] = user
                    HopeJobSkill.objects.update_or_create(**hope_job_skill)
            # update self assessment
            self_assesment = request.data.pop('self_assesment', None)
            if self_assesment:
                EngSelfAssesment.objects.update_or_create(
                    engineer=user,  # Assuming `user` is the engineer instance
                    defaults=self_assesment
                )

            return profile_response.copy(data=UserDetailsSerializers(user_data).data).to_custom_response()

    @swagger_auto_schema(
        method='put',
        operation_id='update-password',
        responses=BaseResponse(message=_('Password updated successfully'),
                               status_code=status.HTTP_200_OK).to_openapi_response(),
        request_body=UpdatePasswordSerializer,
    )
    @api_view(['PUT'])
    @permission_classes([IsAuthenticated, IsEmailVerified])
    def update_password(request):

        serializer = UpdatePasswordSerializer(data=request.data)
        is_valid, errors = validate_serializer(serializer)
        if not is_valid:
            return CustomResponse(errors=errors, status=status.HTTP_400_BAD_REQUEST)
        current_password = serializer.validated_data['current_password']
        new_password = serializer.validated_data['new_password']

        if current_password == new_password:
            return CustomResponse(message=_('New password must be different from the current password.'), status=status.HTTP_400_BAD_REQUEST)

        is_valid_new_password, errorValidNewPassword = check_password_valid(
            new_password)
        if is_valid_new_password == False:
            return CustomResponse(message=errorValidNewPassword, status=status.HTTP_400_BAD_REQUEST)

        user = authenticate(username=request.user.username,
                            password=current_password)

        if user is not None:
            # Check if the new password is different from the current one
            if not user.check_password(new_password):
                # Update the user's password
                # Blacklist all stored refresh tokens for this user
                user.set_password(new_password)
                user.save()
                blacklist_all_refresh_token(request.user)
                invalidate_all_access_token(user)

                return CustomResponse(message=_('Password updated successfully.'), status=status.HTTP_200_OK)
            else:
                return CustomResponse(message=_('New password must be different from the current password.'), status=status.HTTP_400_BAD_REQUEST)
        else:
            return CustomResponse(message=_('Invalid current password.'), status=status.HTTP_400_BAD_REQUEST)

    @swagger_auto_schema(
        method='get',
        operation_id='get_remote_work_skills',
        responses=BaseResponse(status_code=status.HTTP_200_OK, message=_(
            'Success'), responseModel=SelfAssessmentResponseModel).to_openapi_response()
    )
    @api_view(['GET'])
    @permission_classes([IsAuthenticated, IsEmailVerified])
    def get_remote_work_skills(request):
        try:
            user = request.user

            # Fetch the user's self-assessment data
            self_assessment = EngSelfAssesment.objects.filter(
                engineer=user).first()

            # Define the path to the JSON file
            file_path = os.path.join(
                settings.BASE_DIR, 'data_question', 'remote_work_skills.json')

            # Load the JSON data from the file
            with open(file_path, 'r', encoding='utf-8') as json_file:
                json_data = json.load(json_file)

            # Shuffle the options for each question
            for index, question in enumerate(json_data['questions']):

                # Add 'is_expanded' field and set it to False
                question['is_expanded'] = False

                # Check if option (index + 1) == score with self_assessment communication_skills_$index
                score_key = f'remote_skill_{index + 1}'
                if hasattr(self_assessment, score_key):
                    score = getattr(self_assessment, score_key)
                    for option_index, option in enumerate(question['options']):
                        if option_index + 1 == score:
                            option['is_selected'] = True
                        else:
                            option['is_selected'] = False

                # Shuffle the options
                random.shuffle(question['options'])

            return CustomResponse(data=json_data, status=status.HTTP_200_OK)
        except User.DoesNotExist:
            return CustomResponse(message=_('User not found'), status=status.HTTP_404_NOT_FOUND)

    # Input list select option after that check uuid with json data if have store score to db
    @swagger_auto_schema(
        method='post',
        operation_id='save_remote_work_skills',
        request_body=SelfAssessmentAnswerSerializer,
        responses=BaseResponse(status_code=status.HTTP_200_OK, message=_(
            'Success')).to_openapi_response()
    )
    @api_view(['POST'])
    @permission_classes([IsAuthenticated, IsEmailVerified])
    def save_remote_work_skills(request):
        user = request.user
        serializer = SelfAssessmentAnswerSerializer(data=request.data)
        if not serializer.is_valid():
            return CustomResponse(errors=serializer.errors, status=status.HTTP_400_BAD_REQUEST)

        # Get request data is a list of selected options
        list_answer = serializer.validated_data['list_answer']

        # Get the user's self-assessment data or create a new one if it doesn't exist add created date
        self_assessment, created = EngSelfAssesment.objects.get_or_create(
            engineer=user, defaults={'created': Now()})
        # If the object already exists, update the 'updated' field
        if not created:
            self_assessment.updated = Now()
        # Get the JSON data
        file_path = os.path.join(
            settings.BASE_DIR, 'data_question', 'remote_work_skills.json')
        with open(file_path, 'r', encoding='utf-8') as json_file:
            json_data = json.load(json_file)
            # list_answer position is index of json_data['questions']
            for index, answer in enumerate(list_answer):
                question = json_data['questions'][index]
                if answer == "":
                    score = None
                else:
                    selected_option = next(
                        (option for option in question['options'] if option['uuid'] == answer), None)
                    if selected_option:
                        score = selected_option.get('score')
                setattr(self_assessment, f'remote_skill_{
                        index + 1}', score)

        self_assessment.save()
        return CustomResponse(data={'message': 'Success'}, status=status.HTTP_200_OK)

    @swagger_auto_schema(
        method='get',
        operation_id='get_global_responsiveness_skills',
        responses=BaseResponse(status_code=status.HTTP_200_OK, message=_(
            'Success'), responseModel=SelfAssessmentResponseModel).to_openapi_response()
    )
    @api_view(['GET'])
    @permission_classes([IsAuthenticated, IsEmailVerified])
    def get_global_responsiveness_skills(request):
        try:
            user = request.user

            # Fetch the user's self-assessment data
            self_assessment = EngSelfAssesment.objects.filter(
                engineer=user).first()

            # Define the path to the JSON file
            file_path = os.path.join(
                settings.BASE_DIR, 'data_question', 'global_responsiveness_skills.json')

            # Load the JSON data from the file
            with open(file_path, 'r', encoding='utf-8') as json_file:
                json_data = json.load(json_file)

            # Shuffle the options for each question
            for index, question in enumerate(json_data['questions']):

                # Add 'is_expanded' field and set it to False
                question['is_expanded'] = False

                # Check if option (index + 1) == score with self_assessment communication_skills_$index
                score_key = f'global_skill_{index + 1}'
                if hasattr(self_assessment, score_key):
                    score = getattr(self_assessment, score_key)
                    for option_index, option in enumerate(question['options']):
                        if option_index + 1 == score:
                            option['is_selected'] = True
                        else:
                            option['is_selected'] = False

                # Shuffle the options
                random.shuffle(question['options'])

            return CustomResponse(data=json_data, status=status.HTTP_200_OK)
        except User.DoesNotExist:
            return CustomResponse(message=_('User not found'), status=status.HTTP_404_NOT_FOUND)

    # Input list select option after that check uuid with json data if have store score to db
    @swagger_auto_schema(
        method='post',
        operation_id='save_global_responsiveness_skills',
        request_body=SelfAssessmentAnswerSerializer,
        responses=BaseResponse(status_code=status.HTTP_200_OK, message=_(
            'Success')).to_openapi_response()
    )
    @api_view(['POST'])
    @permission_classes([IsAuthenticated, IsEmailVerified])
    def save_global_responsiveness_skills(request):
        user = request.user
        serializer = SelfAssessmentAnswerSerializer(data=request.data)
        if not serializer.is_valid():
            return CustomResponse(errors=serializer.errors, status=status.HTTP_400_BAD_REQUEST)

        # Get request data is a list of selected options
        list_answer = serializer.validated_data['list_answer']

        # Get the user's self-assessment data or create a new one if it doesn't exist add created date
        self_assessment, created = EngSelfAssesment.objects.get_or_create(
            engineer=user, defaults={'created': Now()})
        # If the object already exists, update the 'updated' field
        if not created:
            self_assessment.updated = Now()
        # Get the JSON data
        file_path = os.path.join(
            settings.BASE_DIR, 'data_question', 'global_responsiveness_skills.json')
        with open(file_path, 'r', encoding='utf-8') as json_file:
            json_data = json.load(json_file)
            # list_answer position is index of json_data['questions']
            for index, answer in enumerate(list_answer):
                question = json_data['questions'][index]
                if answer == "":
                    score = None
                else:
                    selected_option = next(
                        (option for option in question['options'] if option['uuid'] == answer), None)
                    if selected_option:
                        score = selected_option.get('score')
                setattr(self_assessment, f'global_skill_{
                        index + 1}', score)

        self_assessment.save()
        return CustomResponse(data={'message': 'Success'}, status=status.HTTP_200_OK)

    @swagger_auto_schema(
        method='get',
        operation_id='get_communication_skills_self_assessment',
        responses=BaseResponse(status_code=status.HTTP_200_OK, message=_(
            'Success'), responseModel=SelfAssessmentResponseModel).to_openapi_response()
    )
    @api_view(['GET'])
    @permission_classes([IsAuthenticated, IsEmailVerified])
    def get_communication_skills_self_assessment(request):
        try:
            user = request.user

            # Fetch the user's self-assessment data
            self_assessment = EngSelfAssesment.objects.filter(
                engineer=user).first()

            # Define the path to the JSON file
            file_path = os.path.join(
                settings.BASE_DIR, 'data_question', 'communication_skills_self_assessment.json')

            # Load the JSON data from the file
            with open(file_path, 'r', encoding='utf-8') as json_file:
                json_data = json.load(json_file)

            # Shuffle the options for each question
            for index, question in enumerate(json_data['questions']):

                # Add 'is_expanded' field and set it to False
                question['is_expanded'] = False

                # Check if option (index + 1) == score with self_assessment communication_skills_$index
                score_key = f'communication_skill_{index + 1}'
                if hasattr(self_assessment, score_key):
                    score = getattr(self_assessment, score_key)
                    for option_index, option in enumerate(question['options']):
                        if option_index + 1 == score:
                            option['is_selected'] = True
                        else:
                            option['is_selected'] = False

                # Shuffle the options
                random.shuffle(question['options'])

            return CustomResponse(data=json_data, status=status.HTTP_200_OK)
        except User.DoesNotExist:
            return CustomResponse(message=_('User not found'), status=status.HTTP_404_NOT_FOUND)

    # Input list select option after that check uuid with json data if have store score to db
    @swagger_auto_schema(
        method='post',
        operation_id='save_communication_skills_self_assessment',
        request_body=SelfAssessmentAnswerSerializer,
        responses=BaseResponse(status_code=status.HTTP_200_OK, message=_(
            'Success')).to_openapi_response()
    )
    @api_view(['POST'])
    @permission_classes([IsAuthenticated, IsEmailVerified])
    def save_communication_skills_self_assessment(request):
        user = request.user
        serializer = SelfAssessmentAnswerSerializer(data=request.data)
        if not serializer.is_valid():
            return CustomResponse(errors=serializer.errors, status=status.HTTP_400_BAD_REQUEST)

        # Get request data is a list of selected options
        list_answer = serializer.validated_data['list_answer']

        # Get the user's self-assessment data or create a new one if it doesn't exist add created date
        self_assessment, created = EngSelfAssesment.objects.get_or_create(
            engineer=user, defaults={'created': Now()})
        # If the object already exists, update the 'updated' field
        if not created:
            self_assessment.updated = Now()
        # Get the JSON data
        file_path = os.path.join(
            settings.BASE_DIR, 'data_question', 'communication_skills_self_assessment.json')
        with open(file_path, 'r', encoding='utf-8') as json_file:
            json_data = json.load(json_file)
            # list_answer position is index of json_data['questions']
            for index, answer in enumerate(list_answer):
                question = json_data['questions'][index]
                if answer == "":
                    score = None
                else:
                    selected_option = next(
                        (option for option in question['options'] if option['uuid'] == answer), None)
                    if selected_option:
                        score = selected_option.get('score')
                setattr(self_assessment, f'communication_skill_{
                        index + 1}', score)

        self_assessment.save()
        return CustomResponse(data={'message': 'Success'}, status=status.HTTP_200_OK)

    @swagger_auto_schema(
        method='get',
        operation_id='get_reporting_consultation_skills_self_evaluation',
        responses=BaseResponse(status_code=status.HTTP_200_OK, message=_(
            'Success'), responseModel=SelfAssessmentResponseModel).to_openapi_response()
    )
    @api_view(['GET'])
    @permission_classes([IsAuthenticated, IsEmailVerified])
    def get_reporting_consultation_skills_self_evaluation(request):
        try:
            user = request.user

            # Fetch the user's self-assessment data
            self_assessment = EngSelfAssesment.objects.filter(
                engineer=user).first()

            # Define the path to the JSON file
            file_path = os.path.join(
                settings.BASE_DIR, 'data_question', 'reporting_consultation_skills_self_evaluation.json')

            # Load the JSON data from the file
            with open(file_path, 'r', encoding='utf-8') as json_file:
                json_data = json.load(json_file)

            # Shuffle the options for each question
            for index, question in enumerate(json_data['questions']):

                # Add 'is_expanded' field and set it to False
                question['is_expanded'] = False

                # Check if option (index + 1) == score with self_assessment communication_skills_$index
                score_key = f'report_skill_{index + 1}'
                if hasattr(self_assessment, score_key):
                    score = getattr(self_assessment, score_key)
                    for option_index, option in enumerate(question['options']):
                        if option_index + 1 == score:
                            option['is_selected'] = True
                        else:
                            option['is_selected'] = False

                # Shuffle the options
                random.shuffle(question['options'])

            return CustomResponse(data=json_data, status=status.HTTP_200_OK)
        except User.DoesNotExist:
            return CustomResponse(message=_('User not found'), status=status.HTTP_404_NOT_FOUND)

    # Input list select option after that check uuid with json data if have store score to db
    @swagger_auto_schema(
        method='post',
        operation_id='save_reporting_consultation_skills_self_evaluation',
        request_body=SelfAssessmentAnswerSerializer,
        responses=BaseResponse(status_code=status.HTTP_200_OK, message=_(
            'Success')).to_openapi_response()
    )
    @api_view(['POST'])
    @permission_classes([IsAuthenticated, IsEmailVerified])
    def save_reporting_consultation_skills_self_evaluation(request):
        user = request.user
        serializer = SelfAssessmentAnswerSerializer(data=request.data)
        if not serializer.is_valid():
            return CustomResponse(errors=serializer.errors, status=status.HTTP_400_BAD_REQUEST)

        # Get request data is a list of selected options
        list_answer = serializer.validated_data['list_answer']

        # Get the user's self-assessment data or create a new one if it doesn't exist add created date
        self_assessment, created = EngSelfAssesment.objects.get_or_create(
            engineer=user, defaults={'created': Now()})
        # If the object already exists, update the 'updated' field
        if not created:
            self_assessment.updated = Now()
        # Get the JSON data
        file_path = os.path.join(
            settings.BASE_DIR, 'data_question', 'reporting_consultation_skills_self_evaluation.json')
        with open(file_path, 'r', encoding='utf-8') as json_file:
            json_data = json.load(json_file)
            # list_answer position is index of json_data['questions']
            for index, answer in enumerate(list_answer):
                question = json_data['questions'][index]
                if answer == "":
                    score = None
                else:
                    selected_option = next(
                        (option for option in question['options'] if option['uuid'] == answer), None)
                    if selected_option:
                        score = selected_option.get('score')
                setattr(self_assessment, f'report_skill_{
                        index + 1}', score)

        self_assessment.save()
        return CustomResponse(data={'message': 'Success'}, status=status.HTTP_200_OK)

    @swagger_auto_schema(
        method='get',
        operation_id='get_project_management_skills_self_evaluation',
        responses=BaseResponse(status_code=status.HTTP_200_OK, message=_(
            'Success'), responseModel=SelfAssessmentResponseModel).to_openapi_response()
    )
    @api_view(['GET'])
    @permission_classes([IsAuthenticated, IsEmailVerified])
    def get_project_management_skills_self_evaluation(request):
        try:
            user = request.user

            # Fetch the user's self-assessment data
            self_assessment = EngSelfAssesment.objects.filter(
                engineer=user).first()

            # Define the path to the JSON file
            file_path = os.path.join(
                settings.BASE_DIR, 'data_question', 'project_management_skills_self_evaluation.json')

            # Load the JSON data from the file
            with open(file_path, 'r', encoding='utf-8') as json_file:
                json_data = json.load(json_file)

            # Shuffle the options for each question
            for index, question in enumerate(json_data['questions']):

                # Add 'is_expanded' field and set it to False
                question['is_expanded'] = False

                # Check if option (index + 1) == score with self_assessment communication_skills_$index
                score_key = f'management_skill_{index + 1}'
                if hasattr(self_assessment, score_key):
                    score = getattr(self_assessment, score_key)
                    for option_index, option in enumerate(question['options']):
                        if option_index + 1 == score:
                            option['is_selected'] = True
                        else:
                            option['is_selected'] = False

                # Shuffle the options
                random.shuffle(question['options'])

            return CustomResponse(data=json_data, status=status.HTTP_200_OK)
        except User.DoesNotExist:
            return CustomResponse(message=_('User not found'), status=status.HTTP_404_NOT_FOUND)

    # Input list select option after that check uuid with json data if have store score to db
    @swagger_auto_schema(
        method='post',
        operation_id='save_project_management_skills_self_evaluation',
        request_body=SelfAssessmentAnswerSerializer,
        responses=BaseResponse(status_code=status.HTTP_200_OK, message=_(
            'Success')).to_openapi_response()
    )
    @api_view(['POST'])
    @permission_classes([IsAuthenticated, IsEmailVerified])
    def save_project_management_skills_self_evaluation(request):
        user = request.user
        serializer = SelfAssessmentAnswerSerializer(data=request.data)
        if not serializer.is_valid():
            return CustomResponse(errors=serializer.errors, status=status.HTTP_400_BAD_REQUEST)

        # Get request data is a list of selected options
        list_answer = serializer.validated_data['list_answer']

        # Get the user's self-assessment data or create a new one if it doesn't exist add created date
        self_assessment, created = EngSelfAssesment.objects.get_or_create(
            engineer=user, defaults={'created': Now()})
        # If the object already exists, update the 'updated' field
        if not created:
            self_assessment.updated = Now()
        # Get the JSON data
        file_path = os.path.join(
            settings.BASE_DIR, 'data_question', 'project_management_skills_self_evaluation.json')
        with open(file_path, 'r', encoding='utf-8') as json_file:
            json_data = json.load(json_file)
            # list_answer position is index of json_data['questions']
            for index, answer in enumerate(list_answer):
                question = json_data['questions'][index]
                if answer == "":
                    score = None
                else:
                    selected_option = next(
                        (option for option in question['options'] if option['uuid'] == answer), None)
                    if selected_option:
                        score = selected_option.get('score')
                setattr(self_assessment, f'management_skill_{
                        index + 1}', score)

        self_assessment.save()
        return CustomResponse(data={'message': 'Success'}, status=status.HTTP_200_OK)

    @swagger_auto_schema(
        method='post',
        operation_description='Download cv',
        operation_id='download_cv',
        responses={
            status.HTTP_200_OK: openapi.Schema(
               type=openapi.TYPE_FILE,
               format=openapi.FORMAT_BINARY
            ),
        },
    )
    @api_view(['POST'])
    @permission_classes([IsAuthenticated, IsEmailVerified])
    def download_cv(request):
        try:
            user = request.user
            engineer_id = user.user_id

            # Export engineer to cv
            cv_template = AdminViews.export_engineer_to_cv(
                engineer_id, request)
            docx_bytes = AdminViews.generate_cv_docx(cv_template, engineer_id)

            # Name file from user name
            engineer = User.objects.get(user_id=engineer_id)
            full_name = f"{engineer.first_name or ''}_{engineer.last_name or ''}".strip(
            ).replace(" ", "_")
            now = datetime.now().strftime("%Y%m%d%H%M%S")
            filename = f"{full_name}_{now}.docx"

            # Return file DOCX
            response = HttpResponse(
                docx_bytes, content_type='application/vnd.openxmlformats-officedocument.wordprocessingml.document')
            response['Content-Disposition'] = f'attachment; filename="{filename}"'
            return response

        except Exception as e:
            return CustomResponse(message=f"Error {str(e)}", status=status.HTTP_400_BAD_REQUEST)
