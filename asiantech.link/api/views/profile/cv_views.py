

from rest_framework.decorators import api_view, permission_classes
from utils.permissions import IsEmailVerified
import tempfile
from django.core.cache import cache
import uuid
from rest_framework.parsers import <PERSON>Part<PERSON>ars<PERSON>, FormParser
from utils.ai_helper import AIHelper
import logging
from utils.utils import *

from django.db.models.functions import Now


from rest_framework.permissions import IsAuthenticated
from rest_framework.views import APIView
from django.contrib.auth import get_user_model

from rest_framework import status
from api.serializers.engineers.user_serializers import *
from utils.responses import BaseResponse, CustomResponse
from drf_yasg.utils import swagger_auto_schema
from django.utils.translation import gettext as _
from api.serializers.cv.cv_serializers import *
from rest_framework.decorators import api_view
from pypdf import PdfReader
import pdfplumber
User = get_user_model()
logger = logging.getLogger("api_logger")
ai_helper = AIHelper()


class CVView(APIView):
    # Ensure correct parser_classes are defined
    parser_classes = (<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, FormParser)
    # Ensure correct permission_classes are defined
    permission_classes = [IsAuthenticated, IsEmailVerified]

    @swagger_auto_schema(
        operation_description='Upload CV PDF',
        operation_id='Upload CV PDF',
        manual_parameters=[
            openapi.Parameter(
                name="cv_pdf",
                in_=openapi.IN_FORM,
                type=openapi.TYPE_FILE,
                required=True,
                description="CV PDF to upload"
            )
        ],
        responses=BaseResponse(status_code=status.HTTP_200_OK, message=_(
            'CV uploaded successfully'), responseModel=UploadCVUploadedResponseSerializer).to_openapi_response()
    )
    def post(self, request, *args, **kwargs):
        uploaded_file = request.data.get('cv_pdf')
        if uploaded_file.size > Constants.MAX_SIZE_CV:
            return CustomResponse(errors=[
                {"message": _('File size must be less than 5MB')}],
                status=status.HTTP_400_BAD_REQUEST)

        cv_text = ""
        with pdfplumber.open(uploaded_file) as pdf:
            for page in pdf.pages:
                cv_text += page.extract_text()

        cv_text_cleaned = re.sub(
            r'[\x00-\x08\x0b\x0c\x0e-\x1f\x7f]', '', cv_text)
        cv_text_cleaned = cv_text_cleaned.strip()
        cv_text = cv_text_cleaned

        qualifications = get_list_qualification()

        prompt = """
Resume text: \n
""" + cv_text + """ \n

I. Primary Task: Evaluate the provided input text.
II. Conditional Logic 1 (If Not a Resume):
    1,  If the input is not a resume, respond with a JSON object.
    2,  JSON format: {"error": 400, "type": "not_a_resume"}
III. Conditional Logic 2 (If It Is a Resume):
    1,  If the input is a resume, proceed to extract candidate profile data.
    2,  Key Fields to Extract:
        -   Contact Information
        -   Work Experience (including company, title, dates, job description)
        -   Education
        -   Skills (both hard and soft)
        -   Certifications and Licenses
        -   Highlight Projects
    3,  Extraction Priority: Focus on keywords that indicate a candidate's professional background.

IV, Extract this resume to json include all fields and give me full result, adhering strictly to the provided schema and rules.

V, Ensure that all list-like descriptions, specifically "experiences.job_description" and "highlight_projects.responsibilities", are formatted as newline-separated bullet points, each prefixed with "- ".

VI, Expected output format:
[INSERT JSON STRUCTURE FROM ABOVE]

        {
    "email": "",
    "first_name": "", // First name
    "last_name": "", // Last name
    "nickname": "",// optional, converted to non-diacritics if Vietnamese
    "sex_type": 2, // 0: Unknown, 1: Male, 2: Female, 3: Other
    "birth_date": "2000-03-08",
    "country_code": "",
    "phone_number": "", // sample: 909090909 not include iso code
    "iso_code_tel": "", // this is iso code, sample: +84
    "address_details": "",
    "address_country_code": "", // extract from address_details, sample: US,VN,JP
    "address_city_name": "", // extract from address_details, sample: Hà Nội, Hồ Chí Minh, Đà Nẵng, Tokyo, Osaka, etc.
    "educations": [
      {

        "school": "",
        "type": 20, // 1: High school, 10: Vocational school, 20: Junior College, 100: University, 200: Graduate School
        "in_date": "YYYY-MM-DD",
        "out_date": "YYYY-MM-DD", // optional if out_date is present leave it null
        "faculty": "",
      }
    ],
    "languages": [
      {
        "language_level_type": 3, // 1: Beginner, 3: Intermediate, 5: Advanced, 7: Native
        "language_code": "", //sample: EN,VI,JA
      }
    ],
    "qualifications": [{
        "licence_code": "", // Map with the sample list
        "licence_name": "",
        "get_date": "YYYY-MM", // sample: 2020-01
        "licence_point": "" // e.g., 9.0 for IELTS, 990 for TOEIC
    }],
    "skills": [
      {
        "skill": "", // Skill Name like Java, Python, React, etc.
        "level_type": integer, // 1: lessThan1Year, 2: lessThan2Years, 3: lessThan3Years, 4: lessThan4Years, 5: lessThan5Years, 6: moreThan5Years
        "category_id" 100: Programming language, 101: Framework, 102: Database, 103: Project management tool, 104: Source control, 105: IDE (Integrated Development Environment), 106: OS, 999: Other skills and tools
      },

    ],
    // experiences mean experience working at company
    "experiences": [
      {
        "company_name": "",
        "job_description": "", // Description of main role at that company. You can overview by responsibility of all project this company.
        "entering_date": "YYYY-MM-DD",
        "quitting_date": "YYYY-MM-DD",
        "role_name": null,
        "career_type": int,   0 : EMPLOYEE, 1: RETIRED
      },
    ],
    "skills_for_cv_display": "",// Analyze the candidate’s list of skills, tools, and technologies, and present them as a clean, readable CV section. Group skills into relevant categories (e.g., Programming Languages, Frameworks, Project Management Tools, Design, Automation, etc.).
                                // For each category, use the format: Category: item1, item2, item3.
                                // Separate each category with a new line. Avoid repetition and keep the content concise yet informative.
                                // Example: Programming Languages: Java, Python, JavaScript, TypeScript, etc.
                                // Example: Frameworks: React, Angular, Vue, etc.
                                // Example: Project Management Tools: Jira, Trello, etc.
                                // Example: Design: Figma, Adobe XD, etc.
                                // Example: Automation: Selenium, Playwright, etc.
    "professional_summary": "",
    "highlight_projects": [
      {
        "name": "",
        "description": "",
        "size": "", // team size
        "role_name": "", // role
        "responsibilities": "",  // As a single string: include newline + "- " before each responsibility. MUST BE EXTRACTED VERBATIM.
        "technology_used": "", // technology used
        "from_date": "YYYY-MM-DD",
        "to_date": "YYYY-MM-DD"
      }
    ]
  }
}
VII, Extract CV data into JSON with these rules:

1. FORMAT:
- Dates: YYYY-MM-DD for full dates, YYYY-MM for months
- Phone:
    - **phone_number**: Extract only the local phone number (e.g., 909090909). Do NOT include the country ISO code.
    - **iso_code_tel**: Extract the country ISO code separately (e.g., +84).
- Vietnamese names: Convert to non-diacritics for nickname
- Empty optional fields: Omit from output
- Required fields: Use null if missing

2. ENUMS:
sex_type: 0=Unknown, 1=Male, 2=Female, 3=Other
education.type: 1=High school, 10=Vocational, 20=Junior College, 100=University, 200=Graduate
language_level_type: 1=Beginner, 3=Intermediate, 5=Advanced, 7=Native
skills.level_type: 1-5=lessThanXYears, 6=moreThan5Years (IMPORTANT: If a skill's level is not explicitly mentioned in the resume, default level_type to 1 (lessThan1Year).)

3. EXPERIENCES VS HIGHLIGHT PROJECTS CLASSIFICATION:
    a, Objective: Accurately classify professional experience into two distinct categories: "experiences" for general company roles and "highlight_projects" for specific project details.
    b, Experiences
        -   This section should detail a candidate's general employment history at a company.
        -   Content: Include information about the company name, a **GENERAL SUMMARY of the main role and overall responsibilities (avoiding specific project details if they are better suited for 'highlight_projects')**, entering and quitting dates, role name (if available), and career type.
        -   When to Use:
            +   If the CV describes a general role and responsibilities at a company, not tied to a specific project.
            +   If the CV clearly states the employment period at a company.
            +   **Key Clarification:** If there are descriptions of *specific projects* at a company, these should be extracted into `highlight_projects`. The `experiences.job_description` should then be a **high-level, concise summary** of the overall role, separate from project specifics.
    c, Highlight Projects
        -   This section should detail specific projects undertaken by the candidate.
        -   Content: Include the project name, description, team size, specific role within the project, responsibilities (formatted as a single string with \\n- before each responsibility), technologies used, and the project's start and end dates.
        -   When to Use:
            +   If the CV contains information about a specific project, including details like project name, technologies used, role in the project, and specific responsibilities.
            +   If a job description follows directly after a company name and/or has a "Project:" heading, **it MUST be treated as a project description and included here.**
            +   If there are multiple projects at the same company, create separate entries in highlight_projects for each project.
        -   Date Handling:
            +   from_date and to_date are required.
            +   If specific project dates aren't provided, use the candidate's employment period at the corresponding company as a reference for these dates.
        -   Responsibility Handling:
            +   If no specific responsibilities are detected for a project, highlight_projects.responsibilities must be null. 
    d,  Prioritization Rule: **CRITICAL: Always prioritize classifying specific, detailed project descriptions into "highlight_projects". Only use "experiences" for general roles not tied to any particular project, or for a very high-level summary if projects are also present for that company.**
    e,  Important Note on Company Dates (for "experiences" when not explicitly stated):
        -   If the CV doesn't explicitly state the working period for a company in the "experiences" section, but there are projects associated with that company in the highlight_projects section, infer the company's employment duration:
            +   entering_date: Use the from_date of the earliest project completed at that company.
            +   quitting_date: Use the to_date of the latest project completed at that company. If the latest project has no to_date, leave quitting_date blank, indicating the candidate is still working there.

# JOB DESCRIPTION AND HIGHLIGHT PROJECT RESPONSIBILITIES SPLITTING:
For each `experiences.job_description` and `highlight_projects.responsibilities` entry:
- Output as a single string with newline-separated bullet items.
- Split the raw CV text for that entry according to:
  1. Bullet markers (“-”, “•”, “*”) if present.
  2. Semicolons (“;”) if present.
  3. Line breaks if present.
  4. Sentence boundaries otherwise, but further split overly long sentences into logical sub-items.
- Trim whitespace; normalize spacing/diacritics.
- Prefix each item with "- " and join with "\\n".
- If no description text exists, set to empty string or omit.

4. QUALIFICATIONS:
   - **Mapping list: {qualifications} ** use the provided list to map exact `licence_name` → `licence_code`; if no match, set `licence_code = null` and keep `licence_name` as extracted.
   - **Known-test maximum scores:** AI must know common certificates’ maximum scores, e.g.:
     - IELTS → 9.0 (format 0.0–9.0 in 0.5 increments)
     - TOEIC (Listening & Reading) → 990 (integer 10–990)
     - TOEFL iBT → 120 (integer 0–120)
     - Duolingo English Test → 160 (integer 10–160)
     - PTE Academic → 90 (integer 10–90)
     - (Add other known tests as needed)
   - **licence_point extraction & default:**
     - If the resume explicitly mentions an achieved score for a known test, and it is within the valid range and correct format (e.g., “IELTS 5.5”, “TOEIC 750”), extract that numeric value as `licence_point` (float for IELTS, integer for TOEIC, etc.).
     - If the resume mentions a test but no score is provided, **set `licence_point` to the null score** for that qualification (e.g., 9.0 for IELTS, 990 for TOEIC).
     - If the extracted score is outside the valid range or in an invalid format (e.g., “IELTS 12”, “TOEIC 2000”, “N2”, “B1”), disregard the invalid value and instead set `licence_point` to null score.
     - If the qualification is unrecognized (not in known-test mapping), and no numeric score is mentioned, omit the `licence_point` to null value.
   - `get_date`: `"YYYY-MM"` if available; otherwise omit the field.

5. FIX BROKEN LINES & CHARACTER SPACING:
    This resume was extracted from a PDF, leading to potential issues with line breaks and incorrect spacing, both *extra spaces* and *missing spaces*.
    •	Merge lines that belong to the same sentence or paragraph.
    •	**Crucially, normalize Vietnamese character spacing:**
        * **Remove any extraneous spaces inserted *within* or *around* Vietnamese characters with diacritics.** For instance, "H ọ c vi ệ n" must be corrected to "Học viện".
        * **Insert missing spaces between words that have been incorrectly merged, especially in place names or compound words.** For instance, "QuỳnhPhụ" should be "Quỳnh Phụ" and "TháiBình" should be "Thái Bình".
    •	Ensure the output is clean, grammatically correct, and easy to read.

6. Nickname Rules:
    - If the CV contains nickname information: use the provided nickname
    - If the CV does NOT contain nickname information:
    - For Vietnamese/English names: use full name without diacritics (accents)
    - For Japanese names: use full name in Katakana format
    - For other languages: use romanized version of full name
    - Examples:
        Nguyễn Văn An → Nickname: Nguyen Van An
        田中太郎 (Tanaka Taro) → Nickname: タナカタロウ
        John Smith → Nickname: John Smith

7. Language Consistency Rule
    - **ALWAYS respond in the SAME language as the user's input**
    - **DO NOT automatically translate or switch languages**
"""
        prompt = prompt.replace("{qualifications}", str(qualifications))
        logger.info(prompt)
        raw_text = ai_helper.call_gemini(prompt)

        clean_text = raw_text.strip().lstrip("```json").rstrip("```").strip()
        clean_text = re.sub(r'[\x00-\x1f\x7f]', '',
                            clean_text)  # remove control chars

        profile_data = json.loads(clean_text)

        if isinstance(profile_data, dict) and "error" in profile_data:
            error_code = profile_data.get("error")
            error_reason = profile_data.get("type", "Unknown error")

            if error_code == 400 and "not_a_resume" in error_reason.lower():
                return CustomResponse(
                    errors=[
                        {"message": _('Invalid document type: not a resume')}],
                    status=status.HTTP_400_BAD_REQUEST
                )
        cv_id = uuid.uuid4()
        response_data = {
            "cv_id": cv_id,
            "cv_data": profile_data
        }
        # cache cv for 24 hours
        cache.set(cv_id, {"data": profile_data}, timeout=60*60*24)
        return CustomResponse(response_data, status=status.HTTP_200_OK)

    def merge_broken_lines(text):
        lines = text.split("\n")
        merged_lines = []
        for i in range(len(lines)):
            line = lines[i].strip()

            # If line is not start with bullet point, merge with previous line
            if i > 0 and not re.match(r"^[•\-\*\d]", line):
                merged_lines[-1] += " " + line
            else:
                merged_lines.append(line)
        return "\n".join(merged_lines)


class SaveCVUploadedView(APIView):
    @swagger_auto_schema(
        operation_description='Save CV uploaded',
        operation_id='Save CV uploaded',
        method='post',
        request_body=SaveCVUploadedSerializer,
        responses=BaseResponse(status_code=status.HTTP_200_OK, message=_(
            'CV uploaded successfully'), responseModel=SaveCVUploadedResponseSerializer).to_openapi_response()
    )
    @api_view(['POST'])
    @permission_classes([IsAuthenticated, IsEmailVerified])
    def save_cv_uploaded(request):
        cv_id = request.data.get('cv_id')
        cv_data = cache.get(cv_id)
        data = cv_data['data']
        if not cv_data:
            return CustomResponse(status=status.HTTP_404_NOT_FOUND, message=_(
                'CV not found'))
        
        if data.get('phone_number', None):
            data['tel'] = data['phone_number']
        if data.get('address_city_name', None):
            data['address_code'] = data['address_city_name']
        if data.get('address_details', None):
            data['city_name'] = data['address_details']
        user = request.user
        if user.is_authenticated and user.auth_type == AuthType.AUTHENTICATED.value:
            clear_data = {
                'skills': None,
                'educations': None,
                'experiences': None,
                'highlight_projects': None,
                'qualifications': None,
                'skills_for_cv_display': None,
                'professional_summary': None,
                'tel': None,
                'country_code': None,
                'birth_date': None,
                'pr': None,
                'city_name': None,
                'address_code': None
            }

            clear_serializer = ClearUserCVDataSerializer(
                user, data=clear_data, partial=True)
            if clear_serializer.is_valid():
                clear_serializer.save()
            else:
                return CustomResponse(message="Failed to clear existing data", errors=clear_serializer.errors, status=status.HTTP_400_BAD_REQUEST)

            serializer = UpdateUserCVSerializer(
                user, data=cv_data['data'], partial=True)
            if serializer.is_valid():
                serializer.save()

                return CustomResponse(message="Success", status=status.HTTP_200_OK)
            else:
                return CustomResponse(message="Invalid data", errors=serializer.errors, status=status.HTTP_400_BAD_REQUEST)
        else:
            return CustomResponse(message="User not authenticated", status=status.HTTP_401_UNAUTHORIZED)


class CVViewV2(APIView):
    parser_classes = (MultiPartParser, FormParser)
    # Ensure correct permission_classes are defined

    def normalize_cv_data(data):
        FIELDS_TO_NORMALIZE = [
            'educations',
            'languages',
            'skills',
            'experiences',
            'highlight_projects',
            'qualifications'
        ]

        normalized = {}

        for key, value in data.items():
            # Normalize nested lists of dict
            if key in FIELDS_TO_NORMALIZE and isinstance(value, list):
                normalized_list = []
                for item in value:
                    if isinstance(item, dict):
                        normalized_item = {
                            k: None if v in ['null', '', None] else v
                            for k, v in item.items()
                        }
                        normalized_list.append(normalized_item)
                    else:
                        normalized_list.append(
                            None if item in ['null', '', None] else item)
                normalized[key] = normalized_list

            # Normalize single-level values
            else:
                normalized[key] = None if value in [
                    'null', '', None] else value

        return normalized

    @swagger_auto_schema(
        operation_description='Upload CV PDF V2',
        operation_id='Upload CV PDF V2',
        manual_parameters=[
            openapi.Parameter(
                name="cv_pdf",
                in_=openapi.IN_FORM,
                type=openapi.TYPE_FILE,
                required=True,
                description="CV PDF to upload"
            )
        ],
        responses=BaseResponse(status_code=status.HTTP_200_OK, message=_(
            'CV uploaded successfully'), responseModel=UploadCVUploadedResponseSerializer).to_openapi_response()
    )
    def post(self, request, *args, **kwargs):
        uploaded_file = request.data.get('cv_pdf')
        if uploaded_file.size > Constants.MAX_SIZE_CV:
            return CustomResponse(
                {"message": "File size must be less than 5MB"}, status=status.HTTP_400_BAD_REQUEST)
        data_parsed = ai_helper.call_gemini_with_file(uploaded_file)

        profile_data = data_parsed
        if profile_data['is_valid_resume'] == False:
            return CustomResponse(
                errors=[
                    {"message": _('Invalid document type: not a resume')}],
                status=status.HTTP_400_BAD_REQUEST
            )
        cv_id = uuid.uuid4()
        profile_data = CVViewV2.normalize_cv_data(profile_data)
        response_data = {
            "cv_id": cv_id,
            "cv_data": profile_data
        }

        # cache cv for 24 hours
        cache.set(cv_id, {"data": profile_data}, timeout=60*60*24)
        return CustomResponse(response_data, status=status.HTTP_200_OK)


class CVViewTest(APIView):
    parser_classes = (MultiPartParser, FormParser)
    # Ensure correct permission_classes are defined

    @swagger_auto_schema(
        operation_description='Upload CV Test',
        operation_id='Upload CV Test',
        manual_parameters=[
            openapi.Parameter(
                name="cv_pdf",
                in_=openapi.IN_FORM,
                type=openapi.TYPE_FILE,
                required=True,
                description="CV PDF to upload"
            )
        ],
        responses=BaseResponse(status_code=status.HTTP_200_OK, message=_(
            'CV uploaded successfully'), responseModel=UploadCVUploadedResponseSerializer).to_openapi_response()
    )
    def post(self, request, *args, **kwargs):
        # get api domain
        api_domain = request.META.get('HTTP_HOST')
        if api_domain != "staging.asiantech.link" and api_domain != "localhost:8000" and api_domain != "127.0.0.1:8000":
            return CustomResponse(message="Not allowed", status=status.HTTP_403_FORBIDDEN)

        uploaded_file = request.data.get('cv_pdf')

        if uploaded_file.size > Constants.MAX_SIZE_CV:
            return CustomResponse(
                {"message": "File size must be less than 5MB"}, status=status.HTTP_400_BAD_REQUEST)

        reader = PdfReader(uploaded_file)
        number_of_pages = len(reader.pages)
        cv_text = ""
        for i in range(number_of_pages):
            page = reader.pages[i]
            text = page.extract_text()
            cv_text += text

        cv_text_cleaned = re.sub(
            r'[\x00-\x08\x0b\x0c\x0e-\x1f\x7f]', '', cv_text)
        cv_text_cleaned = cv_text_cleaned.strip()
        cv_text = cv_text_cleaned
        return CustomResponse({"data": cv_text}, status=status.HTTP_200_OK)
