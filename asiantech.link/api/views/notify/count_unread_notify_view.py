from rest_framework.views import APIView
from rest_framework import status
from rest_framework.response import Response
from rest_framework.decorators import api_view
from drf_yasg.utils import swagger_auto_schema
from utils.validators import validate_serializer
from utils.responses import BaseResponse, CustomResponse
from django.utils.translation import gettext as _
from utils.permissions import IsEmailVerified
from rest_framework.permissions import IsAuthenticated
from rest_framework.decorators import api_view, permission_classes
from api.models.rec import RecNotify
from api.serializers.notify.count_unread_notify_serializer import CountUnreadNotifyResponseModel, CountUnreadNotifySerializer
from django.utils import timezone
from datetime import timedelta


class CountUnreadNotifyView(APIView):
    @swagger_auto_schema(
        method='get',
        operation_id='get_count_unread_notify',
        responses=BaseResponse(
            status_code=status.HTTP_200_OK,
            message=_("Success"),
            responseModel=CountUnreadNotifyResponseModel
        ).to_openapi_response()
    )
    @api_view(['GET'])
    @permission_classes([IsAuthenticated, IsEmailVerified])
    def get_count_unread_notify(request):
        user_id = request.user.user_id
        # Get the date 30 days ago from now
        thirty_days_ago = timezone.now() - timedelta(days=30)

        notify = RecNotify.objects.filter(
            user_id=user_id,
            # Get records created greater than or equal to 30 days ago
            created__gte=thirty_days_ago
        ).order_by('-created')

        total_notify = notify.count()
        total_unread_notify = notify.filter(read_at__isnull=True).count()

        value_data = {
            "total_notify": total_notify,
            "total_unread_notify": total_unread_notify
        }
        serializer = CountUnreadNotifySerializer(data=value_data)
        serializer.is_valid(raise_exception=True)
        return CustomResponse(status=status.HTTP_200_OK, message=_("Success"), data=serializer.validated_data)
