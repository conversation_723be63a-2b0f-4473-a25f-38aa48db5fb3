from rest_framework.views import APIView
from rest_framework import status
from rest_framework.response import Response
from rest_framework.decorators import api_view
from drf_yasg.utils import swagger_auto_schema
from utils.validators import validate_serializer
from utils.responses import BaseResponse, CustomResponse
from django.utils.translation import gettext as _
from utils.permissions import IsEmailVerified
from rest_framework.permissions import IsAuthenticated
from rest_framework.decorators import api_view, permission_classes
from api.models.rec import RecNotify
from api.serializers.notify.set_read_notify_serializer import SetReadNotifySerializer, SetReadNotifyResponseModel
from django.utils import timezone


class SetReadAllNotifyView(APIView):
    @swagger_auto_schema(
        method='post',
        operation_id='set_read_all_notify',
        responses=BaseResponse(
            status_code=status.HTTP_200_OK,
            message=_("Success"),
            responseModel=SetReadNotifyResponseModel
        ).to_openapi_response()
    )
    @api_view(['POST'])
    @permission_classes([IsAuthenticated, IsEmailVerified])
    def set_read_all_notify(request):
        RecNotify.objects.filter(user_id=request.user.user_id).update(
            read_at=timezone.now())
        return CustomResponse(status=status.HTTP_200_OK, message=_("Success"), data=True)
