from rest_framework.views import APIView
from rest_framework import status
from rest_framework.parsers import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>arser
from drf_yasg.utils import swagger_auto_schema
from drf_yasg import openapi

from api.serializers.media_serializers import *
# Import your utility functions as needed
from utils.utils import compress_image, get_saved_file_path

from rest_framework.permissions import IsAuthenticated
from rest_framework.views import APIView
from drf_yasg import openapi
from drf_yasg.utils import swagger_auto_schema
from django.utils.translation import gettext as _
from utils.permissions import IsEmailVerified
from utils.responses import CustomResponse, BaseResponse
from utils.validators import validate_serializer
from utils.constants import MediaType
from api.models.rec import *
from utils.constants import *
import os
import logging
from api.models.user import User
from api.services.notify_service.support_company_notify_service import SupportCompanyNotifyService
from api.services.notify_service.company_notify_service import CompanyNotifyService
from utils.utils import get_access_token_from_request
logger = logging.getLogger("api_logger")


class ContractMediaViews(APIView):
    # Ensure correct parser_classes are defined
    parser_classes = (MultiPartParser, FormParser)
    # Ensure correct permission_classes are defined
    permission_classes = [IsAuthenticated, IsEmailVerified]

    @swagger_auto_schema(
        operation_description='Upload image and compress it',
        operation_id='Upload Contract Image',
        manual_parameters=[
            openapi.Parameter(
                name="image",
                in_=openapi.IN_FORM,
                type=openapi.TYPE_FILE,
                required=True,
                description="Image file to upload"
            ),
            openapi.Parameter(
                name="recruit_id",
                in_=openapi.IN_FORM,
                type=openapi.TYPE_STRING,
                required=True
            ),
        ],
        responses=BaseResponse(status_code=status.HTTP_200_OK, message=_(
            'Image uploaded successfully'), responseModel=MediaResponseModel).to_openapi_response()
    )
    def post(self, request, *args, **kwargs):
        try:
            user = request.user
            token = get_access_token_from_request(request)
            request.data['type'] = MediaType.CONTRACT.value
            recruit_id = request.data.get('recruit_id')
            apply = RecApply.objects.get(
                recruit_id=recruit_id, engineer=user)

            if apply.recruit_progress_code != RecruitProgressCode.OFFER_ACCEPTED.value:
                return CustomResponse(message=_('You can not upload contract image now, recruit progress code !=60'), status=status.HTTP_400_BAD_REQUEST)
            apply_id = apply.apply_id
            user_id = user.user_id

            request.data['main_folder_path'] = f"contracts/{apply_id}"
            serializer = ImageSerializer(data=request.data)
            is_valid, error = validate_serializer(serializer)
            if not is_valid:
                return CustomResponse(errors=error, status=status.HTTP_400_BAD_REQUEST)
            image_file = request.data.get('image')
            compressed_image = compress_image(image_file)
            serializer.validated_data['image'] = compressed_image
            serializer.validated_data['user'] = request.user
            serializer.save()

            imageUrl = get_saved_file_path(request, serializer.data['image'])
            data = {
                'accept_sign_path': imageUrl,
                'user': user,
                'apply': apply
            }
            RecAcceptSign.objects.update_or_create(
                apply=apply,
                user=user,
                defaults={'accept_sign_path': imageUrl}
            )

            # Send notify to host_agent
            host_agent = RecAcceptSign.objects.filter(
                apply_id=apply.apply_id,
                user=apply.host_agent
            ).first()
            if host_agent is None:

                CompanyNotifyService().send_notify_engineer_sign_contract(
                    token, apply.engineer, apply.apply_id)
            # Send notify to support_agent
            support_agent = User.objects.filter(
                company_id=apply.agency_company.company_id).first()
            sign_support_agent = RecAcceptSign.objects.filter(
                apply_id=apply.apply_id,
                user=support_agent).first()
            if sign_support_agent is None:
                SupportCompanyNotifyService().send_notify_engineer_sign_contract(
                    token,  apply.engineer, apply.apply_id)

            data = serializer.data
            data.update(
                {'image': imageUrl})
            return CustomResponse(data, message=_('Image uploaded successfully'), status=status.HTTP_200_OK)

        except Exception as e:
            return CustomResponse(errors=str(e), status=status.HTTP_400_BAD_REQUEST)


class ContractCompanyMediaViews(APIView):
    # Ensure correct parser_classes are defined
    parser_classes = (MultiPartParser, FormParser)
    # Ensure correct permission_classes are defined
    permission_classes = [IsAuthenticated, IsEmailVerified]

    @swagger_auto_schema(
        operation_description='Upload image and compress it',
        operation_id='Upload Company Contract Image',
        manual_parameters=[
            openapi.Parameter(
                name="image",
                in_=openapi.IN_FORM,
                type=openapi.TYPE_FILE,
                required=True,
                description="Image file to upload"
            ),
            openapi.Parameter(
                name="apply_id",
                in_=openapi.IN_FORM,
                type=openapi.TYPE_STRING,
                required=True
            ),
        ],
        responses=BaseResponse(status_code=status.HTTP_200_OK, message=_(
            'Image uploaded successfully'), responseModel=MediaResponseModel).to_openapi_response()
    )
    def post(self, request, *args, **kwargs):
        try:
            user = request.user
            access_token = get_access_token_from_request(request)
            # Make a mutable copy of the request data
            data = request.data.copy()
            data.update({'type':  MediaType.CONTRACT_COMPANY.value})

            apply_id = data.get('apply_id')
            company_id = user.company_id
            filter = {'apply_id': apply_id}

            if user.user_type == UserType.HOST_COMPANY_STAFF.value:
                filter['host_company_id'] = company_id
            elif user.user_type == UserType.HOST_SUPPORT_AGENCY_STAFF.value:
                filter['support_company_id'] = company_id
            else:
                return CustomResponse(message=_('You are not authorized to upload contract image'), status=status.HTTP_400_BAD_REQUEST)
            apply = RecApply.objects.get(**filter)

            if apply.recruit_progress_code not in [RecruitProgressCode.JOB_OFFER.value, RecruitProgressCode.OFFER_ACCEPTED.value, RecruitProgressCode.EMPLOYED.value]:
                return CustomResponse(message=_('You can not upload contract image now, status progress code is invalid'), status=status.HTTP_400_BAD_REQUEST)

            apply_id = apply.apply_id
            path = f"contracts/{user.user_id}/{apply_id}_{user.user_id}"
            data['main_folder_path'] = path

            serializer = ImageSerializer(data=data)
            is_valid, error = validate_serializer(serializer)
            if not is_valid:
                return CustomResponse(errors=error, status=status.HTTP_400_BAD_REQUEST)

            image_file = data.get('image')
            compressed_image = compress_image(image_file)
            serializer.validated_data['image'] = compressed_image
            serializer.validated_data['user'] = request.user
            serializer.save()

            imageUrl = get_saved_file_path(request, serializer.data['image'])
            data = {
                'accept_sign_path': imageUrl,
                'user': user,
                'apply': apply
            }
            RecAcceptSign.objects.update_or_create(
                apply=apply,
                user=user,
                defaults={'accept_sign_path': imageUrl}
            )
            if apply.support_company is not None:
                support_agent = User.objects.filter(
                    company_id=apply.support_company.company_id).first()
                sign_support_agent = RecAcceptSign.objects.filter(
                    apply_id=apply_id,
                    user=support_agent).first()
                if sign_support_agent is None:
                    SupportCompanyNotifyService().send_notify_engineer_sign_contract(
                        access_token, apply.engineer, apply_id)

            data = serializer.data
            data.update(
                {'image': imageUrl})
            return CustomResponse(data, message=_('Image uploaded successfully'), status=status.HTTP_200_OK)

        except Exception as e:
            return CustomResponse(errors=str(e), status=status.HTTP_400_BAD_REQUEST)
