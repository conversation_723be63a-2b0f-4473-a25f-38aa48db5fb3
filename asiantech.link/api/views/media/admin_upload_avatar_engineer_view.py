from django.contrib.auth import get_user_model
from rest_framework.views import APIView
from rest_framework import status
from rest_framework.parsers import <PERSON>Part<PERSON>ars<PERSON>, FormParser
from drf_yasg.utils import swagger_auto_schema
from drf_yasg import openapi

from api.serializers.media_serializers import *
from utils.utils import compress_image, get_saved_file_path

from rest_framework.permissions import IsAuthenticated
from rest_framework.views import APIView
from drf_yasg import openapi
from drf_yasg.utils import swagger_auto_schema
from django.utils.translation import gettext as _
from utils.permissions import IsEmailVerified
from utils.responses import CustomResponse, BaseResponse
from utils.validators import validate_serializer
from utils.constants import MediaType
import os
import logging
from rest_framework.decorators import permission_classes
logger = logging.getLogger("api_logger")
User = get_user_model()


class AdminUploadAvatarViewEngineerView(APIView):
    # Ensure correct parser_classes are defined
    parser_classes = (<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, FormParser)
    # Ensure correct permission_classes are defined
    permission_classes = [IsAuthenticated, IsEmailVerified]

    @swagger_auto_schema(
        operation_description='Upload image and compress it',
        operation_id='Admin upload avatar',
        manual_parameters=[
            openapi.Parameter(
                name="image",
                in_=openapi.IN_FORM,
                type=openapi.TYPE_FILE,
                required=True,
                description="Image file to upload"
            ),
            openapi.Parameter(
                name="engineer_id",
                in_=openapi.IN_FORM,
                type=openapi.TYPE_INTEGER,
                required=True,
                description="Engineer ID"
            )
        ],
        responses=BaseResponse(status_code=status.HTTP_200_OK, message=_(
            'Image uploaded successfully'), responseModel=MediaResponseModel).to_openapi_response()
    )
    def post(self, request, *args, **kwargs):
        user = User.objects.get(user_id=request.data['engineer_id'])
        request.data['type'] = MediaType.AVATAR.value
        serializer = ImageSerializer(data=request.data)
        is_valid, error = validate_serializer(serializer)
        if not is_valid:
            return CustomResponse(errors=error, status=status.HTTP_400_BAD_REQUEST)
        image_file = request.data.get('image')
        compressed_image = compress_image(image_file)
        serializer.validated_data['image'] = compressed_image
        serializer.validated_data['user'] = request.user
        serializer.save()
        imageUrl = get_saved_file_path(request, serializer.data['image'])
        userMediaSerializer = UserMediaSerializer(user, data={
            'profile_image_path': imageUrl,
        }, partial=True)
        userMediaSerializer.is_valid(raise_exception=True)
        userMediaSerializer.save()

        data = serializer.data
        data.update(
            {'image': imageUrl})
        return CustomResponse(data, message=_('Image uploaded successfully'), status=status.HTTP_200_OK)

    @swagger_auto_schema(
        operation_description='Delete user avatar',
        operation_id='Admin Delete avatar engineer',
        manual_parameters=[
            openapi.Parameter(
                name="engineer_id",
                in_=openapi.IN_FORM,
                type=openapi.TYPE_INTEGER,
                required=True,
                description="Engineer ID"
            )
        ],
        responses=BaseResponse(status_code=status.HTTP_200_OK, message=_(
            'Image deleted successfully')).to_openapi_response()
    )
    def delete(self, request):
        user = User.objects.get(user_id=request.data['engineer_id'])
        image_path = user.profile_image_path
        if image_path is not None:
            images = MediaImage.objects.filter(
                user=user, type=MediaType.AVATAR.value)
            if images:
                for image in images:
                    try:
                        os.remove(image.image.path)
                        image.delete()
                    except Exception as e:
                        logger.error(f"Error deleting image: {e}")

        user.profile_image_path = None
        user.save()
        return CustomResponse(message=_('Image deleted successfully'), status=status.HTTP_200_OK)
