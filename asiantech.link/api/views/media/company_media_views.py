from rest_framework.views import APIView
from rest_framework import status
from rest_framework.parsers import <PERSON><PERSON>art<PERSON>ars<PERSON>, <PERSON><PERSON>ars<PERSON>
from drf_yasg.utils import swagger_auto_schema
from drf_yasg import openapi

from api.serializers.media_serializers import *
from utils.utils import compress_image, get_saved_file_path

from rest_framework.permissions import IsAuthenticated
from rest_framework.views import APIView
from drf_yasg import openapi
from drf_yasg.utils import swagger_auto_schema
from django.utils.translation import gettext as _
from utils.permissions import IsEmailVerified
from utils.responses import CustomResponse, BaseResponse
from utils.validators import validate_serializer
from utils.constants import MediaType
import os
import logging
from urllib.parse import urlparse
from django.conf import settings
from django.utils import timezone


logger = logging.getLogger("api_logger")


class CompanyLogoViews(APIView):
    # Ensure correct parser_classes are defined
    parser_classes = (<PERSON><PERSON>art<PERSON><PERSON><PERSON>, FormParser)
    # Ensure correct permission_classes are defined
    permission_classes = [IsAuthenticated, IsEmailVerified]

    @swagger_auto_schema(
        operation_description='Upload image and compress it',
        operation_id='upload-company-logo',
        manual_parameters=[
            openapi.Parameter(
                name="image",
                in_=openapi.IN_FORM,
                type=openapi.TYPE_FILE,
                required=True,
                description="Image file to upload"
            )
        ],
        responses=BaseResponse(status_code=status.HTTP_200_OK, message=_(
            'Image uploaded successfully'), responseModel=MediaResponseModel).to_openapi_response()
    )
    def post(self, request, *args, **kwargs):
        user = request.user
        if user.company_id is None:
            return CustomResponse(errors=[{'message': _('You are not a company')}], status=status.HTTP_400_BAD_REQUEST)
        try:
            company = ComCompany.objects.get(company_id=user.company_id)
            company.updated = timezone.now()
            company.save()
        except ComCompany.DoesNotExist:
            return CustomResponse(message=_("Company not found"), status=status.HTTP_404_NOT_FOUND)
        request.data['type'] = MediaType.COMPANY_LOGO.value
        serializer = ImageSerializer(data=request.data)
        is_valid, error = validate_serializer(serializer)
        if not is_valid:
            return CustomResponse(errors=error, status=status.HTTP_400_BAD_REQUEST)
        image_file = request.data.get('image')
        compressed_image = compress_image(image_file)
        serializer.validated_data['image'] = compressed_image
        serializer.validated_data['user'] = request.user
        serializer.save()

        imageUrl = get_saved_file_path(request, serializer.data['image'])

        userMediaSerializer = CompanyMediaSerializer(company, data={
            'logo_image_path': imageUrl,
        }, partial=True)
        userMediaSerializer.is_valid(raise_exception=True)
        userMediaSerializer.save()

        data = serializer.data
        data.update({'image': imageUrl})
        return CustomResponse(data, message=_('Image uploaded successfully'), status=status.HTTP_200_OK)


class CompanyPRViews(APIView):
    # Ensure correct parser_classes are defined
    parser_classes = (MultiPartParser, FormParser)
    # Ensure correct permission_classes are defined
    permission_classes = [IsAuthenticated, IsEmailVerified]

    @swagger_auto_schema(
        operation_description='Upload image and compress it',
        operation_id='upload-company-pr',
        manual_parameters=[
            openapi.Parameter(
                name="image",
                in_=openapi.IN_FORM,
                type=openapi.TYPE_FILE,
                required=True,
                description="Image file to upload"
            ),
            openapi.Parameter(
                name="index",
                in_=openapi.IN_FORM,
                type=openapi.TYPE_INTEGER,
                required=True,
                description="Index of the image to upload(0,1,2)"
            )
        ],
        responses=BaseResponse(status_code=status.HTTP_200_OK, message=_(
            'Image uploaded successfully'), responseModel=MediaResponseModel).to_openapi_response()
    )
    def post(self, request, *args, **kwargs):

        user = request.user
        if user.company_id is None:
            return CustomResponse(errors=[{'message': _('You are not a company')}], status=status.HTTP_400_BAD_REQUEST)
        try:
            company = ComCompany.objects.get(company_id=user.company_id)
            company.updated = timezone.now()
            company.save()
        except ComCompany.DoesNotExist:
            return CustomResponse(message=_("Company not found"), status=status.HTTP_404_NOT_FOUND)
        request.data['type'] = MediaType.COMPANY_PR.value
        serializer = ImageSerializer(data=request.data)
        is_valid, error = validate_serializer(serializer)
        if not is_valid:
            return CustomResponse(errors=error, status=status.HTTP_400_BAD_REQUEST)
        image_file = request.data.get('image')
        compressed_image = compress_image(image_file)
        serializer.validated_data['image'] = compressed_image
        serializer.validated_data['user'] = request.user
        serializer.save()

        imageUrl = get_saved_file_path(request, serializer.data['image'])

        index = int(request.data.get('index'))

        if index not in [0, 1, 2]:
            return CustomResponse(errors=[{'message': _('Index must be 0,1,2')}], status=status.HTTP_400_BAD_REQUEST)
        # delete old pr image
        try:
            if index == 0:
                old_image = company.pr_image_path1

            elif index == 1:
                old_image = company.pr_image_path2

            elif index == 2:
                old_image = company.pr_image_path3

            if old_image:
                parsed_url = urlparse(old_image)
                # Remove leading '/media/' part
                media_path = parsed_url.path.lstrip('/media/')
                full_path = os.path.join(settings.MEDIA_ROOT, media_path)
                if os.path.exists(full_path):
                    os.remove(full_path)
                MediaImage.objects.filter(image=media_path).delete()
        except Exception as e:
            logger.error(f"Error deleting image: {e}")

        # save pr image
        image_field = f'pr_image_path{index+1}'

        userMediaSerializer = CompanyMediaSerializer(company, data={
            image_field: imageUrl,
        }, partial=True)

        userMediaSerializer.is_valid(raise_exception=True)
        userMediaSerializer.save()

        data = serializer.data
        data.update({'image': imageUrl})
        return CustomResponse(data, message=_('Image uploaded successfully'), status=status.HTTP_200_OK)
