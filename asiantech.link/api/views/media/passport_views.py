from rest_framework.views import APIView
from rest_framework import status
from rest_framework.parsers import <PERSON>Part<PERSON>ars<PERSON>, FormParser
from drf_yasg.utils import swagger_auto_schema
from drf_yasg import openapi

from api.serializers.media_serializers import *
# Import your utility functions as needed
from utils.utils import compress_image,get_saved_file_path

from rest_framework.permissions import IsAuthenticated
from rest_framework.views import APIView
from drf_yasg import openapi
from drf_yasg.utils import swagger_auto_schema
from django.utils.translation import gettext as _
from utils.permissions import IsEmailVerified
from utils.responses import CustomResponse, BaseResponse
from utils.validators import validate_serializer
from utils.constants import MediaType
import os
import logging
logger = logging.getLogger("api_logger")


class PassportViews(APIView):
    # Ensure correct parser_classes are defined
    parser_classes = (MultiPartParser, FormParser)
    # Ensure correct permission_classes are defined
    permission_classes = [IsAuthenticated, IsEmailVerified]

    @swagger_auto_schema(
        operation_description='Upload image and compress it',
        operation_id='Upload Passport Image',
        manual_parameters=[
            openapi.Parameter(
                name="image",
                in_=openapi.IN_FORM,
                type=openapi.TYPE_FILE,
                required=True,
                description="Image file to upload"
            )
        ],
        responses=BaseResponse(status_code=status.HTTP_200_OK, message=_(
            'Image uploaded successfully'), responseModel=MediaResponseModel).to_openapi_response()
    )
    def post(self, request, *args, **kwargs):
        user = request.user
        request.data['type'] = MediaType.PASSPORT.value
        serializer = ImageSerializer(data=request.data)
        is_valid, error = validate_serializer(serializer)
        if not is_valid:
            return CustomResponse(errors=error, status=status.HTTP_400_BAD_REQUEST)
        image_file = request.data.get('image')
        compressed_image = compress_image(image_file)
        serializer.validated_data['image'] = compressed_image
        serializer.validated_data['user'] = request.user
        serializer.save()

        imageUrl = get_saved_file_path(request, serializer.data['image'])
        userMediaSerializer = UserMediaSerializer(user, data={
            'passport_image_path': imageUrl,
        }, partial=True)
        userMediaSerializer.is_valid(raise_exception=True)
        userMediaSerializer.save()

        data = serializer.data
        data.update(
            {'image':imageUrl})
        return CustomResponse(data, message=_('Image uploaded successfully'), status=status.HTTP_200_OK)

    @swagger_auto_schema(
        operation_description='Delete passport image',
        operation_id='Delete Passport Image',
        responses=BaseResponse(status_code=status.HTTP_200_OK, message=_(
            'Passport image deleted successfully')).to_openapi_response()
    )
    def delete(self, request):
        user = request.user
        image_path = user.passport_image_path
        if image_path is not None:
            images = MediaImage.objects.filter(
                user=user, type=MediaType.PASSPORT.value)
            if images:
                for image in images:
                    try:
                        os.remove(image.image.path)
                        image.delete()
                    except Exception as e:
                        logger.error(f"Error deleting image: {e}")

        user.passport_image_path = None
        user.save()
        return CustomResponse(message=_('Passport image deleted successfully'), status=status.HTTP_200_OK)
