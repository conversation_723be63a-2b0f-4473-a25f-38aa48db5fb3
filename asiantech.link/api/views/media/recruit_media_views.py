from rest_framework.views import APIView
from rest_framework import status
from rest_framework.parsers import <PERSON>Part<PERSON>ars<PERSON>, FormParser
from drf_yasg.utils import swagger_auto_schema
from drf_yasg import openapi

from api.serializers.media_serializers import *
from utils.utils import compress_image,get_saved_file_path

from rest_framework.permissions import IsAuthenticated
from rest_framework.views import APIView
from drf_yasg import openapi
from drf_yasg.utils import swagger_auto_schema
from django.utils.translation import gettext as _
from utils.permissions import IsEmailVerified
from utils.responses import  CustomResponse,BaseResponse
from utils.validators import validate_serializer
from utils.constants import MediaType
import os
import logging
from urllib.parse import urlparse
from django.conf import settings


logger = logging.getLogger("api_logger")



class RecruitLogoViews(APIView):
    parser_classes = (MultiPartParser, FormParser)  # Ensure correct parser_classes are defined
    permission_classes = [IsAuthenticated, IsEmailVerified]  # Ensure correct permission_classes are defined
    @swagger_auto_schema(
        operation_description='Upload image and compress it',
        operation_id='upload-recruit-cover',
        manual_parameters=[
            openapi.Parameter(
                name="image",
                in_=openapi.IN_FORM,
                type=openapi.TYPE_FILE,
                required=True,
                description="Image file to upload"
            )
        ],
        responses=BaseResponse(status_code=status.HTTP_200_OK, message=_('Image uploaded successfully'), responseModel=MediaResponseModel).to_openapi_response()
    )
    def post(self, request, *args, **kwargs):
        user = request.user
        if user.company_id is None:
            return CustomResponse(errors=[{'message':_('You are not a company')}],status=status.HTTP_400_BAD_REQUEST)
        request.data['type'] = MediaType.COVER_RECRUIT.value
        serializer = ImageSerializer(data=request.data)
        is_valid, error = validate_serializer(serializer)
        if not is_valid:
            return CustomResponse(errors=error, status=status.HTTP_400_BAD_REQUEST)
        image_file = request.data.get('image')  
        compressed_image = compress_image(image_file)
        serializer.validated_data['image'] = compressed_image
        serializer.validated_data['user'] = request.user
        serializer.save()
        
        imageUrl = get_saved_file_path(request, serializer.data['image'])
         
        data = serializer.data
        data.update({'image': imageUrl})
        return CustomResponse(data, message=_('Image uploaded successfully'), status=status.HTTP_200_OK)


