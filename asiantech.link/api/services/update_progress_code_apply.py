from api.models.rec import RecApply
from utils.constants import *
from django.utils import timezone as tz
from api.services.notify_service.company_notify_service import CompanyNotifyService
from api.services.notify_service.support_company_notify_service import SupportCompanyNotifyService
from api.models.black_listed_access_token import UserAccessToken
import logging
logger = logging.getLogger("api_logger")


def update_progress_code_apply_after_interview():

    # Get recruit_progress_code = INTERVIEW_DATE_CONFIRMED  and interview_time <= tz.now()
    items_to_update = RecApply.objects.filter(
        recruit_progress_code=RecruitProgressCode.INTERVIEW_DATE_CONFIRMED.value, interview_datetime__lt=tz.now())

    # Update to INTERVIEW_COMPLETED
    updated_count = items_to_update.update(
        recruit_progress_code=RecruitProgressCode.INTERVIEW_COMPLETED.value, updated=tz.now())
    print(f"{updated_count} records updated to status 40")
    if items_to_update.exists():
        for item in items_to_update:
            try:
                # Send notify to host_agent
                company_user = item.host_agent
                access_token_company = UserAccessToken.objects.filter(
                    user=company_user, expires_at__gte=tz.now()).first()
                if access_token_company:
                    CompanyNotifyService().send_notify_update_status_interview(
                        access_token_company, item.engineer, item.apply_id)
                support_user = item.support_agent
                if support_user:
                    access_token_support = UserAccessToken.objects.filter(
                        user=support_user, expires_at__gte=tz.now()).first()
                    SupportCompanyNotifyService().send_notify_update_status_interview(
                        access_token_support,   item.engineer, item.apply_id)
            except Exception as e:
                logger.error(f"Error sending notify to from service: {e}")
                # update OFFER_ACCEPTED to EMPLOYED
    list_apply_offer_accepted = RecApply.objects.filter(
        recruit_progress_code=RecruitProgressCode.OFFER_ACCEPTED.value, joing_date__lte=tz.now())
    updated_count = list_apply_offer_accepted.update(
        recruit_progress_code=RecruitProgressCode.EMPLOYED.value, updated=tz.now())
    print(f"{updated_count} records updated to status 80")
