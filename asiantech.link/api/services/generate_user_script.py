from utils.currency_converter import CurrencyConverter
from faker import Faker
import random
import lorem
from datetime import datetime, timedelta
import json
from django.contrib.auth.models import User
from api.models.eng import *
import string
from django.contrib.auth import get_user_model
User = get_user_model()

currency_converter = CurrencyConverter()


def generate_academic():
    school_names = [
        "Greenhill Academy",
        "Silverlake",
        "Oakridge",
        "Horizon Valley",
        "Redwood Preparatory",
        "Bluebell Elementary",
        "Crestview High",
        "Willowdale Academy",
        "Starlight Public",
        "Maple Heights",
        "Phoenix Ridge Secondary",
        "Evergreen Charter",
        "Liberty Springs Academy",
        "Golden Gate High",
        "Highland Grove",
        "Beacon Hill International",
        "Riverstone Middle",
        "Meadowbrook",
        "Summit Peak Academy",
        "Crystal Creek High"
    ]
    list_type = [
        1, 10, 20, 100, 200
    ]
    country_code = [
        "JP",
        "VN",
        "ID",
        "MY",
        "SG",
        "AU",
        "ID",
    ]
    faculties = [
        "Computer Science",
        "Information Technology",
        "Software Engineering",
        "Computer Engineering",
        "Data Science",
        "Artificial Intelligence",
        "Cybersecurity",
        "Information Systems",
        "Network Engineering",
        "Web Development",
        "Mobile Application Development",
        "Cloud Computing",
        "Game Development",
        "Database Management",
        "Machine Learning",
        "Computer Networks",
        "IT Project Management",
        "Digital Forensics",
        "UI/UX Design",
        "IT Infrastructure"
    ]
    now = datetime.now()
    in_date = now - timedelta(days=random.randint(365 * 3, 365 * 8))
    out_date = now - timedelta(days=random.randint(0, 365 * 2))
    return {
        "type": random.choice(list_type),
        "country_code": random.choice(country_code),
        "school": random.choice(school_names),
        "faculty": random.choice(faculties),
        "in_date": in_date,
        "out_date": out_date
    }


def generate_eng_hope():

    with open("api/static/data/city.json", 'r') as file:
        city_data = json.load(file)
    payroll_price = random.randint([600, 700, 800, 900, 1000, 1200, 1300, 1500,
                                   1800, 2000, 2200, 2500, 2700, 2800, 3000, 3500, 4000, 4500, 5000, 5500, 6000])

    payroll_code = "USD"

    employ_code = str(random.randint(1, 5))
    place_code1 = random.choice(city_data)["code"]
    place_code2 = random.choice(city_data)["code"]
    place_code3 = random.choice(city_data)["code"]
    remote_code = str(random.choice([1, 2, 4]))
    payroll_price_usd = payroll_price
    return {
        "employ_code": employ_code,
        "place_code1": place_code1,
        "place_code2": place_code2,
        "place_code3": place_code3,
        "payroll_price": payroll_price,
        "payroll_code": payroll_code,
        "remote_code": remote_code,
        "payroll_price_usd": payroll_price_usd
    }


def generate_eng_language():
    list_language_code = [
        "JA",
        "EN",
        "KO",
        "VI",
        "MS",
        "ID",
        "ZH",
    ]
    list_language_level_type = [
        1, 3, 5, 7
    ]
    language_level_type = random.choice(list_language_level_type)

    return {
        "language_code": random.choice(list_language_code),
        "language_level_type": language_level_type
    }


def generate_eng_career():
    career_type = 1

    it_companies = [
        "Google",
        "Microsoft",
        "Apple",
        "Amazon Web Services",
        "Meta",
        "IBM",
        "Oracle",
        "Intel",
        "Cisco Systems",
        "Adobe",
        "SAP",
        "Salesforce",
        "NVIDIA",
        "Samsung SDS",
        "HP Enterprise",
        "Dell Technologies",
        "VMware",
        "Tencent",
        "Alibaba Cloud",
        "Qualcomm",
        "Infosys",
        "Wipro",
        "Tata Consultancy Services",
        "Capgemini",
        "Accenture",
        "Cognizant",
        "Tech Mahindra",
        "Zoho Corporation",
        "Epic Systems",
        "Palantir Technologies",
        "Atlassian",
        "SquareSoft",
        "Unity Technologies",
        "DigitalOcean",
        "Snowflake Inc.",
        "Datadog",
        "Bitbucket Labs",
        "CyberNova Tech",
        "PixelLogic Inc.",
        "BlueWave Solutions"
    ]
    job_description = lorem.paragraph()
    now = datetime.now()
    entering_date = now - timedelta(days=random.randint(365 * 3, 365 * 8))
    quitting_date = now - timedelta(days=random.randint(0, 365 * 2))
    return {
        "career_type": career_type,
        "company_name": random.choice(it_companies),
        "job_description": job_description,
        "entering_date": entering_date,
        "quitting_date": quitting_date
    }


def generate_eng_skill():
    skill_code = str(random.randint(100001, 100091))
    level_type = random.randint(1, 6)
    return {
        "job_code": "100",
        "skill_code": skill_code,
        "level_type": level_type
    }


def generate_eng_license():
    with open("api/static/data/qualification.json", 'r') as file:
        qualification_data = json.load(file)
    license = random.choice(qualification_data["qualification"])
    license_name = license["name"]
    license_code = license["id"]
    license_point = random.randint(5, 10)
    now = datetime.now()
    get_date = now - timedelta(days=random.randint(0, 365 * 5))

    return {
        "job_code": "100",
        "licence_code": license_code,
        "licence_point": license_point,
        "licence_name": license_name,
        "get_date": get_date
    }


def generate_hop_job_skill():
    job_code = "100"
    skill_code = str(random.randint(100001, 100091))
    return {
        "job_code": job_code,
        "skill_code": skill_code
    }


def generate_career_job_skill():
    job_code = "100"
    skill_code = str(random.randint(100001, 100091))
    years_of_experience = random.randint(1, 6)
    return {
        "job_code": job_code,
        "skill_code": skill_code,
        "years_of_experience": years_of_experience
    }


def generate_self_assesment():
    remote_exp_years = random.randint(1, 6)
    remote_job_description = lorem.paragraph()

    durability_score = random.randint(1, 6)
    global_work_exp = random.randint(0, 1)
    remote_skill_1 = random.randint(1, 4)
    remote_skill_2 = random.randint(1, 4)
    remote_skill_3 = random.randint(1, 4)
    global_skill_1 = random.randint(1, 4)
    global_skill_2 = random.randint(1, 4)
    global_skill_3 = random.randint(1, 4)
    social_style = random.randint(1, 4)
    communication_skill_1 = random.randint(1, 4)
    communication_skill_2 = random.randint(1, 4)
    communication_skill_3 = random.randint(1, 4)
    report_skill_1 = random.randint(1, 4)
    report_skill_2 = random.randint(1, 4)
    report_skill_3 = random.randint(1, 4)
    management_skill_1 = random.randint(1, 4)
    management_skill_2 = random.randint(1, 4)
    management_skill_3 = random.randint(1, 4)

    return {
        "remote_exp_years": remote_exp_years,
        "remote_job_description": remote_job_description,
        "remote_skill_1": remote_skill_1,
        "remote_skill_2": remote_skill_2,
        "remote_skill_3": remote_skill_3,
        "global_work_exp": global_work_exp,
        "global_skill_1": global_skill_1,
        "global_skill_2": global_skill_2,
        "global_skill_3": global_skill_3,
        "social_style": social_style,
        "communication_skill_1": communication_skill_1,
        "communication_skill_2": communication_skill_2,
        "communication_skill_3": communication_skill_3,
        "report_skill_1": report_skill_1,
        "report_skill_2": report_skill_2,
        "report_skill_3": report_skill_3,
        "management_skill_1": management_skill_1,
        "management_skill_2": management_skill_2,
        "management_skill_3": management_skill_3,
        "durability_score": durability_score
    }


def generate_user_script():
    # for from 0 10
    list_country_code = [
        "JP",
        "VN",
        "ID",
        "MY",
        "SG",
        "AU",
        "ID",
    ]
    list_birth_date = [
        2004,
        2003,
        2002,
        2001,
        2000,
        1999,
        1998,
        1997,
        1996,
        1995,
        1994,
        1993,
        1992,
        1991,
        1990,
        1989,
        1988,
    ]
    with open("api/static/data/city.json", 'r') as file:
        city_data = json.load(file)
    count = 0
    for i in range(0, 10000):
        count += 1
        year_dob = random.choice(list_birth_date)
        month_dob = random.randint(1, 12)
        day_dob = random.randint(1, 28)
        sex_type = random.randint(1, 2)
        facebook_id = random.randint(100000000000000, 999999999999999)
        linkedin_id = random.randint(100000000000000, 999999999999999)
        facebook_url = f"https://www.facebook.com/profile.php?id={facebook_id}"
        linkedin_url = f"https://www.linkedin.com/in/profile.php?id={linkedin_id}"
        pr = lorem.paragraph()

        # create user
        fake = Faker()
        random_name = fake.name()
        list_name = random_name.split(" ")
        if len(list_name) == 1:
            first_name = list_name[0]
            last_name = ''.join(random.choices(
                string.ascii_letters + string.digits, k=5))
        else:
            first_name = list_name[0] + ''.join(random.choices(
                string.ascii_letters + string.digits, k=3))
            last_name = list_name[1] + ''.join(random.choices(
                string.ascii_letters + string.digits, k=3))
        birth_date = datetime(year_dob, month_dob, day_dob)
        # random 10 character
        random_character = ''.join(random.choices(
            string.ascii_letters + string.digits, k=10))
        email = f"engineer_test_{first_name.lower()}_{last_name.lower()}_{random_character}@mailinator.com"
        state_city = random.choice(city_data)
        address_code = state_city['code']
        country_code = address_code.split("-")[0]

        # EngAcademic
        academic = generate_academic()

        user = User.objects.create(
            country_code=country_code,
            birth_date=birth_date,
            sex_type=sex_type,
            email=email,
            password="$argon2id$v=19$m=65536,t=3,p=4$cm34LI0/vDnr5bJeJ9VNsg$+mA5OU2eb32+Ld2cpOskaOERtRc487G9ZxvSjWZu3wY",
            first_name=first_name,
            last_name=last_name,
            pr=pr,
            facebook_url=facebook_url,
            linkedin_url=linkedin_url,
            created=datetime.now(),
            deleted=0,
            user_type=0,
            auth_type=1,
            job_status=0,
            username=email,
            international_tel="+84",
            tel="397450000",
            address_code=address_code,
            city_name=lorem.sentence(),
            last_academic_code=academic['type']
        )

        EngAcademic.objects.create(
            engineer=user,
            type=academic["type"],
            country_code=academic["country_code"],
            school=academic["school"],
            faculty=academic["faculty"],
            in_date=academic["in_date"],
            out_date=academic["out_date"]
        )
        enghopeData = generate_eng_hope()
        EngHope.objects.create(
            engineer=user,
            employ_code=enghopeData["employ_code"],
            place_code1=enghopeData["place_code1"],
            place_code2=enghopeData["place_code2"],
            place_code3=enghopeData["place_code3"],
            payroll_code=enghopeData["payroll_code"],
            payroll_price=enghopeData["payroll_price"],
            remote_code=enghopeData["remote_code"],
            payroll_price_usd=enghopeData["payroll_price_usd"]
        )
        list_eng_job_skill = []
        for i in range(0, random.randint(1, 3)):
            # check duplicate skill_code
            hop_job_skill = generate_hop_job_skill()
            if hop_job_skill["skill_code"] not in list_eng_job_skill:
                list_eng_job_skill.append(hop_job_skill["skill_code"])
                HopeJobSkill.objects.create(
                    engineer=user,
                    job_code=hop_job_skill["job_code"],
                    skill_code=hop_job_skill["skill_code"]
                )
        list_eng_language = []
        for i in range(0, random.randint(1, 3)):
            eng_language = generate_eng_language()
            if eng_language["language_code"] not in list_eng_language:
                list_eng_language.append(eng_language["language_code"])
                EngLanguage.objects.create(
                    engineer=user,
                    language_code=eng_language["language_code"],
                    language_level_type=eng_language["language_level_type"]
                )
        list_eng_career = []
        for i in range(0, random.randint(1, 3)):
            eng_career = generate_eng_career()
            if eng_career["company_name"] not in list_eng_career:
                list_eng_career.append(eng_career["company_name"])
                career = EngCareer.objects.create(
                    engineer=user,
                    career_type=eng_career["career_type"],
                    company_name=eng_career["company_name"],
                    job_description=eng_career["job_description"],
                    entering_date=eng_career["entering_date"],
                    quitting_date=eng_career["quitting_date"]
                )
                list_eng_career_job_skill = []
                for i in range(0, random.randint(2, 8)):
                    career_job_skill = generate_career_job_skill()
                    if career_job_skill["skill_code"] not in list_eng_career_job_skill:
                        list_eng_career_job_skill.append(
                            career_job_skill["skill_code"])
                        CareerJobSkill.objects.create(
                            engineer=user,
                            career=career,
                            job_code=career_job_skill["job_code"],
                            skill_code=career_job_skill["skill_code"],
                            years_of_experience=career_job_skill["years_of_experience"]
                        )

        list_eng_skill = []
        for i in range(0, random.randint(3, 10)):
            eng_skill = generate_eng_skill()
            if eng_skill["skill_code"] not in list_eng_skill:
                list_eng_skill.append(eng_skill["skill_code"])
                EngSkill.objects.create(
                    engineer=user,
                    job_code=eng_skill["job_code"],
                    skill_code=eng_skill["skill_code"],
                    level_type=eng_skill["level_type"]
                )
        list_eng_license = []
        for i in range(0, random.randint(1, 3)):
            eng_license = generate_eng_license()
            if eng_license["licence_code"] not in list_eng_license:
                list_eng_license.append(eng_license["licence_code"])
                EngLicence.objects.create(
                    engineer=user,
                    job_code=eng_license["job_code"],
                    licence_code=eng_license["licence_code"],
                    licence_point=eng_license["licence_point"],
                    licence_name=eng_license["licence_name"],
                    get_date=eng_license["get_date"]
                )
        self_assesment = generate_self_assesment()
        EngSelfAssesment.objects.create(
            engineer=user,
            **self_assesment
        )
        print(f"User {email} created, index {count}")


if __name__ == "__main__":
    generate_user_script()
