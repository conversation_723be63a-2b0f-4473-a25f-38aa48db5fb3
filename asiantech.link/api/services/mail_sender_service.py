import smtplib
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
from core.settings import common
from utils.logger_mixins import log_method_call


class EmailSender:
    def __init__(self):
        self.smtp_server = common.SMTP_SETTINGS['SMTP_SERVER']
        self.smtp_port = common.SMTP_SETTINGS['SMTP_PORT']
        self.sender_email = common.SMTP_SETTINGS['SENDER_MAIL']
        self.password = common.SMTP_SETTINGS['PASSWORD']

    @log_method_call
    def send_email(self, receiver_email, subject, body):
        # Create a multipart message
        message = MIMEMultipart("alternative")
        message["From"] = self.sender_email
        message["To"] = receiver_email
        message["Subject"] = subject

        html_part = MIMEText(body, "html")
        message.attach(html_part)

        # Connect to SMTP server
        with smtplib.SMTP(self.smtp_server, self.smtp_port) as server:
            server.starttls()  # Secure the connection
            server.login(self.sender_email, self.password)
            text = message.as_string()
            server.sendmail(self.sender_email, receiver_email, text)

    @log_method_call
    def send_bulk_email(self, receiver_emails, subject, body):
        """
        Send email to multiple recipients.

        Args:
            receiver_emails (list): A list of email addresses for the recipients.
            subject (str): Subject of the email.
            body (str): HTML body of the email.
        """
        # Ensure receiver_emails is a list
        if not isinstance(receiver_emails, list):
            raise ValueError(
                "receiver_emails must be a list of email addresses.")

        # Connect to SMTP server
        with smtplib.SMTP(self.smtp_server, self.smtp_port) as server:
            server.starttls()  # Secure the connection
            server.login(self.sender_email, self.password)

            # Send emails to all recipients
            for email in receiver_emails:
                # Create a new message for each recipient
                message = MIMEMultipart("alternative")
                message["From"] = self.sender_email
                message["To"] = email
                message["Subject"] = subject

                html_part = MIMEText(body, "html")
                message.attach(html_part)

                # Send email
                server.sendmail(self.sender_email, email, message.as_string())
