import os
from django.db.models.functions import Coalesce
from datetime import date, timedelta
from django.db.models import F, ExpressionWrapper, <PERSON>ration<PERSON>ield, Sum
from api.models.rec import RecApply
from utils.constants import *
from django.utils import timezone as tz
from django.contrib.auth import get_user_model
from api.services import mail_sender_service
from api.models.email_schedules import EmailSchedules
from cryptography.fernet import Fernet
from utils.utils import get_weekday_to_number
User = get_user_model()


def schedule_send_email_service():
    """
    Schedule send email service
    """
    email_schedules_admin_notify = EmailSchedules.objects.filter(
        type=EmailScheduleType.ADMIN_NOTIFY.value,
        is_deleted=0,
        is_valid=1,
    ).order_by("created")
    email_schedules_agree_email = EmailSchedules.objects.filter(
        type=EmailScheduleType.AGREE_EMAIL.value,
        is_deleted=0,
        is_valid=1,
    ).order_by("created")
    list_email_schedules = []
    if email_schedules_admin_notify.count() > 0:
        list_email_schedules.append(email_schedules_admin_notify.last())
    if email_schedules_agree_email.count() > 0:
        list_email_schedules.append(email_schedules_agree_email.last())
    if len(list_email_schedules) > 0:
        for email_schedule in list_email_schedules:
            repeat_type = email_schedule.is_repeat
            weekday = email_schedule.weekday
            send_time = email_schedule.send_time
            send_datetime = email_schedule.send_datetime
            now = tz.now()
            print("now", now)
            # get hour and minute
            send_time = send_time.split(":")
            hour = int(send_time[0])
            minute = int(send_time[1])
            # get day of week

            list_weekdays = []
            if weekday:
                list_weekdays = weekday.split(",")

            # get weekday name
            current_weekday_name = now.strftime("%A")
            current_weekday = get_weekday_to_number(current_weekday_name)
            print(f"weekday: {current_weekday}, {current_weekday_name}")
            can_send_now = False
            current_day = now.day
            current_month = now.month
            current_year = now.year
            if repeat_type == RepeatType.NO_REPEAT.value:

                if current_day == send_datetime.day and current_month == send_datetime.month and current_year == send_datetime.year:
                    if hour == now.hour and minute == now.minute:
                        can_send_now = True
                if hour == now.hour and minute == now.minute:
                    can_send_now = True
            elif repeat_type == RepeatType.WEEKLY.value:
                if str(current_weekday) in list_weekdays:
                    if hour == now.hour and minute == now.minute:
                        can_send_now = True
            elif repeat_type == RepeatType.MONTHLY.value:
                current_day = now.day
                if current_day == send_datetime.day:
                    if hour == now.hour and minute == now.minute:
                        can_send_now = True
            elif repeat_type == RepeatType.YEARLY.value:
                current_day = now.day
                current_month = now.month
                if current_day == send_datetime.day and current_month == send_datetime.month:
                    if hour == now.hour and minute == now.minute:
                        can_send_now = True

            if can_send_now:
                # get all engineers who have not accepted the data policy

                eightDaysAgo = tz.now() - timedelta(days=8)
                # TODO receiver email
                receiver_emails = [
                    '<EMAIL>', '<EMAIL>', '<EMAIL>']
                if email_schedule.type == EmailScheduleType.ADMIN_NOTIFY.value:
                    engineers = User.objects.filter(user_type=UserType.ENGINEER.value,
                                                    deleted=0,
                                                    email__in=receiver_emails,
                                                    )
                else:
                    engineers = User.objects.filter(user_type=UserType.ENGINEER.value,
                                                    deleted=0,
                                                    is_import=1,
                                                    is_data_policy_accept__isnull=True,
                                                    sent_agree_mail_date__isnull=True,
                                                    email__in=receiver_emails,
                                                    ).annotate(
                        duration_sent_agree_mail=ExpressionWrapper(
                            tz.now() - Coalesce(F('sent_agree_mail_date'), eightDaysAgo),
                            output_field=DurationField()
                        ),
                    ).filter(duration_sent_agree_mail__gte=timedelta(days=7))

                email_sender = mail_sender_service.EmailSender()

                if email_schedule.type == EmailScheduleType.ADMIN_NOTIFY.value:
                    receiver_emails = [
                        engineer.email for engineer in engineers]

                    print("email to send", receiver_emails)
                    email_sender.send_bulk_email(
                        receiver_emails=receiver_emails,
                        subject=f"{email_schedule.subject}",
                        body=f"{email_schedule.body}"
                    )
                else:

                    for engineer in engineers:
                        receiver_email = engineer.email

                        print("email to send", receiver_email)
                        user_id = engineer.user_id
                        key = os.getenv('CRYPTOGRAPHY_KEY')
                        f = Fernet(key)
                        content = f"user_id:{user_id}".encode('utf-8')
                        encrypt_data = f.encrypt(content)
                        encrypted_code = encrypt_data.decode('utf-8')
                        env = os.getenv('DJANGO_SETTINGS_MODULE')
                        domain = "https://asiantech.link"
                        if env == 'core.settings.dev' or env == 'core.settings.staging':
                            domain = "https://staging.asiantech.link"
                        link = f"{domain}/update-data-policy?code={
                            encrypted_code}"
                        email_sender.send_email(
                            receiver_email=receiver_email,
                            subject=f"{email_schedule.subject}",
                            body=f"""
                    <!DOCTYPE html>
                        <html lang="en">
                        <head>
                            <meta charset="UTF-8">
                            <meta name="viewport" content="width=device-width, initial-scale=1.0">
                            <title>{email_schedule.subject}</title>
                        </head>
                        <body style="font-family: Arial, sans-serif; background-color: #ffffff; margin: 0; padding: 0; line-height: 1.6;">
                            <div style="max-width: 100%; margin: 20px auto; background: #ffffff; border: 0px solid #ffffff; border-radius: 0px; overflow: hidden;">
                                <!-- Content -->
                                <div style="padding: 0px; color: #333;">
                                    <div style="background-color: #ffffff; border: 0px solid #ddd; border-radius: 5px; padding: 15px; margin-bottom: 10px; font-family: monospace; overflow: auto;">
                                        {email_schedule.body}
                                    </div>
                                </div>

                                <!-- Button -->
                                <div style="text-align: center; margin-top: 10px; margin-bottom: 10px">
                                    <a href="{link}" style="display: inline-block; background-color: #28a745; color: white; text-decoration: none; padding: 10px 20px; font-size: 1em; border-radius: 5px; cursor: pointer;">Review</a>
                                </div>
                            </div>
                        </body>
                        </html>
                        """
                        )
                        engineer.sent_agree_mail_date = tz.now()

                        engineer.save()
