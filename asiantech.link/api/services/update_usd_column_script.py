from faker import Faker
import random
import lorem
from datetime import datetime, timedelta
import json
from api.models.user import User
from api.models.eng import *
from api.models.rec import RecRecruit
from django.contrib.auth import get_user_model
from utils.currency_converter import CurrencyConverter
User = get_user_model()
currency_converter = CurrencyConverter()


def update_usd_column_script():
    # this script will override current value of payroll_price_usd
    eng_hope_records = EngHope.objects.filter(
        payroll_price__isnull=False,
        payroll_code__isnull=False,
    )
    for eng_hope_record in eng_hope_records:
        payroll_price = eng_hope_record.payroll_price
        payroll_code = eng_hope_record.payroll_code
        usd_price = currency_converter.convert(
            payroll_price, payroll_code, "USD")
        eng_hope_record.payroll_price_usd = usd_price
        eng_hope_record.save()

    recruit_records = RecRecruit.objects.filter(
        payroll_price_from__isnull=False,
        payroll_price_to__isnull=False,
        payroll_code__isnull=False,

    )
    for recruit_record in recruit_records:
        payroll_price_from = recruit_record.payroll_price_from
        payroll_price_to = recruit_record.payroll_price_to
        payroll_code = recruit_record.payroll_code
        usd_price_from = currency_converter.convert(
            payroll_price_from, payroll_code, "USD")
        usd_price_to = currency_converter.convert(
            payroll_price_to, payroll_code, "USD")
        recruit_record.payroll_price_from_usd = usd_price_from
        recruit_record.payroll_price_to_usd = usd_price_to
        recruit_record.save()


if __name__ == "__main__":
    update_usd_column_script()
