from api.models.rec import RecNotify, RecApply
from api.models.user import User
from django.utils import timezone
import json
from django.utils.translation import gettext as _
from utils.constants import UserType
from api.services.notify_service.notification_service import NotificationService


class SupportCompanyNotifyService:
    def send_notify_engineer_apply_recruitment(self, token, engineer, apply_id):
        rec_apply = RecApply.objects.filter(
            apply_id=apply_id,
        ).first()
        support_company = rec_apply.support_company
        if support_company is not None:
            support_agents = User.objects.filter(
                company_id=support_company.company_id,
                user_type=UserType.HOST_SUPPORT_AGENCY_STAFF.value
            )
            for support_agent in support_agents:
                payload = {
                    "sender_id": engineer.user_id,
                    "sender_first_name": engineer.first_name,
                    "sender_last_name": engineer.last_name,
                    "notify_type": "support_company_notify_engineer_apply_recruitment",
                    "apply_id": apply_id,
                }
                RecNotify.objects.create(
                    user_id=support_agent.user_id,
                    message=_(
                        f"Please register the document screening result for"),
                    payload=json.dumps(payload),
                    type=0,
                    created=timezone.now()
                )
                NotificationService.send_notification(
                    support_agent.user_id, token, payload)

    def send_notify_engineer_request_time_interview(self, token, engineer, apply_id):
        rec_apply = RecApply.objects.filter(
            apply_id=apply_id,
        ).first()
        support_company = rec_apply.support_company
        if support_company is not None:
            support_agents = User.objects.filter(
                company_id=support_company.company_id,
                user_type=UserType.HOST_SUPPORT_AGENCY_STAFF.value
            )
            for support_agent in support_agents:
                payload = {
                    "sender_id": engineer.user_id,
                    "sender_first_name": engineer.first_name,
                    "sender_last_name": engineer.last_name,
                    "notify_type": "support_company_notify_engineer_request_time_interview",
                    "apply_id": apply_id,
                }
                RecNotify.objects.create(
                    user_id=support_agent.user_id,
                    message=_(
                        f"Please register the interview date and time with"),
                    payload=json.dumps(payload),
                    type=0,
                    created=timezone.now()
                )
                NotificationService.send_notification(
                    support_agent.user_id, token, payload)

    def send_notify_update_status_interview(self, token, engineer, apply_id):
        rec_apply = RecApply.objects.filter(
            apply_id=apply_id,
        ).first()
        support_company = rec_apply.support_company
        if support_company is not None:
            support_agents = User.objects.filter(
                company_id=support_company.company_id,
                user_type=UserType.HOST_SUPPORT_AGENCY_STAFF.value
            )
            for support_agent in support_agents:
                payload = {
                    "sender_id": engineer.user_id,
                    "sender_first_name": engineer.first_name,
                    "sender_last_name": engineer.last_name,
                    "notify_type": "support_company_notify_update_status_interview",
                    "apply_id": apply_id,
                }
                RecNotify.objects.create(
                    user_id=support_agent.user_id,
                    message=_(
                        f"Please register the interview date and time with"),
                    payload=json.dumps({
                        "sender_id": engineer.user_id,
                        "sender_first_name": engineer.first_name,
                        "sender_last_name": engineer.last_name,
                        "notify_type": "support_company_notify_update_status_interview",
                        "apply_id": apply_id,
                    }),
                    type=0,
                    created=timezone.now()
                )
                NotificationService.send_notification(
                    support_agent.user_id, token, payload)

    def send_notify_engineer_sign_contract(self, token, engineer, apply_id):
        rec_apply = RecApply.objects.filter(
            apply_id=apply_id,
        ).first()
        support_company = rec_apply.support_company
        if support_company is not None:
            support_agents = User.objects.filter(
                company_id=support_company.company_id,
                user_type=UserType.HOST_SUPPORT_AGENCY_STAFF.value
            )
            for support_agent in support_agents:
                payload = {
                    "sender_id": engineer.user_id,
                    "sender_first_name": engineer.first_name,
                    "sender_last_name": engineer.last_name,
                    "notify_type": "support_company_notify_engineer_sign_contract",
                    "apply_id": apply_id,
                }
                RecNotify.objects.create(
                    user_id=support_agent.user_id,
                    message=_(
                        f"Please register the interview date and time with"),
                    payload=json.dumps(payload),
                    type=0,
                    created=timezone.now()
                )
                NotificationService.send_notification(
                    support_agent.user_id, token, payload)
