from api.models.rec import RecNotify, RecApply
from api.models.user import User
from django.utils import timezone
import json
from django.utils.translation import gettext as _
from utils.constants import UserType
from api.services.notify_service.notification_service import NotificationService


class CompanyNotifyService:
    def send_notify_engineer_apply_recruitment(self, token, engineer, apply_id):
        rec_apply = RecApply.objects.filter(
            apply_id=apply_id,
        ).first()
        host_company = rec_apply.host_company
        host_agents = User.objects.filter(
            company_id=host_company.company_id,
            user_type=UserType.HOST_COMPANY_STAFF.value
        )

        payload = json.dumps({
            "sender_id": engineer.user_id,
            "sender_first_name": engineer.first_name,
            "sender_last_name": engineer.last_name,
            "notify_type": "company_notify_engineer_apply_recruitment",
            "apply_id": apply_id,
        })
        for host_agent in host_agents:
            RecNotify.objects.create(
                user_id=host_agent.user_id,
                message=_(
                    f"The interview result registration for is incomplete"),
                payload=payload,
                type=0,
                created=timezone.now()
            )
            # Send notification to user
            NotificationService.send_notification(
                engineer.user_id, token, payload)

    def send_notify_engineer_request_time_interview(self, token, engineer, apply_id):
        rec_apply = RecApply.objects.filter(
            apply_id=apply_id,
        ).first()
        host_company = rec_apply.host_company
        host_agents = User.objects.filter(
            company_id=host_company.company_id,
            user_type=UserType.HOST_COMPANY_STAFF.value
        )
        for host_agent in host_agents:
            payload = {
                "sender_id": engineer.user_id,
                "sender_first_name": engineer.first_name,
                "sender_last_name": engineer.last_name,
                "notify_type": "company_notify_engineer_request_time_interview",
                "apply_id": apply_id,
            }
            RecNotify.objects.create(
                user_id=host_agent.user_id,
                message=_(
                    f"Please register the interview date and time with"),
                payload=json.dumps(payload),
                type=0,
                created=timezone.now()
            )
            NotificationService.send_notification(
                engineer.user_id, token, payload)

    def send_notify_update_status_interview(self, token, engineer, apply_id):
        rec_apply = RecApply.objects.filter(
            apply_id=apply_id,
        ).first()
        host_company = rec_apply.host_company
        host_agents = User.objects.filter(
            company_id=host_company.company_id,
            user_type=UserType.HOST_COMPANY_STAFF.value
        )
        for host_agent in host_agents:
            payload = {
                "sender_id": engineer.user_id,
                "sender_first_name": engineer.first_name,
                "sender_last_name": engineer.last_name,
                "notify_type": "company_notify_update_status_interview",
                "apply_id": apply_id,
            }
            RecNotify.objects.create(
                user_id=host_agent.user_id,
                message=_(
                    f"Please register the interview outcome for"),

                payload=json.dumps(payload),
                type=0,
                created=timezone.now()
            )
            NotificationService.send_notification(
                engineer.user_id, token, payload)

    def send_notify_engineer_sign_contract(self, token, engineer, apply_id):
        rec_apply = RecApply.objects.filter(
            apply_id=apply_id,
        ).first()
        host_company = rec_apply.host_company
        host_agents = User.objects.filter(
            company_id=host_company.company_id,
            user_type=UserType.HOST_COMPANY_STAFF.value
        )
        for host_agent in host_agents:
            payload = {
                "sender_id": engineer.user_id,
                "sender_first_name": engineer.first_name,
                "sender_last_name": engineer.last_name,
                "notify_type": "company_notify_engineer_sign_contract",
                "apply_id": apply_id,
            }
            RecNotify.objects.create(
                user_id=host_agent.user_id,
                message=_(
                    f"Please sign the offer acceptance and employment contract with"),
                payload=json.dumps(payload),
                type=0,
                created=timezone.now()
            )
            NotificationService.send_notification(
                engineer.user_id, token, payload)
