import websockets
import json
import asyncio
from django.conf import settings
from asgiref.sync import async_to_sync
from django.utils.translation import gettext as _


class NotificationService:
    SOCKET_SERVER = "ws://127.0.0.1:8001/ws/notification/?token={token}"

    @classmethod
    async def _send_ws_message(cls, ws_url, notification_data):
        """Async function to send a single websocket message"""
        try:
            async with websockets.connect(ws_url) as websocket:
                await websocket.send(json.dumps(notification_data))
                print(f"Notification sent successfully")
        except Exception as e:
            print(f"Failed to send notification: {str(e)}")
            print(f"Notification data: {notification_data}")

    @classmethod
    def send_notification(cls, user_id, token, message, notify_type=None, payload=None):
        """
        Send single notification via WebSocket
        Args:
            user_id: ID of user to receive notification
            token: Authorization token
            message: Notification message
            notify_type: Type of notification (optional)
            payload: Additional data (optional)
        """
        if not user_id:
            print("Error: user_id is required")
            return

        # Format websocket URL
        ws_url = cls.SOCKET_SERVER.format(token=token)
        print(f"Connecting to WebSocket server at: {ws_url}")

        notification_data = {
            "user_id": str(user_id),
            "message": message,
            "type": notify_type or "notification",
        }
        if payload:
            notification_data["payload"] = payload

        # Create and run a new event loop just for this single call
        asyncio.run(cls._send_ws_message(ws_url, notification_data))
