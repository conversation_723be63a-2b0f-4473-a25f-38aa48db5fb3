from api.models.rec import RecNotify, RecApply
from django.utils import timezone
import json
from django.utils.translation import gettext as _
from api.services.notify_service.notification_service import NotificationService


class EngineerNotifyService:
    def send_notify_company_request_interview(self, token, sender, apply_id):
        rec_apply = RecApply.objects.filter(
            apply_id=apply_id,
        ).first()
        engineer = rec_apply.engineer

        payload = {
            "sender_id": sender.user_id,
            "sender_first_name": sender.first_name,
            "sender_last_name": sender.last_name,
            "notify_type": "engineer_notify_company_request_interview",
            "recruit_id": rec_apply.recruit.recruit_id,
            "apply_id": apply_id,
        }
        RecNotify.objects.create(
            user_id=engineer.user_id,
            message=_(
                f"Please respond to the interview request from"),
            payload=json.dumps(payload),
            type=0,
            created=timezone.now()
        )
        NotificationService.send_notification(
            engineer.user_id, token, payload)

    def send_notify_company_job_offer(self, token, host_agent, apply_id):
        rec_apply = RecApply.objects.filter(
            apply_id=apply_id,
        ).first()
        engineer = rec_apply.engineer
        payload = {
            "sender_id": host_agent.user_id,
            "sender_first_name": host_agent.first_name,
            "sender_last_name": host_agent.last_name,
            "notify_type": "engineer_notify_company_job_offer",
            "recruit_id": rec_apply.recruit.recruit_id,
            "apply_id": apply_id,
        }
        RecNotify.objects.create(
            user_id=engineer.user_id,
            message=_(
                f"Please respond to the job offer from"),
            payload=json.dumps(payload),
            type=0,
            created=timezone.now()
        )
        NotificationService.send_notification(
            engineer.user_id, token, payload)
