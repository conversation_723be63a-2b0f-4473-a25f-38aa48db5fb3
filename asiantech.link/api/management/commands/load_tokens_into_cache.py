from django.core.management.base import BaseCommand
from django.core.cache import cache
from django.utils import timezone
from api.models.black_listed_access_token import UserAccessToken

class Command(BaseCommand):
    help = 'Load access tokens into cache'

    def handle(self, *args, **kwargs):
        try:
            tokens = UserAccessToken.objects.filter(expires_at__gte=timezone.now())

            for token in tokens:
                expiration = token.expires_at
                timeout = (expiration - timezone.now()).total_seconds()
                cache.set(token.token, token.user_id, timeout=timeout)
            self.stdout.write(self.style.SUCCESS('Successfully loaded tokens into cache'))        
        except Exception as e:
            self.stdout.write(self.style.ERROR('Failed to load tokens into cache'))
            self.stdout.write(self.style.ERROR(str(e)))
