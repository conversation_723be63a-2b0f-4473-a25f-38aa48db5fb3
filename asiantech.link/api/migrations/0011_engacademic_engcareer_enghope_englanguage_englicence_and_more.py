# Generated by Django 5.0.6 on 2024-07-02 04:13

import utils.custom_fields
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('api', '0010_blacklistedaccesstoken'),
    ]

    operations = [
        migrations.CreateModel(
            name='EngAcademic',
            fields=[
                ('academic_id', models.AutoField(db_comment='学歴ID', primary_key=True, serialize=False)),
                ('type', utils.custom_fields.Bit4Field(db_comment='卒業中退種別', default=None, null=True)),
                ('country_code', models.CharField(db_comment='国コード', default=None, max_length=3, null=True)),
                ('school', utils.custom_fields.TinyTextField(db_comment='学校名', default=None, null=True)),
                ('faculty', utils.custom_fields.TinyTextField(db_comment='学部', default=None, null=True)),
                ('in_date', models.DateField(db_comment='入学日', default=None, null=True)),
                ('out_date', models.DateField(db_comment='卒業/中退日', default=None, null=True)),
                ('created', utils.custom_fields.DateTimeField(db_comment='初期登録日時', default=None, null=True)),
                ('updated', utils.custom_fields.DateTimeField(db_comment='最終更新日時', default=None, null=True)),
            ],
            options={
                'db_table': 'eng_academic',
                'db_table_comment': '学歴',
                'managed': False,
            },
        ),
        migrations.CreateModel(
            name='EngCareer',
            fields=[
                ('career_id', models.AutoField(db_comment='職務経歴ID', primary_key=True, serialize=False)),
                ('career_code', models.CharField(db_comment='職種コード', max_length=3, null=True)),
                ('career_type', utils.custom_fields.Bit2Field(db_comment='在職種別', null=True)),
                ('company_name', utils.custom_fields.TinyTextField(db_comment='会社名', null=True)),
                ('company_department', utils.custom_fields.TinyTextField(db_comment='部署名', null=True)),
                ('entering_date', models.DateField(db_comment='入社日', null=True)),
                ('quitting_date', models.DateField(db_comment='退社日', null=True)),
                ('created', utils.custom_fields.DateTimeField(db_comment='初期登録日時', null=True)),
                ('updated', utils.custom_fields.DateTimeField(db_comment='最終更新日時', null=True)),
            ],
            options={
                'db_table': 'eng_career',
                'db_table_comment': '職務経歴',
                'managed': False,
            },
        ),
        migrations.CreateModel(
            name='EngHope',
            fields=[
                ('hope_id', models.AutoField(db_comment='希望条件ID', primary_key=True, serialize=False)),
                ('save_type', utils.custom_fields.Bit3Field(db_comment='保存種別', null=True)),
                ('job_code', models.CharField(db_comment='希望職種コード', max_length=3, null=True)),
                ('employ_code', models.CharField(db_comment='希望雇用形態コード', max_length=2, null=True)),
                ('place_code1', models.CharField(db_comment='希望勤務地コード1', max_length=9, null=True)),
                ('place_code2', models.CharField(db_comment='希望勤務地コード2', max_length=9, null=True)),
                ('place_code3', models.CharField(db_comment='希望勤務地コード3', max_length=9, null=True)),
                ('payroll_code', models.CharField(db_comment='希望給与通貨コード', max_length=3, null=True)),
                ('payroll_price', models.DecimalField(db_comment='希望給与金額', decimal_places=3, max_digits=10, null=True)),
                ('created', utils.custom_fields.DateTimeField(db_comment='初期登録日時', null=True)),
                ('updated', utils.custom_fields.DateTimeField(db_comment='最終更新日時', null=True)),
            ],
            options={
                'db_table': 'eng_hope',
                'db_table_comment': '希望条件',
                'managed': False,
            },
        ),
        migrations.CreateModel(
            name='EngLanguage',
            fields=[
                ('language_id', models.AutoField(db_comment='言語ID', primary_key=True, serialize=False)),
                ('language_code', models.CharField(db_comment='言語コード', max_length=2, null=True)),
                ('language_level_type', utils.custom_fields.Bit5Field(db_comment='言語レベル種別', null=True)),
                ('created', utils.custom_fields.DateTimeField(db_comment='初期登録日時', null=True)),
                ('updated', utils.custom_fields.DateTimeField(db_comment='最終更新日時', null=True)),
            ],
            options={
                'db_table': 'eng_language',
                'db_table_comment': '言語',
                'managed': False,
            },
        ),
        migrations.CreateModel(
            name='EngLicence',
            fields=[
                ('licence_id', models.AutoField(db_comment='資格ID', primary_key=True, serialize=False)),
                ('job_code', models.CharField(db_comment='職種コード', max_length=3, null=True)),
                ('licence_code', models.CharField(db_comment='資格コード', max_length=3, null=True)),
                ('licence_point', models.SmallIntegerField(db_comment='資格点数', null=True)),
                ('get_date', models.DateField(db_comment='取得年月日', null=True)),
                ('created', utils.custom_fields.DateTimeField(db_comment='初期登録日時', null=True)),
                ('updated', utils.custom_fields.DateTimeField(db_comment='最終更新日時', null=True)),
            ],
            options={
                'db_table': 'eng_licence',
                'db_table_comment': '資格',
                'managed': False,
            },
        ),
        migrations.CreateModel(
            name='EngSkill',
            fields=[
                ('skill_id', models.AutoField(db_comment='職務保有スキルID', primary_key=True, serialize=False)),
                ('job_code', models.CharField(db_comment='職種コード', max_length=3, null=True)),
                ('skill_code', models.CharField(db_comment='スキルコード', max_length=3, null=True)),
                ('level_type', utils.custom_fields.Bit4Field(db_comment='スキルレベル種別', null=True)),
                ('created', utils.custom_fields.DateTimeField(db_comment='初期登録日時', null=True)),
                ('updated', utils.custom_fields.DateTimeField(db_comment='最終更新日時', null=True)),
            ],
            options={
                'db_table': 'eng_skill',
                'db_table_comment': 'スキル',
                'managed': False,
            },
        ),
        migrations.CreateModel(
            name='EngTrading',
            fields=[
                ('eng_trading_id', models.AutoField(db_comment='取引条件ID', primary_key=True, serialize=False)),
                ('created', utils.custom_fields.DateTimeField(db_comment='初期登録日時', null=True)),
                ('updated', utils.custom_fields.DateTimeField(db_comment='最終更新日時', null=True)),
            ],
            options={
                'db_table': 'eng_trading',
                'db_table_comment': '技術者紹介取引条件',
                'managed': False,
            },
        ),
        migrations.CreateModel(
            name='MapEngAgc',
            fields=[
                ('engineer_id', models.IntegerField(db_comment='技術者ID', primary_key=True, serialize=False)),
                ('agency_company_id', models.IntegerField(db_comment='技術者紹介機関ID')),
                ('agency_agent_id', models.IntegerField(db_comment='技術者紹介機関担当者ID', null=True)),
                ('eng_trading_id', models.IntegerField(db_comment='技術者紹介条件ID', null=True)),
                ('introduction_pr', models.CharField(db_comment='紹介PR', max_length=10000, null=True)),
                ('introduction_start_datetime', utils.custom_fields.DateTimeField(db_comment='紹介開始日時', null=True)),
                ('create_engineer_id', models.IntegerField(db_comment='初期登録技術者ID', null=True)),
                ('create_datetime', utils.custom_fields.DateTimeField(db_comment='初期登録日時', null=True)),
                ('update_engineer_id', models.IntegerField(db_comment='最終更新技術者ID', null=True)),
                ('update_datetime', utils.custom_fields.DateTimeField(db_comment='最終更新日時', null=True)),
            ],
            options={
                'db_table': 'map_eng_agc',
                'db_table_comment': '技術者紹介機関紐づけ',
                'managed': False,
            },
        ),
        migrations.RemoveField(
            model_name='user',
            name='is_verified',
        ),
        migrations.RemoveField(
            model_name='user',
            name='mailauth_code',
        ),
        migrations.RemoveField(
            model_name='user',
            name='mailauth_datetime',
        ),
    ]
