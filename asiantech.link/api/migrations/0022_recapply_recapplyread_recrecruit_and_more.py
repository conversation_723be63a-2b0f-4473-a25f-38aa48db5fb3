# Generated by Django 5.0.6 on 2024-07-23 06:30

import utils.custom_fields
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('api', '0021_merge_20240719_0401'),
    ]

    operations = [
        migrations.CreateModel(
            name='RecApply',
            fields=[
                ('apply_id', models.AutoField(db_comment='応募ID', primary_key=True, serialize=False)),
                ('recruit_id', models.IntegerField(db_comment='募集要項ID', null=True)),
                ('engineer_id', models.IntegerField(db_comment='技術者ID')),
                ('agency_company_id', models.IntegerField(db_comment='技術者紹介機関ID', null=True)),
                ('agency_agent_id', models.IntegerField(db_comment='技術者紹介機関担当者ID', null=True)),
                ('host_company_id', models.IntegerField(db_comment='受入企業ID')),
                ('host_agent_id', models.IntegerField(db_comment='受入企業担当者ID', null=True)),
                ('support_company_id', models.IntegerField(db_comment='受入サポート機関ID', null=True)),
                ('support_agent_id', models.IntegerField(db_comment='受入サポート機関担当者ID', null=True)),
                ('recruit_progress_code', models.CharField(db_comment='採用進捗状況コード', max_length=6, null=True)),
                ('progress_update_datetime', utils.custom_fields.DateTimeField(db_comment='進捗変更日時', null=True)),
                ('interview_datetime', utils.custom_fields.DateTimeField(db_comment='面接日時', null=True)),
                ('joing_date', models.DateField(db_comment='入社予定日', null=True)),
                ('job_code', models.CharField(db_comment='採用職種コード', max_length=9, null=True)),
                ('employ_code', models.CharField(db_comment='採用雇用形態コード', max_length=9, null=True)),
                ('place_code', models.CharField(db_comment='就業勤務地コード', max_length=9, null=True)),
                ('payroll_code', models.CharField(db_comment='採用給与通貨コード', max_length=3, null=True)),
                ('payroll_price', models.DecimalField(db_comment='採用給与金額', decimal_places=3, max_digits=10, null=True)),
                ('benefits', models.CharField(db_comment='その他待遇', max_length=10000, null=True)),
                ('created', utils.custom_fields.DateTimeField(db_comment='初期登録日時', null=True)),
                ('updated', utils.custom_fields.DateTimeField(db_comment='最終更新日時', null=True)),
            ],
            options={
                'db_table': 'rec_apply',
                'db_table_comment': '応募状況',
                'managed': False,
            },
        ),
        migrations.CreateModel(
            name='RecApplyRead',
            fields=[
                ('apply_id', models.AutoField(db_comment='応募ID', primary_key=True, serialize=False)),
                ('user_id', models.IntegerField(db_comment='技術者/担当者ID')),
                ('created', utils.custom_fields.DateTimeField(db_comment='初期登録日時', null=True)),
            ],
            options={
                'db_table': 'rec_apply_read',
                'db_table_comment': '応募未読',
                'managed': False,
            },
        ),
        migrations.CreateModel(
            name='RecRecruit',
            fields=[
                ('recruit_id', models.AutoField(db_comment='募集要項ID', primary_key=True, serialize=False)),
                ('save_type', utils.custom_fields.Bit3Field(db_comment='保存種別', null=True)),
                ('title', utils.custom_fields.TinyTextField(db_comment='タイトル', null=True)),
                ('catch_copy', models.TextField(blank=True, db_comment='キャッチコピー', null=True)),
                ('start_date', models.DateTimeField(db_comment='募集開始日', null=True)),
                ('end_date', models.DateTimeField(db_comment='募集終了日', null=True)),
                ('display_flag', utils.custom_fields.Bit3Field(db_comment='表示フラグ', null=True)),
                ('job_code', models.CharField(blank=True, db_comment='募集職種コード', max_length=3, null=True)),
                ('employ_code', models.CharField(db_comment='募集雇用形態コード', max_length=3, null=True)),
                ('content', models.CharField(db_comment='募集要項', max_length=10000, null=True)),
                ('place_code1', models.CharField(db_comment='就業勤務地コード1', max_length=9, null=True)),
                ('place_code2', models.CharField(db_comment='就業勤務地コード2', max_length=9, null=True)),
                ('place_code3', models.CharField(db_comment='就業勤務地コード3', max_length=9, null=True)),
                ('payroll_code', models.CharField(db_comment='募集給与通貨コード', max_length=3, null=True)),
                ('payroll_price_from', models.DecimalField(db_comment='募集給与金額(より)', decimal_places=3, max_digits=10, null=True)),
                ('payroll_price_to', models.DecimalField(db_comment='募集給与金額(まで)', decimal_places=3, max_digits=10, null=True)),
                ('country_code', models.CharField(db_comment='募集国籍コード', max_length=3, null=True)),
                ('age_from', utils.custom_fields.Bit7Field(db_comment='募集年齢(より)', null=True)),
                ('age_to', utils.custom_fields.Bit7Field(db_comment='募集年齢(まで）', null=True)),
                ('sex_type', utils.custom_fields.Bit4Field(db_comment='募集性別', null=True)),
                ('pref_code1', models.CharField(db_comment='募集国別県コード1', max_length=6, null=True)),
                ('pref_code2', models.CharField(db_comment='募集国別県コード2', max_length=6, null=True)),
                ('pref_code3', models.CharField(db_comment='募集国別県コード3', max_length=6, null=True)),
                ('last_academic_code', models.CharField(db_comment='最終学歴コード(以上)', max_length=3, null=True)),
                ('language_code1', models.CharField(db_comment='言語コード1', max_length=2, null=True)),
                ('language_level_type1', utils.custom_fields.Bit4Field(db_comment='言語スキル種別1', null=True)),
                ('language_code2', models.CharField(db_comment='言語コード2', max_length=2, null=True)),
                ('language_level_type2', utils.custom_fields.Bit4Field(db_comment='言語スキル種別2', null=True)),
                ('experienced_job_code', models.CharField(blank=True, db_comment='経験職種コード', max_length=3, null=True)),
                ('years_of_experience', models.IntegerField(blank=True, db_comment='経験年数', null=True)),
                ('skill_job_code1', models.CharField(db_comment='職種コード1', max_length=3, null=True)),
                ('skill_code1', models.CharField(db_comment='スキルコード1', max_length=3, null=True)),
                ('skill_level_type1', utils.custom_fields.Bit4Field(db_comment='スキルレベル種別1', null=True)),
                ('skill_job_code2', models.CharField(db_comment='職種コード2', max_length=2, null=True)),
                ('skill_code2', models.CharField(db_comment='スキルコード2', max_length=3, null=True)),
                ('skill_level_type2', utils.custom_fields.Bit4Field(db_comment='スキルレベル種別2', null=True)),
                ('skill_job_code3', models.CharField(db_comment='職種コード3', max_length=2, null=True)),
                ('skill_code3', models.CharField(db_comment='スキルコード3', max_length=3, null=True)),
                ('skill_level_type3', utils.custom_fields.Bit4Field(db_comment='スキルレベル種別3', null=True)),
                ('create_agent_id', models.IntegerField(db_comment='初期登録企業ID', null=True)),
                ('created', utils.custom_fields.DateTimeField(auto_now_add=True, db_comment='初期登録日時', null=True)),
                ('update_agent_id', models.IntegerField(db_comment='最終更新企業ID', null=True)),
                ('updated', utils.custom_fields.DateTimeField(db_comment='最終更新日時', null=True)),
                ('licence_code1', models.CharField(blank=True, db_comment='資格コード1', max_length=3, null=True)),
                ('licence_point1', models.SmallIntegerField(blank=True, db_comment='資格点数1', null=True)),
                ('licence_code2', models.CharField(blank=True, db_comment='資格コード2', max_length=3, null=True)),
                ('licence_point2', models.SmallIntegerField(blank=True, db_comment='資格点数2', null=True)),
                ('licence_code3', models.CharField(blank=True, db_comment='資格コード3', max_length=3, null=True)),
                ('licence_point3', models.SmallIntegerField(blank=True, db_comment='資格点数3', null=True)),
            ],
            options={
                'db_table': 'rec_recruit',
                'db_table_comment': '募集要項',
                'managed': False,
            },
        ),
        migrations.AlterField(
            model_name='user',
            name='passport_image_path',
            field=models.CharField(db_comment='パスポート（ID）写真ファイル名', default=None, max_length=200, null=True),
        ),
        migrations.AlterField(
            model_name='user',
            name='profile_image_path',
            field=models.CharField(db_comment='プロフィール写真ファイル名', default=None, max_length=200, null=True),
        ),
    ]
