# Generated by Django 5.0.6 on 2024-05-31 06:22

import django.contrib.auth.validators
import django.utils.timezone
import utils.custom_fields
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ("auth", "0012_alter_user_first_name_max_length"),
    ]

    operations = [
        migrations.CreateModel(
            name="ComCompany",
            fields=[
                (
                    "company_id",
                    models.AutoField(
                        db_comment="企業ID", primary_key=True, serialize=False
                    ),
                ),
                ("user_type", utils.custom_fields.Bit3Field(db_comment="企業ID種別")),
                ("name", models.CharField(db_comment="会社名", max_length=200, null=True)),
                (
                    "about_us",
                    models.CharField(db_comment="会社概要", max_length=5000, null=True),
                ),
                (
                    "business_details",
                    models.CharField(db_comment="事業内容", max_length=5000, null=True),
                ),
                (
                    "employees_type",
                    utils.custom_fields.TinyInt4Field(db_comment="従業員数種別", null=True),
                ),
                (
                    "country_code",
                    models.CharField(db_comment="国コード", max_length=3, null=True),
                ),
                (
                    "address_code",
                    models.CharField(db_comment="国別県市コード", max_length=9, null=True),
                ),
                (
                    "address",
                    models.CharField(db_comment="住所", max_length=200, null=True),
                ),
                ("tel", models.CharField(db_comment="電話番号", max_length=20, null=True)),
                (
                    "logo_image_path",
                    models.CharField(db_comment="会社ロゴファイル名", max_length=75, null=True),
                ),
                (
                    "pr_image_path",
                    models.CharField(
                        db_comment="会社PR写真ファイル名", max_length=75, null=True
                    ),
                ),
                (
                    "contact_mail",
                    models.CharField(
                        db_comment="問い合わせ先メールアドレス", max_length=100, null=True
                    ),
                ),
                (
                    "web_url",
                    utils.custom_fields.TinyTextField(db_comment="Webサイト", null=True),
                ),
                (
                    "introdoction_url",
                    utils.custom_fields.TinyTextField(db_comment="会社紹介URL", null=True),
                ),
                (
                    "memo",
                    utils.custom_fields.TinyTextField(db_comment="管理用メモ", null=True),
                ),
                (
                    "open_flag",
                    utils.custom_fields.DateTimeField(db_comment="掲載フラグ", null=True),
                ),
                (
                    "deleted",
                    utils.custom_fields.Bit2Field(db_comment="削除フラグ", null=True),
                ),
                (
                    "created",
                    utils.custom_fields.DateTimeField(db_comment="初期登録日時", null=True),
                ),
                (
                    "updated",
                    utils.custom_fields.DateTimeField(db_comment="最終更新日時", null=True),
                ),
            ],
            options={
                "db_table": "com_company",
                "db_table_comment": "企業",
                "managed": False,
            },
        ),
        migrations.CreateModel(
            name="ComContract",
            fields=[
                (
                    "contract_id",
                    models.AutoField(
                        db_comment="契約ID", primary_key=True, serialize=False
                    ),
                ),
                ("company_id", models.IntegerField(db_comment="企業ID")),
                ("contract_start", models.DateField(db_comment="契約開始日", null=True)),
                ("contract_end", models.DateField(db_comment="契約終了日", null=True)),
                (
                    "created",
                    utils.custom_fields.DateTimeField(db_comment="初期登録日時", null=True),
                ),
                (
                    "updated",
                    utils.custom_fields.DateTimeField(db_comment="最終更新日時", null=True),
                ),
            ],
            options={
                "db_table": "com_contract",
                "db_table_comment": "契約",
                "managed": False,
            },
        ),
        migrations.CreateModel(
            name="ComTrading",
            fields=[
                (
                    "com_trading_id",
                    models.AutoField(
                        db_comment="取引条件ID", primary_key=True, serialize=False
                    ),
                ),
                (
                    "created",
                    utils.custom_fields.DateTimeField(db_comment="初期登録日時", null=True),
                ),
                (
                    "updated",
                    utils.custom_fields.DateTimeField(db_comment="最終更新日時", null=True),
                ),
            ],
            options={
                "db_table": "com_trading",
                "db_table_comment": "受入サポート機関取引条件",
                "managed": False,
            },
        ),
        migrations.CreateModel(
            name="EngAcademic",
            fields=[
                (
                    "academic_id",
                    models.AutoField(
                        db_comment="学歴ID", primary_key=True, serialize=False
                    ),
                ),
                (
                    "engineer_id",
                    models.IntegerField(db_comment="技術者ID", default=None, null=True),
                ),
                (
                    "type",
                    utils.custom_fields.Bit4Field(
                        db_comment="卒業中退種別", default=None, null=True
                    ),
                ),
                (
                    "country_code",
                    models.CharField(
                        db_comment="国コード", default=None, max_length=3, null=True
                    ),
                ),
                (
                    "school",
                    utils.custom_fields.TinyTextField(
                        db_comment="学校名", default=None, null=True
                    ),
                ),
                (
                    "faculty",
                    utils.custom_fields.TinyTextField(
                        db_comment="学部", default=None, null=True
                    ),
                ),
                (
                    "in_date",
                    models.DateField(db_comment="入学日", default=None, null=True),
                ),
                (
                    "out_date",
                    models.DateField(db_comment="卒業/中退日", default=None, null=True),
                ),
                (
                    "created",
                    utils.custom_fields.DateTimeField(
                        db_comment="初期登録日時", default=None, null=True
                    ),
                ),
                (
                    "updated",
                    utils.custom_fields.DateTimeField(
                        db_comment="最終更新日時", default=None, null=True
                    ),
                ),
            ],
            options={
                "db_table": "eng_academic",
                "db_table_comment": "学歴",
                "managed": False,
            },
        ),
        migrations.CreateModel(
            name="EngCareer",
            fields=[
                (
                    "career_id",
                    models.AutoField(
                        db_comment="職務経歴ID", primary_key=True, serialize=False
                    ),
                ),
                ("engineer_id", models.IntegerField(db_comment="技術者ID", null=True)),
                (
                    "career_code",
                    models.CharField(db_comment="職種コード", max_length=3, null=True),
                ),
                (
                    "career_type",
                    utils.custom_fields.Bit2Field(db_comment="在職種別", null=True),
                ),
                (
                    "company_name",
                    utils.custom_fields.TinyTextField(db_comment="会社名", null=True),
                ),
                (
                    "company_department",
                    utils.custom_fields.TinyTextField(db_comment="部署名", null=True),
                ),
                ("entering_date", models.DateField(db_comment="入社日", null=True)),
                ("quitting_date", models.DateField(db_comment="退社日", null=True)),
                (
                    "created",
                    utils.custom_fields.DateTimeField(db_comment="初期登録日時", null=True),
                ),
                (
                    "updated",
                    utils.custom_fields.DateTimeField(db_comment="最終更新日時", null=True),
                ),
            ],
            options={
                "db_table": "eng_career",
                "db_table_comment": "職務経歴",
                "managed": False,
            },
        ),
        migrations.CreateModel(
            name="EngHope",
            fields=[
                (
                    "hope_id",
                    models.AutoField(
                        db_comment="希望条件ID", primary_key=True, serialize=False
                    ),
                ),
                ("engineer_id", models.IntegerField(db_comment="技術者ID", null=True)),
                (
                    "save_type",
                    utils.custom_fields.Bit3Field(db_comment="保存種別", null=True),
                ),
                (
                    "job_code",
                    models.CharField(db_comment="希望職種コード", max_length=3, null=True),
                ),
                (
                    "employ_code",
                    models.CharField(db_comment="希望雇用形態コード", max_length=2, null=True),
                ),
                (
                    "place_code1",
                    models.CharField(db_comment="希望勤務地コード1", max_length=9, null=True),
                ),
                (
                    "place_code2",
                    models.CharField(db_comment="希望勤務地コード2", max_length=9, null=True),
                ),
                (
                    "place_code3",
                    models.CharField(db_comment="希望勤務地コード3", max_length=9, null=True),
                ),
                (
                    "payroll_code",
                    models.CharField(db_comment="希望給与通貨コード", max_length=3, null=True),
                ),
                (
                    "payroll_price",
                    models.DecimalField(
                        db_comment="希望給与金額", decimal_places=3, max_digits=10, null=True
                    ),
                ),
                (
                    "created",
                    utils.custom_fields.DateTimeField(db_comment="初期登録日時", null=True),
                ),
                (
                    "updated",
                    utils.custom_fields.DateTimeField(db_comment="最終更新日時", null=True),
                ),
            ],
            options={
                "db_table": "eng_hope",
                "db_table_comment": "希望条件",
                "managed": False,
            },
        ),
        migrations.CreateModel(
            name="EngLanguage",
            fields=[
                (
                    "language_id",
                    models.AutoField(
                        db_comment="言語ID", primary_key=True, serialize=False
                    ),
                ),
                ("engineer_id", models.IntegerField(db_comment="技術者ID", null=True)),
                (
                    "language_code",
                    models.CharField(db_comment="言語コード", max_length=2, null=True),
                ),
                (
                    "language_level_type",
                    utils.custom_fields.Bit5Field(db_comment="言語レベル種別", null=True),
                ),
                (
                    "created",
                    utils.custom_fields.DateTimeField(db_comment="初期登録日時", null=True),
                ),
                (
                    "updated",
                    utils.custom_fields.DateTimeField(db_comment="最終更新日時", null=True),
                ),
            ],
            options={
                "db_table": "eng_language",
                "db_table_comment": "言語",
                "managed": False,
            },
        ),
        migrations.CreateModel(
            name="EngLicence",
            fields=[
                (
                    "licence_id",
                    models.AutoField(
                        db_comment="資格ID", primary_key=True, serialize=False
                    ),
                ),
                ("engineer_id", models.IntegerField(db_comment="技術者ID", null=True)),
                (
                    "job_code",
                    models.CharField(db_comment="職種コード", max_length=3, null=True),
                ),
                (
                    "licence_code",
                    models.CharField(db_comment="資格コード", max_length=3, null=True),
                ),
                (
                    "licence_point",
                    models.SmallIntegerField(db_comment="資格点数", null=True),
                ),
                ("get_date", models.DateField(db_comment="取得年月日", null=True)),
                (
                    "created",
                    utils.custom_fields.DateTimeField(db_comment="初期登録日時", null=True),
                ),
                (
                    "updated",
                    utils.custom_fields.DateTimeField(db_comment="最終更新日時", null=True),
                ),
            ],
            options={
                "db_table": "eng_licence",
                "db_table_comment": "資格",
                "managed": False,
            },
        ),
        migrations.CreateModel(
            name="EngSkill",
            fields=[
                (
                    "skill_id",
                    models.AutoField(
                        db_comment="職務保有スキルID", primary_key=True, serialize=False
                    ),
                ),
                ("engineer_id", models.IntegerField(db_comment="技術者ID", null=True)),
                (
                    "job_code",
                    models.CharField(db_comment="職種コード", max_length=3, null=True),
                ),
                (
                    "skill_code",
                    models.CharField(db_comment="スキルコード", max_length=3, null=True),
                ),
                (
                    "level_type",
                    utils.custom_fields.Bit4Field(db_comment="スキルレベル種別", null=True),
                ),
                (
                    "created",
                    utils.custom_fields.DateTimeField(db_comment="初期登録日時", null=True),
                ),
                (
                    "updated",
                    utils.custom_fields.DateTimeField(db_comment="最終更新日時", null=True),
                ),
            ],
            options={
                "db_table": "eng_skill",
                "db_table_comment": "スキル",
                "managed": False,
            },
        ),
        migrations.CreateModel(
            name="EngTrading",
            fields=[
                (
                    "eng_trading_id",
                    models.AutoField(
                        db_comment="取引条件ID", primary_key=True, serialize=False
                    ),
                ),
                (
                    "created",
                    utils.custom_fields.DateTimeField(db_comment="初期登録日時", null=True),
                ),
                (
                    "updated",
                    utils.custom_fields.DateTimeField(db_comment="最終更新日時", null=True),
                ),
            ],
            options={
                "db_table": "eng_trading",
                "db_table_comment": "技術者紹介取引条件",
                "managed": False,
            },
        ),
        migrations.CreateModel(
            name="LogDecline",
            fields=[
                (
                    "offer_id",
                    models.AutoField(
                        db_comment="内定受理ID", primary_key=True, serialize=False
                    ),
                ),
                ("recruit_id", models.IntegerField(db_comment="募集要項ID", null=True)),
                ("apply_id", models.IntegerField(db_comment="応募ID", null=True)),
                ("engineer_id", models.IntegerField(db_comment="技術者ID")),
                (
                    "agency_company_id",
                    models.IntegerField(db_comment="技術者紹介機関ID", null=True),
                ),
                (
                    "agency_agent_id",
                    models.IntegerField(db_comment="技術者紹介機関担当者ID", null=True),
                ),
                ("host_company_id", models.IntegerField(db_comment="受入企業ID")),
                (
                    "host_agent_id",
                    models.IntegerField(db_comment="受入企業担当者ID", null=True),
                ),
                (
                    "support_company_id",
                    models.IntegerField(db_comment="受入サポート機関ID", null=True),
                ),
                (
                    "support_agent_id",
                    models.IntegerField(db_comment="受入サポート機関担当者ID", null=True),
                ),
                (
                    "before_progress_code",
                    models.CharField(
                        db_comment="辞退発生時進捗状況コード", max_length=6, null=True
                    ),
                ),
                (
                    "decline_progress_code",
                    models.CharField(db_comment="辞退状況コード", max_length=6, null=True),
                ),
                (
                    "created",
                    utils.custom_fields.DateTimeField(db_comment="初期登録日時", null=True),
                ),
                (
                    "updated",
                    utils.custom_fields.DateTimeField(db_comment="最終更新日時", null=True),
                ),
            ],
            options={
                "db_table": "log_decline",
                "db_table_comment": "辞退",
                "managed": False,
            },
        ),
        migrations.CreateModel(
            name="LogOffer",
            fields=[
                (
                    "offer_id",
                    models.AutoField(
                        db_comment="内定受理ID", primary_key=True, serialize=False
                    ),
                ),
                ("recruit_id", models.IntegerField(db_comment="募集要項ID", null=True)),
                ("apply_id", models.IntegerField(db_comment="応募ID", null=True)),
                ("engineer_id", models.IntegerField(db_comment="技術者ID")),
                (
                    "agency_company_id",
                    models.IntegerField(db_comment="技術者紹介機関ID", null=True),
                ),
                (
                    "agency_agent_id",
                    models.IntegerField(db_comment="技術者紹介機関担当者ID", null=True),
                ),
                ("host_company_id", models.IntegerField(db_comment="受入企業ID")),
                (
                    "host_agent_id",
                    models.IntegerField(db_comment="受入企業担当者ID", null=True),
                ),
                (
                    "support_company_id",
                    models.IntegerField(db_comment="受入サポート機関ID", null=True),
                ),
                (
                    "support_agent_id",
                    models.IntegerField(db_comment="受入サポート機関担当者ID", null=True),
                ),
                ("join_date", models.DateField(db_comment="入社予定日", null=True)),
                (
                    "job_code",
                    models.CharField(db_comment="採用職種コード", max_length=3, null=True),
                ),
                (
                    "employ_code",
                    models.CharField(db_comment="募集雇用形態コード", max_length=2, null=True),
                ),
                (
                    "place_code",
                    models.CharField(db_comment="採用勤務地コード", max_length=9, null=True),
                ),
                (
                    "payroll_code",
                    models.CharField(db_comment="募集給与通貨コード", max_length=3, null=True),
                ),
                (
                    "payroll_price_from",
                    models.DecimalField(
                        db_comment="募集給与金額(より)",
                        decimal_places=3,
                        max_digits=10,
                        null=True,
                    ),
                ),
                (
                    "payroll_price_to",
                    models.DecimalField(
                        db_comment="募集給与金額(まで)",
                        decimal_places=3,
                        max_digits=10,
                        null=True,
                    ),
                ),
                (
                    "benefits",
                    models.CharField(db_comment="その他待遇", max_length=10000, null=True),
                ),
                (
                    "offer_contract_date",
                    models.DateField(db_comment="内定受理入社契約日", null=True),
                ),
                (
                    "offer_pdf_path",
                    models.CharField(
                        db_comment="内定受理入社契約書ファイル名", max_length=75, null=True
                    ),
                ),
                (
                    "create_agent_id",
                    models.IntegerField(db_comment="初期登録企業ID", null=True),
                ),
                (
                    "created",
                    utils.custom_fields.DateTimeField(db_comment="初期登録日時", null=True),
                ),
                (
                    "update_agent_id",
                    models.IntegerField(db_comment="最終更新企業ID", null=True),
                ),
                (
                    "updated",
                    utils.custom_fields.DateTimeField(db_comment="最終更新日時", null=True),
                ),
            ],
            options={
                "db_table": "log_offer",
                "db_table_comment": "内容受理",
                "managed": False,
            },
        ),
        migrations.CreateModel(
            name="MapChatGroup",
            fields=[
                (
                    "group_id",
                    models.IntegerField(
                        db_comment="グループID", primary_key=True, serialize=False
                    ),
                ),
                ("user_id", models.IntegerField(db_comment="ユーザーID")),
                ("chat_id", models.IntegerField(db_comment="既読チャットID", null=True)),
                (
                    "updated",
                    utils.custom_fields.DateTimeField(db_comment="既読日時", null=True),
                ),
                (
                    "created",
                    utils.custom_fields.DateTimeField(db_comment="初期登録日時", null=True),
                ),
            ],
            options={
                "db_table": "map_chat_group",
                "db_table_comment": "紐づけチャットグループ",
                "managed": False,
            },
        ),
        migrations.CreateModel(
            name="MapEngAgc",
            fields=[
                (
                    "engineer_id",
                    models.IntegerField(
                        db_comment="技術者ID", primary_key=True, serialize=False
                    ),
                ),
                ("agency_company_id", models.IntegerField(db_comment="技術者紹介機関ID")),
                (
                    "agency_agent_id",
                    models.IntegerField(db_comment="技術者紹介機関担当者ID", null=True),
                ),
                (
                    "eng_trading_id",
                    models.IntegerField(db_comment="技術者紹介条件ID", null=True),
                ),
                (
                    "introduction_pr",
                    models.CharField(db_comment="紹介PR", max_length=10000, null=True),
                ),
                (
                    "introduction_start_datetime",
                    utils.custom_fields.DateTimeField(db_comment="紹介開始日時", null=True),
                ),
                (
                    "create_engineer_id",
                    models.IntegerField(db_comment="初期登録技術者ID", null=True),
                ),
                (
                    "create_datetime",
                    utils.custom_fields.DateTimeField(db_comment="初期登録日時", null=True),
                ),
                (
                    "update_engineer_id",
                    models.IntegerField(db_comment="最終更新技術者ID", null=True),
                ),
                (
                    "update_datetime",
                    utils.custom_fields.DateTimeField(db_comment="最終更新日時", null=True),
                ),
            ],
            options={
                "db_table": "map_eng_agc",
                "db_table_comment": "技術者紹介機関紐づけ",
                "managed": False,
            },
        ),
        migrations.CreateModel(
            name="MapHstSup",
            fields=[
                (
                    "host_company_id",
                    models.IntegerField(
                        db_comment="受入企業ID", primary_key=True, serialize=False
                    ),
                ),
                ("support_company_id", models.IntegerField(db_comment="受入サポート機関ID")),
                (
                    "support_agent_id",
                    models.IntegerField(db_comment="受入サポート機関担当者ID", null=True),
                ),
                (
                    "com_trading_id",
                    models.IntegerField(db_comment="受入サポート機関取引条件ID", null=True),
                ),
                (
                    "introduction_pr",
                    models.CharField(db_comment="紹介PR", max_length=10000, null=True),
                ),
                (
                    "introduction_start_datetime",
                    utils.custom_fields.DateTimeField(db_comment="紹介開始日時", null=True),
                ),
                (
                    "create_agent_id",
                    models.IntegerField(db_comment="初期登録担当者ID", null=True),
                ),
                (
                    "created",
                    utils.custom_fields.DateTimeField(db_comment="初期登録日時", null=True),
                ),
                (
                    "update_agent_id",
                    models.IntegerField(db_comment="最終更新担当者ID", null=True),
                ),
                (
                    "updated",
                    utils.custom_fields.DateTimeField(db_comment="最終更新日時", null=True),
                ),
            ],
            options={
                "db_table": "map_hst_sup",
                "db_table_comment": "紐づけ受入企業サポート機関",
                "managed": False,
            },
        ),
        migrations.CreateModel(
            name="RecApply",
            fields=[
                (
                    "apply_id",
                    models.AutoField(
                        db_comment="応募ID", primary_key=True, serialize=False
                    ),
                ),
                ("recruit_id", models.IntegerField(db_comment="募集要項ID", null=True)),
                ("engineer_id", models.IntegerField(db_comment="技術者ID")),
                (
                    "agency_company_id",
                    models.IntegerField(db_comment="技術者紹介機関ID", null=True),
                ),
                (
                    "agency_agent_id",
                    models.IntegerField(db_comment="技術者紹介機関担当者ID", null=True),
                ),
                ("host_company_id", models.IntegerField(db_comment="受入企業ID")),
                (
                    "host_agent_id",
                    models.IntegerField(db_comment="受入企業担当者ID", null=True),
                ),
                (
                    "support_company_id",
                    models.IntegerField(db_comment="受入サポート機関ID", null=True),
                ),
                (
                    "support_agent_id",
                    models.IntegerField(db_comment="受入サポート機関担当者ID", null=True),
                ),
                (
                    "recruit_progress_code",
                    models.CharField(db_comment="採用進捗状況コード", max_length=6, null=True),
                ),
                (
                    "progress_update_datetime",
                    utils.custom_fields.DateTimeField(db_comment="進捗変更日時", null=True),
                ),
                (
                    "interview_datetime",
                    utils.custom_fields.DateTimeField(db_comment="面接日時", null=True),
                ),
                ("joing_date", models.DateField(db_comment="入社予定日", null=True)),
                (
                    "job_code",
                    models.CharField(db_comment="採用職種コード", max_length=9, null=True),
                ),
                (
                    "employ_code",
                    models.CharField(db_comment="採用雇用形態コード", max_length=9, null=True),
                ),
                (
                    "place_code",
                    models.CharField(db_comment="就業勤務地コード", max_length=9, null=True),
                ),
                (
                    "payroll_code",
                    models.CharField(db_comment="採用給与通貨コード", max_length=3, null=True),
                ),
                (
                    "payroll_price",
                    models.DecimalField(
                        db_comment="採用給与金額", decimal_places=3, max_digits=10, null=True
                    ),
                ),
                (
                    "benefits",
                    models.CharField(db_comment="その他待遇", max_length=10000, null=True),
                ),
                (
                    "created",
                    utils.custom_fields.DateTimeField(db_comment="初期登録日時", null=True),
                ),
                (
                    "updated",
                    utils.custom_fields.DateTimeField(db_comment="最終更新日時", null=True),
                ),
            ],
            options={
                "db_table": "rec_apply",
                "db_table_comment": "応募状況",
                "managed": False,
            },
        ),
        migrations.CreateModel(
            name="RecApplyRead",
            fields=[
                (
                    "apply_id",
                    models.AutoField(
                        db_comment="応募ID", primary_key=True, serialize=False
                    ),
                ),
                ("user_id", models.IntegerField(db_comment="技術者/担当者ID")),
                (
                    "created",
                    utils.custom_fields.DateTimeField(db_comment="初期登録日時", null=True),
                ),
            ],
            options={
                "db_table": "rec_apply_read",
                "db_table_comment": "応募未読",
                "managed": False,
            },
        ),
        migrations.CreateModel(
            name="RecChat",
            fields=[
                (
                    "chat_id",
                    models.IntegerField(
                        db_comment="チャットID", primary_key=True, serialize=False
                    ),
                ),
                ("group_id", models.IntegerField(db_comment="グループID", null=True)),
                ("user_id", models.IntegerField(db_comment="送信ユーザーID", null=True)),
                (
                    "text",
                    models.CharField(db_comment="メッセージ内容", max_length=1000, null=True),
                ),
                (
                    "send",
                    utils.custom_fields.DateTimeField(
                        db_comment="メッセージ投稿日時", null=True
                    ),
                ),
                (
                    "created",
                    utils.custom_fields.DateTimeField(db_comment="初期登録日時", null=True),
                ),
            ],
            options={
                "db_table": "rec_chat",
                "db_table_comment": "チャット",
                "managed": False,
            },
        ),
        migrations.CreateModel(
            name="RecChatGroup",
            fields=[
                (
                    "group_id",
                    models.IntegerField(
                        db_comment="グループID", primary_key=True, serialize=False
                    ),
                ),
                (
                    "name",
                    models.CharField(db_comment="グループ名", max_length=100, null=True),
                ),
                (
                    "created",
                    utils.custom_fields.DateTimeField(db_comment="初期登録日時", null=True),
                ),
            ],
            options={
                "db_table": "rec_chat_group",
                "db_table_comment": "チャットグループ",
                "managed": False,
            },
        ),
        migrations.CreateModel(
            name="RecRecruit",
            fields=[
                (
                    "recruit_id",
                    models.AutoField(
                        db_comment="募集要項ID", primary_key=True, serialize=False
                    ),
                ),
                ("host_company_id", models.IntegerField(db_comment="受入企業ID")),
                ("host_agent_id", models.IntegerField(db_comment="受入企業担当者ID")),
                (
                    "support_company_id",
                    models.IntegerField(db_comment="受入サポート機関ID", null=True),
                ),
                (
                    "support_agent_id",
                    models.IntegerField(db_comment="受入サポート機関担当者ID", null=True),
                ),
                (
                    "save_type",
                    utils.custom_fields.Bit3Field(db_comment="保存種別", null=True),
                ),
                (
                    "title",
                    utils.custom_fields.TinyTextField(db_comment="タイトル", null=True),
                ),
                ("start_date", models.DateField(db_comment="募集開始日", null=True)),
                ("end_date", models.DateField(db_comment="募集終了日", null=True)),
                (
                    "display_flag",
                    utils.custom_fields.Bit3Field(db_comment="表示フラグ", null=True),
                ),
                (
                    "employ_code",
                    models.CharField(db_comment="募集雇用形態コード", max_length=3, null=True),
                ),
                (
                    "content",
                    models.CharField(db_comment="募集要項", max_length=10000, null=True),
                ),
                (
                    "place_code1",
                    models.CharField(db_comment="就業勤務地コード1", max_length=9, null=True),
                ),
                (
                    "place_code2",
                    models.CharField(db_comment="就業勤務地コード2", max_length=9, null=True),
                ),
                (
                    "place_code3",
                    models.CharField(db_comment="就業勤務地コード3", max_length=9, null=True),
                ),
                (
                    "payroll_code",
                    models.CharField(db_comment="募集給与通貨コード", max_length=3, null=True),
                ),
                (
                    "payroll_price_from",
                    models.DecimalField(
                        db_comment="募集給与金額(より)",
                        decimal_places=3,
                        max_digits=10,
                        null=True,
                    ),
                ),
                (
                    "payroll_price_to",
                    models.DecimalField(
                        db_comment="募集給与金額(まで)",
                        decimal_places=3,
                        max_digits=10,
                        null=True,
                    ),
                ),
                (
                    "country_code",
                    models.CharField(db_comment="募集国籍コード", max_length=3, null=True),
                ),
                (
                    "age_from",
                    utils.custom_fields.Bit7Field(db_comment="募集年齢(より)", null=True),
                ),
                (
                    "age_to",
                    utils.custom_fields.Bit7Field(db_comment="募集年齢(まで）", null=True),
                ),
                (
                    "sex_type",
                    utils.custom_fields.Bit4Field(db_comment="募集性別", null=True),
                ),
                (
                    "pref_code1",
                    models.CharField(db_comment="募集国別県コード1", max_length=6, null=True),
                ),
                (
                    "pref_code2",
                    models.CharField(db_comment="募集国別県コード2", max_length=6, null=True),
                ),
                (
                    "pref_code3",
                    models.CharField(db_comment="募集国別県コード3", max_length=6, null=True),
                ),
                (
                    "last_academic_code",
                    models.CharField(db_comment="最終学歴コード(以上)", max_length=3, null=True),
                ),
                (
                    "language_code1",
                    models.CharField(db_comment="言語コード1", max_length=2, null=True),
                ),
                (
                    "language_level_type1",
                    utils.custom_fields.Bit4Field(db_comment="言語スキル種別1", null=True),
                ),
                (
                    "language_code2",
                    models.CharField(db_comment="言語コード2", max_length=2, null=True),
                ),
                (
                    "language_level_type2",
                    utils.custom_fields.Bit4Field(db_comment="言語スキル種別2", null=True),
                ),
                (
                    "skill_job_code1",
                    models.CharField(db_comment="職種コード1", max_length=3, null=True),
                ),
                (
                    "skill_code1",
                    models.CharField(db_comment="スキルコード1", max_length=3, null=True),
                ),
                (
                    "skill_level_type1",
                    utils.custom_fields.Bit4Field(db_comment="スキルレベル種別1", null=True),
                ),
                (
                    "skill_job_code2",
                    models.CharField(db_comment="職種コード2", max_length=2, null=True),
                ),
                (
                    "skill_code2",
                    models.CharField(db_comment="スキルコード2", max_length=3, null=True),
                ),
                (
                    "skill_level_type2",
                    utils.custom_fields.Bit4Field(db_comment="スキルレベル種別2", null=True),
                ),
                (
                    "skill_job_code3",
                    models.CharField(db_comment="職種コード3", max_length=2, null=True),
                ),
                (
                    "skill_code3",
                    models.CharField(db_comment="スキルコード3", max_length=3, null=True),
                ),
                (
                    "skill_level_type3",
                    utils.custom_fields.Bit4Field(db_comment="スキルレベル種別3", null=True),
                ),
                (
                    "create_agent_id",
                    models.IntegerField(db_comment="初期登録企業ID", null=True),
                ),
                (
                    "created",
                    utils.custom_fields.DateTimeField(db_comment="初期登録日時", null=True),
                ),
                (
                    "update_agent_id",
                    models.IntegerField(db_comment="最終更新企業ID", null=True),
                ),
                (
                    "updated",
                    utils.custom_fields.DateTimeField(db_comment="最終更新日時", null=True),
                ),
            ],
            options={
                "db_table": "rec_recruit",
                "db_table_comment": "募集要項",
                "managed": False,
            },
        ),
        migrations.CreateModel(
            name="User",
            fields=[
                (
                    "last_login",
                    models.DateTimeField(
                        blank=True, null=True, verbose_name="last login"
                    ),
                ),
                (
                    "is_superuser",
                    models.BooleanField(
                        default=False,
                        help_text="Designates that this user has all permissions without explicitly assigning them.",
                        verbose_name="superuser status",
                    ),
                ),
                (
                    "username",
                    models.CharField(
                        error_messages={
                            "unique": "A user with that username already exists."
                        },
                        help_text="Required. 150 characters or fewer. Letters, digits and @/./+/-/_ only.",
                        max_length=150,
                        unique=True,
                        validators=[
                            django.contrib.auth.validators.UnicodeUsernameValidator()
                        ],
                        verbose_name="username",
                    ),
                ),
                (
                    "is_staff",
                    models.BooleanField(
                        default=False,
                        help_text="Designates whether the user can log into this admin site.",
                        verbose_name="staff status",
                    ),
                ),
                (
                    "is_active",
                    models.BooleanField(
                        default=True,
                        help_text="Designates whether this user should be treated as active. Unselect this instead of deleting accounts.",
                        verbose_name="active",
                    ),
                ),
                (
                    "date_joined",
                    models.DateTimeField(
                        default=django.utils.timezone.now, verbose_name="date joined"
                    ),
                ),
                (
                    "user_id",
                    models.AutoField(
                        db_comment="ユーザーID", primary_key=True, serialize=False
                    ),
                ),
                ("user_type", utils.custom_fields.Bit3Field(db_comment="ユーザーID種別")),
                (
                    "company_id",
                    models.IntegerField(db_comment="企業ID", default=None, null=True),
                ),
                (
                    "email",
                    models.EmailField(
                        db_comment="メールアドレス", max_length=100, unique=True
                    ),
                ),
                ("password", models.CharField(db_comment="パスワード", max_length=64)),
                ("auth_type", utils.custom_fields.Bit3Field(db_comment="メール認証種別")),
                (
                    "mailauth_code",
                    models.CharField(
                        db_comment="メール認証コード", default=None, max_length=6, null=True
                    ),
                ),
                (
                    "mailauth_datetime",
                    utils.custom_fields.DateTimeField(
                        db_comment="メール認証有効期限", default=None, null=True
                    ),
                ),
                (
                    "last_name",
                    models.CharField(
                        db_comment="姓", default=None, max_length=30, null=True
                    ),
                ),
                (
                    "first_name",
                    models.CharField(
                        db_comment="名", default=None, max_length=30, null=True
                    ),
                ),
                (
                    "display_code",
                    models.CharField(
                        db_comment="表示言語コード", default=None, max_length=2, null=True
                    ),
                ),
                (
                    "country_code",
                    models.CharField(
                        db_comment="国籍コード", default=None, max_length=3, null=True
                    ),
                ),
                (
                    "birth_date",
                    models.DateField(db_comment="生年月日", default=None, null=True),
                ),
                (
                    "sex_type",
                    utils.custom_fields.Bit4Field(
                        db_comment="性別種別", default=None, null=True
                    ),
                ),
                (
                    "address_code",
                    models.CharField(
                        db_comment="住所国別県市コード", default=None, max_length=9, null=True
                    ),
                ),
                (
                    "address",
                    models.CharField(
                        db_comment="住所", default=None, max_length=200, null=True
                    ),
                ),
                (
                    "tel",
                    models.CharField(
                        db_comment="電話番号", default=None, max_length=20, null=True
                    ),
                ),
                (
                    "passport_number",
                    models.CharField(
                        db_comment="パスポート（ID）番号", default=None, max_length=9, null=True
                    ),
                ),
                (
                    "passport_image_path",
                    models.CharField(
                        db_comment="パスポート（ID）写真ファイル名",
                        default=None,
                        max_length=75,
                        null=True,
                    ),
                ),
                (
                    "profile_image_path",
                    models.CharField(
                        db_comment="プロフィール写真ファイル名",
                        default=None,
                        max_length=75,
                        null=True,
                    ),
                ),
                (
                    "last_academic_code",
                    models.CharField(
                        db_comment="最終学位コード", default=None, max_length=3, null=True
                    ),
                ),
                (
                    "pr",
                    models.CharField(
                        db_comment="PR", default=None, max_length=10000, null=True
                    ),
                ),
                (
                    "self_introduction_url",
                    utils.custom_fields.TinyTextField(
                        db_comment="自己紹介URL", default=None, null=True
                    ),
                ),
                (
                    "facebook_url",
                    utils.custom_fields.TinyTextField(
                        db_comment="FacebookURL", default=None, null=True
                    ),
                ),
                (
                    "linkedin_url",
                    utils.custom_fields.TinyTextField(
                        db_comment="LinkedInURL", default=None, null=True
                    ),
                ),
                ("job_status", utils.custom_fields.Bit4Field(db_comment="JOBステータス")),
                (
                    "job_status_update",
                    utils.custom_fields.DateTimeField(
                        db_comment="JOBステータス更新日", default=None, null=True
                    ),
                ),
                ("deleted", utils.custom_fields.Bit2Field(db_comment="退会フラグ")),
                (
                    "created",
                    utils.custom_fields.DateTimeField(
                        db_comment="初期登録日時", default=None
                    ),
                ),
                (
                    "updated",
                    utils.custom_fields.DateTimeField(db_comment="最終更新日時", null=True),
                ),
                (
                    "groups",
                    models.ManyToManyField(
                        blank=True,
                        help_text="The groups this user belongs to. A user will get all permissions granted to each of their groups.",
                        related_name="user_set",
                        related_query_name="user",
                        to="auth.group",
                        verbose_name="groups",
                    ),
                ),
                (
                    "user_permissions",
                    models.ManyToManyField(
                        blank=True,
                        help_text="Specific permissions for this user.",
                        related_name="user_set",
                        related_query_name="user",
                        to="auth.permission",
                        verbose_name="user permissions",
                    ),
                ),
            ],
            options={
                "db_table": "user",
                "db_table_comment": "ユーザー",
            },
        ),
    ]
