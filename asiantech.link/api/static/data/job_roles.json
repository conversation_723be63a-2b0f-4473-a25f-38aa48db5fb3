{"roles_list": [{"id": "1", "name_jp": "オープン系・業務系エンジニア", "name_en": "Open System/Business System Engineer", "name_vn": "Open System/Business System Engineer", "job_code": "100"}, {"id": "2", "name_jp": "汎用系エンジニア", "name_en": "General Purpose System Engineer", "name_vn": "General Purpose System Engineer", "job_code": "100"}, {"id": "3", "name_jp": "Web系エンジニア", "name_en": "Web Engineer", "name_vn": "Web Engineer", "job_code": "100"}, {"id": "4", "name_jp": "スマホアプリエンジニア", "name_en": "Mobile App Engineer", "name_vn": "Mobile App Engineer", "job_code": "100"}, {"id": "5", "name_jp": "フロントエンドエンジニア", "name_en": "Frontend Engineer", "name_vn": "Frontend Engineer", "job_code": "100"}, {"id": "6", "name_jp": "組込・制御系エンジニア", "name_en": "Embedded/Control System Engineer", "name_vn": "Embedded/Control System Engineer", "job_code": "100"}, {"id": "7", "name_jp": "サーバーエンジニア", "name_en": "Server Engineer", "name_vn": "Server Engineer", "job_code": "100"}, {"id": "8", "name_jp": "ネットワークエンジニア", "name_en": "Network Engineer", "name_vn": "Network Engineer", "job_code": "100"}, {"id": "9", "name_jp": "データベースエンジニア", "name_en": "Database Engineer", "name_vn": "Database Engineer", "job_code": "100"}, {"id": "10", "name_jp": "セキュリティエンジニア", "name_en": "Security Engineer", "name_vn": "Security Engineer", "job_code": "100"}, {"id": "11", "name_jp": "フルスタックエンジニア", "name_en": "Full Stack Engineer", "name_vn": "Full Stack Engineer", "job_code": "100"}, {"id": "12", "name_jp": "AIエンジニア", "name_en": "AI Machine Learning Engineer", "name_vn": "AI Machine Learning Engineer", "job_code": "100"}, {"id": "13", "name_jp": "データアナリスト", "name_en": "Data Analyst", "name_vn": "Data Analyst", "job_code": "100"}, {"id": "14", "name_jp": "データエンジニア", "name_en": "Data Engineer", "name_vn": "Data Engineer", "job_code": "100"}, {"id": "15", "name_jp": "プロジェクトマネージャー", "name_en": "Project Manager", "name_vn": "Project Manager", "job_code": "100"}, {"id": "16", "name_jp": "プロダクトオーナー", "name_en": "Product Owner", "name_vn": "Product Owner", "job_code": "100"}, {"id": "17", "name_jp": "システムコンサルタント", "name_en": "System Consultant", "name_vn": "System Consultant", "job_code": "100"}, {"id": "18", "name_jp": "テストエンジニア", "name_en": "Test Engineer", "name_vn": "Test Engineer", "job_code": "100"}, {"id": "19", "name_jp": "セールスエンジニア", "name_en": "Sales Engineer", "name_vn": "Sales Engineer", "job_code": "100"}, {"id": "20", "name_jp": "SRE（Site Reliability Engineer）", "name_en": "Site Reliability Engineer (SRE)", "name_vn": "Site Reliability Engineer (SRE)", "job_code": "100"}, {"id": "21", "name_jp": "CRE（Customer Reliability Engineer）", "name_en": "Customer Reliability Engineer (CRE)", "name_vn": "Customer Reliability Engineer (CRE)", "job_code": "100"}, {"id": "22", "name_jp": "クラウドエンジニア", "name_en": "Cloud Engineer", "name_vn": "Cloud Engineer", "job_code": "100"}, {"id": "23", "name_jp": "スクラムマスター", "name_en": "Scrum Master", "name_vn": "Scrum Master", "job_code": "100"}, {"id": "24", "name_jp": "アジャイルコーチ", "name_en": "Agile Coach", "name_vn": "Agile Coach", "job_code": "100"}, {"id": "25", "name_jp": "テクニカルプロジェクトマネージャー", "name_en": "Technical Project Manager", "name_vn": "Technical Project Manager", "job_code": "100"}, {"id": "26", "name_jp": "ITプログラムマネージャー", "name_en": "IT Program Manager", "name_vn": "IT Program Manager", "job_code": "100"}, {"id": "27", "name_jp": "プロダクトマネージャー", "name_en": "Product Manager", "name_vn": "Product Manager", "job_code": "100"}, {"id": "28", "name_jp": "DevOpsエンジニア", "name_en": "DevOps Engineer", "name_vn": "DevOps Engineer", "job_code": "100"}, {"id": "29", "name_jp": "インフラエンジニア", "name_en": "Infrastructure Engineer", "name_vn": "Infrastructure Engineer", "job_code": "100"}, {"id": "30", "name_jp": "プラットフォームエンジニア", "name_en": "Platform Engineer", "name_vn": "Platform Engineer", "job_code": "100"}, {"id": "31", "name_jp": "QAエンジニア", "name_en": "QA Engineer", "name_vn": "QA Engineer", "job_code": "100"}, {"id": "32", "name_jp": "自動化テストエンジニア", "name_en": "Automation Test Engineer", "name_vn": "Automation Test Engineer", "job_code": "100"}, {"id": "33", "name_jp": "SDET（テスト担当ソフトウェア開発エンジニア）", "name_en": "Software Development Engineer in Test (SDET)", "name_vn": "Software Development Engineer in Test (SDET)", "job_code": "100"}, {"id": "34", "name_jp": "ファームウェアエンジニア", "name_en": "Firmware Engineer", "name_vn": "Firmware Engineer", "job_code": "100"}, {"id": "35", "name_jp": "IoT開発者", "name_en": "IoT Developer", "name_vn": "IoT Developer", "job_code": "100"}, {"id": "36", "name_jp": "組込みシステムアーキテクト", "name_en": "Embedded Systems Architect", "name_vn": "Embedded Systems Architect", "job_code": "100"}, {"id": "37", "name_jp": "組込みソフトウェア開発者", "name_en": "Embedded Software Developer", "name_vn": "Embedded Software Developer", "job_code": "100"}, {"id": "38", "name_jp": "ロボティクスエンジニア", "name_en": "Robotics Engineer", "name_vn": "Robotics Engineer", "job_code": "100"}, {"id": "39", "name_jp": "機械学習エンジニア", "name_en": "Machine Learning Engineer", "name_vn": "Machine Learning Engineer", "job_code": "100"}, {"id": "40", "name_jp": "ディープラーニングスペシャリスト", "name_en": "Deep Learning Specialist", "name_vn": "Deep Learning Specialist", "job_code": "100"}, {"id": "41", "name_jp": "自然言語処理エンジニア", "name_en": "NLP Engineer", "name_vn": "NLP Engineer", "job_code": "100"}, {"id": "42", "name_jp": "コンピュータビジョンエンジニア", "name_en": "Computer Vision Engineer", "name_vn": "Computer Vision Engineer", "job_code": "100"}, {"id": "43", "name_jp": "MLOpsエンジニア", "name_en": "<PERSON><PERSON><PERSON><PERSON> Engineer", "name_vn": "<PERSON><PERSON><PERSON><PERSON> Engineer", "job_code": "100"}, {"id": "44", "name_jp": "ソリューションアーキテクト", "name_en": "Solutions Architect", "name_vn": "Solutions Architect", "job_code": "100"}, {"id": "45", "name_jp": "エンタープライズアーキテクト", "name_en": "Enterprise Architect", "name_vn": "Enterprise Architect", "job_code": "100"}, {"id": "46", "name_jp": "テクニカルリード", "name_en": "Technical Lead", "name_vn": "Technical Lead", "job_code": "100"}, {"id": "47", "name_jp": "ソフトウェアアーキテクト", "name_en": "Software Architect", "name_vn": "Software Architect", "job_code": "100"}, {"id": "48", "name_jp": "最高技術責任者", "name_en": "Chief Technology Officer (CTO)", "name_vn": "Chief Technology Officer (CTO)", "job_code": "100"}, {"id": "49", "name_jp": "研究開発エンジニア", "name_en": "Research & Development Engineer", "name_vn": "Research & Development Engineer", "job_code": "100"}, {"id": "50", "name_jp": "テクノロジーエバンジェリスト", "name_en": "Technology Evangelist", "name_vn": "Technology Evangelist", "job_code": "100"}, {"id": "51", "name_jp": "データサイエンティスト", "name_en": "Data Scientist", "name_vn": "Data Scientist", "job_code": "100"}, {"id": "52", "name_jp": "データベースアドミニストレーター", "name_en": "Database Administrator (DBA)", "name_vn": "Database Administrator (DBA)", "job_code": "100"}, {"id": "53", "name_jp": "データベースアーキテクト", "name_en": "Database Architect", "name_vn": "Database Architect", "job_code": "100"}, {"id": "54", "name_jp": "データベースエンジニア", "name_en": "Database Engineer", "name_vn": "Database Engineer", "job_code": "100"}, {"id": "55", "name_jp": "データベース開発者", "name_en": "Database Developer", "name_vn": "Database Developer", "job_code": "100"}, {"id": "56", "name_jp": "データベースコンサルタント", "name_en": "Database Consultant", "name_vn": "Database Consultant", "job_code": "100"}, {"id": "57", "name_jp": "データベースアナリスト", "name_en": "Database Analyst", "name_vn": "Database Analyst", "job_code": "100"}, {"id": "58", "name_jp": "データベーステスター", "name_en": "Database Tester", "name_vn": "Database Tester", "job_code": "100"}, {"id": "59", "name_jp": "データベースセキュリティスペシャリスト", "name_en": "Database Security Specialist", "name_vn": "Database Security Specialist", "job_code": "100"}, {"id": "60", "name_jp": "データベースパフォーマンスエンジニア", "name_en": "Database Performance Engineer", "name_vn": "Database Performance Engineer", "job_code": "100"}, {"id": "61", "name_jp": "データベース運用エンジニア", "name_en": "Database Operations Engineer", "name_vn": "Database Operations Engineer", "job_code": "100"}, {"id": "62", "name_jp": "データベース運用管理者", "name_en": "Database Operations Administrator", "name_vn": "Database Operations Administrator", "job_code": "100"}, {"id": "63", "name_jp": "データベース運用監視者", "name_en": "Database Operations Monitor", "name_vn": "Database Operations Monitor", "job_code": "100"}, {"id": "64", "name_jp": "データベース運用保守者", "name_en": "Database Operations Maintainer", "name_vn": "Database Operations Maintainer", "job_code": "100"}, {"id": "65", "name_jp": "データベース運用管理者", "name_en": "Database Operations Administrator", "name_vn": "Database Operations Administrator", "job_code": "100"}, {"id": "66", "name_jp": "AIリサーチサイエンティスト", "name_en": "AI Research Scientist", "name_vn": "AI Research Scientist", "job_code": "100"}, {"id": "67", "name_jp": "AI倫理スペシャリスト", "name_en": "AI Ethics Specialist", "name_vn": "AI Ethics Specialist", "job_code": "100"}, {"id": "68", "name_jp": "会話型AI開発者", "name_en": "Conversational AI Developer", "name_vn": "Conversational AI Developer", "job_code": "100"}, {"id": "69", "name_jp": "強化学習エンジニア", "name_en": "Reinforcement Learning Engineer", "name_vn": "Reinforcement Learning Engineer", "job_code": "100"}, {"id": "70", "name_jp": "デリバリーマネージャー", "name_en": "Delivery Manager", "name_vn": "Delivery Manager", "job_code": "100"}, {"id": "71", "name_jp": "リリースマネージャー", "name_en": "Release Manager", "name_vn": "Release Manager", "job_code": "100"}, {"id": "72", "name_jp": "リリースエンジニア", "name_en": "Release Engineer", "name_vn": "Release Engineer", "job_code": "100"}, {"id": "73", "name_jp": "構成管理者", "name_en": "Configuration Manager", "name_vn": "Configuration Manager", "job_code": "100"}, {"id": "74", "name_jp": "自動化エンジニア", "name_en": "Automation Engineer", "name_vn": "Automation Engineer", "job_code": "100"}, {"id": "75", "name_jp": "CI/CDスペシャリスト", "name_en": "CI/CD Specialist", "name_vn": "CI/CD Specialist", "job_code": "100"}, {"id": "76", "name_jp": "DevSecOpsエンジニア", "name_en": "DevSecOps Engineer", "name_vn": "DevSecOps Engineer", "job_code": "100"}, {"id": "77", "name_jp": "パフォーマンステストエンジニア", "name_en": "Performance Test Engineer", "name_vn": "Performance Test Engineer", "job_code": "100"}, {"id": "78", "name_jp": "セキュリティテストエンジニア", "name_en": "Security Test Engineer", "name_vn": "Security Test Engineer", "job_code": "100"}, {"id": "79", "name_jp": "テストアーキテクト", "name_en": "Test Architect", "name_vn": "Test Architect", "job_code": "100"}, {"id": "80", "name_jp": "QAアナリスト", "name_en": "QA Analyst", "name_vn": "QA Analyst", "job_code": "100"}, {"id": "81", "name_jp": "テストマネージャー", "name_en": "Test Manager", "name_vn": "Test Manager", "job_code": "100"}, {"id": "82", "name_jp": "RTOSデベロッパー", "name_en": "RTOS Developer", "name_vn": "RTOS Developer", "job_code": "100"}, {"id": "83", "name_jp": "ハードウェアインターフェースエンジニア", "name_en": "Hardware Interface Engineer", "name_vn": "Hardware Interface Engineer", "job_code": "100"}, {"id": "84", "name_jp": "組込みQAエンジニア", "name_en": "Embedded QA Engineer", "name_vn": "Embedded QA Engineer", "job_code": "100"}, {"id": "85", "name_jp": "エンジニアリング担当副社長", "name_en": "VP of Engineering", "name_vn": "VP of Engineering", "job_code": "100"}, {"id": "86", "name_jp": "最高情報責任者", "name_en": "Chief Information Officer (CIO)", "name_vn": "Chief Information Officer (CIO)", "job_code": "100"}, {"id": "87", "name_jp": "テクニカルディレクター", "name_en": "Technical Director", "name_vn": "Technical Director", "job_code": "100"}, {"id": "88", "name_jp": "ビッグデータエンジニア", "name_en": "Big Data Engineer", "name_vn": "Big Data Engineer", "job_code": "100"}, {"id": "89", "name_jp": "ブロックチェーンデベロッパー", "name_en": "Blockchain Developer", "name_vn": "Blockchain Developer", "job_code": "100"}, {"id": "90", "name_jp": "クラウドアーキテクト", "name_en": "Cloud Architect", "name_vn": "Cloud Architect", "job_code": "100"}, {"id": "91", "name_jp": "ユーザーインターフェースデザイナー", "name_en": "UI Designer", "name_vn": "UI Designer", "job_code": "100"}, {"id": "92", "name_jp": "ユーザーエクスペリエンスデザイナー", "name_en": "UX Designer", "name_vn": "UX Designer", "job_code": "100"}, {"id": "93", "name_jp": "ゲーム開発者", "name_en": "Game Developer", "name_vn": "Game Developer", "job_code": "100"}, {"id": "94", "name_jp": "AR/VR開発者", "name_en": "AR/VR Developer", "name_vn": "AR/VR Developer", "job_code": "100"}, {"id": "95", "name_jp": "量子コンピューティングエンジニア", "name_en": "Quantum Computing Engineer", "name_vn": "Quantum Computing Engineer", "job_code": "100"}, {"id": "96", "name_jp": "サイバーセキュリティアナリスト", "name_en": "Cybersecurity Analyst", "name_vn": "Cybersecurity Analyst", "job_code": "100"}, {"id": "97", "name_jp": "ペネトレーションテスター", "name_en": "Penetration Tester", "name_vn": "Penetration Tester", "job_code": "100"}, {"id": "98", "name_jp": "フォレンジックアナリスト", "name_en": "Digital Forensics Analyst", "name_vn": "Digital Forensics Analyst", "job_code": "100"}, {"id": "99", "name_jp": "クラウドセキュリティエンジニア", "name_en": "Cloud Security Engineer", "name_vn": "Cloud Security Engineer", "job_code": "100"}, {"id": "100", "name_jp": "APIエンジニア", "name_en": "API Engineer", "name_vn": "API Engineer", "job_code": "100"}, {"id": "101", "name_jp": "マイクロサービスアーキテクト", "name_en": "Microservices Architect", "name_vn": "Microservices Architect", "job_code": "100"}, {"id": "102", "name_jp": "サーバーレスアプリケーション開発者", "name_en": "Serverless Application Developer", "name_vn": "Serverless Application Developer", "job_code": "100"}, {"id": "103", "name_jp": "エッジコンピューティングエンジニア", "name_en": "Edge Computing Engineer", "name_vn": "Edge Computing Engineer", "job_code": "100"}, {"id": "104", "name_jp": "ローコード開発者", "name_en": "Low-Code Developer", "name_vn": "Low-Code Developer", "job_code": "100"}, {"id": "105", "name_jp": "ノーコード開発者", "name_en": "No-Code Developer", "name_vn": "No-Code Developer", "job_code": "100"}, {"id": "106", "name_jp": "テクニカルライター", "name_en": "Technical Writer", "name_vn": "Technical Writer", "job_code": "100"}, {"id": "107", "name_jp": "テクニカルドキュメンテーションスペシャリスト", "name_en": "Technical Documentation Specialist", "name_vn": "Technical Documentation Specialist", "job_code": "100"}, {"id": "108", "name_jp": "デジタルトランスフォーメーションコンサルタント", "name_en": "Digital Transformation Consultant", "name_vn": "Digital Transformation Consultant", "job_code": "100"}, {"id": "109", "name_jp": "ITストラテジスト", "name_en": "IT Strategist", "name_vn": "IT Strategist", "job_code": "100"}, {"id": "110", "name_jp": "アクセシビリティスペシャリスト", "name_en": "Accessibility Specialist", "name_vn": "Accessibility Specialist", "job_code": "100"}, {"id": "111", "name_jp": "デジタルプロダクトデザイナー", "name_en": "Digital Product Designer", "name_vn": "Digital Product Designer", "job_code": "100"}, {"id": "112", "name_jp": "ソフトウェア品質保証マネージャー", "name_en": "Software Quality Assurance Manager", "name_vn": "Software Quality Assurance Manager", "job_code": "100"}, {"id": "113", "name_jp": "ITコンプライアンスオフィサー", "name_en": "IT Compliance Officer", "name_vn": "IT Compliance Officer", "job_code": "100"}, {"id": "114", "name_jp": "データプライバシーオフィサー", "name_en": "Data Privacy Officer", "name_vn": "Data Privacy Officer", "job_code": "100"}, {"id": "115", "name_jp": "ITオーディター", "name_en": "IT Auditor", "name_vn": "IT Auditor", "job_code": "100"}, {"id": "116", "name_jp": "バイオインフォマティクスエンジニア", "name_en": "Bioinformatics Engineer", "name_vn": "Bioinformatics Engineer", "job_code": "100"}, {"id": "117", "name_jp": "ヘルスケアITスペシャリスト", "name_en": "Healthcare IT Specialist", "name_vn": "Healthcare IT Specialist", "job_code": "100"}, {"id": "118", "name_jp": "フィンテックエンジニア", "name_en": "Fintech Engineer", "name_vn": "Fintech Engineer", "job_code": "100"}, {"id": "119", "name_jp": "スマートコントラクト開発者", "name_en": "Smart Contract Developer", "name_vn": "Smart Contract Developer", "job_code": "100"}, {"id": "120", "name_jp": "クリプト開発者", "name_en": "Crypto Developer", "name_vn": "Crypto Developer", "job_code": "100"}, {"id": "121", "name_jp": "3Dモデリングエンジニア", "name_en": "3D Modeling Engineer", "name_vn": "3D Modeling Engineer", "job_code": "100"}, {"id": "122", "name_jp": "ゲームエンジンプログラマー", "name_en": "Game Engine Programmer", "name_vn": "Game Engine Programmer", "job_code": "100"}, {"id": "123", "name_jp": "モバイルゲーム開発者", "name_en": "Mobile Game Developer", "name_vn": "Mobile Game Developer", "job_code": "100"}, {"id": "124", "name_jp": "eスポーツプラットフォーム開発者", "name_en": "eSports Platform Developer", "name_vn": "eSports Platform Developer", "job_code": "100"}, {"id": "125", "name_jp": "音声ユーザーインターフェース開発者", "name_en": "Voice User Interface Developer", "name_vn": "Voice User Interface Developer", "job_code": "100"}, {"id": "126", "name_jp": "ジェスチャーインターフェース開発者", "name_en": "Gesture Interface Developer", "name_vn": "Gesture Interface Developer", "job_code": "100"}, {"id": "127", "name_jp": "ウェアラブルデバイス開発者", "name_en": "Wearable <PERSON><PERSON>", "name_vn": "Wearable <PERSON><PERSON>", "job_code": "100"}, {"id": "128", "name_jp": "スマートホーム開発者", "name_en": "Smart Home Developer", "name_vn": "Smart Home Developer", "job_code": "100"}, {"id": "129", "name_jp": "自動運転ソフトウェアエンジニア", "name_en": "Autonomous Vehicle Software Engineer", "name_vn": "Autonomous Vehicle Software Engineer", "job_code": "100"}, {"id": "130", "name_jp": "ドローンソフトウェア開発者", "name_en": "Drone Software Developer", "name_vn": "Drone Software Developer", "job_code": "100"}, {"id": "131", "name_jp": "量子アルゴリズム研究者", "name_en": "Quantum Algorithm Researcher", "name_vn": "Quantum Algorithm Researcher", "job_code": "100"}, {"id": "132", "name_jp": "量子ソフトウェア開発者", "name_en": "Quantum Software Developer", "name_vn": "Quantum Software Developer", "job_code": "100"}, {"id": "133", "name_jp": "エッジAIエンジニア", "name_en": "Edge AI Engineer", "name_vn": "Edge AI Engineer", "job_code": "100"}, {"id": "134", "name_jp": "AIハードウェア最適化エンジニア", "name_en": "AI Hardware Optimization Engineer", "name_vn": "AI Hardware Optimization Engineer", "job_code": "100"}, {"id": "135", "name_jp": "説明可能AIスペシャリスト", "name_en": "Explainable AI Specialist", "name_vn": "Explainable AI Specialist", "job_code": "100"}, {"id": "136", "name_jp": "AIモデル監査人", "name_en": "AI Model Auditor", "name_vn": "AI Model Auditor", "job_code": "100"}, {"id": "137", "name_jp": "拡張インテリジェンスエンジニア", "name_en": "Augmented Intelligence Engineer", "name_vn": "Augmented Intelligence Engineer", "job_code": "100"}, {"id": "138", "name_jp": "クラウドネイティブアーキテクト", "name_en": "Cloud Native Architect", "name_vn": "Cloud Native Architect", "job_code": "100"}, {"id": "139", "name_jp": "マルチクラウド統合スペシャリスト", "name_en": "Multi-Cloud Integration Specialist", "name_vn": "Multi-Cloud Integration Specialist", "job_code": "100"}, {"id": "140", "name_jp": "ハイブリッドクラウドエンジニア", "name_en": "Hybrid Cloud Engineer", "name_vn": "Hybrid Cloud Engineer", "job_code": "100"}, {"id": "141", "name_jp": "インフラストラクチャ自動化エンジニア", "name_en": "Infrastructure Automation Engineer", "name_vn": "Infrastructure Automation Engineer", "job_code": "100"}, {"id": "142", "name_jp": "GitOpsエンジニア", "name_en": "GitOps Engineer", "name_vn": "GitOps Engineer", "job_code": "100"}, {"id": "143", "name_jp": "カオスエンジニア", "name_en": "Chaos Engineer", "name_vn": "Chaos Engineer", "job_code": "100"}, {"id": "144", "name_jp": "サービスメッシュスペシャリスト", "name_en": "Service Mesh Specialist", "name_vn": "Service Mesh Specialist", "job_code": "100"}, {"id": "145", "name_jp": "コンテナオーケストレーションエンジニア", "name_en": "Container Orchestration Engineer", "name_vn": "Container Orchestration Engineer", "job_code": "100"}, {"id": "146", "name_jp": "プラットフォームリライアビリティエンジニア", "name_en": "Platform Reliability Engineer", "name_vn": "Platform Reliability Engineer", "job_code": "100"}, {"id": "147", "name_jp": "テレメトリーエンジニア", "name_en": "Telemetry Engineer", "name_vn": "Telemetry Engineer", "job_code": "100"}, {"id": "148", "name_jp": "ソフトウェアサプライチェーンセキュリティエンジニア", "name_en": "Software Supply Chain Security Engineer", "name_vn": "Software Supply Chain Security Engineer", "job_code": "100"}, {"id": "149", "name_jp": "ゼロトラストアーキテクト", "name_en": "Zero Trust Architect", "name_vn": "Zero Trust Architect", "job_code": "100"}, {"id": "150", "name_jp": "クラウドネイティブセキュリティエンジニア", "name_en": "Cloud Native Security Engineer", "name_vn": "Cloud Native Security Engineer", "job_code": "100"}, {"id": "151", "name_jp": "APIセキュリティスペシャリスト", "name_en": "API Security Specialist", "name_vn": "API Security Specialist", "job_code": "100"}, {"id": "152", "name_jp": "コンテナセキュリティエンジニア", "name_en": "Container Security Engineer", "name_vn": "Container Security Engineer", "job_code": "100"}, {"id": "153", "name_jp": "セキュリティオートメーションエンジニア", "name_en": "Security Automation Engineer", "name_vn": "Security Automation Engineer", "job_code": "100"}, {"id": "154", "name_jp": "脅威インテリジェンスアナリスト", "name_en": "Threat Intelligence Analyst", "name_vn": "Threat Intelligence Analyst", "job_code": "100"}, {"id": "155", "name_jp": "脆弱性管理スペシャリスト", "name_en": "Vulnerability Management Specialist", "name_vn": "Vulnerability Management Specialist", "job_code": "100"}, {"id": "156", "name_jp": "セキュリティアーキテクト", "name_en": "Security Architect", "name_vn": "Security Architect", "job_code": "100"}, {"id": "157", "name_jp": "倫理的ハッカー", "name_en": "Ethical Hacker", "name_vn": "Ethical Hacker", "job_code": "100"}, {"id": "158", "name_jp": "インシデントレスポンスエンジニア", "name_en": "Incident Response Engineer", "name_vn": "Incident Response Engineer", "job_code": "100"}, {"id": "159", "name_jp": "レッドチームエンジニア", "name_en": "Red Team Engineer", "name_vn": "Red Team Engineer", "job_code": "100"}, {"id": "160", "name_jp": "ブルーチームエンジニア", "name_en": "Blue Team Engineer", "name_vn": "Blue Team Engineer", "job_code": "100"}, {"id": "161", "name_jp": "オブザーバビリティエンジニア", "name_en": "Observability Engineer", "name_vn": "Observability Engineer", "job_code": "100"}, {"id": "162", "name_jp": "プログレッシブデリバリーエンジニア", "name_en": "Progressive Delivery Engineer", "name_vn": "Progressive Delivery Engineer", "job_code": "100"}, {"id": "163", "name_jp": "フィーチャーフラグエンジニア", "name_en": "Feature Flag Engineer", "name_vn": "Feature Flag Engineer", "job_code": "100"}, {"id": "164", "name_jp": "デベロッパーエクスペリエンスエンジニア", "name_en": "Developer Experience Engineer", "name_vn": "Developer Experience Engineer", "job_code": "100"}, {"id": "165", "name_jp": "インターナルデベロッパープラットフォームエンジニア", "name_en": "Internal Developer Platform Engineer", "name_vn": "Internal Developer Platform Engineer", "job_code": "100"}, {"id": "166", "name_jp": "Node.js開発者", "name_en": "Node.js Developer", "name_vn": "Node.js Developer", "job_code": "100"}, {"id": "167", "name_jp": "Reactエンジニア", "name_en": "React Engineer", "name_vn": "React Engineer", "job_code": "100"}, {"id": "168", "name_jp": "Angular開発者", "name_en": "Angular Developer", "name_vn": "Angular Developer", "job_code": "100"}, {"id": "169", "name_jp": "Vue.js開発者", "name_en": "Vue.js Developer", "name_vn": "Vue.js Developer", "job_code": "100"}, {"id": "170", "name_jp": "JavaScriptエンジニア", "name_en": "JavaScript Engineer", "name_vn": "JavaScript Engineer", "job_code": "100"}, {"id": "171", "name_jp": "TypeScript開発者", "name_en": "TypeScript Developer", "name_vn": "TypeScript Developer", "job_code": "100"}, {"id": "172", "name_jp": "Python開発者", "name_en": "Python Developer", "name_vn": "Python Developer", "job_code": "100"}, {"id": "173", "name_jp": "Django開発者", "name_en": "<PERSON><PERSON><PERSON>", "name_vn": "<PERSON><PERSON><PERSON>", "job_code": "100"}, {"id": "174", "name_jp": "Flask開発者", "name_en": "Flask Developer", "name_vn": "Flask Developer", "job_code": "100"}, {"id": "175", "name_jp": "Java開発者", "name_en": "Java Developer", "name_vn": "Java Developer", "job_code": "100"}, {"id": "176", "name_jp": "Spring開発者", "name_en": "Spring Developer", "name_vn": "Spring Developer", "job_code": "100"}, {"id": "177", "name_jp": "PHP開発者", "name_en": "PHP Developer", "name_vn": "PHP Developer", "job_code": "100"}, {"id": "178", "name_jp": "Laravel開発者", "name_en": "<PERSON><PERSON>", "name_vn": "<PERSON><PERSON>", "job_code": "100"}, {"id": "179", "name_jp": "Symfony開発者", "name_en": "Symfony Developer", "name_vn": "Symfony Developer", "job_code": "100"}, {"id": "180", "name_jp": "WordPress開発者", "name_en": "WordPress Developer", "name_vn": "WordPress Developer", "job_code": "100"}, {"id": "181", "name_jp": "Ruby開発者", "name_en": "<PERSON>", "name_vn": "<PERSON>", "job_code": "100"}, {"id": "182", "name_jp": "Ruby on Rails開発者", "name_en": "Ruby on Rails Developer", "name_vn": "Ruby on Rails Developer", "job_code": "100"}, {"id": "183", "name_jp": "C#開発者", "name_en": "C# Developer", "name_vn": "C# Developer", "job_code": "100"}, {"id": "184", "name_jp": ".NET開発者", "name_en": ".NET Developer", "name_vn": ".NET Developer", "job_code": "100"}, {"id": "185", "name_jp": "ASP.NET開発者", "name_en": "ASP.NET Developer", "name_vn": "ASP.NET Developer", "job_code": "100"}, {"id": "186", "name_jp": "C++開発者", "name_en": "C++ Developer", "name_vn": "C++ Developer", "job_code": "100"}, {"id": "187", "name_jp": "Golang開発者", "name_en": "Golang Developer", "name_vn": "Golang Developer", "job_code": "100"}, {"id": "188", "name_jp": "Rust開発者", "name_en": "<PERSON><PERSON> Developer", "name_vn": "<PERSON><PERSON> Developer", "job_code": "100"}, {"id": "189", "name_jp": "Swift開発者", "name_en": "Swift Developer", "name_vn": "Swift Developer", "job_code": "100"}, {"id": "190", "name_jp": "Kotlin開発者", "name_en": "<PERSON><PERSON><PERSON>", "name_vn": "<PERSON><PERSON><PERSON>", "job_code": "100"}, {"id": "191", "name_jp": "iOSエンジニア", "name_en": "iOS Engineer", "name_vn": "iOS Engineer", "job_code": "100"}, {"id": "192", "name_jp": "Androidエンジニア", "name_en": "Android Engineer", "name_vn": "Android Engineer", "job_code": "100"}, {"id": "193", "name_jp": "React Native開発者", "name_en": "React Native Developer", "name_vn": "React Native Developer", "job_code": "100"}, {"id": "194", "name_jp": "Flutter開発者", "name_en": "Flutter Developer", "name_vn": "Flutter Developer", "job_code": "100"}, {"id": "195", "name_jp": "<PERSON><PERSON><PERSON>開発者", "name_en": "<PERSON><PERSON><PERSON>", "name_vn": "<PERSON><PERSON><PERSON>", "job_code": "100"}, {"id": "196", "name_jp": "GraphQL開発者", "name_en": "GraphQL Developer", "name_vn": "GraphQL Developer", "job_code": "100"}, {"id": "197", "name_jp": "RESTful API開発者", "name_en": "RESTful API Developer", "name_vn": "RESTful API Developer", "job_code": "100"}, {"id": "198", "name_jp": "MongoDB開発者", "name_en": "MongoDB Developer", "name_vn": "MongoDB Developer", "job_code": "100"}, {"id": "199", "name_jp": "PostgreSQLエンジニア", "name_en": "PostgreSQL Engineer", "name_vn": "PostgreSQL Engineer", "job_code": "100"}, {"id": "200", "name_jp": "MySQLエンジニア", "name_en": "MySQL Engineer", "name_vn": "MySQL Engineer", "job_code": "100"}, {"id": "201", "name_jp": "Redis開発者", "name_en": "<PERSON><PERSON>", "name_vn": "<PERSON><PERSON>", "job_code": "100"}, {"id": "202", "name_jp": "Elasticsearch開発者", "name_en": "Elasticsearch Developer", "name_vn": "Elasticsearch Developer", "job_code": "100"}, {"id": "203", "name_jp": "AWS専門家", "name_en": "AWS Specialist", "name_vn": "AWS Specialist", "job_code": "100"}, {"id": "204", "name_jp": "Google Cloud専門家", "name_en": "Google Cloud Specialist", "name_vn": "Google Cloud Specialist", "job_code": "100"}, {"id": "205", "name_jp": "Azure専門家", "name_en": "Azure Specialist", "name_vn": "Azure Specialist", "job_code": "100"}, {"id": "206", "name_jp": "Salesforce開発者", "name_en": "Salesforce Developer", "name_vn": "Salesforce Developer", "job_code": "100"}, {"id": "207", "name_jp": "Salesforceアーキテクト", "name_en": "Salesforce Architect", "name_vn": "Salesforce Architect", "job_code": "100"}, {"id": "208", "name_jp": "SAP開発者", "name_en": "SAP Developer", "name_vn": "SAP Developer", "job_code": "100"}, {"id": "209", "name_jp": "Dynamics 365開発者", "name_en": "Dynamics 365 Developer", "name_vn": "Dynamics 365 Developer", "job_code": "100"}, {"id": "210", "name_jp": "Shopify開発者", "name_en": "Shopify Developer", "name_vn": "Shopify Developer", "job_code": "100"}, {"id": "211", "name_jp": "Magento開発者", "name_en": "Magento Developer", "name_vn": "Magento Developer", "job_code": "100"}, {"id": "212", "name_jp": "WooCommerce開発者", "name_en": "WooCommerce Developer", "name_vn": "WooCommerce Developer", "job_code": "100"}, {"id": "213", "name_jp": "<PERSON><PERSON><PERSON>開発者", "name_en": "<PERSON><PERSON><PERSON>", "name_vn": "<PERSON><PERSON><PERSON>", "job_code": "100"}, {"id": "214", "name_jp": "<PERSON><PERSON><PERSON>開発者", "name_en": "<PERSON><PERSON><PERSON>", "name_vn": "<PERSON><PERSON><PERSON>", "job_code": "100"}, {"id": "215", "name_jp": "HubSpot開発者", "name_en": "HubSpot Developer", "name_vn": "HubSpot Developer", "job_code": "100"}, {"id": "216", "name_jp": "WebAssembly開発者", "name_en": "WebAssembly Developer", "name_vn": "WebAssembly Developer", "job_code": "100"}, {"id": "217", "name_jp": "PWA開発者", "name_en": "Progressive Web App Developer", "name_vn": "Progressive Web App Developer", "job_code": "100"}, {"id": "218", "name_jp": "JAMstack開発者", "name_en": "JAMstack Developer", "name_vn": "JAMstack Developer", "job_code": "100"}, {"id": "219", "name_jp": "Gatsby開発者", "name_en": "Gatsby Developer", "name_vn": "Gatsby Developer", "job_code": "100"}, {"id": "220", "name_jp": "Next.js開発者", "name_en": "Next.js <PERSON>", "name_vn": "Next.js <PERSON>", "job_code": "100"}, {"id": "221", "name_jp": "Nuxt.js開発者", "name_en": "Nuxt.js Developer", "name_vn": "Nuxt.js Developer", "job_code": "100"}, {"id": "222", "name_jp": "Svelte開発者", "name_en": "Svelte Developer", "name_vn": "Svelte Developer", "job_code": "100"}, {"id": "223", "name_jp": "Ember.js開発者", "name_en": "Ember.js Developer", "name_vn": "Ember.js Developer", "job_code": "100"}, {"id": "224", "name_jp": "Backbone.js開発者", "name_en": "Backbone.js <PERSON>", "name_vn": "Backbone.js <PERSON>", "job_code": "100"}, {"id": "225", "name_jp": "Express.js開発者", "name_en": "Express.js Developer", "name_vn": "Express.js Developer", "job_code": "100"}, {"id": "226", "name_jp": "Koa.js開発者", "name_en": "Koa.js Developer", "name_vn": "Koa.js Developer", "job_code": "100"}, {"id": "227", "name_jp": "NestJS開発者", "name_en": "NestJS Developer", "name_vn": "NestJS Developer", "job_code": "100"}, {"id": "228", "name_jp": "FastAPI開発者", "name_en": "FastAPI Developer", "name_vn": "FastAPI Developer", "job_code": "100"}, {"id": "229", "name_jp": "Gin開発者", "name_en": "<PERSON><PERSON>", "name_vn": "<PERSON><PERSON>", "job_code": "100"}, {"id": "230", "name_jp": "Scala開発者", "name_en": "Scala Developer", "name_vn": "Scala Developer", "job_code": "100"}, {"id": "231", "name_jp": "Elixir開発者", "name_en": "<PERSON><PERSON><PERSON>", "name_vn": "<PERSON><PERSON><PERSON>", "job_code": "100"}, {"id": "232", "name_jp": "Haskell開発者", "name_en": "<PERSON><PERSON> Developer", "name_vn": "<PERSON><PERSON> Developer", "job_code": "100"}, {"id": "233", "name_jp": "Clojure開発者", "name_en": "<PERSON><PERSON><PERSON>", "name_vn": "<PERSON><PERSON><PERSON>", "job_code": "100"}, {"id": "234", "name_jp": "F#開発者", "name_en": "F# Developer", "name_vn": "F# Developer", "job_code": "100"}, {"id": "235", "name_jp": "Dart開発者", "name_en": "<PERSON><PERSON>", "name_vn": "<PERSON><PERSON>", "job_code": "100"}, {"id": "236", "name_jp": "Unity開発者", "name_en": "Unity Developer", "name_vn": "Unity Developer", "job_code": "100"}, {"id": "237", "name_jp": "Unreal Engine開発者", "name_en": "Unreal Engine Developer", "name_vn": "Unreal Engine Developer", "job_code": "100"}, {"id": "238", "name_jp": "<PERSON><PERSON>開発者", "name_en": "Godot Developer", "name_vn": "Godot Developer", "job_code": "100"}, {"id": "239", "name_jp": "AR/VR開発者", "name_en": "AR/VR Developer", "name_vn": "AR/VR Developer", "job_code": "100"}, {"id": "240", "name_jp": "WebXR開発者", "name_en": "WebXR Developer", "name_vn": "WebXR Developer", "job_code": "100"}, {"id": "241", "name_jp": "メタバース開発者", "name_en": "<PERSON><PERSON><PERSON> Developer", "name_vn": "<PERSON><PERSON><PERSON> Developer", "job_code": "100"}, {"id": "242", "name_jp": "ブロックチェーン開発者", "name_en": "Blockchain Developer", "name_vn": "Blockchain Developer", "job_code": "100"}, {"id": "243", "name_jp": "イーサリアム開発者", "name_en": "Ethereum Developer", "name_vn": "Ethereum Developer", "job_code": "100"}, {"id": "244", "name_jp": "ソラナ開発者", "name_en": "<PERSON><PERSON>", "name_vn": "<PERSON><PERSON>", "job_code": "100"}, {"id": "245", "name_jp": "NFT開発者", "name_en": "NFT Developer", "name_vn": "NFT Developer", "job_code": "100"}]}