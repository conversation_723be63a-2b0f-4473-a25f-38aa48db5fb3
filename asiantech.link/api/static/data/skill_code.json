{"skill_code": [{"id": "100001", "category_id": "100", "name": "ABAP"}, {"id": "100002", "category_id": "101", "name": "AngularJS"}, {"id": "100003", "category_id": "999", "name": "Azure"}, {"id": "100004", "category_id": "999", "name": "Power BI"}, {"id": "100005", "category_id": "999", "name": "Cloud"}, {"id": "100006", "category_id": "100", "name": "Dart"}, {"id": "100007", "category_id": "999", "name": "DevOps"}, {"id": "100008", "category_id": "100", "name": "Embedded C"}, {"id": "100009", "category_id": "999", "name": "Games"}, {"id": "100010", "category_id": "999", "name": "IT Support"}, {"id": "100011", "category_id": "100", "name": "JavaScript"}, {"id": "100012", "category_id": "101", "name": "<PERSON><PERSON>"}, {"id": "100013", "category_id": "102", "name": "MongoDB"}, {"id": "100014", "category_id": "999", "name": "Networking"}, {"id": "100015", "category_id": "999", "name": "OOP"}, {"id": "100016", "category_id": "999", "name": "PQA"}, {"id": "100017", "category_id": "999", "name": "Product Owner"}, {"id": "100018", "category_id": "101", "name": "ReactJS"}, {"id": "100019", "category_id": "999", "name": "Salesforce"}, {"id": "100020", "category_id": "999", "name": "Software Architect"}, {"id": "100021", "category_id": "102", "name": "SQL"}, {"id": "100022", "category_id": "999", "name": "Team Leader"}, {"id": "100023", "category_id": "999", "name": "Unity"}, {"id": "100024", "category_id": "999", "name": "Agile"}, {"id": "100025", "category_id": "101", "name": "ASP.NET"}, {"id": "100026", "category_id": "999", "name": "Blockchain"}, {"id": "100027", "category_id": "100", "name": "C#"}, {"id": "100028", "category_id": "999", "name": "Cocos"}, {"id": "100029", "category_id": "999", "name": "Data Analyst"}, {"id": "100030", "category_id": "999", "name": "DevSecOps"}, {"id": "100032", "category_id": "100", "name": "Golang"}, {"id": "100033", "category_id": "101", "name": "J2EE"}, {"id": "100034", "category_id": "101", "name": "<PERSON><PERSON><PERSON><PERSON>"}, {"id": "100035", "category_id": "106", "name": "Linux"}, {"id": "100036", "category_id": "999", "name": "MVC"}, {"id": "100037", "category_id": "101", "name": "NodeJS"}, {"id": "100038", "category_id": "102", "name": "Oracle"}, {"id": "100039", "category_id": "999", "name": "Presale"}, {"id": "100040", "category_id": "999", "name": "Project Manager"}, {"id": "100041", "category_id": "101", "name": "React Native"}, {"id": "100042", "category_id": "100", "name": "Scala"}, {"id": "100043", "category_id": "100", "name": "Solidity"}, {"id": "100044", "category_id": "100", "name": "Swift"}, {"id": "100045", "category_id": "999", "name": "Tester"}, {"id": "100046", "category_id": "101", "name": "VueJS"}, {"id": "100047", "category_id": "106", "name": "Android"}, {"id": "100048", "category_id": "999", "name": "Automation Test"}, {"id": "100049", "category_id": "999", "name": "Bridge Engineer"}, {"id": "100050", "category_id": "100", "name": "C++"}, {"id": "100051", "category_id": "999", "name": "Computer Vision"}, {"id": "100052", "category_id": "102", "name": "Database"}, {"id": "100053", "category_id": "101", "name": "Django"}, {"id": "100054", "category_id": "999", "name": "ERP"}, {"id": "100055", "category_id": "100", "name": "HTML5"}, {"id": "100057", "category_id": "999", "name": "JSON"}, {"id": "100058", "category_id": "101", "name": "Magento"}, {"id": "100059", "category_id": "102", "name": "MySQL"}, {"id": "100060", "category_id": "102", "name": "NoSQL"}, {"id": "100061", "category_id": "100", "name": "PHP"}, {"id": "100062", "category_id": "999", "name": "Product Designer"}, {"id": "100063", "category_id": "100", "name": "Python"}, {"id": "100064", "category_id": "100", "name": "<PERSON>"}, {"id": "100065", "category_id": "999", "name": "Scrum master"}, {"id": "100066", "category_id": "999", "name": "Solution Architect"}, {"id": "100067", "category_id": "999", "name": "System Admin"}, {"id": "100068", "category_id": "100", "name": "TypeScript"}, {"id": "100069", "category_id": "101", "name": "Wordpress"}, {"id": "100070", "category_id": "101", "name": "Angular"}, {"id": "100071", "category_id": "999", "name": "AWS"}, {"id": "100072", "category_id": "999", "name": "Business Analyst"}, {"id": "100073", "category_id": "100", "name": "C"}, {"id": "100074", "category_id": "100", "name": "CSS"}, {"id": "100075", "category_id": "999", "name": "Designer"}, {"id": "100076", "category_id": "999", "name": "Embedded"}, {"id": "100077", "category_id": "101", "name": "Flutter"}, {"id": "100078", "category_id": "106", "name": "iOS"}, {"id": "100079", "category_id": "100", "name": "Java"}, {"id": "100080", "category_id": "100", "name": "<PERSON><PERSON><PERSON>"}, {"id": "100081", "category_id": "999", "name": "Manager"}, {"id": "100082", "category_id": "101", "name": ".NET"}, {"id": "100083", "category_id": "100", "name": "Objective C"}, {"id": "100084", "category_id": "102", "name": "PostgreSql"}, {"id": "100085", "category_id": "999", "name": "Product Manager"}, {"id": "100086", "category_id": "999", "name": "QA QC"}, {"id": "100087", "category_id": "101", "name": "Ruby on Rails"}, {"id": "100088", "category_id": "999", "name": "Security"}, {"id": "100089", "category_id": "101", "name": "Spring"}, {"id": "100090", "category_id": "999", "name": "System Engineer"}, {"id": "100091", "category_id": "999", "name": "UI-UX"}, {"id": "100092", "category_id": "999", "name": "Microsoft Office Suite"}, {"id": "100093", "category_id": "999", "name": "Microsoft Visio"}, {"id": "100094", "category_id": "999", "name": "Axure RP"}, {"id": "100095", "category_id": "999", "name": "Figma"}, {"id": "100096", "category_id": "999", "name": "Smart.io"}, {"id": "100097", "category_id": "105", "name": "Visual Studio"}, {"id": "100098", "category_id": "105", "name": "Visual Studio Code"}, {"id": "100099", "category_id": "999", "name": "Postman"}, {"id": "100100", "category_id": "104", "name": "Git"}, {"id": "100105", "category_id": "104", "name": "SVN (Subversion)"}, {"id": "100106", "category_id": "104", "name": "Mercurial"}, {"id": "100107", "category_id": "104", "name": "TFS (Team Foundation Server)"}, {"id": "100108", "category_id": "104", "name": "Perforce"}, {"id": "100109", "category_id": "104", "name": "CVS"}, {"id": "100101", "category_id": "103", "name": "JIRA"}, {"id": "100102", "category_id": "103", "name": "Trello"}, {"id": "100103", "category_id": "999", "name": "Draw.io"}, {"id": "100104", "category_id": "999", "name": "Bizagi"}, {"id": "100110", "category_id": "105", "name": "IntelliJ IDEA"}, {"id": "100111", "category_id": "105", "name": "GitHub"}, {"id": "100112", "category_id": "105", "name": "AWS Cloud9"}, {"id": "100113", "category_id": "105", "name": "Eclipse"}, {"id": "100114", "category_id": "105", "name": "NetBeans"}, {"id": "100115", "category_id": "105", "name": "PyCharm"}, {"id": "100116", "category_id": "105", "name": "Rider"}, {"id": "100117", "category_id": "105", "name": "Xcode"}, {"id": "100118", "category_id": "105", "name": "Android Studio"}, {"id": "100119", "category_id": "105", "name": "WebStorm"}, {"id": "100120", "category_id": "105", "name": "RubyMine"}, {"id": "100121", "category_id": "105", "name": "PhpStorm"}, {"id": "100122", "category_id": "105", "name": "CLion"}, {"id": "100123", "category_id": "105", "name": "AppCode"}, {"id": "100124", "category_id": "105", "name": "<PERSON>der"}, {"id": "100125", "category_id": "105", "name": "RStudio"}, {"id": "100126", "category_id": "105", "name": "Sublime Text"}, {"id": "100127", "category_id": "105", "name": "Atom"}, {"id": "100128", "category_id": "105", "name": "Notepad++"}, {"id": "100129", "category_id": "105", "name": "Brackets"}, {"id": "100130", "category_id": "999", "name": "Microsoft Word"}, {"id": "100131", "category_id": "999", "name": "Microsoft Excel"}, {"id": "100132", "category_id": "999", "name": "Microsoft PowerPoint"}, {"id": "100133", "category_id": "999", "name": "Epic Systems"}, {"id": "100134", "category_id": "999", "name": "<PERSON><PERSON>"}, {"id": "100135", "category_id": "100", "name": "PowerShell"}, {"id": "100136", "category_id": "999", "name": "<PERSON><PERSON>"}, {"id": "100137", "category_id": "999", "name": "<PERSON><PERSON><PERSON>"}, {"id": "100138", "category_id": "999", "name": "Apache"}, {"id": "100139", "category_id": "999", "name": "Haproxy"}, {"id": "100140", "category_id": "105", "name": "Ubuntu"}, {"id": "100141", "category_id": "105", "name": "Windows Server"}, {"id": "100142", "category_id": "105", "name": "CENTOS"}, {"id": "100143", "category_id": "999", "name": "Ansible"}, {"id": "100144", "category_id": "999", "name": "Terraform"}, {"id": "100145", "category_id": "999", "name": "Python Boto3"}, {"id": "100146", "category_id": "999", "name": "<PERSON>er"}, {"id": "100147", "category_id": "999", "name": "Kubernetes"}, {"id": "100148", "category_id": "999", "name": "ECS (Elastic Container Service)"}, {"id": "100149", "category_id": "999", "name": "EKS (Elastic Kubernetes Service)"}, {"id": "100150", "category_id": "999", "name": "<PERSON>"}, {"id": "100151", "category_id": "999", "name": "FluxCD"}, {"id": "100152", "category_id": "105", "name": "Gitlab"}, {"id": "100163", "category_id": "999", "name": "Gitlab-ci"}, {"id": "100153", "category_id": "999", "name": "ArgoCD"}, {"id": "100154", "category_id": "999", "name": "Bitbucket"}, {"id": "100155", "category_id": "999", "name": "AzureDevops"}, {"id": "100156", "category_id": "999", "name": "Digital Ocean"}, {"id": "100157", "category_id": "999", "name": "<PERSON><PERSON>"}, {"id": "100158", "category_id": "999", "name": "Prometheus"}, {"id": "100159", "category_id": "999", "name": "Zabbix"}, {"id": "100160", "category_id": "999", "name": "PRTG"}, {"id": "100161", "category_id": "999", "name": "Datadog"}, {"id": "100162", "category_id": "999", "name": "AWS Cloudwatch"}, {"id": "100164", "category_id": "999", "name": "Github-action"}, {"id": "100165", "category_id": "999", "name": "SonarQube"}, {"id": "100166", "category_id": "999", "name": "Coverity"}, {"id": "100167", "category_id": "999", "name": "Azure application service"}, {"id": "100168", "category_id": "999", "name": "Azure function app"}, {"id": "100169", "category_id": "102", "name": "RDS (Relational Database Service)"}, {"id": "100170", "category_id": "102", "name": "Redis (Remote Dictionary Server)"}, {"id": "100171", "category_id": "999", "name": "ElastiCache"}, {"id": "100172", "category_id": "999", "name": "<PERSON><PERSON><PERSON>"}, {"id": "100173", "category_id": "999", "name": "Apache Tomcat"}, {"id": "100174", "category_id": "999", "name": "Traefik"}, {"id": "100175", "category_id": "999", "name": "ALB (Application Load Balancer)"}, {"id": "100176", "category_id": "999", "name": "Azure Application Gateway"}, {"id": "100177", "category_id": "999", "name": "Data Dog"}, {"id": "100178", "category_id": "999", "name": "Azure monitor"}, {"id": "100179", "category_id": "999", "name": "ELK stack (Elasticsearch, Logstash, Kibana)"}, {"id": "100180", "category_id": "999", "name": "JMeter"}, {"id": "100181", "category_id": "999", "name": "<PERSON><PERSON><PERSON>"}, {"id": "100182", "category_id": "100", "name": "Groovy"}, {"id": "100183", "category_id": "999", "name": "Soap UI"}, {"id": "100184", "category_id": "999", "name": "Insomnia"}, {"id": "100185", "category_id": "999", "name": "Rest-assured"}, {"id": "100186", "category_id": "999", "name": "Go-resty"}, {"id": "100187", "category_id": "999", "name": "InfluxDB"}, {"id": "100188", "category_id": "999", "name": "Prometheus"}, {"id": "100189", "category_id": "999", "name": "Testrail"}, {"id": "100190", "category_id": "999", "name": "Qmetry"}, {"id": "100191", "category_id": "999", "name": "Minio"}, {"id": "100192", "category_id": "999", "name": "Amazon S3"}, {"id": "100193", "category_id": "999", "name": "Gatling"}, {"id": "100194", "category_id": "999", "name": "k6 (<PERSON><PERSON><PERSON><PERSON> 6)"}, {"id": "100195", "category_id": "999", "name": "Serenity"}, {"id": "100196", "category_id": "999", "name": "Selenium"}, {"id": "100197", "category_id": "999", "name": "Cypress"}, {"id": "100198", "category_id": "999", "name": "Playwright"}, {"id": "100199", "category_id": "999", "name": "Playwright"}, {"id": "100199", "category_id": "106", "name": "Mac OS"}, {"id": "100200", "category_id": "999", "name": "BrowserStack"}, {"id": "100201", "category_id": "999", "name": "Sauce Labs"}, {"id": "100202", "category_id": "105", "name": "<PERSON><PERSON>"}, {"id": "100203", "category_id": "999", "name": "Winium"}, {"id": "100204", "category_id": "999", "name": "<PERSON><PERSON><PERSON>ber"}, {"id": "100205", "category_id": "999", "name": "Specflow"}]}