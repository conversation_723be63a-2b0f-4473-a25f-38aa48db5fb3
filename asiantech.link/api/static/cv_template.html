<html>

<head>
    <meta content="text/html; charset=UTF-8" http-equiv="content-type">
    <style type="text/css">
        @import url(https://themes.googleusercontent.com/fonts/css?kit=fpjTOVmNbO4Lz34iLyptLUXza5VhXqVC6o75Eld_V98);

        table td,
        table th {
            padding: 0
        }

        li {
            color: #000000;
            font-size: 12pt;
            font-family: "Times New Roman"
        }

        p {
            margin: 0;
            color: #000000;
            font-size: 12pt;
            font-family: "Times New Roman"
        }

        h1 {
            padding-top: 24pt;
            color: #000000;
            font-weight: 700;
            font-size: 24pt;
            padding-bottom: 6pt;
            font-family: "Times New Roman";
            line-height: 1.0;
            page-break-after: avoid;
            orphans: 2;
            widows: 2;
            text-align: center
        }

        .personal-details th {

            font-weight: normal;
            font-family: 'Times New Roman', Times, serif;
            font-size: 12pt;
            text-align: bottom;
        }

        .personal-details td {
            text-align: bottom;
            padding-top: 6px;
        }

        .highlight-project-table th {
            font-weight: normal;
            font-family: 'Times New Roman', Times, serif;
            font-size: 12pt;
            text-align: bottom;
        }

        .highlight-project-table td {
            padding-top: 10px;
            padding-bottom: 10px;
            line-height: 1.5;
            text-align: left;
        }
    </style>
</head>

<body>

    <p style="text-align: center;">
        <strong>
            <span style="font-size:18pt; font-weight:bold; font-family:'Times New Roman';">
                CURRICULUM VITAE
            </span>
        </strong>
    </p>

    <table class="personal-details">
        <thead>
            <tr>
                <th colspan="2">PERSONAL DETAILS</th>
            </tr>
        </thead>
        <tbody>
            <tr>
                <th>Full name</th>
                <td>
                    <strong>
                        <span
                            style="font-size:14pt; font-weight:bold; font-family:'Times New Roman';">{engineerName}</span>
                    </strong>
                </td>
            </tr>
            <tr>
                <th>Job Titles</th>
                <td></td>
            </tr>
            <tr>
                <th>Date of Birth</th>
                <td>{dateOfBirthValue}</td>
            </tr>
            <tr>
                <th>Gender</th>
                <td>{genderValue}</td>
            </tr>
            <tr>
                <th>Address</th>
                <td>{addressValue}</td>
            </tr>
        </tbody>
    </table>
</body>

</html>