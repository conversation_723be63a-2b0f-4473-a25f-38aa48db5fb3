<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ emailVerification }}</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            background-color: #f0f0f0;
            text-align: center;
            margin: 0;
            padding: 0;
        }
        .container {
            width: 100%;
            max-width: 600px;
            margin: auto;
            background-color: #fff;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
        }
        .message {
            font-size: 16px;
            margin-bottom: 20px;
            text-align: left;
            padding-left: 10px;
            padding-right: 10px;
        }
        .verification-code {
            font-size: 24px;
            font-weight: bold;
            padding: 10px 20px;
            background-color: #f0f0f0;
            border-radius: 4px;
            display: inline-block;
        }
        .note {
            font-size: 14px;
            color: #666;
            margin-top: 20px;
            text-align: left;
            padding-left: 10px;
            padding-right: 10px;
        }
    </style>
</head>
<body style="margin: 0; padding: 0;">
    <table class="container" align="center" cellpadding="0" cellspacing="0" border="0" width="100%" style="max-width: 600px; background-color: #fff; padding: 20px; border-radius: 8px; box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);">
        <tr>
            <td align="center">
                <h2 style="font-size: 24px; margin-bottom: 20px;">{{ emailVerification }}</h2>
                <p class="message" style="font-size: 16px; margin-bottom: 20px; text-align: left; padding-left: 10px; padding-right: 10px;">{{ emailConfirmLoginContent }}</p>
                <div class="verification-code" style="font-size: 24px; font-weight: bold; padding: 10px 20px; background-color: #f0f0f0; border-radius: 4px; display: inline-block;">{{ code }}</div>
                <p class="note" style="font-size: 14px; color: #666; margin-top: 20px; text-align: left; padding-left: 10px; padding-right: 10px;">{{ emailConfirmLoginContentNote }}</p>
            </td>
        </tr>
    </table>
</body>
</html>
