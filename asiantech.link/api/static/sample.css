* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}
body {
    background-color: rgb(253, 254, 255);
    display: flex;
    justify-content: center;
    align-items: center;
}
.full {
    width: 100%;
    max-width: 1000px;
    min-height: 100px;
    background-color: rgb(245, 239, 231);
    margin: 0px;
    display: grid;
    grid-template-columns: 2fr 4fr;
}
.left {
    position: initial;
    background-color: rgb(126, 219, 231);
    padding: 20px;
 
}
.right {
    position: initial;
    background-color: rgb(162, 202, 206);
    padding: 20px;
 
}
.image, .Contact, .Skills, .Language, .Hobbies, .title, 
.Summary, .Experience, .Education, .project {
    margin-bottom: 30px;
}
.h2 {
    background-color: rgb(4, 96, 150);
}