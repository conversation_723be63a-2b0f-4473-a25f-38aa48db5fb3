from django.db import models
from utils.custom_fields import *


class EmailSchedules(models.Model):
    type = models.PositiveSmallIntegerField(blank=True, null=True)
    subject = models.CharField(
        max_length=255, db_collation='latin1_swedish_ci', blank=True, null=True)
    body = models.TextField(
        db_collation='latin1_swedish_ci', blank=True, null=True)
    weekday = models.CharField(
        max_length=255, db_collation='latin1_swedish_ci', blank=True, null=True)
    send_time = models.CharField(
        max_length=255, db_collation='latin1_swedish_ci', blank=True, null=True)
    send_datetime = models.DateTimeField(blank=True, null=True)
    is_valid = models.IntegerField(blank=True, null=True)
    is_deleted = models.IntegerField(blank=True, null=True)
    is_repeat = models.IntegerField(
        blank=True, null=True, db_comment='0: No repeat\\\\\\\\n1: Repeat weekday\\\\\\\\n2: Repeat by month\\\\\\\\n3: Repeat by year')
    created = models.DateTimeField(blank=True, null=True, db_comment='初期登録日時')

    class Meta:
        managed = False
        db_table = 'email_schedules'
