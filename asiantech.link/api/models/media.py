import hashlib
from api.models.company import ComCompany
import logging
from utils.constants import *
import os
from django.db import models
from django.contrib.auth.models import User
from django.contrib.auth import get_user_model
from django.utils import timezone as tz
from uuid import uuid4
User = get_user_model()
logger = logging.getLogger("api_logger")


def upload_to(instance, filename):
    ext = filename.split('.')[-1]
    filename = f'{uuid4()}.{ext}'
    user_id = instance.user.user_id

    type_to_folder = {
        MediaType.PASSPORT.value: 'passport',
        MediaType.AVATAR.value: 'avatar',
        MediaType.COMPANY_LOGO.value: 'logo',
        MediaType.COMPANY_PR.value: 'pr',
        MediaType.CONTRACT.value: 'contract',
        MediaType.COVER_RECRUIT.value: 'cover-recruit',
        MediaType.CONTRACT_COMPANY.value: 'contract-company'

    }

    folderPath = type_to_folder.get(instance.type, 'other')
    if folderPath in ['logo', 'pr']:
        company_id = instance.user.company_id or 'unknown'
        company = ComCompany.objects.filter(pk=company_id).first()
        timestamp = company.updated.strftime(
            '%Y%m%d%H%M%S') if company.updated else tz.now().strftime('%Y%m%d%H%M%S')
        hash_string = hashlib.sha256(str(company_id).encode()).hexdigest()[
            :8]  # Hash of company_id

        # Format the filename with company-specific details
        filename = f'{company_id}+({timestamp}+{hash_string}).{ext}'

        return f'company/{company_id}/{folderPath}/{filename}'
    if folderPath in ['passport', 'avatar']:
        user_id = instance.user.user_id
        timestamp = instance.user.created.strftime(
            '%Y%m%d%H%M%S') if instance.user.created else 'unknown'
        hash_string = hashlib.sha256(str(user_id).encode()).hexdigest()[
            :8]  # Hash of user_id

        # Format the filename with user-specific details
        filename = f'{user_id}+({timestamp}+{hash_string}).{ext}'
        return f'member/{user_id}/{folderPath}/{filename}'
    if folderPath in ['contract']:
        user_id = instance.user.user_id
        apply_id = instance.main_folder_path.split("/")[-1]

        timestamp = instance.user.created.strftime(
            '%Y%m%d%H%M%S') if instance.user.created else 'unknown'
        hash_string = hashlib.sha256(str(user_id).encode()).hexdigest()[
            :8]
        filename = f'{apply_id}+({timestamp}+{hash_string}).{ext}'
        return f"{instance.main_folder_path}/{filename}"
    if folderPath in ['cover-recruit']:
        user_id = instance.user.user_id
        timestamp = instance.user.created.strftime(
            '%Y%m%d%H%M%S') if instance.user.created else 'unknown'
        hash_string = hashlib.sha256(str(user_id).encode()).hexdigest()[
            :8]  # Hash of user_id

        # Format the filename with user-specific details
        filename = f'{user_id}+({timestamp}+{hash_string}).{ext}'
        return f'recruit/{user_id}/{folderPath}/{filename}'
    if folderPath in ['contract-company']:
        path = instance.main_folder_path + f".{ext}"
        return path
    return f'member/{user_id}/{folderPath}/{filename}'


class MediaImage(models.Model):

    user = models.ForeignKey(
        User, on_delete=models.CASCADE, null=True, blank=True)
    title = models.CharField(max_length=100, null=True, blank=True)
    # Use the updated upload_to function
    image = models.ImageField(upload_to=upload_to)
    uploaded_at = models.DateTimeField(auto_now_add=True)
    type = models.IntegerField(default=0, null=True)
    main_folder_path = models.CharField(
        max_length=100, null=True, blank=True)

    def __str__(self):
        return self.title

    def save(self, *args, **kwargs):
        # Delete old avatar,password, company_logo image of user
        if self.type == MediaType.AVATAR.value or self.type == MediaType.PASSPORT.value or self.type == MediaType.COMPANY_LOGO.value:
            images = MediaImage.objects.filter(user=self.user, type=self.type)
            if images:
                for image in images:
                    try:
                        image.delete()
                        os.remove(image.image.path)
                    except Exception as e:
                        logger.error(f"Error deleting image: {e}")
        if self.type == MediaType.CONTRACT_COMPANY.value:
            images = MediaImage.objects.filter(
                main_folder_path=self.main_folder_path)
            if images:
                for image in images:
                    try:
                        image.delete()
                        os.remove(image.image.path)
                    except Exception as e:
                        logger.error(f"Error deleting image: {e}")
        super().save(*args, **kwargs)

    class Meta:
        db_table = 'media_image'
