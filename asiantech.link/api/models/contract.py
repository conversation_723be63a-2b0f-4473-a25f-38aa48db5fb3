from django.db import models
from django.utils.translation import gettext_lazy as _
from utils.custom_fields import *

class ComContract(models.Model):
    contract_id = models.AutoField(primary_key=True, db_comment='契約ID')  # The composite primary key (contract_id, company_id) found, that is not supported. The first column is selected.        
    company_id = models.IntegerField(db_comment='企業ID')
    contract_start = models.DateField(null=True, db_comment='契約開始日')
    contract_end = models.DateField(null=True, db_comment='契約終了日')
    created = DateTimeField(null=True, db_comment='初期登録日時')
    updated = DateTimeField(null=True, db_comment='最終更新日時')

    class Meta:
        managed = False
        db_table = 'com_contract'
        unique_together = (('contract_id', 'company_id'),)
        db_table_comment = '契約'