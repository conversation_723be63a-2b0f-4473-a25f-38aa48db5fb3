from django.db import models
from django.utils.translation import gettext_lazy as _
from utils.custom_fields import *

class LogOffer(models.Model):
    offer_id = models.AutoField(primary_key=True, db_comment='内定受理ID')
    recruit_id = models.IntegerField(null=True, db_comment='募集要項ID')
    apply_id = models.IntegerField(null=True, db_comment='応募ID')
    engineer_id = models.IntegerField(db_comment='技術者ID')
    agency_company_id = models.IntegerField(null=True, db_comment='技術者紹介機関ID')
    agency_agent_id = models.IntegerField(null=True, db_comment='技術者紹介機関担当者ID')
    host_company_id = models.IntegerField(db_comment='受入企業ID')
    host_agent_id = models.IntegerField(null=True, db_comment='受入企業担当者ID')
    support_company_id = models.IntegerField(null=True, db_comment='受入サポート機関ID')
    support_agent_id = models.IntegerField(null=True, db_comment='受入サポート機関担当者ID')
    join_date = models.DateField(null=True, db_comment='入社予定日')
    job_code = models.CharField(max_length=3, null=True, db_comment='採用職種コード')
    employ_code = models.CharField(max_length=2, null=True, db_comment='募集雇用形態コード')
    place_code = models.CharField(max_length=9, null=True, db_comment='採用勤務地コード')
    payroll_code = models.CharField(max_length=3, null=True, db_comment='募集給与通貨コード')
    payroll_price_from = models.DecimalField(max_digits=10, decimal_places=3, null=True, db_comment='募集給与金額(より)')
    payroll_price_to = models.DecimalField(max_digits=10, decimal_places=3, null=True, db_comment='募集給与金額(まで)')
    benefits = models.CharField(max_length=10000, null=True, db_comment='その他待遇')
    offer_contract_date = models.DateField(null=True, db_comment='内定受理入社契約日')
    offer_pdf_path = models.CharField(max_length=75, null=True, db_comment='内定受理入社契約書ファイル名')
    create_agent_id = models.IntegerField(null=True, db_comment='初期登録企業ID')
    created = DateTimeField(null=True, db_comment='初期登録日時')
    update_agent_id = models.IntegerField(null=True, db_comment='最終更新企業ID')
    updated = DateTimeField(null=True, db_comment='最終更新日時')

    class Meta:
        managed = False
        db_table = 'log_offer'
        db_table_comment = '内容受理'

class LogDecline(models.Model):
    offer_id = models.AutoField(primary_key=True, db_comment='内定受理ID')
    recruit_id = models.IntegerField(null=True, db_comment='募集要項ID')
    apply_id = models.IntegerField(null=True, db_comment='応募ID')
    engineer_id = models.IntegerField(db_comment='技術者ID')
    agency_company_id = models.IntegerField(null=True, db_comment='技術者紹介機関ID')
    agency_agent_id = models.IntegerField(null=True, db_comment='技術者紹介機関担当者ID')
    host_company_id = models.IntegerField(db_comment='受入企業ID')
    host_agent_id = models.IntegerField(null=True, db_comment='受入企業担当者ID')
    support_company_id = models.IntegerField(null=True, db_comment='受入サポート機関ID')
    support_agent_id = models.IntegerField(null=True, db_comment='受入サポート機関担当者ID')
    before_progress_code = models.CharField(max_length=6, null=True, db_comment='辞退発生時進捗状況コード')
    decline_progress_code = models.CharField(max_length=6, null=True, db_comment='辞退状況コード')
    created = DateTimeField(null=True, db_comment='初期登録日時')
    updated = DateTimeField(null=True, db_comment='最終更新日時')

    class Meta:
        managed = False
        db_table = 'log_decline'
        db_table_comment = '辞退'