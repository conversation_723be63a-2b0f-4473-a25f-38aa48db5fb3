
from django.db import models
from django.utils.translation import gettext_lazy as _
from utils.custom_fields import *


class ComCompany(models.Model):
    # The composite primary key (company_id, user_type) found, that is not supported. The first column is selected.
    company_id = models.AutoField(primary_key=True, db_comment='企業ID')
    user_type = Bit3Field(db_comment='企業ID種別')  # This field type is a guess.
    name = models.CharField(max_length=200, blank=True,
                            null=True, db_comment='会社名')
    about_us = models.CharField(
        max_length=5000, blank=True, null=True, db_comment='会社概要')
    business_details = models.CharField(
        max_length=5000, blank=True, null=True, db_comment='事業内容')
    employees_type = TinyInt4Field(null=True, db_comment='従業員数種別')
    country_code = models.CharField(
        max_length=3, blank=True, null=True, db_comment='国コード')
    address_code = models.Char<PERSON><PERSON>(
        max_length=9, blank=True, null=True, db_comment='国別県市コード')
    address = models.CharField(
        max_length=200, blank=True, null=True, db_comment='住所')
    tel = models.CharField(max_length=20, blank=True,
                           null=True, db_comment='電話番号')
    logo_image_path = models.CharField(
        max_length=500, blank=True, null=True, db_comment='会社ロゴファイル名')
    pr_image_path1 = models.CharField(
        max_length=500, blank=True, null=True, db_comment='会社PR写真ファイル名1')
    pr_image_path2 = models.CharField(
        max_length=500, blank=True, null=True, db_comment='会社PR写真ファイル名2')
    pr_image_path3 = models.CharField(
        max_length=500, blank=True, null=True, db_comment='会社PR写真ファイル名3')
    contact_mail = models.CharField(
        max_length=100, blank=True, null=True, db_comment='問い合わせ先メールアドレス')
    web_url = models.TextField(blank=True, null=True, db_comment='Webサイト')
    introduction_url = models.TextField(
        blank=True, null=True, db_comment='PR動画URL')
    memo = TinyTextField(null=True, db_comment='管理用メモ')
    created = models.DateTimeField(
        blank=True, null=True, db_comment='初期登録日時', auto_now_add=True)
    updated = models.DateTimeField(blank=True, null=True, db_comment='最終更新日時')
    benefits = models.CharField(
        max_length=1000, blank=True, null=True, db_comment='福利厚生')
    capital_stock = models.DecimalField(
        max_digits=20, decimal_places=3, blank=True, null=True, db_comment='資本金')
    capital_stock_curr_code = models.CharField(
        max_length=3, blank=True, null=True, db_comment='資本金通貨コード')
    international_tel = models.CharField(
        max_length=3, blank=True, null=True, db_comment='国際電話国番号')
    agent_fee = models.DecimalField(
        max_digits=20, decimal_places=3, blank=True, null=True, db_comment='人材紹介手数料')
    agent_fee_curr_code = models.CharField(
        max_length=3, blank=True, null=True, db_comment='人材紹介手数料通貨コード')
    accepting_fee = models.DecimalField(
        max_digits=20, decimal_places=3, blank=True, null=True, db_comment='受入初期費用')
    accepting_fee_curr_code = models.CharField(
        max_length=3, blank=True, null=True, db_comment='受入初期費用通貨コード')
    support_outsourcing_fee = models.DecimalField(
        max_digits=20, decimal_places=3, blank=True, null=True, db_comment='支援委託費')
    support_outsourcing_fee_curr_code = models.CharField(
        max_length=3, blank=True, null=True, db_comment='支援委託費通貨コード')
    support = models.CharField(
        max_length=5000, blank=True, null=True, db_comment='支援内容')
    status = Bit2Field(blank=True, null=True,
                       db_comment='00:非公開 01:公開 10:削除済 日時バッチで契約テーブルと連動')
    working_hours_from = models.TimeField(blank=True, null=True)
    working_hours_to = models.TimeField(blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'com_company'
        unique_together = (('company_id', 'user_type'),)
        db_table_comment = '企業'
