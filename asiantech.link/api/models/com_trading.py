from django.db import models
from django.utils.translation import gettext_lazy as _
from utils.custom_fields import *

class ComTrading(models.Model):
    com_trading_id = models.AutoField(primary_key=True, db_comment='取引条件ID')
    created = DateTimeField(null=True, db_comment='初期登録日時')
    updated = DateTimeField(null=True, db_comment='最終更新日時')

    class Meta:
        managed = False
        db_table = 'com_trading'
        db_table_comment = '受入サポート機関取引条件'
