from django.db import models
from django.utils.translation import gettext_lazy as _
from utils.custom_fields import *


class AiPromptHistory(models.Model):
    company = models.ForeignKey(
        'ComCompany', models.DO_NOTHING, db_comment='com_companyテーブルへの外部キー')
    prompt = models.TextField(db_comment='AIへのプロンプト内容')
    result = models.TextField(blank=True, null=True, db_comment='AIからの応答結果')
    total_token = models.IntegerField(
        blank=True, null=True, db_comment='使用したトークン数合計')
    created_at = models.DateTimeField(db_comment='作成日時', auto_now_add=True)
    result_en = models.TextField(
        blank=True, null=True, db_comment='AIからの応答結果（英語）')
    result_vi = models.TextField(
        blank=True, null=True, db_comment='AIからの応答結果（ベトナム語）')
    result_ja = models.TextField(
        blank=True, null=True, db_comment='AIからの応答結果（日本語）')

    class Meta:
        managed = False
        db_table = 'ai_prompt_history'
        db_table_comment = '企業とエンジニアごとのAIプロンプト履歴を記録するテーブル'
