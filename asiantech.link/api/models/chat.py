from django.db import models
from django.utils.translation import gettext_lazy as _
from utils.custom_fields import *


class RecChat(models.Model):
    chat_id = models.AutoField(primary_key=True, db_comment='チャットID')
    group = models.ForeignKey(
        'RecChatGroup', models.CASCADE, blank=True, null=True, db_comment='グループID')
    user = models.ForeignKey('User', models.CASCADE,
                             blank=True, null=True, db_comment='送信ユーザーID')
    text = models.CharField(max_length=1000, blank=True,
                            null=True, db_comment='メッセージ内容')
    send = models.DateTimeField(blank=True, null=True, db_comment='メッセージ投稿日時')
    created = models.DateTimeField(
        blank=True, null=True, db_comment='初期登録日時', auto_now_add=True)

    class Meta:
        managed = False
        db_table = 'rec_chat'
        db_table_comment = 'チャット'


class RecChatGroup(models.Model):
    group_id = models.AutoField(primary_key=True, db_comment='グループID')
    created = models.DateTimeField(
        blank=True, null=True, db_comment='初期登録日時', auto_now_add=True)

    class Meta:
        managed = False
        db_table = 'rec_chat_group'
        db_table_comment = 'チャットグループ'


class MapChatGroup(models.Model):
    id = models.IntegerField(primary_key=True)
    group = models.ForeignKey(
        'RecChatGroup', models.DO_NOTHING, db_comment='グループID')
    user = models.ForeignKey('User', models.DO_NOTHING, db_comment='ユーザーID')
    chat = models.ForeignKey('RecChat', models.DO_NOTHING,
                             blank=True, null=True, db_comment='既読チャットID')
    updated = models.DateTimeField(blank=True, null=True, db_comment='既読日時')
    created = models.DateTimeField(
        blank=True, null=True, db_comment='初期登録日時', auto_now_add=True)

    class Meta:
        managed = False
        db_table = 'map_chat_group'
        unique_together = (('group', 'user'),)
        db_table_comment = '紐づけチャットグループ'
