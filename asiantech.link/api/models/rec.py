
from django.db import models
from django.utils.translation import gettext_lazy as _
from api.models.chat import RecChatGroup
from api.models.eng import MapEngAgc
from utils.custom_fields import *
from api.models.company import *


class RecRecruit(models.Model):
    recruit_id = models.AutoField(primary_key=True, db_comment='募集要項ID')
    host_company = models.ForeignKey(
        ComCompany, models.DO_NOTHING, db_comment='受入企業ID')
    host_agent = models.ForeignKey(
        'User', models.DO_NOTHING, db_comment='受入企業担当者ID')
    support_company = models.ForeignKey(
        ComCompany, models.DO_NOTHING, related_name='recrecruit_support_company_set', blank=True, null=True, db_comment='受入サポート機関ID')
    support_agent = models.ForeignKey(
        'User', models.DO_NOTHING, related_name='recrecruit_support_agent_set', blank=True, null=True, db_comment='受入サポート機関担当者ID')
    save_type = Bit3Field(null=True, db_comment='保存種別')  # 0:Draft 1:Saved
    title = TinyTextField(null=True, db_comment='タイトル')
    catch_copy = models.TextField(blank=True, null=True, db_comment='キャッチコピー')
    start_date = models.DateTimeField(null=True, db_comment='募集開始日')
    end_date = models.DateTimeField(null=True, db_comment='募集終了日')
    display_flag = Bit3Field(null=True, db_comment='表示フラグ')  # 0:Hide 1:Show
    job_code = models.CharField(
        max_length=3, blank=True, null=True, db_comment='募集職種コード')
    employ_code = models.CharField(
        max_length=3, null=True, db_comment='募集雇用形態コード')
    content = models.CharField(max_length=10000, null=True, db_comment='募集要項')
    # 2-character country code + "- (hyphen)" + 2- or 3-character prefecture code
    place_code1 = models.CharField(
        max_length=9, null=True, db_comment='就業勤務地コード1')
    # 2-character country code + "- (hyphen)" + 2- or 3-character prefecture code
    place_code2 = models.CharField(
        max_length=9, null=True, db_comment='就業勤務地コード2')
    # 2-character country code + "- (hyphen)" + 2- or 3-character prefecture code
    place_code3 = models.CharField(
        max_length=9, null=True, db_comment='就業勤務地コード3')
    payroll_code = models.CharField(
        max_length=3, null=True, db_comment='募集給与通貨コード')
    payroll_price_from = models.DecimalField(
        max_digits=20, decimal_places=3, blank=True, null=True, db_comment='募集給与金額(より)')
    payroll_price_to = models.DecimalField(
        max_digits=20, decimal_places=3, blank=True, null=True, db_comment='募集給与金額(まで)')
    country_code = models.CharField(
        max_length=3, null=True, db_comment='募集国籍コード')
    age_from = Bit7Field(null=True, db_comment='募集年齢(より)')
    age_to = Bit7Field(null=True, db_comment='募集年齢(まで）')
    sex_type = Bit4Field(null=True, db_comment='募集性別')
    pref_code1 = models.CharField(
        max_length=6, null=True, db_comment='募集国別県コード1')
    pref_code2 = models.CharField(
        max_length=6, null=True, db_comment='募集国別県コード2')
    pref_code3 = models.CharField(
        max_length=6, null=True, db_comment='募集国別県コード3')
    last_academic_code = models.CharField(
        max_length=3, null=True, db_comment='最終学歴コード(以上)')
    language_code1 = models.CharField(
        max_length=2, null=True, db_comment='言語コード1')
    # This field type is a guess.
    language_level_type1 = Bit4Field(null=True, db_comment='言語スキル種別1')
    language_code2 = models.CharField(
        max_length=2, null=True, db_comment='言語コード2')
    # This field type is a guess.
    language_level_type2 = Bit4Field(null=True, db_comment='言語スキル種別2')
    experienced_job_code = models.CharField(
        max_length=3, blank=True, null=True, db_comment='経験職種コード')
    years_of_experience = models.IntegerField(
        blank=True, null=True, db_comment='経験年数')
    skill_job_code1 = models.CharField(
        max_length=3, null=True, db_comment='職種コード1')
    skill_code1 = models.CharField(
        max_length=6, null=True, db_comment='スキルコード1')
    # This field type is a guess.
    skill_level_type1 = Bit4Field(null=True, db_comment='スキルレベル種別1')
    skill_job_code2 = models.CharField(
        max_length=3, null=True, db_comment='職種コード2')
    skill_code2 = models.CharField(
        max_length=6, null=True, db_comment='スキルコード2')
    # This field type is a guess.
    skill_level_type2 = Bit4Field(null=True, db_comment='スキルレベル種別2')
    skill_job_code3 = models.CharField(
        max_length=3, null=True, db_comment='職種コード3')
    skill_code3 = models.CharField(
        max_length=6, null=True, db_comment='スキルコード3')
    # This field type is a guess.
    skill_level_type3 = Bit4Field(null=True, db_comment='スキルレベル種別3')
    create_agent_id = models.IntegerField(null=True, db_comment='初期登録企業ID')
    created = DateTimeField(null=True, auto_now_add=True, db_comment='初期登録日時')
    update_agent_id = models.IntegerField(null=True, db_comment='最終更新企業ID')
    updated = DateTimeField(null=True, db_comment='最終更新日時')
    licence_code1 = models.CharField(
        max_length=3, blank=True, null=True, db_comment='資格コード1')
    licence_name1 = models.TextField(blank=True, null=True)
    # TOEIC, TOEFL, etc.
    licence_point1 = models.SmallIntegerField(
        blank=True, null=True, db_comment='資格点数1')
    licence_code2 = models.CharField(
        max_length=3, blank=True, null=True, db_comment='資格コード2')
    licence_name2 = models.TextField(blank=True, null=True)
    licence_point2 = models.SmallIntegerField(
        blank=True, null=True, db_comment='資格点数2')
    # TOEIC, TOEFL, etc.
    licence_code3 = models.CharField(
        max_length=3, blank=True, null=True, db_comment='資格コード3')
    licence_name3 = models.TextField(blank=True, null=True)
    licence_point3 = models.SmallIntegerField(
        blank=True, null=True, db_comment='資格点数3')
    recruit_image_path = models.CharField(
        max_length=200, blank=True, null=True, db_comment='募集要項写真ファイル名')
    remote_code = models.CharField(max_length=2, blank=True, null=True)
    payroll_price_from_usd = models.DecimalField(
        max_digits=20, decimal_places=3, blank=True, null=True)
    payroll_price_to_usd = models.DecimalField(
        max_digits=20, decimal_places=3, blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'rec_recruit'
        db_table_comment = '募集要項'


class RecApply(models.Model):
    apply_id = models.AutoField(primary_key=True, db_comment='応募ID')
    recruit = models.ForeignKey(
        'RecRecruit', models.CASCADE, blank=True, null=True, db_comment='募集要項ID')
    group = models.ForeignKey(
        'RecChatGroup', models.DO_NOTHING, blank=True, null=True, db_comment='グループID')
    engineer = models.ForeignKey('User', models.DO_NOTHING, db_comment='技術者ID')
    agency_company = models.ForeignKey(
        ComCompany, models.DO_NOTHING, blank=True, null=True, db_comment='技術者紹介機関ID')
    agency_agent = models.ForeignKey(
        'User', models.DO_NOTHING, related_name='recapply_agency_agent_set', blank=True, null=True, db_comment='技術者紹介機関担当者ID')
    host_company = models.ForeignKey(
        ComCompany, models.DO_NOTHING, related_name='recapply_host_company_set', blank=True, null=True, db_comment='受入企業ID')
    host_agent = models.ForeignKey(
        'User', models.DO_NOTHING, related_name='recapply_host_agent_set', blank=True, null=True, db_comment='受入企業担当者ID')
    support_company = models.ForeignKey(
        ComCompany, models.DO_NOTHING, related_name='recapply_support_company_set', blank=True, null=True, db_comment='受入サポート機関ID')
    support_agent = models.ForeignKey(
        'User', models.DO_NOTHING, related_name='recapply_support_agent_set', blank=True, null=True, db_comment='受入サポート機関担当者ID')
    recruit_progress_code = models.IntegerField(
        blank=True, null=True, db_comment='採用進捗状況コード')
    progress_update_datetime = models.DateTimeField(
        blank=True, null=True, db_comment='進捗変更日時')
    interview_datetime = models.DateTimeField(
        blank=True, null=True, db_comment='面接日時')
    joing_date = models.DateField(blank=True, null=True, db_comment='入社予定日')
    job_code = models.CharField(
        max_length=3, blank=True, null=True, db_comment='採用職種コード')
    employ_code = models.CharField(
        max_length=2, blank=True, null=True, db_comment='採用雇用形態コード')
    place_code = models.CharField(
        max_length=6, blank=True, null=True, db_comment='就業勤務地コード')
    payroll_code = models.CharField(
        max_length=3, blank=True, null=True, db_comment='採用給与通貨コード')
    payroll_price = models.DecimalField(
        max_digits=20, decimal_places=3, blank=True, null=True, db_comment='採用給与金額')
    benefits = models.CharField(
        max_length=10000, blank=True, null=True, db_comment='その他待遇')
    offer_contract_date = models.DateField(
        blank=True, null=True, db_comment='内定受理入社契約日')
    offer_pdf_path = models.CharField(
        max_length=75, blank=True, null=True, db_comment='内定受理ID+(登録日時+内定受理IDのHash文字列)')
    engineer_accept_sign = models.ForeignKey(
        'User', models.DO_NOTHING, related_name='recapply_engineer_accept_sign_set', blank=True, null=True)
    expiry_date = models.DateField(blank=True, null=True, db_comment='表示期限')
    is_from_support = Bit1Field(
        blank=True, null=True, db_comment='0: 受入企業\n1: 受入支援機関企業', default=0)
    created = models.DateTimeField(
        blank=True, null=True, db_comment='初期登録日時', auto_now_add=True)
    updated = models.DateTimeField(blank=True, null=True, db_comment='最終更新日時')

    class Meta:
        managed = False
        db_table = 'rec_apply'
        db_table_comment = '技術者の選考状況を管理するためのテーブル\n技術者・紹介機関・受入企業・受入支援機関といった採用に関わる関係者IDも保持する'


class RecApplyRead(models.Model):
    # The composite primary key (apply_id, user_id) found, that is not supported. The first column is selected.
    apply_id = models.AutoField(primary_key=True, db_comment='応募ID')
    user_id = models.IntegerField(db_comment='技術者/担当者ID')
    created = models.DateTimeField(blank=True, null=True, db_comment='初期登録日時')

    class Meta:
        managed = False
        db_table = 'rec_apply_read'
        unique_together = (('apply_id', 'user_id'),)
        db_table_comment = '応募未読'


class RecAcceptSign(models.Model):
    accept_sign_id = models.AutoField(primary_key=True, db_comment='承諾サインID')
    apply = models.ForeignKey(
        'RecApply', models.CASCADE, blank=True, null=True, db_comment='応募ID')
    user = models.ForeignKey('User', models.DO_NOTHING,
                             blank=True, null=True, db_comment='技術者/担当者ID')
    accept_sign_path = models.CharField(
        max_length=125, blank=True, null=True, db_comment='承諾サインファイル名')
    created = models.DateTimeField(
        blank=True, null=True, db_comment='初期登録日時', auto_now_add=True)

    class Meta:
        managed = False
        db_table = 'rec_accept_sign'
        unique_together = (('apply', 'user'),)
        db_table_comment = '署名時のサイン画像を管理するためのテーブル\nサイン画像はサーバに保持し、テーブルにはファイル名のみを登録する'


class RecFilter(models.Model):
    filter_id = models.AutoField(primary_key=True, db_comment='検索条件ID')
    host_agent = models.ForeignKey(
        'User', models.CASCADE, db_comment='受入企業担当者ID')
    filter_name = models.TextField(db_comment='検索フィルター名')
    age_from = models.IntegerField(
        blank=True, null=True, db_comment='募集年齢(より)')
    age_to = models.IntegerField(blank=True, null=True, db_comment='募集年齢(まで）')
    sex_type = models.CharField(
        max_length=1, blank=True, null=True, db_comment='募集性別')
    country_code = models.CharField(
        max_length=3, blank=True, null=True, db_comment='募集国籍コード')
    address_code1 = models.CharField(
        max_length=6, blank=True, null=True, db_comment='住所コード1')
    address_code2 = models.CharField(
        max_length=6, blank=True, null=True, db_comment='住所コード2')
    last_academic_code = models.CharField(
        max_length=3, blank=True, null=True, db_comment='最終学位コード')
    language_code1 = models.CharField(
        max_length=3, blank=True, null=True, db_comment='言語コード1')
    language_level_type1 = models.IntegerField(
        blank=True, null=True, db_comment='言語レベル種別1')
    language_code2 = models.CharField(
        max_length=3, blank=True, null=True, db_comment='言語コード2')
    language_level_type2 = models.IntegerField(
        blank=True, null=True, db_comment='言語レベル種別2')
    experienced_job_code1 = models.CharField(
        max_length=3, blank=True, null=True, db_comment='経験職種コード1')
    years_of_experience1 = models.IntegerField(
        blank=True, null=True, db_comment='経験年数1')
    experienced_job_code2 = models.CharField(
        max_length=3, blank=True, null=True, db_comment='経験職種コード2')
    years_of_experience2 = models.IntegerField(
        blank=True, null=True, db_comment='経験年数2')
    experienced_job_code3 = models.CharField(
        max_length=3, blank=True, null=True, db_comment='経験職種コード3')
    years_of_experience3 = models.IntegerField(
        blank=True, null=True, db_comment='経験年数3')
    skill_code1_1 = Bit6Field(
        blank=True, null=True, db_comment='スキルコード1')

    skill_level_type1_1 = Bit4Field(
        blank=True, null=True, db_comment='スキルレベル種別1')
    skill_code1_2 = Bit6Field(
        blank=True, null=True, db_comment='スキルコード2')
    skill_level_type1_2 = Bit4Field(
        blank=True, null=True, db_comment='スキルレベル種別2')
    skill_code1_3 = Bit6Field(
        blank=True, null=True, db_comment='スキルコード3')
    skill_level_type1_3 = Bit4Field(
        blank=True, null=True, db_comment='スキルレベル種別3')
    skill_code2_1 = Bit6Field(blank=True, null=True)
    skill_level_type2_1 = Bit4Field(blank=True, null=True)
    skill_code2_2 = Bit6Field(blank=True, null=True)
    skill_level_type2_2 = Bit4Field(blank=True, null=True)
    skill_code2_3 = Bit6Field(blank=True, null=True)
    skill_level_type2_3 = Bit4Field(blank=True, null=True)
    skill_code3_1 = Bit6Field(blank=True, null=True)
    skill_level_type3_1 = Bit4Field(blank=True, null=True)
    skill_code3_2 = Bit6Field(blank=True, null=True)
    skill_level_type3_2 = Bit4Field(blank=True, null=True)
    skill_code3_3 = Bit6Field(blank=True, null=True)

    skill_level_type3_3 = Bit4Field(blank=True, null=True)
    licence_code1 = models.CharField(
        max_length=3, blank=True, null=True, db_comment='資格コード1')
    licence_point1 = models.SmallIntegerField(
        blank=True, null=True, db_comment='資格点数1')
    licence_code2 = models.CharField(
        max_length=3, blank=True, null=True, db_comment='資格コード2')
    licence_point2 = models.SmallIntegerField(
        blank=True, null=True, db_comment='資格点数2')
    licence_code3 = models.CharField(
        max_length=3, blank=True, null=True, db_comment='資格コード3')
    licence_point3 = models.SmallIntegerField(
        blank=True, null=True, db_comment='資格点数3')
    recruiting_job_code = models.CharField(
        max_length=3, blank=True, null=True, db_comment='募集職種コード')
    recruiting_employ_code = models.CharField(
        max_length=2, blank=True, null=True, db_comment='募集雇用形態コード')
    work_place_code1 = models.CharField(
        max_length=6, blank=True, null=True, db_comment='勤務地コード1')
    work_place_code2 = models.CharField(
        max_length=6, blank=True, null=True, db_comment='勤務地コード2')
    work_place_code3 = models.CharField(
        max_length=6, blank=True, null=True, db_comment='勤務地コード3')
    payroll_code = models.CharField(
        max_length=3, blank=True, null=True, db_comment='給与通貨コード')
    payroll_price_from = models.DecimalField(
        max_digits=20, decimal_places=3, blank=True, null=True, db_comment='給与金額下限')
    payroll_price_to = models.DecimalField(
        max_digits=20, decimal_places=3, blank=True, null=True, db_comment='給与金額上限')
    agent_fee = models.DecimalField(
        max_digits=10, decimal_places=3, blank=True, null=True, db_comment='人材紹介手数料')
    agent_fee_curr_code = models.CharField(
        max_length=3, blank=True, null=True, db_comment='人材紹介手数料通貨コード')
    created = models.DateTimeField(
        blank=True, null=True, db_comment='初期登録日時', auto_now_add=True)

    class Meta:
        managed = False
        db_table = 'rec_filter'
        db_table_comment = '検索条件'


class RecInterestedEngineer(models.Model):
    host_company = models.ForeignKey(
        ComCompany, models.DO_NOTHING, db_comment='受入企業ID')
    engineer = models.ForeignKey('User', models.DO_NOTHING, db_comment='技術者ID')
    interested_flag = models.CharField(
        max_length=1, blank=True, null=True, db_comment='興味ありフラグ')
    created = models.DateTimeField(blank=True, null=True, db_comment='初期登録日時')
    updated = models.DateTimeField(blank=True, null=True, db_comment='最終更新日時')

    class Meta:
        managed = False
        db_table = 'rec_interested_engineer'
        unique_together = (('host_company', 'engineer'),)
        db_table_comment = '企業がスカウトを検討している技術者を管理するテーブル'


class RecNotify(models.Model):
    rec_id = models.AutoField(primary_key=True)
    type = models.IntegerField(blank=True, null=True)
    message = models.TextField(blank=True, null=True)
    user_id = models.IntegerField(blank=True, null=True)
    read_at = models.DateTimeField(blank=True, null=True)
    payload = models.CharField(max_length=255, blank=True, null=True)
    created = models.DateTimeField(blank=True, null=True, db_comment='通知')
    updated = models.DateTimeField(blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'rec_notify'
