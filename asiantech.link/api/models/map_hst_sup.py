from django.db import models
from django.utils.translation import gettext_lazy as _
from utils.custom_fields import *
from api.models.company import *


class MapHstSup(models.Model):
    id = models.IntegerField(primary_key=True)
    host_company = models.ForeignKey(
        ComCompany, models.CASCADE, db_comment='受入企業ID')
    support_company = models.ForeignKey(
        ComCompany, models.CASCADE, related_name='maphstsup_support_company_set', db_comment='受入サポート機関ID')
    support_agent = models.ForeignKey(
        'User', models.CASCADE, blank=True, null=True, db_comment='受入サポート機関担当者ID')
    com_trading_id = models.IntegerField(
        blank=True, null=True, db_comment='受入サポート機関取引条件ID')
    introduction_pr = models.CharField(
        max_length=10000, blank=True, null=True, db_comment='紹介PR')
    introduction_start_datetime = models.DateTimeField(
        blank=True, null=True, db_comment='紹介開始日時')
    deleted = Bit1Field(blank=True, null=True, db_comment='削除フラグ')
    create_agent = models.ForeignKey(
        'User', models.CASCADE, related_name='maphstsup_create_agent_set', blank=True, null=True, db_comment='初期登録担当者ID')
    created = models.DateTimeField(
        blank=True, null=True, db_comment='初期登録日時', auto_now_add=True)
    update_agent = models.ForeignKey(
        'User', models.CASCADE, related_name='maphstsup_update_agent_set', blank=True, null=True, db_comment='最終更新担当者ID')
    updated = models.DateTimeField(blank=True, null=True, db_comment='最終更新日時')

    class Meta:
        managed = False
        db_table = 'map_hst_sup'
        db_table_comment = '紐づけ受入企業サポート機関'
