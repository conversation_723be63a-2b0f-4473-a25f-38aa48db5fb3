
from django.db import models

from api.models.rec import RecRecruit


class Rec<PERSON>aitingCompany(models.Model):
    engineer = models.ForeignKey('User', models.DO_NOTHING, db_comment='技術者ID')
    recruit = models.ForeignKey(
        RecRecruit, models.DO_NOTHING, db_comment='募集要項ID', related_name='waiting_company_set')
    waiting_flag = models.CharField(
        max_length=1, blank=True, null=True, db_comment='検討中フラグ')
    created = models.DateTimeField(blank=True, null=True, db_comment='初期登録日時')
    updated = models.DateTimeField(blank=True, null=True, db_comment='最終更新日時')

    class Meta:
        managed = False
        db_table = 'rec_waiting_company'
        db_table_comment = '検討中企業'
