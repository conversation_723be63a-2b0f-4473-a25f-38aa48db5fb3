import hashlib
from django.utils import timezone
from django.db import models
from django.contrib.auth.models import AbstractUser
from django.contrib.auth.base_user import BaseUserManager
from django.utils.translation import gettext_lazy as _
from utils.custom_fields import *
from utils.decorators import log_exceptions
from argon2 import PasswordHasher
from django.utils import timezone as tz


class CustomUserManager(BaseUserManager):

    def create_user(self, email, password, **extra_fields):
        if not email:
            raise ValueError(_("Email should be provided"))

        email = self.normalize_email(email)
        new_user = self.model(email=email, **extra_fields)
        new_user.set_password(password)
        new_user.deleted = 0
        new_user.created = timezone.now()
        new_user.save()

        return new_user

    def create_superuser(self, email, password, **extra_fields):
        extra_fields.setdefault('is_staff', True)
        extra_fields.setdefault('is_superuser', True)
        extra_fields.setdefault('is_active', True)

        extra_fields.setdefault('user_type', 0)
        extra_fields.setdefault('auth_type', 4)
        extra_fields.setdefault('job_status', 0)

        if extra_fields.get('is_staff') is not True:
            raise ValueError(_("Superuser must have is_staff as True"))

        if extra_fields.get('is_superuser') is not True:
            raise ValueError(_("Superuser must have is_superuser as True"))

        if extra_fields.get('is_active') is not True:
            raise ValueError(_("Superuser must have is_active as True"))

        return self.create_user(email, password, **extra_fields)


class User(AbstractUser):

    USERNAME_FIELD = 'email'
    REQUIRED_FIELDS = []

    objects = CustomUserManager()

    # Argon2 password hasher
    password_hasher = PasswordHasher()

    def set_password(self, raw_password):
        self.password = self.password_hasher.hash(raw_password)

    def check_password(self, raw_password):
        try:
            return self.password_hasher.verify(self.password, raw_password)
        except Exception:
            return False

    user_id = models.AutoField(primary_key=True, db_comment="ユーザーID")
    # 0: Engineer 1: Referral agency staff 2: Host company staff 3: Host support agency staff 4 Other support agency staff 5: Management account
    user_type = Bit3Field(db_comment="ユーザーID種別")
    company_id = models.IntegerField(
        null=True, db_comment="企業ID", default=None)
    email = models.EmailField(
        max_length=100, db_comment='メールアドレス', unique=True)
    password = models.CharField(max_length=128, db_comment='パスワード')
    # 0: Authentication in progress 1: Authenticated (application in progress)
    auth_type = Bit3Field(db_comment='メール認証種別')
    last_name = models.CharField(
        max_length=30, null=True, db_comment='姓', default=None)
    first_name = models.CharField(
        max_length=30, null=True, db_comment='名', default=None)
    nickname = models.CharField(max_length=30, blank=True, null=True)
    display_code = models.CharField(
        # ISO 639-1, updated on login
        max_length=2, null=True, db_comment='表示言語コード', default=None)
    country_code = models.CharField(
        max_length=9, null=True, db_comment='国籍コード', default=None)  # ISO 3166-1 Numeric
    birth_date = models.DateField(
        null=True, db_comment='生年月日', default=None)  # YYYY-MM-DD
    # 0: Unknown 1: Male 2: Female 3: Other
    sex_type = Bit4Field(null=True, db_comment='性別種別', default=None)
    # 3-digit country code + 3-digit prefecture code + 3-digit city code
    address_code = models.CharField(
        max_length=9, null=True, db_comment='住所国別県市コード', default=None)

    international_tel = models.CharField(max_length=3, blank=True, null=True)
    tel = models.CharField(max_length=20, null=True,
                           db_comment='電話番号', default=None)  # Numbers only
    passport_number = models.CharField(
        max_length=9, null=True, db_comment='パスポート（ID）番号', default=None)
    # Technician ID + (registration date and time + hash string of technician ID)
    passport_image_path = models.CharField(
        max_length=200, null=True, db_comment='パスポート（ID）写真ファイル名', default=None)
    profile_image_path = models.CharField(
        max_length=200, null=True, db_comment='プロフィール写真ファイル名', default=None)
    last_academic_code = models.CharField(
        max_length=3, null=True, db_comment='最終学位コード', default=None)
    pr = models.CharField(max_length=10000, null=True,
                          db_comment='PR', default=None)
    self_introduction_url = TinyTextField(
        null=True, db_comment='自己紹介URL', default=None)
    facebook_url = models.TextField(
        blank=True, null=True, db_comment='FacebookURL')
    linkedin_url = models.TextField(
        blank=True, null=True, db_comment='LinkedInURL')
    whatsapp_url = models.TextField(
        blank=True, null=True, db_comment='WhatsAppURL')
    facebook_id = models.CharField(max_length=50, blank=True, null=True)
    linkedin_id = models.CharField(max_length=50, blank=True, null=True)
    whatsapp_id = models.CharField(max_length=50, blank=True, null=True)
    zalo_id = models.CharField(max_length=50, blank=True, null=True)
    facebook_url = TinyTextField(
        null=True, db_comment='FacebookURL', default=None)
    linkedin_url = TinyTextField(
        null=True, db_comment='LinkedInURL', default=None)
    # 0: Preparing 1: Looking for work 2: Received a job offer 3: Currently employed
    job_status = Bit4Field(db_comment='JOBステータス')
    job_status_update = DateTimeField(
        null=True, db_comment='JOBステータス更新日', default=None)
    # 0: Normal 1: Cancelled
    deleted = Bit2Field(db_comment='退会フラグ', default=0)
    created = DateTimeField(db_comment='初期登録日時', default=tz.now)
    updated = DateTimeField(null=True, db_comment='最終更新日時', default=tz.now)
    city_name = models.CharField(
        max_length=50, null=True, db_comment='市区町村名', default=None)
    analysis_results_id = models.IntegerField(
        blank=True, null=True, db_comment='適正検査ID')
    email_temp = models.CharField(max_length=100, blank=True, null=True)
    is_import = Bit4Field(null=True, db_comment='インポートフラグ', default=0)
    created_user = models.CharField(
        max_length=60, blank=True, null=True, db_comment='初期登録ユーザー')
    updated_user = models.CharField(
        max_length=60, blank=True, null=True, db_comment='最終更新ユーザー')
    is_data_policy_accept = Bit2Field()
    sent_agree_mail_date = models.DateTimeField(blank=True, null=True)
    data_policy_accept_date = models.DateTimeField(blank=True, null=True)
    # This field type is a guess.
    employment_status = Bit4Field()
    sales_memo = models.TextField(blank=True, null=True)
    last_login = models.DateTimeField(blank=True, null=True)
    skills_for_cv_display = models.TextField(blank=True, null=True)
    professional_summary = models.TextField(blank=True, null=True)

    class Meta:
        db_table = 'user'
        db_table_comment = 'ユーザー'

        def __str__(self):
            return f"<User {self.username}"
