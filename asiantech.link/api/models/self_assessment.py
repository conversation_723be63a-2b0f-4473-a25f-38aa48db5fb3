from django.db import models


class SelfAssessmentSheet(models.Model):
    text = models.CharField(max_length=255)
    text_en = models.CharField(max_length=255)
    text_vi = models.CharField(max_length=255)


class EvaluationMethod(models.Model):
    sheet = models.ForeignKey(SelfAssessmentSheet,
                              related_name='evaluation_methods', on_delete=models.CASCADE)
    text = models.TextField()
    text_en = models.TextField()
    text_vi = models.TextField()


class SelfAssessmentData(models.Model):
    text = models.TextField()
    text_en = models.TextField()
    text_vi = models.TextField()


class Question(models.Model):
    # sheet = models.ForeignKey(CommunicationSkillsSelfAssessmentSheet,
    #                           related_name='questions', on_delete=models.CASCADE)
    text = models.TextField()
    text_en = models.TextField()
    text_vi = models.TextField()
    is_expanded = models.BooleanField(default=False)


class Questions(models.Model):
    score = models.IntegerField()


class Option(models.Model):
    question = models.ForeignKey(
        Question, related_name='options', on_delete=models.CASCADE)
    uuid = models.UUIDField()
    text = models.TextField()
    text_en = models.TextField()
    text_vi = models.TextField()
    is_selected = models.BooleanField(default=False)


class Description(models.Model):
    text = models.TextField()
    text_en = models.TextField()
    text_vi = models.TextField()
