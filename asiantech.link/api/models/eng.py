from typing import Iterable
from django.db import models
from django.utils.translation import gettext_lazy as _
from api.models.company import ComCompany
from utils.custom_fields import *


class EngAcademic(models.Model):
    academic_id = models.AutoField(primary_key=True, db_comment='学歴ID')
    engineer = models.ForeignKey(
        'User', models.CASCADE, blank=True, null=True, db_comment='技術者ID')
    # 0: Currently enrolled 1: Graduated 2: Dropped out
    type = models.SmallIntegerField(
        null=True, db_comment='卒業中退種別', default=None)
    country_code = models.CharField(
        max_length=3, null=True, db_comment='国コード', default=None)
    school = TinyTextField(null=True, db_comment='学校名', default=None)
    faculty = TinyTextField(null=True, db_comment='学部', default=None)
    in_date = models.DateField(null=True, db_comment='入学日', default=None)
    out_date = models.DateField(null=True, db_comment='卒業/中退日', default=None)
    created = DateTimeField(null=True, auto_now_add=True, db_comment='初期登録日時')
    updated = DateTimeField(null=True, db_comment='最終更新日時', default=None)
    created_user = models.CharField(max_length=60, blank=True, null=True)
    updated_user = models.CharField(max_length=60, blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'eng_academic'
        db_table_comment = '学歴'

        def __str__(self):
            return self.academic_id

# ===============================================


class EngHope(models.Model):
    engineer = models.OneToOneField(
        'User', models.CASCADE, primary_key=True, db_comment='技術者ID', related_name='enghope_set')
    save_type = Bit3Field(blank=True, null=True, db_comment='保存種別')
    employ_code = models.CharField(
        max_length=2, blank=True, null=True, db_comment='希望雇用形態コード')
    place_code1 = models.CharField(
        max_length=9, blank=True, null=True, db_comment='希望勤務地コード1')
    place_code2 = models.CharField(
        max_length=9, blank=True, null=True, db_comment='希望勤務地コード2')
    place_code3 = models.CharField(
        max_length=9, blank=True, null=True, db_comment='希望勤務地コード3')
    payroll_code = models.CharField(
        max_length=3, blank=True, null=True, db_comment='希望給与通貨コード')
    payroll_price = models.DecimalField(
        max_digits=20, decimal_places=3, blank=True, null=True, db_comment='希望給与金額')
    remote_code = models.CharField(max_length=2, blank=True, null=True)
    created = models.DateTimeField(blank=True, null=True, db_comment='初期登録日時')
    created_user = models.CharField(max_length=60, blank=True, null=True)
    updated = models.DateTimeField(blank=True, null=True, db_comment='最終更新日時')
    updated_user = models.CharField(max_length=60, blank=True, null=True)
    payroll_price_usd = models.DecimalField(
        max_digits=20, decimal_places=3, blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'eng_hope'
        db_table_comment = 'エンジニアの希望条件を管理するテーブル'
# ===============================================


class EngLanguage(models.Model):
    language_id = models.AutoField(primary_key=True, db_comment='言語ID')
    engineer = models.ForeignKey(
        'User', models.CASCADE, blank=True, null=True, db_comment='技術者ID')
    language_code = models.CharField(
        max_length=3, null=True, db_comment='言語コード')  # ISO 639-1
    # World Standard CEFR: 1:A1, 2:A2, 3:B1, 4:B2, 5:C1, 6:C2
    language_level_type = Bit5Field(null=True, db_comment='言語レベル種別')
    created = DateTimeField(null=True, auto_now_add=True, db_comment='初期登録日時')
    created_user = models.CharField(max_length=60, blank=True, null=True)
    updated = DateTimeField(null=True, db_comment='最終更新日時')
    updated_user = models.CharField(max_length=60, blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'eng_language'
        db_table_comment = '言語'

        def __str__(self):
            return self.language_id

# ===============================================


class EngCareer(models.Model):
    career_id = models.AutoField(primary_key=True, db_comment='職務経歴ID')
    engineer = models.ForeignKey(
        'User', models.DO_NOTHING, blank=True, null=True, db_comment='技術者ID')
    job_code = models.CharField(max_length=3, blank=True, null=True)
    role_name = models.CharField(
        max_length=1000, blank=True, null=True, db_comment='役職名')
    career_type = Bit2Field(blank=True, null=True, db_comment='在職種別')
    company_name = models.TextField(blank=True, null=True, db_comment='会社名')
    job_description = models.TextField(blank=True, null=True)
    entering_date = models.DateField(blank=True, null=True, db_comment='入社日')
    quitting_date = models.DateField(blank=True, null=True, db_comment='退社日')
    created = models.DateTimeField(
        blank=True, null=True, db_comment='初期登録日時', auto_now_add=True)
    created_user = models.CharField(max_length=60, blank=True, null=True)
    updated = models.DateTimeField(blank=True, null=True, db_comment='最終更新日時')
    updated_user = models.CharField(max_length=60, blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'eng_career'
        db_table_comment = 'エンジニアの職務経歴を管理するテーブル'


# ===============================================


class EngLicence(models.Model):
    licence_id = models.AutoField(primary_key=True, db_comment='資格ID')
    engineer = models.ForeignKey(
        'User', models.CASCADE, blank=True, null=True, db_comment='技術者ID')
    job_code = models.CharField(max_length=3, null=True, db_comment='職種コード')
    licence_code = models.CharField(
        max_length=3, null=True, db_comment='資格コード')
    licence_point = models.DecimalField(
        max_digits=6, decimal_places=2, blank=True, null=True, db_comment='資格点数   TOEIC, TOEFLなど')
    licence_name = models.TextField(blank=True, null=True)
    get_date = models.CharField(
        max_length=8, blank=True, null=True, db_comment='取得年月日')
    created = DateTimeField(null=True, auto_now_add=True, db_comment='初期登録日時')
    created_user = models.CharField(max_length=60, blank=True, null=True)
    updated = DateTimeField(null=True, db_comment='最終更新日時')
    updated_user = models.CharField(max_length=60, blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'eng_licence'
        db_table_comment = '資格'

# ===============================================


class EngTrading(models.Model):
    eng_trading_id = models.AutoField(primary_key=True, db_comment='取引条件ID')
    created = DateTimeField(null=True, auto_now_add=True, db_comment='初期登録日時')
    updated = DateTimeField(null=True, db_comment='最終更新日時')

    class Meta:
        managed = False
        db_table = 'eng_trading'
        db_table_comment = '技術者紹介取引条件'


class HopeJobSkill(models.Model):
    engineer = models.ForeignKey('User', models.CASCADE, db_comment='技術者ID')
    job_code = models.CharField(
        max_length=3, blank=True, null=True, db_comment='職種コード')
    skill_code = models.CharField(
        max_length=6, blank=True, null=True, db_comment='スキルコード')
    created = models.DateTimeField(blank=True, null=True, db_comment='初期登録日時')
    created_user = models.CharField(max_length=60, blank=True, null=True)
    updated = models.DateTimeField(blank=True, null=True, db_comment='最終更新日時')
    updated_user = models.CharField(max_length=60, blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'hope_job_skill'
        db_table_comment = 'エンジニアの希望職種とスキルを管理するテーブル'


class CareerJobSkill(models.Model):
    career = models.ForeignKey(
        'EngCareer', models.CASCADE, db_comment='職務経歴ID')
    engineer = models.ForeignKey('User', models.CASCADE, db_comment='技術者ID')
    job_code = models.CharField(
        max_length=3, blank=True, null=True, db_comment='職種コード')
    skill_code = models.CharField(
        max_length=6, blank=True, null=True, db_comment='スキルコード')
    years_of_experience = Bit3Field(
        blank=True, null=True, db_comment='経験年数')
    created = models.DateTimeField(
        blank=True, null=True, db_comment='初期登録日時', auto_now_add=True)
    updated = models.DateTimeField(blank=True, null=True, db_comment='最終更新日時')

    class Meta:
        managed = False
        db_table = 'career_job_skill'
        db_table_comment = 'エンジニアの職種とスキルの経歴を管理するテーブル'


class EngSkill(models.Model):
    skill_id = models.AutoField(primary_key=True, db_comment='職務保有スキルID')
    engineer = models.ForeignKey(
        'User', models.CASCADE, blank=True, null=True, db_comment='技術者ID')
    job_code = models.CharField(
        max_length=3, blank=True, null=True, db_comment='職種コード')
    skill_code = models.CharField(
        max_length=6, blank=True, null=True, db_comment='スキルコード')
    level_type = Bit4Field(blank=True, null=True, db_comment='スキルレベル種別')
    temp_name = models.CharField(max_length=255, blank=True, null=True)
    temp_category_id = models.IntegerField(blank=True, null=True)
    created = models.DateTimeField(
        blank=True, null=True, db_comment='初期登録日時', auto_now_add=True)
    updated = models.DateTimeField(blank=True, null=True, db_comment='最終更新日時')

    class Meta:
        managed = False
        db_table = 'eng_skill'
        db_table_comment = 'スキル'


class MapEngAgc(models.Model):
    map_id = models.AutoField(primary_key=True)
    engineer = models.ForeignKey('User', models.DO_NOTHING, db_comment='技術者ID')
    agency_company = models.ForeignKey(
        ComCompany, models.DO_NOTHING, db_comment='技術者紹介機関ID')
    agency_agent = models.ForeignKey('User', models.DO_NOTHING, related_name='mapengagc_agency_agent_set',
                                     blank=True, null=True, db_comment='技術者紹介機関担当者ID \n担当者がいる場合')
    introduction_pr = models.CharField(
        max_length=10000, blank=True, null=True, db_comment='紹介PR')
    deleted = Bit1Field(blank=True, null=True, db_comment='削除フラグ')
    create_user = models.ForeignKey(
        'User', models.DO_NOTHING, related_name='mapengagc_create_user_set', blank=True, null=True, db_comment='初期登録ユーザーID')
    create_datetime = models.DateTimeField(
        blank=True, null=True, db_comment='初期登録日時', auto_now_add=True)
    update_user = models.ForeignKey(
        'User', models.DO_NOTHING, related_name='mapengagc_update_user_set', blank=True, null=True, db_comment='最終更新ユーザーID')
    update_datetime = models.DateTimeField(
        blank=True, null=True, db_comment='最終更新日時')

    class Meta:
        managed = False
        db_table = 'map_eng_agc'
        db_table_comment = '紹介機関と、紹介機関が管理するエンジニアとを紐付けするテーブル'


class EngAnalysisResults(models.Model):
    analysis_results_id = models.AutoField(
        primary_key=True, db_comment='分析結果ID')
    engineer_id = models.IntegerField(
        blank=True, null=True, db_comment='技術者ID')
    cubic_personal_code = models.CharField(
        max_length=10, blank=True, null=True, db_comment='CUBIC個人コード')
    confidence_coefficient = models.IntegerField(
        blank=True, null=True, db_comment='信頼係数')
    introjective_score = models.IntegerField(
        blank=True, null=True, db_comment='内閉性')
    objectivity_score = models.IntegerField(
        blank=True, null=True, db_comment='客観性')
    corporeality_score = models.IntegerField(
        blank=True, null=True, db_comment='身体性')
    moodiness_score = models.IntegerField(
        blank=True, null=True, db_comment='気分性')
    durability_score = models.IntegerField(
        blank=True, null=True, db_comment='持続性')
    regularity_score = models.IntegerField(
        blank=True, null=True, db_comment='規則性')
    competitiveness_score = models.IntegerField(
        blank=True, null=True, db_comment='競争性')
    # Field renamed to remove unsuitable characters.
    self_esteem_score = models.IntegerField(
        db_column='self-esteem_score', blank=True, null=True, db_comment='自尊心')
    prudence_score = models.IntegerField(
        blank=True, null=True, db_comment='慎重性')
    weakness_score = models.IntegerField(
        blank=True, null=True, db_comment='弱気さ')
    peripheral_score = models.IntegerField(
        blank=True, null=True, db_comment='日常周辺事型')
    scientific_score = models.IntegerField(
        blank=True, null=True, db_comment='客観・科学型')
    economic_score = models.IntegerField(
        blank=True, null=True, db_comment='社会・経済型')
    emotional_score = models.IntegerField(
        blank=True, null=True, db_comment='心理・情緒型')
    artistic_score = models.IntegerField(
        blank=True, null=True, db_comment='審美・芸術型')
    proactive_score = models.IntegerField(
        blank=True, null=True, db_comment='積極性')
    cooperative_score = models.IntegerField(
        blank=True, null=True, db_comment='協調性')
    self_responsibility_score = models.IntegerField(
        blank=True, null=True, db_comment='自己認識責任感')
    # Field renamed to remove unsuitable characters.
    self_reliability_score = models.IntegerField(
        db_column='self-reliability_score', blank=True, null=True, db_comment='自己信頼性')
    coaching_score = models.IntegerField(
        blank=True, null=True, db_comment='指導性')
    empathy_score = models.IntegerField(
        blank=True, null=True, db_comment='共感性')
    emotional_stability_score = models.IntegerField(
        blank=True, null=True, db_comment='感情安定性')
    submissiveness_score = models.IntegerField(
        blank=True, null=True, db_comment='従順性')
    autonomy_score = models.IntegerField(
        blank=True, null=True, db_comment='自主性')
    moratorium_tendency_score = models.IntegerField(
        blank=True, null=True, db_comment='ﾓﾗﾄﾘｱﾑ傾向')
    desire_achievement_score = models.IntegerField(
        blank=True, null=True, db_comment='達成欲求')
    desire_affinity_score = models.IntegerField(
        blank=True, null=True, db_comment='親和欲求')
    desire_seeking_score = models.IntegerField(
        blank=True, null=True, db_comment='求知欲求')
    desire_show_off_score = models.IntegerField(
        blank=True, null=True, db_comment='顕示欲求')
    desire_order_score = models.IntegerField(
        blank=True, null=True, db_comment='秩序欲求')
    desire_material_things_score = models.IntegerField(
        blank=True, null=True, db_comment='物質的欲望')
    crisis_tolerance_score = models.IntegerField(
        blank=True, null=True, db_comment='危機耐性')
    desire_autonomy_score = models.IntegerField(
        blank=True, null=True, db_comment='自律欲求')
    desire_control_score = models.IntegerField(
        blank=True, null=True, db_comment='支配欲求')
    desire_work_score = models.IntegerField(
        blank=True, null=True, db_comment='勤労意欲')
    positive_action_score = models.IntegerField(
        blank=True, null=True, db_comment='積極実行')
    motivation_score = models.IntegerField(
        blank=True, null=True, db_comment='意欲熱意')
    perseverance_score = models.IntegerField(
        blank=True, null=True, db_comment='根気強さ')
    responsibility_score = models.IntegerField(
        blank=True, null=True, db_comment='責任感')
    decisions_score = models.IntegerField(
        blank=True, null=True, db_comment='決断勇気')
    instructiong_score = models.IntegerField(
        blank=True, null=True, db_comment='指導力')
    leader_score = models.IntegerField(
        blank=True, null=True, db_comment='リーダー')
    # Field renamed to remove unsuitable characters.
    self_reliance_score = models.IntegerField(
        db_column='self-reliance_score', blank=True, null=True, db_comment='自己信頼')
    # Field name made lowercase.
    coordination_score = models.IntegerField(
        db_column='Coordination_score', blank=True, null=True, db_comment='調整力')
    negotiation_score = models.IntegerField(
        blank=True, null=True, db_comment='折衝力')
    innovation_score = models.IntegerField(
        blank=True, null=True, db_comment='独創斬新')
    analysis_situation_score = models.IntegerField(
        blank=True, null=True, db_comment='現状分析')
    insight_score = models.IntegerField(
        blank=True, null=True, db_comment='洞察力')
    planning_score = models.IntegerField(
        blank=True, null=True, db_comment='企画立案')
    expertise_score = models.IntegerField(
        blank=True, null=True, db_comment='専門知識')
    information_utilization_score = models.IntegerField(
        blank=True, null=True, db_comment='情報活用')
    stability_score = models.IntegerField(
        blank=True, null=True, db_comment='定着・安定')
    judgment_results = models.CharField(
        max_length=1, blank=True, null=True, db_comment='判定結果')
    personality_sketch = models.TextField(
        blank=True, null=True, db_comment='ﾊﾟｰｿﾅﾘﾃｨｽｹｯﾁ')
    general_score = models.IntegerField(
        blank=True, null=True, db_comment='一般的')
    mental_score = models.IntegerField(blank=True, null=True, db_comment='精神力')
    massive_score = models.IntegerField(
        blank=True, null=True, db_comment='足\u3000腰')
    concentration_score = models.IntegerField(
        blank=True, null=True, db_comment='集中力')
    standardization_score = models.IntegerField(
        blank=True, null=True, db_comment='標準化')
    created = models.DateTimeField(
        blank=True, null=True, db_comment='初期登録日時', auto_now_add=True)
    updated = models.DateTimeField(blank=True, null=True, db_comment='最終更新日時')

    class Meta:
        managed = False
        db_table = 'eng_analysis_results'
        db_table_comment = '個人特性分析結果'


class EngSelfAssesment(models.Model):
    self_assessment_id = models.AutoField(
        primary_key=True, db_comment='分析結果ID')
    engineer = models.ForeignKey('User', models.CASCADE, db_comment='技術者ID')
    remote_exp_years = Bit7Field(
        blank=True, null=True, db_comment='リモートワーク経験年数')
    remote_job_description = models.CharField(
        max_length=256, blank=True, null=True, db_comment='リモートワーク業務内容')
    remote_skill_1 = Bit4Field(
        blank=True, null=True, db_comment='リモートワークスキル評価1')
    remote_skill_2 = Bit4Field(
        blank=True, null=True, db_comment='リモートワークスキル評価2')
    remote_skill_3 = Bit4Field(
        blank=True, null=True, db_comment='リモートワークスキル評価3')
    global_work_exp = Bit2Field(
        blank=True, null=True, db_comment='海外での業務経験')
    global_skill_1 = Bit4Field(
        blank=True, null=True, db_comment='グローバルスキル評価1')
    global_skill_2 = Bit4Field(
        blank=True, null=True, db_comment='グローバルスキル評価2')
    global_skill_3 = Bit4Field(
        blank=True, null=True, db_comment='グローバルスキル評価3')

    social_style = Bit4Field(
        blank=True, null=True, db_comment='ソーシャルスタイル')
    communication_skill_1 = Bit4Field(
        blank=True, null=True, db_comment='コミュニケーションスキル評価1')
    communication_skill_2 = Bit4Field(
        blank=True, null=True, db_comment='コミュニケーションスキル評価2')
    communication_skill_3 = Bit4Field(
        blank=True, null=True, db_comment='コミュニケーションスキル評価3')

    report_skill_1 = Bit4Field(
        blank=True, null=True, db_comment='報連相スキル評価1')
    report_skill_2 = Bit4Field(
        blank=True, null=True, db_comment='報連相スキル評価2')
    report_skill_3 = Bit4Field(
        blank=True, null=True, db_comment='報連相スキル評価3')

    management_skill_1 = Bit4Field(
        blank=True, null=True, db_comment='プロジェクト管理スキル評価1')
    management_skill_2 = Bit4Field(
        blank=True, null=True, db_comment='プロジェクト管理スキル評価2')

    management_skill_3 = Bit4Field(
        blank=True, null=True, db_comment='プロジェクト管理スキル評価3')

    durability_score = Bit11Field(
        blank=True, null=True, db_comment='フィードバック評価')
    created = models.DateTimeField(
        blank=True, null=True, db_comment='初期登録日時', auto_now_add=True)
    created_user = models.CharField(
        max_length=60, blank=True, null=True, db_comment='初期登録ユーザー')
    updated = models.DateTimeField(blank=True, null=True, db_comment='最終更新日時')
    updated_user = models.CharField(
        max_length=60, blank=True, null=True, db_comment='最終更新ユーザー')

    class Meta:
        managed = False
        db_table = 'eng_self_assesment'
        db_table_comment = '自己能力評価'


class EngHighlightProject(models.Model):
    engineer = models.ForeignKey(
        'User', models.DO_NOTHING, blank=True, null=True, db_comment='技術者ID')
    name = models.TextField(blank=True, null=True, db_comment='プロジェクト名')
    description = models.TextField(
        blank=True, null=True, db_comment='プロジェクト概要')
    size = models.CharField(max_length=20, blank=True,
                            null=True, db_comment='プロジェクト規模')
    role_name = models.CharField(
        max_length=255, blank=True, null=True, db_comment='役職名\n複数の役割名を含み、各名前はカンマ一つで区切られる')
    responsibilities = models.TextField(
        blank=True, null=True, db_comment='担当業務')
    technology_used = models.TextField(
        blank=True, null=True, db_comment='使用技術')
    from_date = models.DateField(blank=True, null=True, db_comment='開始日')
    to_date = models.DateField(blank=True, null=True, db_comment='終了日')

    class Meta:
        managed = False
        db_table = 'eng_highlight_project'
