
from rest_framework import serializers
from django.utils.translation import gettext_lazy as _

from utils.custom_fields import *
from api.models.user import User
from api.models.company import *

from utils.responses import ErrorDetailSerializer
from api.models.eng import *
from utils.constants import *
from utils.logger_mixins import *

from utils.utils import *
from utils.currency_converter import *
from api.serializers.engineers.user_serializers import *
from utils.utils import *
from django.utils import timezone

currency_converter = CurrencyConverter()


class UploadCVUploadedSerializer(serializers.Serializer):
    cv_id = serializers.CharField()
    cv_data = serializers.JSONField()


class UploadCVUploadedResponseSerializer(serializers.Serializer):
    message = serializers.CharField(allow_null=True)
    errors = serializers.ListField(
        child=ErrorDetailSerializer(), allow_null=True)
    data = UploadCVUploadedSerializer(allow_null=True)


class SaveCVUploadedSerializer(serializers.Serializer):
    cv_id = serializers.UUIDField()


class SaveCVUploadedResponseSerializer(serializers.Serializer):
    message = serializers.CharField(allow_null=True)
    errors = serializers.ListField(
        child=ErrorDetailSerializer(), allow_null=True)
    data = serializers.CharField(allow_null=True)


class UpdateSkillFromCVSerializer(serializers.Serializer):
    skill = serializers.CharField()
    level_type = serializers.IntegerField()


class ClearUserCVDataSerializer(BaseModelSerializer):
    """
    Serializer to clear user CV data with null values before inserting new records.
    This serializer accepts null values for all fields to clear existing data.
    """
    first_name = serializers.CharField(required=False, allow_null=True)
    last_name = serializers.CharField(required=False, allow_null=True)
    nickname = serializers.CharField(required=False, allow_null=True)
    sex_type = serializers.IntegerField(required=False, allow_null=True)
    birth_date = serializers.DateField(required=False, allow_null=True)
    country_code = serializers.CharField(required=False, allow_null=True)
    tel = serializers.CharField(required=False, allow_null=True)
    address_code = serializers.CharField(required=False, allow_null=True)
    city_name = serializers.CharField(required=False, allow_null=True)
    international_tel = serializers.CharField(required=False, allow_null=True)
    pr = serializers.CharField(required=False, allow_null=True)
    professional_summary = serializers.CharField(required=False, allow_null=True, allow_blank=True)
    skills_for_cv_display = serializers.CharField(required=False, allow_null=True, allow_blank=True)

    # Related data fields - when null, will clear existing data
    skills = serializers.ListField(required=False, allow_null=True, allow_empty=True)
    educations = serializers.ListField(required=False, allow_null=True, allow_empty=True)
    languages = serializers.ListField(required=False, allow_null=True, allow_empty=True)
    experiences = serializers.ListField(required=False, allow_null=True, allow_empty=True)
    highlight_projects = serializers.ListField(required=False, allow_null=True, allow_empty=True)
    qualifications = serializers.ListField(required=False, allow_null=True, allow_empty=True)

    class Meta:
        model = User
        fields = ['first_name', 'last_name', 'nickname', 'sex_type',
                  'birth_date', 'country_code', 'tel',
                  'address_code', 'city_name', 'international_tel',
                  'pr', 'skills', 'educations', 'languages',
                  'experiences', 'highlight_projects', 'professional_summary',
                  'qualifications', 'skills_for_cv_display']

    def update(self, instance, validated_data):
        """
        Update method that clears data when null values are provided.
        """
        # Clear related data when null values are provided
        if 'skills' in validated_data and validated_data['skills'] is None:
            instance.engskill_set.all().delete()
            validated_data.pop('skills')

        if 'educations' in validated_data and validated_data['educations'] is None:
            instance.engacademic_set.all().delete()
            validated_data.pop('educations')

        if 'languages' in validated_data and validated_data['languages'] is None:
            instance.englanguage_set.all().delete()
            validated_data.pop('languages')

        if 'experiences' in validated_data and validated_data['experiences'] is None:
            instance.engcareer_set.all().delete()
            validated_data.pop('experiences')

        if 'highlight_projects' in validated_data and validated_data['highlight_projects'] is None:
            instance.enghighlightproject_set.all().delete()
            validated_data.pop('highlight_projects')

        if 'qualifications' in validated_data and validated_data['qualifications'] is None:
            instance.englicence_set.all().delete()
            validated_data.pop('qualifications')

        # Clear basic fields when null values are provided
        for field in ['tel', 'country_code', 'birth_date', 'pr', 'address_code',
                     'professional_summary', 'skills_for_cv_display']:
            if field in validated_data and validated_data[field] is None:
                setattr(instance, field, None)
                validated_data.pop(field)

        # Update remaining fields
        instance = super().update(instance, validated_data)
        instance.updated = timezone.now()
        instance.save()

        return instance


class UpdateUserCVSerializer(BaseModelSerializer):

    first_name = serializers.CharField(required=False, allow_null=True)
    last_name = serializers.CharField(required=False, allow_null=True)
    nickname = serializers.CharField(required=False, allow_null=True)
    sex_type = serializers.IntegerField(required=False, allow_null=True)
    birth_date = serializers.DateField(required=False, allow_null=True)
    country_code = serializers.CharField(required=False, allow_null=True)
    tel = serializers.CharField(required=False, allow_null=True)
    address_code = serializers.CharField(required=False, allow_null=True)
    city_name = serializers.CharField(required=False, allow_null=True)
    international_tel = serializers.CharField(required=False, allow_null=True)
    pr = serializers.CharField(required=False, allow_null=True)
    skills = UpdateSkillFromCVSerializer(
        many=True, source='engskill_set', allow_null=True)
    educations = UpdateEngAcademicSerializer(
        many=True, source='engacademic_set', allow_null=True)
    languages = UpdateEngLanguageSerializer(
        many=True, source='englanguage_set', allow_null=True)
    experiences = UpdateEngCareerSerializer(
        many=True, source='engcareer_set', allow_null=True)
    highlight_projects = UpdateEngHighLightProjectSerializer(
        many=True, source='enghighlightproject_set', allow_null=True)
    professional_summary = serializers.CharField(
        required=False, allow_null=True, allow_blank=True)
    qualifications = UpdateEngLicenseSerializer(
        many=True, source='englicence_set', allow_null=True)
    skills_for_cv_display = serializers.CharField(
        required=False, allow_null=True, allow_blank=True)

    class Meta:
        model = User
        fields = ['first_name', 'last_name', 'nickname', 'sex_type',
                  'birth_date', 'country_code', 'tel',
                  'address_code', 'city_name', 'international_tel',
                  'pr', 'skills', 'educations', 'languages',
                  'experiences', 'highlight_projects', 'professional_summary', 'qualifications', 'skills_for_cv_display']

    def validate(self, attrs):
        educations_data = attrs.get('engacademic_set', None)
        languages_data = attrs.get('englanguage_set', None)
        qualifications_data = attrs.get('englicence_set', None)

        list_academic_type = get_list_academic_type()
        list_language_level_types = get_list_language_level_type()
        list_language_level_types = [language_level_type['id']
                                     for language_level_type in list_language_level_types]
        list_academic_type = [academic_type['id']
                              for academic_type in list_academic_type]
        list_qualification_codes = get_list_qualification()

        list_qualification_codes = [qualification_code['id']
                                    for qualification_code in list_qualification_codes]
        list_qualification_codes = [str(qualification_code)
                                    for qualification_code in list_qualification_codes]

        if educations_data:
            for education_data in educations_data:
                type = education_data['type']
                if type not in list_academic_type:
                    raise serializers.ValidationError({
                        'type': _('Invalid degree code.'),
                    })
        if languages_data:
            for language_data in languages_data:
                if language_data['language_level_type'] not in list_language_level_types:
                    raise serializers.ValidationError({
                        'language_level_type': _('Invalid language level type.'),
                    })

        if qualifications_data:
            for qualification_data in qualifications_data:
                licence_code = qualification_data.get('licence_code', None)
                if licence_code:
                    if str(licence_code) not in list_qualification_codes:
                        qualification_data.pop('licence_code')

        return super().validate(attrs)

    def update(self, instance, validated_data):
        educations_data = validated_data.pop('engacademic_set', None)
        languages_data = validated_data.pop('englanguage_set', None)
        skills_data = validated_data.pop('engskill_set', None)
        experiences_data = validated_data.pop('engcareer_set', None)
        highlight_projects_data = validated_data.pop(
            'enghighlightproject_set', None)
        qualifications_data = validated_data.pop('englicence_set', None)

        address_code = validated_data.pop('address_code', None)
        if address_code:
            get_all_city = get_list_city()
            city_code = [city['code'] for city in get_all_city if city['name_en'].lower() == address_code.lower(
            ) or city['name_vn'].lower() == address_code.lower() or city['name_jp'].lower() == address_code.lower()]
            if len(city_code) > 0:
                validated_data['address_code'] = city_code[0]

        list_all_skills = get_list_skill_codes()
        job_code = Constants.DEFAULT_JOB_CODE
        # skills
        if skills_data is not None:
            instance.engskill_set.all().delete()
            skills_data_to_save = []
            for skill in skills_data:
                skill_name = skill.get('skill')
                skill_level = skill.get('level_type')

                skill_category_id = skill.get('category_id', None)
                skill_code = [skill['id']
                              for skill in list_all_skills if skill['name'].lower() == skill_name.lower()]
                if len(skill_code) > 0:
                    skill_code = skill_code[0]
                    skills_data_to_save.append({
                        'job_code': job_code,
                        'skill_code': skill_code,
                        'level_type': skill_level,
                        'engineer': instance
                    })
                else:
                    if skill_category_id == "" or skill_category_id is None:
                        skill_category_id = Constants.DEFAULT_OTHER_CATEGORY_ID
                    skill_code = Constants.DEFAULT_CUSTOM_SKILL_CODE
                    skills_data_to_save.append({
                        'job_code': job_code,
                        'skill_code': skill_code,
                        'level_type': skill_level,
                        'temp_name': skill_name,
                        'temp_category_id': skill_category_id,
                        'engineer': instance
                    })
            for skill in skills_data_to_save:
                EngSkill.objects.create(**skill)

        # educations
        if educations_data is not None:

            instance.engacademic_set.all().delete()
            highest_level = 1
            for education_data in educations_data:
                academic_type = education_data['type']
                if academic_type > highest_level:
                    highest_level = academic_type
                EngAcademic.objects.create(engineer=instance, **education_data)
            instance.last_academic_code = highest_level

        # languages
        if languages_data is not None:
            instance.englanguage_set.all().delete()
            for language_data in languages_data:
                EngLanguage.objects.create(engineer=instance, **language_data)

        # experiences
        if experiences_data is not None:
            instance.engcareer_set.all().delete()
            for experience_data in experiences_data:
                EngCareer.objects.create(engineer=instance, **experience_data)
        # highlight projects
        if highlight_projects_data is not None:
            instance.enghighlightproject_set.all().delete()
            for highlight_project_data in highlight_projects_data:
                EngHighlightProject.objects.create(
                    engineer=instance, **highlight_project_data)
            instance.updated = timezone.now()

        # qualifications
        if qualifications_data is not None:
            instance.englicence_set.all().delete()
            for qualification_data in qualifications_data:
                licence_code = qualification_data.get('licence_code', None)
                get_date = qualification_data.get('get_date', None)
                if get_date:
                    # remove all - in get_date
                    get_date = get_date.replace('-', '')
                    if len(get_date) == 6:
                        get_date = get_date + '01'
                    qualification_data['get_date'] = get_date

                if licence_code is None or licence_code == "":
                    licence_code = Constants.DEFAULT_CUSTOM_JOB_CODE
                    qualification_data['licence_code'] = licence_code

                if licence_code:
                    EngLicence.objects.create(
                        engineer=instance, **qualification_data)
        instance = super().update(instance, validated_data)
        instance.save()

        return instance
