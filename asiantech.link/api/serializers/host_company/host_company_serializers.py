from rest_framework import serializers
from api.models.chat import MapChatGroup, RecChat
from api.models.eng import MapEngAgc
from api.models.rec import RecApply, RecApplyRead, RecRecruit
from api.models.rec_waiting_company import RecWaitingCompany
from utils.custom_fields import *
from api.models.company import *
from utils.constants import *
from django.contrib.auth.models import User
from django.contrib.auth import get_user_model

from django.utils.translation import gettext_lazy as _

from utils.responses import ErrorDetailSerializer
from utils.utils import *
from utils.logger_mixins import *
from api.models.map_hst_sup import MapHstSup
from api.serializers.engineers.user_serializers import UserExploreDetailsSerializers
from api.models.rec import RecAcceptSign
from drf_yasg.utils import swagger_serializer_method
from api.models.eng import *
from utils.validators import check_email_valid
User = get_user_model()


class HostCompanySerializer(BaseModelSerializer):
    contact_mail = serializers.CharField(max_length=200)
    web_url = serializers.CharField(
        allow_null=True, allow_blank=True, max_length=255)
    capital_stock = serializers.CharField(allow_null=True, allow_blank=True)
    capital_stock_curr_code = serializers.CharField(
        allow_null=True, allow_blank=True)
    accepting_fee = serializers.CharField(allow_null=True, allow_blank=True)
    accepting_fee_curr_code = serializers.CharField(
        allow_null=True, allow_blank=True)
    support_outsourcing_fee = serializers.CharField(
        allow_null=True, allow_blank=True)
    support_outsourcing_fee_curr_code = serializers.CharField(
        allow_null=True, allow_blank=True)
    support = serializers.CharField(allow_null=True, max_length=5000)

    class Meta:
        model = ComCompany
        fields = ['name',
                  'web_url', 'employees_type',
                  'country_code', 'address_code', 'address', 'tel', 'contact_mail', 'international_tel',
                  'capital_stock', 'capital_stock_curr_code', 'logo_image_path',
                  'accepting_fee', 'accepting_fee_curr_code',
                  'support_outsourcing_fee', 'support_outsourcing_fee_curr_code',
                  'support'
                  ]

    def validate(self, attrs):
        # check url format
        web_url = attrs.get('web_url')
        if web_url is not None:
            if not check_url_valid(web_url):
                raise serializers.ValidationError(
                    {'web_url': _('Invalid url format.')})
        # check email format
        contact_mail = attrs.get('contact_mail')
        if contact_mail is not None:
            if not check_email_valid(contact_mail, max_length=100):
                raise serializers.ValidationError(
                    {'contact_mail': _('Invalid email format.')})
        return super().validate(attrs)


class HostCompanyParamsSupportCompanySerializer(BaseSerializer):
    email = serializers.CharField(allow_null=True, required=False)


class HostCompanyGetSupportCompanySerializer(BaseModelSerializer):
    introduction_pr = serializers.SerializerMethodField()

    class Meta:
        model = ComCompany
        fields = ['company_id', 'name', 'logo_image_path', 'accepting_fee',
                  'support_outsourcing_fee', 'address_code', 'country_code', 'support_outsourcing_fee_curr_code',
                  'contact_mail',
                  'tel', 'international_tel',
                  'accepting_fee_curr_code',
                  'introduction_pr'
                  ]

    def get_introduction_pr(self, obj) -> str:
        try:
            if self.context is not None and self.context.get("request", None) is not None:
                request = self.context.get('request')
                host_company_id = request.user.company_id
                data = MapHstSup.objects.get(
                    support_company_id=obj.company_id, host_company_id=host_company_id)
                return data.introduction_pr
            else:
                return None
        except Exception as e:
            return None


class HostCompanySubscribeSupportCompanySerializer(BaseSerializer):
    support_company_id = serializers.CharField()


class HostCompanyUnsubscribeSupportCompanySerializer(BaseSerializer):
    support_company_id = serializers.CharField()


class CreateSupportCompanySerializer(BaseModelSerializer):
    contact_mail = serializers.EmailField()
    introduction_url = serializers.URLField()
    support = serializers.CharField(
        max_length=5000, allow_null=True)

    class Meta:
        model = ComCompany
        fields = ['name',
                  'introduction_url', 'employees_type',
                  'country_code', 'address_code', 'address', 'international_tel', 'tel',
                  'contact_mail', 'accepting_fee', 'accepting_fee_curr_code',
                  'support_outsourcing_fee', 'support_outsourcing_fee_curr_code',
                  'support'
                  ]


class CreateReferralAgencyCompanySerializer(BaseModelSerializer):
    contact_mail = serializers.EmailField()
    web_url = serializers.URLField()

    class Meta:
        model = ComCompany
        fields = ['name',
                  'web_url', 'employees_type',
                  'country_code', 'address_code', 'address', 'international_tel', 'tel',
                  'contact_mail', 'agent_fee', 'agent_fee_curr_code'
                  ]


# response


class HostCompanySimilarRecruitCompanySerializer(BaseModelSerializer):
    class Meta:
        model = ComCompany
        fields = ['company_id', 'name', 'logo_image_path']


class HostCompanyInformationSerializer(BaseModelSerializer):
    capital_stock = serializers.CharField(allow_null=True, allow_blank=True)
    capital_stock_curr_code = serializers.CharField(
        allow_null=True, allow_blank=True)

    class Meta:
        model = ComCompany
        fields = ['company_id', 'name', 'logo_image_path', 'pr_image_path1',
                  'pr_image_path2', 'pr_image_path3', 'introduction_url', 'address', 'capital_stock',
                  'employees_type', 'tel', 'international_tel',
                  'contact_mail', 'about_us', 'business_details',
                  'benefits', 'capital_stock_curr_code', 'country_code', 'address_code', 'web_url']


class HostCompanyRecWaitingCompanySerializer(BaseModelSerializer):

    class Meta:
        model = RecWaitingCompany
        fields = ['waiting_flag']


class HostCompanyListSupportCompanyResponseModel(BaseSerializer):
    message = serializers.CharField(allow_null=True)
    errors = serializers.ListField(
        child=ErrorDetailSerializer(), allow_null=True)
    data = HostCompanyGetSupportCompanySerializer(many=True)


class HostCompanyParamListAgencyCompany(BaseSerializer):
    contact_mail = serializers.CharField(allow_null=True, required=False)


class HostCompanyRequestInterviewSerializer(BaseSerializer):
    recruit_id = serializers.IntegerField(required=True)
    user_id = serializers.IntegerField(required=True)
    message = serializers.CharField(required=True)


class AgencyCompanySerializer(BaseModelSerializer):
    class Meta:
        model = MapEngAgc
        fields = ['engineer_id', 'agency_company_id', 'agency_agent_id',
                  'introduction_pr', 'create_user_id', 'create_datetime', 'update_user_id', 'update_datetime']


class HostCompanyUpdateInterviewAdmissionSerializer(BaseSerializer):
    apply_id = serializers.IntegerField(required=True)
    job_code = serializers.CharField(required=True)
    employ_code = serializers.CharField(required=True)
    place_code = serializers.CharField(required=True)
    payroll_code = serializers.CharField(required=True)
    payroll_price = serializers.DecimalField(
        max_digits=20, decimal_places=3, required=True)
    joining_date = serializers.DateField(required=True)


# List recruitment title
class HostCompanyListRecruitmentTitleSerializer(BaseModelSerializer):
    class Meta:
        model = RecRecruit
        fields = ['recruit_id', 'host_company_id', 'title']


class HostCompanyParamListUserAppliedCompanySerializer(BaseSerializer):
    recruit_ids = serializers.ListField(
        child=serializers.IntegerField(),
        allow_null=True,
        required=False)
    ordering = serializers.CharField(
        allow_null=False, required=False, allow_blank=True)
    apply_status_filter_codes = serializers.ListField(
        child=serializers.IntegerField(),
        allow_null=True,
        required=False)
    search = serializers.CharField(
        allow_null=False, required=False, allow_blank=True)


class HostCompanyListAppliedEngineerSerializer(BaseSerializer):
    data = HostCompanyParamListUserAppliedCompanySerializer(many=True)
    total_engineers = serializers.IntegerField()


class HostCompanyListUserAppliedCompanyResponseModel(BaseSerializer):
    message = serializers.CharField(allow_null=True)
    errors = serializers.ListField(
        child=ErrorDetailSerializer(), allow_null=True)
    data = HostCompanyListAppliedEngineerSerializer()
