from typing import List
from rest_framework import serializers
from api.serializers.common_serializers import PagingResponseModel
from utils.custom_fields import *
from api.models.company import *
from utils.constants import *
from django.contrib.auth.models import User
from django.contrib.auth import get_user_model
from django.utils import timezone as tz
from utils.validators import check_password_valid
import json
from django.utils.translation import gettext_lazy as _
from api.models.rec import *
from utils.constants import *
from utils.responses import CustomResponse, ErrorDetailSerializer
from utils.utils import *
from utils.logger_mixins import *
from drf_yasg.utils import swagger_serializer_method
from api.models.map_hst_sup import MapHstSup
from utils.currency_converter import CurrencyConverter
User = get_user_model()
currency_converter = CurrencyConverter()


class RecruitCompanySerializer(BaseModelSerializer):
    user_type = serializers.IntegerField
    introduction_pr = serializers.SerializerMethodField()
    capital_stock = serializers.CharField(allow_null=True, allow_blank=True)
    capital_stock_curr_code = serializers.CharField(
        allow_null=True, allow_blank=True)

    class Meta:
        model = ComCompany
        fields = [
            'company_id', 'name', 'about_us', 'business_details', 'employees_type',
            'country_code', 'address_code', 'address', 'tel', 'logo_image_path',
            'pr_image_path1', 'pr_image_path2', 'pr_image_path3', 'contact_mail',
            'web_url', 'introduction_url', 'memo', 'created', 'updated', 'benefits',
            'capital_stock', 'capital_stock_curr_code', 'international_tel', 'agent_fee',
            'agent_fee_curr_code', 'accepting_fee', 'accepting_fee_curr_code',
            'support_outsourcing_fee', 'support_outsourcing_fee_curr_code', 'support', 'status', 'introduction_pr', 'working_hours_from', 'working_hours_to'
        ]

    def get_introduction_pr(self, obj) -> str:
        try:
            if self.context is not None and self.context.get("request", None) is not None:
                request = self.context.get('request')
                host_company_id = request.user.company_id
                data = MapHstSup.objects.get(
                    support_company_id=obj.company_id, host_company_id=host_company_id)
                return data.introduction_pr
            else:
                return None
        except Exception as e:
            return None


class RecruitGetSupportCompanySerializer(BaseModelSerializer):
    introduction_pr = serializers.SerializerMethodField()

    class Meta:
        model = ComCompany
        fields = ['company_id', 'name', 'logo_image_path', 'accepting_fee',
                  'support_outsourcing_fee', 'address_code', 'country_code', 'support_outsourcing_fee_curr_code',
                  'contact_mail',
                  'tel', 'international_tel',
                  'accepting_fee_curr_code',
                  'introduction_pr'
                  ]

    def get_introduction_pr(self, obj) -> str:
        try:
            if self.context is not None and self.context.get("request", None) is not None:
                request = self.context.get('request')
                host_company_id = request.user.company_id
                data = MapHstSup.objects.get(
                    support_company_id=obj.company_id, host_company_id=host_company_id)
                return data.introduction_pr
            else:
                return None
        except Exception as e:
            return None


class RecruitmentSerializer(BaseModelSerializer):
    host_company = RecruitCompanySerializer()
    support_company = RecruitGetSupportCompanySerializer()
    payroll_price_from = serializers.CharField()
    payroll_price_to = serializers.CharField()

    class Meta:
        model = RecRecruit
        fields = "__all__"


class RecRecruitSerializer(BaseModelSerializer):
    host_company = RecruitCompanySerializer()
    host_company_id = serializers.IntegerField()

    class Meta:
        model = RecRecruit
        fields = ['title', 'recruit_id', 'job_code', 'catch_copy',
                  'host_company_id', 'host_company']


class CreateRecruitmentSerializer(BaseModelSerializer):
    catch_copy = serializers.CharField(
        max_length=100, allow_null=True, allow_blank=True)
    content = serializers.CharField(
        max_length=10000, allow_null=True, allow_blank=True)
    payroll_price_from = serializers.CharField(
        allow_null=True, allow_blank=True)
    payroll_price_to = serializers.CharField(allow_null=True, allow_blank=True)
    title = serializers.CharField(allow_null=True, allow_blank=True)
    remote_code = serializers.CharField(allow_null=True, allow_blank=True)

    class Meta:
        model = RecRecruit
        fields = ['catch_copy', 'content',
                  'job_code', 'employ_code',
                  'place_code1', 'place_code2', 'place_code3',
                  'payroll_price_from', 'payroll_price_to',
                  'country_code', 'age_from', 'age_to', 'sex_type',
                  'pref_code1', 'pref_code2', 'pref_code3',
                  'payroll_code',
                  'last_academic_code',
                  'language_code1', 'language_level_type1',
                  'language_code2', 'language_level_type2',
                  'experienced_job_code', 'years_of_experience',
                  'skill_job_code1', 'skill_code1', 'skill_level_type1',
                  'skill_job_code2', 'skill_code2', 'skill_level_type2',
                  'skill_job_code3', 'skill_code3', 'skill_level_type3',
                  'licence_code1', 'licence_point1', 'licence_code2', 'licence_point2', 'licence_code3', 'licence_point3',
                  'start_date', 'end_date',
                  'support_company',
                  'title',
                  'recruit_image_path',
                  'licence_name1',
                  'licence_name2',
                  'licence_name3',
                  'display_flag',
                  'remote_code'
                  ]

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)

    def create(self, validated_data):
        return RecRecruit.objects.create(**validated_data)

    def validate(self, attrs):

        attrs['save_type'] = 1
        display_flag = attrs['display_flag']

        if display_flag == 0:
            if attrs['payroll_price_from'] == '':
                attrs['payroll_price_from'] = None
            if attrs['payroll_price_to'] == '':
                attrs['payroll_price_to'] = None

            # skip check validate if display_flag = 0
            return attrs

       # check sex type
        sex_type = attrs.get("sex_type", None)
        if sex_type is not None and sex_type not in SexType._value2member_map_:
            raise serializers.ValidationError(
                {"sex_type": _("Invalid sex type")})

        # check last academic code
        last_academic_code = attrs.get('last_academic_code', None)
        if last_academic_code:
            if check_degree_code_valid(last_academic_code) == False:
                raise serializers.ValidationError(
                    {'last_academic_code': _('Invalid degree code.')})
        else:
            attrs['last_academic_code'] = None

        # check date
        if attrs['start_date'] > attrs['end_date']:
            raise serializers.ValidationError(
                {"start_date": _("start_date must be less than end_date")}
            )

        # check age
        age_from = attrs.get('age_from', None)
        age_to = attrs.get('age_to', None)
        if age_from is not None and age_to is not None:
            if age_from > age_to:
                raise serializers.ValidationError({
                    "age_from": _("age_from must be less than age_to")})

        # check price
        if attrs['payroll_price_from'] is not None and attrs['payroll_price_to'] is not None:
            price_from = float(attrs['payroll_price_from'])
            price_to = float(attrs['payroll_price_to'])
            if price_from > price_to:
                raise serializers.ValidationError({
                    "payroll_price_from": _("payroll_price_from must be less than payroll_price_to")})

        # check country code
        country_code = attrs.get('country_code', None)
        if country_code is not None:
            if check_country_code_valid(country_code) == False:
                raise serializers.ValidationError(
                    {'country_code': _('Invalid country code.')})

        # check place_code
        place_code1 = attrs.get('place_code1', None)
        place_code2 = attrs.get('place_code2', None)
        place_code3 = attrs.get('place_code3', None)
        if place_code1 is not None and check_address_code_valid(place_code1) == False:
            raise serializers.ValidationError(
                {'place_code1': _('Invalid address code.')})
        if place_code2 is not None and check_address_code_valid(place_code2) == False:
            raise serializers.ValidationError(
                {'place_code2': _('Invalid address code.')})
        if place_code3 is not None and check_address_code_valid(place_code3) == False:
            raise serializers.ValidationError(
                {'place_code3': _('Invalid address code.')})

        # check pref_code
        pref_code1 = attrs.get('pref_code1', None)
        pref_code2 = attrs.get('pref_code2', None)
        pref_code3 = attrs.get('pref_code3', None)
        if pref_code1 is None:
            attrs['pref_code1'] = None
        if pref_code2 is None:
            attrs['pref_code2'] = None
        if pref_code3 is None:
            attrs['pref_code3'] = None

        if pref_code1 is not None and check_address_code_valid(pref_code1) == False:
            raise serializers.ValidationError(
                {'pref_code1': _('Invalid pref code.')})
        if pref_code2 is not None and check_address_code_valid(pref_code2) == False:
            raise serializers.ValidationError(
                {'pref_code2': _('Invalid pref code.')})
        if pref_code3 is not None and check_address_code_valid(pref_code3) == False:
            raise serializers.ValidationError(
                {'pref_code3': _('Invalid pref code.')})

        # check currency code
        payroll_code = attrs['payroll_code']
        if not check_currency_code_valid(payroll_code):
            raise serializers.ValidationError(
                {'payroll_code': _('Invalid currency code.')})

        # check support company
        support_company = attrs.get('support_company', None)
        if support_company is not None:
            if support_company.user_type != UserType.HOST_SUPPORT_AGENCY_STAFF.value:
                raise serializers.ValidationError(
                    {'support_company': _('Invalid support company.')})
        return attrs


class DeleteRecruitmentSerializer(BaseSerializer):
    recruit_id = serializers.CharField()


class RecruitExploreParamsSerializer(BaseSerializer):
    page_size = serializers.IntegerField(allow_null=False, required=False)
    ordering = serializers.CharField(
        allow_null=False, required=False, allow_blank=True)
    name = serializers.CharField(
        allow_null=False, required=False, allow_blank=True)
    employ_code = serializers.CharField(
        allow_null=False, required=False, allow_blank=True)
    places = serializers.ListField(
        child=serializers.CharField(), allow_empty=True, required=False
    )
    job_code = serializers.CharField(
        allow_null=False, required=False, allow_blank=True)
    skill_codes_1 = serializers.ListField(
        child=serializers.CharField(), allow_empty=True, required=False
    )
    skill_codes_2 = serializers.ListField(
        child=serializers.CharField(), allow_empty=True, required=False
    )
    skill_codes_3 = serializers.ListField(
        child=serializers.CharField(), allow_empty=True, required=False
    )
    payroll_code = serializers.CharField(
        allow_null=False, required=False, allow_blank=True)
    payroll_price_from = serializers.CharField(
        allow_null=False, required=False)
    payroll_price_to = serializers.CharField(
        allow_null=False, required=False)
    language_codes = serializers.ListField(
        child=serializers.CharField(), allow_empty=True, required=False
    )
    cursor = serializers.CharField(
        allow_null=False, required=False, allow_blank=True)
    show_old_post = serializers.BooleanField(allow_null=False, required=True)
    company_id = serializers.IntegerField(allow_null=False, required=False)
    job_codes = serializers.ListField(
        child=serializers.CharField(), allow_empty=True, required=False
    )
    waiting_flag = serializers.IntegerField(allow_null=False, required=False)
    remote_code = serializers.CharField(allow_null=False, required=False)

    def to_representation(self, instance):
        """
        Object instance -> Dict of primitive datatypes.
        """
        ret = super(RecruitExploreParamsSerializer,
                    self).to_representation(instance)
        # Remove 'page_size' from response if it is None
        if ret.get('page_size') is None:
            ret.pop('page_size')
        return ret


class RecruitExploreSerializer(BaseModelSerializer):
    host_company = RecruitCompanySerializer(
        read_only=True)
    waiting_flag = serializers.SerializerMethodField()
    payroll_price_from = serializers.CharField()
    payroll_price_to = serializers.CharField()

    class Meta:
        model = RecRecruit
        fields = ['recruit_id', 'title', 'catch_copy', 'payroll_price_from',
                  'payroll_price_to', 'payroll_code', 'start_date', 'end_date', 'job_code', 'host_company',
                  'skill_job_code1', 'skill_job_code2', 'skill_job_code3', 'waiting_flag', 'created'
                  ]

    def get_waiting_flag(self, obj):
        request = self.context.get('request')
        # Check if not authenticated return default WaitingFlag.CANCELLED
        user = request.user
        if not user.is_authenticated:
            return WaitingFlag.CANCELLED.value
        user_id = user.user_id

        # Check if there are any related RecWaitingCompany objects
        if obj.waiting_company_set.filter(engineer_id=user_id).exists():
            # Return the waiting_flag of the first related RecWaitingCompany object for the user
            return obj.waiting_company_set.filter(engineer_id=user_id).first().waiting_flag
        else:
            # Return 0 if there are no related RecWaitingCompany objects for the user
            return WaitingFlag.CANCELLED.value


class RecruitDetailSerializer(BaseModelSerializer):
    host_company = RecruitCompanySerializer(read_only=True)
    waiting_flag = serializers.SerializerMethodField()
    support_company = serializers.SerializerMethodField()
    total_recruit = serializers.SerializerMethodField()
    similar_recruits = serializers.SerializerMethodField()
    payroll_price_from = serializers.SerializerMethodField()
    payroll_price_to = serializers.SerializerMethodField()
    payroll_code = serializers.SerializerMethodField()

    class Meta:
        model = RecRecruit
        fields = ['recruit_id', 'title', 'catch_copy', 'payroll_price_from',
                  'payroll_price_to', 'payroll_code', 'start_date', 'end_date', 'job_code', 'host_company',
                  'waiting_flag', 'employ_code', 'place_code1', 'place_code2', 'place_code3',
                  'country_code', 'pref_code1', 'pref_code2', 'pref_code3', 'age_from', 'age_to',
                  'last_academic_code', 'language_code1', 'language_level_type1',
                  'language_code2', 'language_level_type2', 'experienced_job_code', 'years_of_experience',
                  'skill_job_code1', 'skill_code1', 'skill_level_type1',
                  'skill_job_code2', 'skill_code2', 'skill_level_type2',
                  'skill_job_code3', 'skill_code3', 'skill_level_type3', 'content', 'sex_type',
                  'support_company', 'licence_code1', 'licence_name1', 'licence_point1', 'licence_code2', 'licence_name2', 'licence_point2',
                  'licence_code3', 'licence_name3', 'licence_point3', 'total_recruit', 'similar_recruits', 'recruit_image_path', 'remote_code']

    def get_payroll_price_from(self, obj) -> str:
        if obj.payroll_code is not None and obj.payroll_price_from is not None:
            try:
                user_currency = get_currency_from_request(
                    self.context.get('request'))
                if user_currency is not None:
                    amount = currency_converter.convert(
                        obj.payroll_price_from,
                        obj.payroll_code,
                        user_currency
                    )
                    return str(amount)

                else:
                    return str(obj.payroll_price_from)

            except Exception as e:
                return str(obj.payroll_price_from)
        return str(obj.payroll_price_from)

    def get_payroll_price_to(self, obj) -> str:
        if obj.payroll_code is not None and obj.payroll_price_to is not None:
            try:
                user_currency = get_currency_from_request(
                    self.context.get('request'))
                if user_currency is not None:
                    amount = currency_converter.convert(
                        obj.payroll_price_to,
                        obj.payroll_code,
                        user_currency
                    )
                    return str(amount)

                else:
                    return str(obj.payroll_price_to)

            except Exception as e:
                return str(obj.payroll_price_to)
        return str(obj.payroll_price_to)

    def get_payroll_code(self, obj) -> str:
        if obj.payroll_code is not None:
            try:
                user_currency = get_currency_from_request(
                    self.context.get('request'))
                if user_currency is not None:
                    return user_currency
                else:
                    return obj.payroll_code
            except Exception as e:
                return ""
        else:
            return ""

    def get_waiting_flag(self, obj):
        request = self.context.get('request')
        user = request.user if request else None
        if not user or not user.is_authenticated:
            return WaitingFlag.CANCELLED.value

        user_id = user.user_id
        waiting_company = obj.waiting_company_set.filter(
            engineer_id=user_id).first()

        if waiting_company:
            return waiting_company.waiting_flag
        return WaitingFlag.CANCELLED.value

    def get_total_recruit(self, obj) -> int:
        return RecApply.objects.filter(recruit_id=obj.recruit_id).count()

    @swagger_serializer_method(serializer_or_field=RecRecruitSerializer(many=True))
    def get_similar_recruits(self, obj):
        now = timezone.now()
        similar_recruits = RecRecruit.objects.filter(
            host_company=obj.host_company,
            display_flag=1,  # Assuming there's a field to show active recruits
            start_date__lte=now,
            end_date__gte=now
            # Limit to 10 similar recruits
        ).exclude(recruit_id=obj.recruit_id)[:10]

        return RecRecruitSerializer(similar_recruits, many=True).data

    @swagger_serializer_method(serializer_or_field=RecruitCompanySerializer)
    def get_support_company(self, obj):
        try:
            # Assuming `support_company` is a related object
            support_company = obj.support_company
            if support_company:
                return RecruitCompanySerializer(support_company, context={'request': self.context.get('request')}).data
        except Exception:
            return None


class CompanyRecruitDetailSerializer(BaseModelSerializer):
    support_company = serializers.SerializerMethodField()
    host_company = RecruitCompanySerializer(read_only=True)
    payroll_price_from = serializers.CharField(
        allow_null=True, allow_blank=True)
    payroll_price_to = serializers.CharField(allow_null=True, allow_blank=True)

    class Meta:
        model = RecRecruit
        fields = "__all__"

    @swagger_serializer_method(serializer_or_field=RecruitCompanySerializer)
    def get_support_company(self, obj):
        try:
            # Assuming `support_company` is a related object
            support_company = obj.support_company
            if support_company:
                return RecruitCompanySerializer(support_company, context={'request': self.context.get('request')}).data
        except Exception:
            return None


class GetMyRecruitParamsSerializer(BaseSerializer):
    recruit_id = serializers.CharField(allow_null=False, required=True)


class GetMyRecruitSerializer(BaseModelSerializer):

    class Meta:
        model = RecRecruit
        fields = "__all__"


class GetMyRecruitResponseModel(BaseSerializer):
    message = serializers.CharField(allow_null=True)
    data = GetMyRecruitSerializer()
    errors = serializers.ListField(
        child=ErrorDetailSerializer(), allow_null=True)


class RecruitUploadedParamsSerializer(BaseSerializer):
    page_size = serializers.IntegerField(allow_null=False, required=False)
    cursor = serializers.CharField(
        allow_null=True, required=False, allow_blank=True)
    ordering = serializers.CharField(
        allow_null=False, required=False, allow_blank=True)
    show_old_post = serializers.BooleanField(required=True, allow_null=False)
    display_flag = serializers.IntegerField(allow_null=True, required=False)
    engineer_id = serializers.IntegerField(allow_null=True, required=False)


class RecruitmentParamsSerializer(BaseSerializer):
    recruit_id = serializers.IntegerField(allow_null=False, required=True)


class RecruitUploadedSerializers(BaseModelSerializer):
    company_user_id = serializers.SerializerMethodField()
    total_apply = serializers.SerializerMethodField()
    job_name = serializers.SerializerMethodField(allow_null=True)
    payroll_price_from = serializers.CharField(allow_null=True)
    payroll_price_to = serializers.CharField(allow_null=True)
    applied = serializers.SerializerMethodField()
    is_expired = serializers.SerializerMethodField()
    total_request = serializers.SerializerMethodField()
    total_under_selection = serializers.SerializerMethodField()

    class Meta:
        model = RecRecruit
        fields = ['recruit_id', 'title', 'catch_copy', 'payroll_price_from',
                  'payroll_price_to', 'payroll_code', 'start_date', 'end_date', 'job_code', 'host_company',
                  'skill_job_code1', 'skill_job_code2', 'skill_job_code3', 'host_agent',
                  'company_user_id',
                  'total_apply',
                  'job_name',
                  'applied', 'recruit_image_path', 'is_expired',
                  'place_code1', 'place_code2', 'place_code3',
                  'total_request', 'total_under_selection', 'display_flag', 'remote_code'
                  ]

    def get_is_expired(self, obj) -> bool:
        if obj.end_date is not None:
            return obj.end_date < timezone.now()
        else:
            return False

    def get_job_name(self, obj) -> str:
        current_language_header = get_language_code_from_header(
            self.context.get('request'))
        return get_job_code_name(obj.job_code, current_language_header)

    def get_company_user_id(self, obj) -> int:
        try:
            user = User.objects.get(company_id=obj.host_company.company_id)
            return user.user_id
        except Exception as e:
            return None

    def get_total_apply(self, obj) -> int:
        try:
            total_count = RecApply.objects.filter(
                recruit_id=obj.recruit_id).count()
            return total_count
        except Exception as e:
            return 0

    def get_total_request(self, obj) -> int:
        try:
            total_count = RecApply.objects.filter(
                recruit_id=obj.recruit_id, recruit_progress_code__in=[RecruitProgressCode.APPLICATION.value, RecruitProgressCode.INTERVIEW_REQUEST.value]).count()
            return total_count
        except Exception as e:
            return 0

    def get_total_under_selection(self, obj) -> int:
        try:
            total_count = RecApply.objects.filter(
                recruit_id=obj.recruit_id,  recruit_progress_code__in=[RecruitProgressCode.INTERVIEW_SCHEDULING.value, RecruitProgressCode.JOB_OFFER.value]).count()
            return total_count
        except Exception as e:
            return 0

    def get_applied(self, obj) -> bool:
        request = self.context.get('request')
        params = request.query_params
        engineer_id = params.get('engineer_id', None)
        if engineer_id is not None and engineer_id != "":
            exist = RecApply.objects.filter(
                recruit_id=obj.recruit_id, engineer_id=engineer_id).exists()
            return exist
        return False


class UpdateRecruitmentSerializer(BaseModelSerializer):
    catch_copy = serializers.CharField(
        max_length=100, allow_null=True, allow_blank=True)
    content = serializers.CharField(
        max_length=10000, allow_null=True, allow_blank=True)
    payroll_price_from = serializers.CharField(
        allow_null=True, allow_blank=True)
    payroll_price_to = serializers.CharField(allow_null=True, allow_blank=True)
    title = serializers.CharField(allow_null=True, allow_blank=True)
    remote_code = serializers.CharField(allow_null=True, allow_blank=True)

    class Meta:
        model = RecRecruit
        fields = [
            'title', 'catch_copy', 'content', 'payroll_price_from',
            'payroll_price_to', 'payroll_code', 'start_date', 'end_date', 'job_code',
            'employ_code', 'place_code1', 'place_code2', 'place_code3',
            'country_code',
            'pref_code1', 'pref_code2', 'pref_code3',
            'age_from', 'age_to',
            'last_academic_code',
            'language_code1', 'language_level_type1',
            'language_code2', 'language_level_type2',
            'experienced_job_code', 'years_of_experience',
            'skill_job_code1', 'skill_code1', 'skill_level_type1',
            'skill_job_code2', 'skill_code2', 'skill_level_type2',
            'skill_job_code3', 'skill_code3', 'skill_level_type3',
            'sex_type',
            'support_company',
            'licence_code1', 'licence_point1', 'licence_code2', 'licence_point2', 'licence_code3', 'licence_point3',
            'recruit_image_path',
            'licence_name1', 'licence_name2', 'licence_name3',
            'display_flag',
            'remote_code',
            'payroll_price_from_usd',
            'payroll_price_to_usd'
        ]

    def update(self, instance, validated_data):

        payroll_code = validated_data.get('payroll_code', None)
        if payroll_code:
            payroll_price_from = validated_data.get('payroll_price_from', None)
            payroll_price_to = validated_data.get('payroll_price_to', None)
            if payroll_price_from:
                payroll_price_from_usd = currency_converter.convert(
                    payroll_price_from, payroll_code, 'USD')
                validated_data['payroll_price_from_usd'] = payroll_price_from_usd
            if payroll_price_to:
                payroll_price_to_usd = currency_converter.convert(
                    payroll_price_to, payroll_code, 'USD')
                validated_data['payroll_price_to_usd'] = payroll_price_to_usd
        for attr, value in validated_data.items():
            setattr(instance, attr, value)
        # Update the 'updated' field to the current time
        instance.updated = tz.now()
        instance.save()
        return instance

    def validate(self, attrs):
        display_flag = attrs['display_flag']

        if display_flag == 0:
            if attrs['payroll_price_from'] == '':
                attrs['payroll_price_from'] = None
            if attrs['payroll_price_to'] == '':
                attrs['payroll_price_to'] = None

            # skip check validate if display_flag = 0
            return attrs

        sex_type = attrs.get("sex_type", None)
        if sex_type is not None and sex_type not in SexType._value2member_map_:
            raise serializers.ValidationError(
                {"sex_type": _("Invalid sex type")})
        else:
            attrs['sex_type'] = None

        # check last academic code
        last_academic_code = attrs.get('last_academic_code', None)
        if last_academic_code:
            if check_degree_code_valid(last_academic_code) == False:
                raise serializers.ValidationError(
                    {'last_academic_code': _('Invalid degree code.')})
        else:
            attrs['last_academic_code'] = None

        # check payroll price
        payroll_price_from = attrs.get('payroll_price_from', None)
        payroll_price_to = attrs.get('payroll_price_to', None)
        if payroll_price_from is not None and payroll_price_to is not None:
            price_from = float(payroll_price_from)
            price_to = float(payroll_price_to)
            if price_from > price_to:
                raise serializers.ValidationError(
                    {"payroll_price_from": _("payroll_price_from must be less than payroll_price_to")})

        # check payroll code
        payroll_code = attrs.get('payroll_code', None)
        if not check_currency_code_valid(payroll_code):
            raise serializers.ValidationError(
                {'payroll_code': _('Invalid currency code.')})

        # check date
        if attrs['start_date'] > attrs['end_date']:
            raise serializers.ValidationError(
                {"start_date": _("start_date must be less than end_date")}
            )
        # check place code
        place_code1 = attrs.get('place_code1', None)
        place_code2 = attrs.get('place_code2', None)
        place_code3 = attrs.get('place_code3', None)

        if place_code1 and check_address_code_valid(place_code1) == False:
            raise serializers.ValidationError(
                {'place_code1': _('Invalid address code.')})
        if place_code2 and check_address_code_valid(place_code2) == False:
            raise serializers.ValidationError(
                {'place_code2': _('Invalid address code.')})
        if place_code3 and check_address_code_valid(place_code3) == False:
            raise serializers.ValidationError(
                {'place_code3': _('Invalid address code.')})
        # check pref code
        pref_code1 = attrs.get('pref_code1', None)
        pref_code2 = attrs.get('pref_code2', None)
        pref_code3 = attrs.get('pref_code3', None)

        if pref_code1 and check_address_code_valid(pref_code1) == False:
            raise serializers.ValidationError(
                {'pref_code1': _('Invalid pref code.')})
        if pref_code2 and check_address_code_valid(pref_code2) == False:
            raise serializers.ValidationError(
                {'pref_code2': _('Invalid pref code.')})
        if pref_code3 and check_address_code_valid(pref_code3) == False:
            raise serializers.ValidationError(
                {'pref_code3': _('Invalid pref code.')})

        # check country code
        country_code = attrs.get('country_code', None)
        if country_code is None:
            attrs['country_code'] = None
        if country_code and not check_country_code_valid(country_code):
            raise serializers.ValidationError(
                {'country_code': _('Invalid country code.')})

        # check age
        age_from = attrs.get('age_from', None)
        age_to = attrs.get('age_to', None)
        if age_from is not None and age_to is not None:
            if age_from > age_to:
                raise serializers.ValidationError({
                    "age_from": _("age_from must be less than age_to")})
        else:
            attrs['age_from'] = None
            attrs['age_to'] = None

        # check experienced job code
        experienced_job_code = attrs.get('experienced_job_code', None)
        if experienced_job_code is None:
            attrs['experienced_job_code'] = None
            attrs['years_of_experience'] = None

        # check year of experience
        years_of_experience = attrs.get('years_of_experience', None)
        if years_of_experience is not None:
            if years_of_experience < 0:
                raise serializers.ValidationError({
                    "years_of_experience": _("years_of_experience must be greater than 0")})

        # check language code
        language_code1 = attrs.get('language_code1', None)
        language_code2 = attrs.get('language_code2', None)
        if language_code1 is None:
            attrs['language_code1'] = None
            attrs['language_level_type1'] = None
        if language_code2 is None:
            attrs['language_code2'] = None
            attrs['language_level_type2'] = None

        if language_code1 is not None and not check_language_code_valid(language_code1):
            raise serializers.ValidationError(
                {'language_code1': _('Invalid language code.')})
        if language_code2 is not None and not check_language_code_valid(language_code2):
            raise serializers.ValidationError(
                {'language_code2': _('Invalid language code.')})
        # check pref_code
        pref_code1 = attrs.get('pref_code1', None)
        pref_code2 = attrs.get('pref_code2', None)
        pref_code3 = attrs.get('pref_code3', None)
        if pref_code1 is None:
            attrs['pref_code1'] = None
        if pref_code2 is None:
            attrs['pref_code2'] = None
        if pref_code3 is None:
            attrs['pref_code3'] = None

        skill_code1 = attrs.get('skill_code1', None)
        skill_code2 = attrs.get('skill_code2', None)
        skill_code3 = attrs.get('skill_code3', None)

        if skill_code1 is None:
            attrs['skill_code1'] = None
            attrs['skill_level_type1'] = None

        if skill_code2 is None:
            attrs['skill_code2'] = None
            attrs['skill_level_type2'] = None
        if skill_code3 is None:
            attrs['skill_code3'] = None
            attrs['skill_level_type3'] = None

        place_code1 = attrs.get('place_code1', None)
        place_code2 = attrs.get('place_code2', None)
        place_code3 = attrs.get('place_code3', None)
        if place_code1 is None:
            attrs['place_code1'] = None
        if place_code2 is None:
            attrs['place_code2'] = None
        if place_code3 is None:
            attrs['place_code3'] = None

        return attrs


class FeaturedJobSerializer(BaseSerializer):
    id = serializers.CharField()
    name_jp = serializers.CharField()
    name_en = serializers.CharField()
    image = serializers.CharField()
    total_jobs = serializers.IntegerField()


# Response models


class CreateRecruitResponseModel(BaseSerializer):
    message = serializers.CharField(allow_null=True)
    data = RecruitmentSerializer()
    errors = serializers.ListField(
        child=ErrorDetailSerializer(), allow_null=True)


class CompanyMyRecruitmentDetailsResponseModel(BaseSerializer):
    message = serializers.CharField(allow_null=True)
    data = CompanyRecruitDetailSerializer()
    errors = serializers.ListField(
        child=ErrorDetailSerializer(), allow_null=True)


class RecruitmentDetailsResponseModel(BaseSerializer):
    message = serializers.CharField(allow_null=True)
    data = RecruitDetailSerializer()
    errors = serializers.ListField(
        child=ErrorDetailSerializer(), allow_null=True)


class CompanyRecruitDetailsResponseModel(BaseSerializer):
    message = serializers.CharField(allow_null=True)
    data = RecruitDetailSerializer()
    errors = serializers.ListField(
        child=ErrorDetailSerializer(), allow_null=True)


class ListRecruitmentResponseModel(BaseSerializer):
    message = serializers.CharField(allow_null=True)
    data = PagingResponseModel(input_serializer=RecruitmentSerializer)
    errors = serializers.ListField(
        child=ErrorDetailSerializer(), allow_null=True)


class ListRecruitSerializers(BaseSerializer):
    next = serializers.CharField(allow_null=True)
    previous = serializers.CharField(allow_null=True)
    results = RecruitUploadedSerializers(many=True)
    total_count = serializers.IntegerField()


class ListRecruitOfHostCompanyParams(BaseSerializer):
    host_company_id = serializers.IntegerField(
        required=True, allow_null=False)
    page_size = serializers.IntegerField(allow_null=False, required=False)
    cursor = serializers.CharField(
        allow_null=True, required=False, allow_blank=True)
    ordering = serializers.CharField(
        allow_null=False, required=False, allow_blank=True)


class ListRecruitUploadedResponseModel(BaseSerializer):
    message = serializers.CharField(allow_null=True)
    data = ListRecruitSerializers()
    errors = serializers.ListField(
        child=ErrorDetailSerializer(), allow_null=True)


class RecruitExploreResponseModel(BaseSerializer):
    message = serializers.CharField(allow_null=True)
    data = PagingResponseModel(input_serializer=RecruitExploreSerializer)
    errors = serializers.ListField(
        child=ErrorDetailSerializer(), allow_null=True)


class RecruitCountFilterParamsSerializer(BaseSerializer):
    name = serializers.CharField(allow_null=False, required=False)
    employ_code = serializers.CharField(allow_null=False, required=False)
    places = serializers.ListField(
        child=serializers.CharField(), allow_empty=True, required=False
    )
    job_code = serializers.CharField(allow_null=False, required=False)
    skill_codes_1 = serializers.ListField(
        child=serializers.CharField(), allow_empty=True, required=False
    )
    skill_codes_2 = serializers.ListField(
        child=serializers.CharField(), allow_empty=True, required=False
    )
    skill_codes_3 = serializers.ListField(
        child=serializers.CharField(), allow_empty=True, required=False
    )
    payroll_code = serializers.CharField(allow_null=False, required=False)
    payroll_price_from = serializers.CharField(
        allow_null=False, required=False)
    payroll_price_to = serializers.CharField(allow_null=False, required=False)
    language_codes = serializers.ListField(
        child=serializers.CharField(), allow_empty=True, required=False
    )
    show_old_post = serializers.BooleanField(required=True, allow_null=False)
    company_id = serializers.IntegerField(allow_null=False, required=False)
    job_codes = serializers.ListField(
        child=serializers.CharField(), allow_empty=True, required=False
    )
    waiting_flag = serializers.IntegerField(allow_null=False, required=False)
    remote_code = serializers.CharField(allow_null=False, required=False)


class RecruitCountFilterResponseModel(BaseSerializer):
    message = serializers.CharField(allow_null=True)
    data = serializers.IntegerField()
    errors = serializers.ListField(
        child=ErrorDetailSerializer(), allow_null=True)


class SignContractSerializer(BaseModelSerializer):
    host_company = RecruitCompanySerializer(allow_null=True)
    payroll_price = serializers.CharField(allow_null=True)

    class Meta:
        model = RecApply
        fields = ['apply_id', 'job_code', 'employ_code', 'payroll_price',
                  'payroll_code', 'place_code', 'joing_date', 'host_company',
                  'interview_datetime', 'recruit_id', 'recruit_progress_code']


class UpdateInterviewDatetimeSerializer(BaseSerializer):
    interview_datetime = serializers.DateTimeField(
        allow_null=True, required=False)


class AcceptSignSerializer(BaseModelSerializer):

    class Meta:
        model = RecAcceptSign
        fields = "__all__"


class RecruitCompanyInformationSerializer(BaseModelSerializer):
    capital_stock = serializers.CharField(allow_null=True, allow_blank=True)
    capital_stock_curr_code = serializers.CharField(
        allow_null=True, allow_blank=True)

    class Meta:
        model = ComCompany
        fields = ['company_id', 'name', 'logo_image_path', 'pr_image_path1',
                  'pr_image_path2', 'pr_image_path3', 'introduction_url', 'address', 'capital_stock',
                  'employees_type', 'tel', 'international_tel',
                  'contact_mail', 'about_us', 'business_details',
                  'benefits', 'capital_stock_curr_code', 'country_code', 'address_code', 'web_url']


class RecruitmentManagementDetailSerializer(BaseModelSerializer):
    host_company = RecruitCompanyInformationSerializer(
        read_only=True)
    recruit_progress_code = serializers.IntegerField(
        allow_null=True, required=False)
    payroll_price = serializers.DecimalField(
        max_digits=20, decimal_places=3,
        allow_null=True, required=False)
    place_code = serializers.CharField(
        max_length=6, allow_null=True, required=False)
    joing_date = serializers.DateField(
        allow_null=True, required=False)
    interview_datetime = serializers.DateTimeField(
        allow_null=True, required=False)
    waiting_flag = serializers.IntegerField(allow_null=True, required=False)
    total_recruit = serializers.SerializerMethodField()
    similar_recruits = serializers.SerializerMethodField()
    group_id = serializers.IntegerField(
        allow_null=True, required=False)
    apply_id = serializers.SerializerMethodField()
    payroll_price_from = serializers.SerializerMethodField()
    payroll_price_to = serializers.SerializerMethodField()
    payroll_code = serializers.SerializerMethodField()

    class Meta:
        model = RecRecruit
        fields = ['recruit_id', 'title', 'total_recruit', 'catch_copy', 'payroll_price_from',
                  'payroll_price_to', 'payroll_code', 'start_date', 'end_date', 'job_code',
                  'employ_code', 'place_code1', 'place_code2', 'place_code3',
                  'pref_code1', 'pref_code2', 'pref_code3',
                  'country_code',
                  'age_from', 'age_to',
                  'last_academic_code',
                  'language_code1', 'language_level_type1',
                  'language_code2', 'language_level_type2',
                  'experienced_job_code', 'years_of_experience',
                  'skill_job_code1', 'skill_code1', 'skill_level_type1',
                  'skill_job_code2', 'skill_code2', 'skill_level_type2',
                  'skill_job_code3', 'skill_code3', 'skill_level_type3',
                  'content',
                  'sex_type',
                  'licence_code1', 'licence_point1', 'licence_code2',
                  'licence_name1', 'licence_name2', 'licence_name3',
                  'licence_point2', 'licence_code3', 'licence_point3',
                  'recruit_progress_code',
                  'waiting_flag',
                  'host_company',
                  'similar_recruits',
                  'interview_datetime',
                  'payroll_price',
                  'place_code',
                  'joing_date',
                  'group_id',
                  'apply_id',
                  ]

    def get_payroll_price_from(self, obj) -> str:
        value = obj.payroll_price_from
        payroll_code = obj.payroll_code
        if payroll_code is not None and value is not None:
            try:
                user_currency = get_currency_from_request(
                    self.context.get('request'))
                if user_currency is not None:
                    amount = currency_converter.convert(
                        value,
                        payroll_code,
                        user_currency
                    )
                    return str(amount)

                else:
                    return str(value)

            except Exception as e:
                return str(value)
        return str(value)

    def get_payroll_price_to(self, obj) -> str:
        value = obj.payroll_price_to
        payroll_code = obj.payroll_code
        if payroll_code is not None and value is not None:
            try:
                user_currency = get_currency_from_request(
                    self.context.get('request'))
                if user_currency is not None:
                    amount = currency_converter.convert(
                        value,
                        payroll_code,
                        user_currency
                    )
                    return str(amount)

                else:
                    return str(value)

            except Exception as e:
                return str(value)
        return str(value)

    def get_payroll_code(self, obj) -> str:
        payroll_code = obj.payroll_code
        if payroll_code is not None:
            try:
                user_currency = get_currency_from_request(
                    self.context.get('request'))
                if user_currency is not None:
                    return user_currency
                else:
                    return payroll_code
            except Exception as e:
                return ""
        else:
            return ""

    def get_apply_id(self, obj) -> int:
        user = self.context['request'].user
        rec_apply = RecApply.objects.filter(
            recruit_id=obj.recruit_id,
            engineer_id=user.user_id
        ).first()

        if rec_apply:
            return rec_apply.apply_id
        return None

    def get_total_recruit(self, obj) -> int:
        return RecApply.objects.filter(
            recruit_id=obj.recruit_id,
            expiry_date__gte=timezone.now(),
            recruit_progress_code__gte=RecruitProgressCode.APPLICATION.value).count()

    @swagger_serializer_method(serializer_or_field=RecRecruitSerializer(many=True))
    def get_similar_recruits(self, obj):
        similar_recruits = RecRecruit.objects.filter(
            job_code=obj.job_code,
        ).exclude(recruit_id=obj.recruit_id)
        return RecRecruitSerializer(similar_recruits, many=True).data


class RecruitGetContractDetailsSerializer(BaseModelSerializer):
    host_company_id = serializers.IntegerField(
        source='host_company.company_id', read_only=True, allow_null=True)
    host_company_name = serializers.CharField(
        source='host_company.name', read_only=True, allow_null=True)
    host_agent_first_name = serializers.CharField(
        source='host_agent.first_name', read_only=True, allow_null=True)
    host_agent_last_name = serializers.CharField(
        source='host_agent.last_name', read_only=True, allow_null=True)
    host_agent_accept_sign_path = serializers.SerializerMethodField()
    support_company_id = serializers.IntegerField(
        source='support_company.company_id', read_only=True, allow_null=True)
    support_company_name = serializers.CharField(
        source='support_company.name', read_only=True, allow_null=True)
    support_agent_first_name = serializers.CharField(
        source='support_agent.first_name', read_only=True, allow_null=True)
    support_agent_last_name = serializers.CharField(
        source='support_agent.last_name', read_only=True, allow_null=True)
    support_agent_accept_sign_path = serializers.SerializerMethodField()
    agency_company_id = serializers.IntegerField(
        source='agency_company.company_id', read_only=True, allow_null=True)
    agency_company_name = serializers.CharField(
        source='agency_company.name', read_only=True, allow_null=True)
    agency_agent_first_name = serializers.CharField(
        source='agency_agent.first_name', read_only=True, allow_null=True)
    agency_agent_last_name = serializers.CharField(
        source='agency_agent.last_name', read_only=True, allow_null=True)
    agency_agent_accept_sign_path = serializers.SerializerMethodField()
    engineer_id = serializers.IntegerField(
        source='engineer.user_id', read_only=True, allow_null=True)
    engineer_first_name = serializers.CharField(
        source='engineer.first_name', read_only=True, allow_null=True)
    engineer_last_name = serializers.CharField(
        source='engineer.last_name', read_only=True, allow_null=True)
    engineer_accept_sign_path = serializers.SerializerMethodField()

    class Meta:
        model = RecApply
        fields = ['apply_id',
                  'host_company_id',
                  'host_company_name',
                  'host_agent_first_name',
                  'host_agent_last_name',
                  'host_agent_accept_sign_path',
                  'support_company_id',
                  'support_company_name',
                  'support_agent_first_name',
                  'support_agent_last_name',
                  'support_agent_accept_sign_path',
                  'agency_company_id',
                  'agency_company_name',
                  'agency_agent_first_name',
                  'agency_agent_last_name',
                  'agency_agent_accept_sign_path',
                  'engineer_id',
                  'engineer_first_name',
                  'engineer_last_name',
                  'engineer_accept_sign_path'
                  ]

    def get_host_agent_accept_sign_path(self, obj) -> str:
        try:
            apply_id = obj.apply_id
            data = RecAcceptSign.objects.get(
                apply_id=apply_id,
                user=obj.host_agent
            )
            return data.accept_sign_path
        except Exception as e:
            return None

    def get_support_agent_accept_sign_path(self, obj) -> str:
        try:
            apply_id = obj.apply_id
            data = RecAcceptSign.objects.get(
                apply_id=apply_id,
                user=obj.support_agent
            )
            return data.accept_sign_path
        except Exception as e:
            return None

    def get_agency_agent_accept_sign_path(self, obj) -> str:
        try:
            apply_id = obj.apply_id
            data = RecAcceptSign.objects.get(
                apply_id=apply_id,
                user=obj.agency_agent
            )
            return data.accept_sign_path
        except Exception as e:
            return None

    def get_engineer_accept_sign_path(self, obj) -> str:
        try:
            apply_id = obj.apply_id
            data = RecAcceptSign.objects.get(
                apply_id=apply_id,
                user=obj.engineer
            )
            return data.accept_sign_path
        except Exception as e:
            return None


class RecruitmentManagementDetailsResponseModel(BaseSerializer):
    message = serializers.CharField(allow_null=True)
    data = RecruitmentManagementDetailSerializer()
    errors = serializers.ListField(
        child=ErrorDetailSerializer(), allow_null=True)


class GetContractParamsSerializer(BaseSerializer):
    recruit_id = serializers.IntegerField(allow_null=False, required=True)


class ContractDetailsSerializer(BaseSerializer):
    accept_sign = AcceptSignSerializer(allow_null=True)


class GetContractDetailsResponseModel(BaseSerializer):
    message = serializers.CharField(allow_null=True)
    data = ContractDetailsSerializer(allow_null=True)
    errors = serializers.ListField(
        child=ErrorDetailSerializer(), allow_null=True)


class SignContractResponseModel(BaseSerializer):
    message = serializers.CharField(allow_null=True)
    data = SignContractSerializer(allow_null=True)
    errors = serializers.ListField(
        child=ErrorDetailSerializer(), allow_null=True)


class UpdateInterviewDatetimeResponseModel(BaseSerializer):
    message = serializers.CharField(allow_null=True)
    data = AcceptSignSerializer(allow_null=True)
    errors = serializers.ListField(
        child=ErrorDetailSerializer(), allow_null=True)


class ListTopFeaturedJobsResponseModel(BaseSerializer):
    message = serializers.CharField(allow_null=True)
    data = FeaturedJobSerializer(many=True)
    errors = serializers.ListField(
        child=ErrorDetailSerializer(), allow_null=True)


class RecruitContractDetailsResponseModel(BaseSerializer):
    message = serializers.CharField(allow_null=True)
    errors = serializers.ListField(
        child=ErrorDetailSerializer(), allow_null=True)
    data = RecruitGetContractDetailsSerializer()
