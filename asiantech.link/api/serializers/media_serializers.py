from api.models.company import ComCompany
from rest_framework import serializers
from api.models.media import MediaImage
from utils.responses import ErrorDetailSerializer
from django.contrib.auth import get_user_model
from utils.logger_mixins import *
from api.models.rec import RecAcceptSign
User = get_user_model()


class UserMediaSerializer(BaseModelSerializer):

    class Meta:
        model = User
        fields = ['profile_image_path', 'passport_image_path']


class CompanyMediaSerializer(BaseModelSerializer):
    class Meta:
        model = ComCompany
        fields = ['logo_image_path', 'pr_image_path1',
                  'pr_image_path2', 'pr_image_path3']


class ContractMediaSerializer(BaseModelSerializer):

    class Meta:
        model = RecAcceptSign
        fields = ['accept_sign_path', 'user', 'apply']


class ImageSerializer(BaseModelSerializer):

    class Meta:
        model = MediaImage
        fields = ['title', 'image', 'uploaded_at',
                  'user', 'type', 'main_folder_path']


# Response
class MediaResponseModel(BaseSerializer):
    message = serializers.CharField()
    data = ImageSerializer()
    errors = serializers.ListField(child=ErrorDetailSerializer())
