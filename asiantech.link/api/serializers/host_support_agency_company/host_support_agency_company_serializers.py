from requests import request
from rest_framework import serializers

from utils.custom_fields import *
from api.models.company import *
from utils.constants import *
from django.contrib.auth import get_user_model
from django.utils import timezone as tz

from django.utils.translation import gettext_lazy as _

from utils.responses import ErrorDetailSerializer
from utils.utils import *
from utils.logger_mixins import *
from drf_yasg.utils import swagger_serializer_method
from api.models.rec import RecApply
from api.models.chat import MapChatGroup, RecChat

User = get_user_model()


class ListRecruitOfHostCompanyParams(BaseSerializer):
    host_company_id = serializers.IntegerField(
        required=True, allow_null=False)
    page_size = serializers.IntegerField(allow_null=False, required=False)
    cursor = serializers.CharField(
        allow_null=True, required=False, allow_blank=True)
    ordering = serializers.CharField(
        allow_null=False, required=False, allow_blank=True)


class SPCompanyRegisteredSerializer(BaseModelSerializer):
    class Meta:
        model = ComCompany
        fields = ['company_id', 'name', 'logo_image_path', 'about_us',
                  'business_details', 'country_code', 'address_code']


class SPListRegisteredCompanyResponseModel(BaseSerializer):
    message = serializers.CharField(allow_null=True)
    errors = serializers.ListField(
        child=ErrorDetailSerializer(), allow_null=True)
    data = SPCompanyRegisteredSerializer(many=True)


class SPRequestInterviewBodySerializer(BaseSerializer):
    host_company_id = serializers.IntegerField(
        required=True, allow_null=False)
    host_company_recruit_id = serializers.IntegerField(
        required=True, allow_null=False)
    engineer_id = serializers.IntegerField(
        required=True, allow_null=False)
    message = serializers.CharField(
        required=True, allow_null=False)


class SPListRecruitOfHostCompanyParams(BaseSerializer):
    host_company_id = serializers.IntegerField(
        required=True, allow_null=False)
    page_size = serializers.IntegerField(allow_null=False, required=False)
    cursor = serializers.CharField(
        allow_null=True, required=False, allow_blank=True)
    ordering = serializers.CharField(
        allow_null=False, required=False, allow_blank=True)


class ManageHostCompanySerializer(BaseModelSerializer):
    message_count = serializers.SerializerMethodField()
    total_applicants = serializers.SerializerMethodField()
    new_applicants = serializers.SerializerMethodField()
    unprocessed_task = serializers.SerializerMethodField()
    last_status_update = serializers.SerializerMethodField()
    active_job_listings = serializers.SerializerMethodField()

    class Meta:
        model = ComCompany
        fields = ['company_id', 'name', 'logo_image_path', 'message_count', 'total_applicants',
                  'new_applicants', 'unprocessed_task', 'last_status_update', 'active_job_listings']

    @swagger_serializer_method(serializer_or_field=serializers.IntegerField)
    def get_message_count(self, obj):
        try:
            request = self.context.get('request')
            user = request.user
            host_company_id = obj.company_id
            host_company_user_id = User.objects.get(
                company_id=host_company_id).user_id

            support_company_id = user.company_id
            list_groups = RecApply.objects.filter(
                host_company_id=host_company_id,
                support_company_id=support_company_id,
            ).values_list('group_id')
            count = 0

            for group in list_groups:
                lasted_chat_id = RecChat.objects.filter(
                    group_id=group
                ).order_by('-chat_id').first()
                if lasted_chat_id:
                    last_chat_of_host_company = MapChatGroup.objects.filter(
                        group_id=group,
                        user_id=host_company_user_id).order_by('-chat_id').first()
                    if last_chat_of_host_company:
                        unread_message_count = RecChat.objects.filter(
                            group_id=group, chat_id__gt=last_chat_of_host_company.chat_id).count()
                        count += unread_message_count
                    else:
                        unread_message_count = RecChat.objects.filter(
                            group_id=group).count()
                        count += unread_message_count

            return count
        except Exception as e:
            return 0

    @swagger_serializer_method(serializer_or_field=serializers.IntegerField)
    def get_total_applicants(self, obj):
        return 0

    @swagger_serializer_method(serializer_or_field=serializers.IntegerField)
    def get_new_applicants(self, obj):
        return 0

    @swagger_serializer_method(serializer_or_field=serializers.IntegerField)
    def get_unprocessed_task(self, obj):
        return 0

    @swagger_serializer_method(serializer_or_field=serializers.DateTimeField)
    def get_last_status_update(self, obj):
        return tz.now()

    @swagger_serializer_method(serializer_or_field=serializers.IntegerField)
    def get_active_job_listings(self, obj):
        return 0


class ListManageHostCompanyParams(BaseSerializer):
    page_size = serializers.IntegerField(allow_null=False, required=False)
    cursor = serializers.CharField(
        allow_null=True, required=False, allow_blank=True)
    sort_by = serializers.CharField(
        allow_null=False, required=False, allow_blank=True)
    progress_status = serializers.CharField(
        allow_null=False, required=False, allow_blank=True)
    search = serializers.CharField(
        allow_null=False, required=False, allow_blank=True)


class ListManageHostCompany(BaseSerializer):
    next = serializers.CharField(allow_null=True)
    previous = serializers.CharField(allow_null=True)
    results = ManageHostCompanySerializer(many=True)


class ListManageHostCompanyResponseModel(BaseSerializer):
    message = serializers.CharField(allow_null=True)
    data = ListManageHostCompany()
    errors = serializers.ListField(
        child=ErrorDetailSerializer(), allow_null=True)
