from utils.currency_converter import *
from api.models.rec import *
from api.serializers.engineers.user_serializers import EngWorkHistory, UserExploreDetailsSerializers, EngLicenseSerializer, EngCareerSerializer
from api.models.rec import <PERSON><PERSON><PERSON><PERSON>er, RecInterestedEngineer
from django.contrib.auth.models import AnonymousUser
from drf_yasg.utils import swagger_serializer_method
from rest_framework import serializers

from utils.custom_fields import *
from api.models.company import *
from utils.constants import *

from django.contrib.auth import get_user_model
from django.utils import timezone as tz
from django.utils.translation import gettext_lazy as _
import phonenumbers
from utils.responses import ErrorDetailSerializer
from utils.utils import *
from utils.logger_mixins import *
from api.models.eng import *
from utils.validators import check_password_valid
from utils.validators import check_email_valid
from api.serializers.engineers.user_serializers import EngLanguageSerializer, EngSkillSerializer, EngHopeSerializer, HopeJobSkillSerializer
from api.models.chat import MapChatGroup, RecChat
from django.db.models import Q
from api.serializers.engineers.user_chat_serializers import UserChatSerializer

User = get_user_model()
currency_converter = CurrencyConverter()


class GeneralCompanySaveFilterParamsSerializer(BaseModelSerializer):
    class Meta:
        model = RecFilter
        # Add the fields you want to exclude here
        exclude = ['created', 'host_agent']


class GeneralCompanyFilterDetailsSerializer(BaseModelSerializer):
    class Meta:
        model = RecFilter
        fields = "__all__"


class GeneralCompanyFilterSerializer(BaseModelSerializer):
    class Meta:
        model = RecFilter
        fields = ['filter_id', 'filter_name']


class GeneralCompanySaveFilterSerializer(BaseModelSerializer):
    class Meta:
        model = RecFilter
        exclude = ['created', 'filter_id']


class GeneralCompanyDeleteFilterSerializer(BaseSerializer):
    filter_id = serializers.IntegerField()


class MainSkillSerializer(BaseSerializer):
    skill_code = serializers.CharField()
    skill_name = serializers.CharField()


class GeneralCompanyExploreUserSerializer(BaseModelSerializer):
    age = serializers.SerializerMethodField()
    languages = EngLanguageSerializer(
        many=True, read_only=True, source='englanguage_set')

    requirements = serializers.SerializerMethodField()
    interested_flag = serializers.SerializerMethodField()
    skills = EngSkillSerializer(
        many=True, read_only=True, source='engskill_set')
    academic_level_name = serializers.SerializerMethodField()
    school_name = serializers.SerializerMethodField()
    exist_apply_id = serializers.SerializerMethodField()
    qualifications = EngLicenseSerializer(
        many=True, read_only=True, source='englicence_set')
    payroll_price_usd = serializers.CharField(
        source='enghope_set.payroll_price_usd', allow_null=True)
    total_score = serializers.IntegerField(read_only=True)
    total_apply_count = serializers.IntegerField(read_only=True)
    total_company_changed_count = serializers.IntegerField(read_only=True)
    job_code_has_worked_recently = serializers.IntegerField(read_only=True)
    total_recruit_progress_code_active = serializers.IntegerField(
        read_only=True)

    current_recruit_progress_code_with_my_company = serializers.IntegerField(
        read_only=True)
    total_match_skill_codes = serializers.IntegerField(read_only=True)
    remote_code_score = serializers.IntegerField(read_only=True)
    salary_score = serializers.IntegerField(read_only=True)
    total_score = serializers.IntegerField(read_only=True)
    main_skill = serializers.SerializerMethodField()
    hope_job_codes = serializers.SerializerMethodField()
    work_histories = EngWorkHistory(
        many=True, read_only=True, source='engcareer_set')
    reason_ai_recommend = serializers.CharField(allow_null=True)
    reason_ai_recommend_ja = serializers.CharField(allow_null=True)
    reason_ai_recommend_vi = serializers.CharField(allow_null=True)

    recruit_id = serializers.IntegerField(allow_null=True)

    class Meta:
        model = User
        fields = ['user_id', 'profile_image_path',
                  'first_name', 'last_name', 'age', 'created',
                  'languages',
                  'last_academic_code',
                  'interested_flag',
                  'skills',
                  'country_code',
                  'academic_level_name',
                  'requirements',
                  'nickname',
                  'school_name',
                  'exist_apply_id',
                  'updated',
                  'qualifications',
                  'payroll_price_usd',
                  'last_login',
                  'total_apply_count',
                  'total_company_changed_count',
                  'job_code_has_worked_recently',
                  'total_recruit_progress_code_active',
                  'current_recruit_progress_code_with_my_company',
                  'total_match_skill_codes',
                  'remote_code_score',
                  'salary_score',
                  'total_score',
                  'sex_type',
                  'address_code',
                  'main_skill',
                  'hope_job_codes',
                  'work_histories',
                  'reason_ai_recommend',

                  'reason_ai_recommend_ja',
                  'reason_ai_recommend_vi',
                  'recruit_id'
                  ]

    @swagger_serializer_method(serializer_or_field=EngHopeSerializer)
    def get_requirements(self, obj):
        try:
            hope = EngHope.objects.get(engineer=obj)
            skills = HopeJobSkill.objects.filter(engineer=obj)

            data = EngHopeSerializer(hope, context=self.context).data
            data['job_skills'] = HopeJobSkillSerializer(skills, many=True).data
            return data
        except Exception as e:
            return None

    def get_interested_flag(self, obj) -> int:
        request = self.context.get('request')
        if isinstance(request.user, AnonymousUser):
            return None
        company_id = request.user.company_id
        try:
            data = RecInterestedEngineer.objects.get(
                host_company_id=company_id, engineer_id=obj.user_id)
            return int(data.interested_flag)
        except RecInterestedEngineer.DoesNotExist:
            return None

    def get_age(self, obj) -> int:
        # calculate age
        return get_age(obj.birth_date)

    def get_academic_level_name(self, obj) -> str:
        if obj.last_academic_code is None:
            return None
        current_language_header = get_language_code_from_header(
            self.context.get('request'))
        return get_academic_type_name(obj.last_academic_code, current_language_header)

    def get_school_name(self, obj) -> str:
        academics = EngAcademic.objects.filter(engineer=obj)
        if academics.exists():
            highest_level = 0
            school_name = None
            for academic in academics:
                if academic.type > highest_level:
                    highest_level = academic.type
                    school_name = academic.school
            return school_name
        return None

    def get_exist_apply_id(self, obj) -> int:
        try:
            request = self.context.get('request')
            if isinstance(request.user, AnonymousUser):
                return None
            company_id = request.user.company_id
            # get newest apply
            apply = RecApply.objects.filter(
                Q(host_company_id=company_id) | Q(support_company_id=company_id), engineer_id=obj.user_id).order_by('-apply_id').first()
            if apply:
                return apply.apply_id
            else:
                return None
        except Exception as e:
            logger.error("error get exist apply id: %s", e)
            return None

    def get_total_apply_count(self, obj) -> int:
        try:
            return RecApply.objects.filter(engineer_id=obj.user_id, recruit_progress_code__in=[
                RecruitProgressCode.INTERVIEW_REQUEST.value,
                RecruitProgressCode.INTERVIEW_SCHEDULING.value,
                RecruitProgressCode.INTERVIEW_DATE_CONFIRMED.value,
                RecruitProgressCode.INTERVIEW_COMPLETED.value,
                RecruitProgressCode.JOB_OFFER.value,
            ]).count()
        except Exception as e:
            return 0

    def get_total_recruit_progress_code_active(self, obj) -> int:
        try:
            return RecApply.objects.filter(engineer_id=obj.user_id, recruit_progress_code__in=[
                RecruitProgressCode.INTERVIEW_REQUEST.value,
                RecruitProgressCode.INTERVIEW_SCHEDULING.value,
                RecruitProgressCode.INTERVIEW_DATE_CONFIRMED.value,
                RecruitProgressCode.INTERVIEW_COMPLETED.value,
                RecruitProgressCode.JOB_OFFER.value,
                RecruitProgressCode.EMPLOYED.value,
                RecruitProgressCode.NOT_PASSED.value,
                RecruitProgressCode.INTERVIEW_WITHDRAWN.value,
                RecruitProgressCode.OFFER_DECLINED.value
            ]).count()
        except Exception as e:
            return 0

    @swagger_serializer_method(serializer_or_field=MainSkillSerializer(many=True))
    def get_main_skill(self, obj) -> list:
        return []

    @swagger_serializer_method(serializer_or_field=serializers.ListField(child=serializers.CharField()))
    def get_hope_job_codes(self, obj) -> list:
        job_codes = list(HopeJobSkill.objects.filter(
            engineer=obj).values_list('job_code', flat=True))
        # remove duplicate
        job_codes = list(set(job_codes))
        return job_codes


class GeneralCompanyUserSerializer(BaseModelSerializer):
    email = serializers.EmailField()

    class Meta:
        model = User
        fields = ['email', 'password', 'first_name', 'last_name']


class GeneralCompanySerializer(BaseModelSerializer):
    contact_mail = serializers.CharField(max_length=200)
    web_url = serializers.CharField(
        allow_null=True, allow_blank=True, max_length=255)
    capital_stock = serializers.CharField(allow_null=True, allow_blank=True)
    capital_stock_curr_code = serializers.CharField(
        allow_null=True, allow_blank=True)
    accepting_fee = serializers.CharField(allow_null=True, allow_blank=True)
    accepting_fee_curr_code = serializers.CharField(
        allow_null=True, allow_blank=True)
    support_outsourcing_fee = serializers.CharField(
        allow_null=True, allow_blank=True)
    support_outsourcing_fee_curr_code = serializers.CharField(
        allow_null=True, allow_blank=True)
    support = serializers.CharField(allow_null=True, max_length=5000)

    class Meta:
        model = ComCompany
        fields = ['name',
                  'web_url', 'employees_type',
                  'country_code', 'address_code', 'address', 'tel', 'contact_mail', 'international_tel',
                  'capital_stock', 'capital_stock_curr_code', 'logo_image_path',
                  'accepting_fee', 'accepting_fee_curr_code',
                  'support_outsourcing_fee', 'support_outsourcing_fee_curr_code',
                  'support'
                  ]

    def validate(self, attrs):
        # check url format
        web_url = attrs.get('web_url')
        if web_url is not None:
            if not check_url_valid(web_url):
                raise serializers.ValidationError(
                    {'web_url': _('Invalid url format.')})
        # check email format
        contact_mail = attrs.get('contact_mail')
        if contact_mail is not None:
            if not check_email_valid(contact_mail, max_length=100):
                raise serializers.ValidationError(
                    {'contact_mail': _('Invalid email format.')})
        return super().validate(attrs)


class GeneralCompanyRegisterSerializer(BaseSerializer):
    user = GeneralCompanyUserSerializer()
    company = GeneralCompanySerializer()
    user_type = serializers.IntegerField()

    def create(self, validated_data):

        # Create company
        company_data = validated_data.pop('company')
        company_data['user_type'] = validated_data.get('user_type')
        company_data['status'] = CompanyStatus.PRIVATE.value
        company = ComCompany.objects.create(**company_data)

        # Create user
        user_data = validated_data.pop('user')
        user_data['user_type'] = validated_data.get('user_type')
        user_data['auth_type'] = AuthType.IN_PROGRESS.value
        user_data['job_status'] = JobStatus.PREPARING.value
        user_data['company_id'] = company.company_id
        email = user_data['email']
        user_data.pop('email')
        user = User(email=email, username=email, **user_data)
        user.set_password(user_data['password'])
        user.deleted = 0
        user.created = tz.now()
        user.updated = tz.now()
        user.save()

        return {
            'user': user,
            'company': company
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)

    def validate(self, data):
        # check password
        user_data = data.get('user')
        password = user_data.get('password')
        is_valid, error = check_password_valid(password)
        if not is_valid:
            raise serializers.ValidationError({'password': error})

        # check country code
        company_data = data.get('company')
        country_code = company_data.get('country_code', None)

        if country_code and check_country_code_valid(country_code) is False:
            raise serializers.ValidationError(
                {'country_code': _('Invalid country code.')})

        # check address code
        address_code = company_data.get('address_code', None)
        if address_code and check_address_code_valid(address_code) is False:
            raise serializers.ValidationError(
                {'address_code': _('Invalid address code.')})

        # check tel
        tel = company_data.get('international_tel')+company_data.get('tel')
        if tel is not None:
            try:

                phone_number = phonenumbers.parse(tel, keep_raw_input=True)
                if not phonenumbers.is_valid_number(phone_number):
                    raise serializers.ValidationError({
                        'tel': _('Invalid phone number.'),
                    })
            except phonenumbers.NumberParseException:
                raise serializers.ValidationError({
                    'tel': _('Invalid phone number format.'),
                })
        return data


class GeneralCompanyUpdateSerializer(BaseModelSerializer):
    web_url = serializers.CharField(allow_null=True)
    introduction_url = serializers.CharField(allow_null=True, allow_blank=True)
    contact_mail = serializers.EmailField(allow_null=True)
    support = serializers.CharField(allow_null=True, max_length=5000)
    capital_stock = serializers.CharField(allow_null=True)
    capital_stock_curr_code = serializers.CharField(
        allow_null=True, allow_blank=True)
    accepting_fee = serializers.CharField(allow_null=True, allow_blank=True)
    support_outsourcing_fee = serializers.CharField(
        allow_null=True, allow_blank=True)
    support_outsourcing_fee_curr_code = serializers.CharField(
        allow_null=True)
    accepting_fee_curr_code = serializers.CharField(
        allow_null=True)

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)

    class Meta:
        model = ComCompany
        fields = ['company_id', 'name', 'introduction_url', 'employees_type',
                  'country_code', 'address_code', 'address', 'tel',
                  'contact_mail', 'about_us', 'business_details', 'web_url',
                  'capital_stock',
                  'capital_stock_curr_code',
                  'accepting_fee', 'accepting_fee_curr_code',
                  'support_outsourcing_fee', 'support_outsourcing_fee_curr_code',
                  'support',
                  'benefits',
                  'international_tel', 'working_hours_from', 'working_hours_to'

                  ]

    def validate(self, data):
        contact_email = data.get("contact_mail", None)
        introduction_url = data.get("introduction_url", None)
        web_url = data.get("web_url", None)
        capital_stock = data.get("capital_stock", None)
        data["updated"] = tz.now()
        accepting_fee = data.get("accepting_fee", None)
        support_outsourcing_fee = data.get("support_outsourcing_fee", None)
        support = data.get("support", None)

        if contact_email is None:
            data.pop("contact_mail")
        if introduction_url is None:
            data.pop("introduction_url")
        if web_url is None:
            data.pop("web_url")

        if capital_stock is None:
            data.pop("capital_stock")

        # check web url
        if web_url is not None:
            if not check_url_valid(web_url):
                raise serializers.ValidationError(
                    {'web_url': _('Invalid url format.')})
        if introduction_url is not None:
            if introduction_url == "":
                pass
            elif not check_url_valid(introduction_url):
                raise serializers.ValidationError(
                    {'introduction_url': _('Invalid url format.')})

        # check country code
        country_code = data.get('country_code')
        if country_code is not None:

            if check_country_code_valid(country_code) is False:
                raise serializers.ValidationError(
                    {'country_code': _('Invalid country code.')})

        # check address code
        address_code = data.get('address_code')
        if address_code is not None:
            if check_address_code_valid(address_code) is False:
                raise serializers.ValidationError(
                    {'address_code': _('Invalid address code.')})

        # check tel
        tel = data.get('tel')
        international_tel = data.get("international_tel", None)
        if tel is not None and international_tel is not None:
            number = international_tel+tel
            try:

                phone_number = phonenumbers.parse(number, keep_raw_input=True)
                if not phonenumbers.is_valid_number(phone_number):
                    raise serializers.ValidationError({
                        'tel': _('Invalid phone number.'),
                    })
            except phonenumbers.NumberParseException:
                raise serializers.ValidationError({
                    'tel': _('Invalid phone number format.'),
                })

        # check fee code
        accepting_fee_curr_code = data.get('accepting_fee_curr_code', None)
        support_outsourcing_fee_curr_code = data.get(
            'support_outsourcing_fee_curr_code', None)

        if accepting_fee_curr_code is not None:
            if check_currency_code_valid(accepting_fee_curr_code) is False:
                raise serializers.ValidationError(
                    {'accepting_fee_curr_code': _('Invalid currency code.')})
        if accepting_fee_curr_code is None:
            data.pop("accepting_fee_curr_code", None)
        if support_outsourcing_fee_curr_code is not None:
            if check_currency_code_valid(support_outsourcing_fee_curr_code) is False:
                raise serializers.ValidationError(
                    {'support_outsourcing_fee_curr_code': _('Invalid currency code.')})
        if support_outsourcing_fee_curr_code is None:
            data.pop("support_outsourcing_fee_curr_code", None)
        if accepting_fee is not None:
            if accepting_fee == "":
                data['accepting_fee'] = None
        else:
            data.pop("accepting_fee", None)
        if support_outsourcing_fee is not None:
            if support_outsourcing_fee == "":
                data['support_outsourcing_fee'] = None
        else:
            data.pop("support_outsourcing_fee", None)
        if support is None:
            data.pop("support", None)

        return data


class GeneralCompanyApplyDetailsSerializer(BaseModelSerializer):
    payroll_price = serializers.SerializerMethodField()
    payroll_code = serializers.SerializerMethodField()

    class Meta:
        model = RecApply
        fields = "__all__"

    def get_payroll_price(self, obj) -> str:
        request = self.context.get('request')
        if request is not None and request.user.user_type == UserType.ENGINEER.value:
            return obj.payroll_price
        if obj.payroll_code is not None and obj.payroll_price is not None:
            try:
                user_currency = get_currency_from_request(
                    self.context.get('request'))
                if user_currency is not None:
                    amount = currency_converter.convert(
                        obj.payroll_price,
                        obj.payroll_code,
                        user_currency
                    )

                    return str(amount or "")

                else:
                    return str(obj.payroll_price or "")

            except Exception as e:
                return str(obj.payroll_price or "")
        return str(obj.payroll_price or "")

    def get_payroll_code(self, obj) -> str:
        request = self.context.get('request')
        if request is not None and request.user.user_type == UserType.ENGINEER.value:
            return obj.payroll_price
        if obj.payroll_code is not None and obj.payroll_price is not None:
            try:
                user_currency = get_currency_from_request(
                    self.context.get('request'))
                if user_currency is not None:
                    return user_currency
                else:
                    return obj.payroll_code
            except Exception as e:
                return ""
        else:
            return ""


class GeneralCompanyRecruitInfoSerializer(BaseModelSerializer):
    payroll_price_from = serializers.SerializerMethodField()
    payroll_price_to = serializers.SerializerMethodField()
    payroll_code = serializers.SerializerMethodField()

    class Meta:
        model = RecRecruit
        fields = "__all__"

    def get_payroll_price_from(self, obj) -> str:
        if obj.payroll_code is not None and obj.payroll_price_from is not None:
            try:
                user_currency = get_currency_from_request(
                    self.context.get('request'))
                if user_currency is not None:
                    amount = currency_converter.convert(
                        obj.payroll_price_from,
                        obj.payroll_code,
                        user_currency
                    )
                    return str(amount)

                else:
                    return str(obj.payroll_price_from)

            except Exception as e:
                return str(obj.payroll_price_from)
        return str(obj.payroll_price_from)

    def get_payroll_price_to(self, obj) -> str:
        if obj.payroll_code is not None and obj.payroll_price_to is not None:
            try:
                user_currency = get_currency_from_request(
                    self.context.get('request'))
                if user_currency is not None:
                    amount = currency_converter.convert(
                        obj.payroll_price_to,
                        obj.payroll_code,
                        user_currency
                    )
                    return str(amount)

                else:
                    return str(obj.payroll_price_to)

            except Exception as e:
                return str(obj.payroll_price_to)
        return str(obj.payroll_price_to)

    def get_payroll_code(self, obj) -> str:
        if obj.payroll_code is not None:
            try:
                user_currency = get_currency_from_request(
                    self.context.get('request'))
                if user_currency is not None:
                    return user_currency
                else:
                    return obj.payroll_code
            except Exception as e:
                return ""
        else:
            return ""


class GeneralCompanyPRCommentSerializer(BaseModelSerializer):

    class Meta:
        model = MapEngAgc
        fields = ['introduction_pr', 'create_datetime', 'map_id',
                  'agency_company', 'agency_agent', 'update_datetime']


class GeneralCompanyCompareDetailsSerializer(BaseSerializer):
    description = serializers.SerializerMethodField()
    point = serializers.SerializerMethodField()
    salary_matched = serializers.SerializerMethodField()
    work_location_matched = serializers.SerializerMethodField()
    employ_code_matched = serializers.SerializerMethodField()
    job_skills_matched = serializers.SerializerMethodField()

    def get_description(self, obj) -> str:
        point = self.get_point(obj)
        text = ""
        if point <= 50:
            text = _("compare_content_1")
        elif point == 75:
            text = _("compare_content_2")
        elif point == 100:
            text = _("compare_content_3")
        return text

    def get_point(self, obj) -> int:
        point = 0
        if self.get_salary_matched(obj):
            point += 25
        if self.get_work_location_matched(obj):
            point += 25
        if self.get_employ_code_matched(obj):
            point += 25
        if self.get_job_skills_matched(obj):
            point += 25
        return point

    def get_salary_matched(self, obj) -> bool:
        try:
            recruit = obj['recruit']
            engineer = obj['engineer']
            requirement = EngHope.objects.get(engineer=engineer)
            engineer_payroll = requirement.payroll_price
            if engineer_payroll is None:
                engineer_payroll = 0
            engineer_payroll_code = requirement.payroll_code
            if engineer_payroll_code is None:
                engineer_payroll_code = "usd"

            engineer_payroll = currency_converter.convert(
                engineer_payroll, engineer_payroll_code, 'usd')
            recruit_payroll_from = recruit.payroll_price_from
            recruit_payroll_to = recruit.payroll_price_to
            if recruit_payroll_from is None:
                recruit_payroll_from = 0
            if recruit_payroll_to is None:
                recruit_payroll_to = 0
            recruit_payroll_code = recruit.payroll_code
            if recruit_payroll_code is None:
                recruit_payroll_code = "usd"
            recruit_payroll_from = currency_converter.convert(
                recruit_payroll_from, recruit_payroll_code, 'usd')
            recruit_payroll_to = currency_converter.convert(
                recruit_payroll_to, recruit_payroll_code, 'usd')

            if recruit_payroll_from <= engineer_payroll <= recruit_payroll_to:
                return True
            return False
        except Exception as e:
            return False

    def get_work_location_matched(self, obj) -> bool:
        try:
            recruit = obj['recruit']
            engineer = obj['engineer']
            requirement = EngHope.objects.get(engineer=engineer)
            recruit_place_codes = [recruit.place_code1,
                                   recruit.place_code2, recruit.place_code3]
            recruit_place_codes = [
                code for code in recruit_place_codes if code is not None]
            engineer_place_codes = [requirement.place_code1,
                                    requirement.place_code2, requirement.place_code3]
            engineer_place_codes = [
                code for code in engineer_place_codes if code is not None]
            if len(set(recruit_place_codes).intersection(engineer_place_codes)) > 0:
                return True
            return False
        except Exception as e:
            return False

    def get_employ_code_matched(self, obj) -> bool:
        try:
            recruit = obj['recruit']
            engineer = obj['engineer']
            requirement = EngHope.objects.get(engineer=engineer)
            if requirement.employ_code == recruit.employ_code:
                return True
            return False
        except Exception as e:
            return False

    def get_job_skills_matched(self, obj) -> bool:
        try:
            recruit = obj['recruit']
            engineer = obj['engineer']
            hope_job_skills = HopeJobSkill.objects.filter(engineer=engineer)
            job_skills = [skill.skill_code for skill in hope_job_skills]
            job_skills = [code for code in job_skills if code is not None]
            recruit_job_skills = [recruit.skill_code1,
                                  recruit.skill_code2, recruit.skill_code3]
            recruit_job_skills = [
                code for code in recruit_job_skills if code is not None]
            if len(set(job_skills).intersection(recruit_job_skills)) > 0:
                return True
            return False
        except Exception as e:
            return False


class GeneralCompanyGetApplyDetailsSerializer(BaseSerializer):
    apply = GeneralCompanyApplyDetailsSerializer()
    recruit = GeneralCompanyRecruitInfoSerializer()
    engineer = serializers.SerializerMethodField()
    pr_comments = GeneralCompanyPRCommentSerializer(many=True)
    compare_details = serializers.SerializerMethodField()
    host_company_address = serializers.CharField(
        source='recruit.host_company.address', read_only=True, allow_null=True)
    host_company_contact_email = serializers.CharField(
        source='recruit.host_company.contact_mail', read_only=True, allow_null=True)
    support_company_contact_email = serializers.CharField(
        source='recruit.support_company.contact_mail', read_only=True, allow_null=True)
    agency_company_contact_email = serializers.CharField(
        source='recruit.agency_company.contact_mail', read_only=True, allow_null=True)

    @swagger_serializer_method(serializer_or_field=UserExploreDetailsSerializers)
    def get_engineer(self, obj):
        try:
            engineer = obj['apply'].engineer
            return UserExploreDetailsSerializers(engineer, context=self.context).data
        except Exception as e:
            return None

    @swagger_serializer_method(serializer_or_field=GeneralCompanyCompareDetailsSerializer)
    def get_compare_details(self, obj):
        return GeneralCompanyCompareDetailsSerializer({
            "recruit": obj['recruit'],
            "engineer": obj['apply'].engineer
        }, context=self.context).data


class GeneralCompanyParamListUserAppliedCompanySerializer(BaseSerializer):
    recruit_ids = serializers.ListField(
        child=serializers.IntegerField(),
        allow_null=True,
        required=False)
    ordering = serializers.CharField(
        allow_null=False, required=False, allow_blank=True)
    apply_status_filter_codes = serializers.ListField(
        child=serializers.IntegerField(),
        allow_null=True,
        required=False)
    search = serializers.CharField(
        allow_null=False, required=False, allow_blank=True)
    host_company_id = serializers.IntegerField(
        allow_null=True, required=False)
    # Add pagination parameters
    page_size = serializers.IntegerField(required=False)
    page = serializers.IntegerField(required=False)


class GeneralCompanyGetContractDetailsSerializer(BaseModelSerializer):
    host_company_id = serializers.IntegerField(
        source='host_company.company_id', read_only=True, allow_null=True)
    host_company_name = serializers.CharField(
        source='host_company.name', read_only=True, allow_null=True)
    host_agent_first_name = serializers.CharField(
        source='host_agent.first_name', read_only=True, allow_null=True)
    host_agent_last_name = serializers.CharField(
        source='host_agent.last_name', read_only=True, allow_null=True)
    host_agent_accept_sign_path = serializers.SerializerMethodField()
    support_company_id = serializers.IntegerField(
        source='support_company.company_id', read_only=True, allow_null=True)
    support_company_name = serializers.CharField(
        source='support_company.name', read_only=True, allow_null=True)
    support_agent_first_name = serializers.CharField(
        source='support_agent.first_name', read_only=True, allow_null=True)
    support_agent_last_name = serializers.CharField(
        source='support_agent.last_name', read_only=True, allow_null=True)
    support_agent_accept_sign_path = serializers.SerializerMethodField()
    agency_company_id = serializers.IntegerField(
        source='agency_company.company_id', read_only=True, allow_null=True)
    agency_company_name = serializers.CharField(
        source='agency_company.name', read_only=True, allow_null=True)
    agency_agent_first_name = serializers.CharField(
        source='agency_agent.first_name', read_only=True, allow_null=True)
    agency_agent_last_name = serializers.CharField(
        source='agency_agent.last_name', read_only=True, allow_null=True)
    agency_agent_accept_sign_path = serializers.SerializerMethodField()
    engineer_id = serializers.IntegerField(
        source='engineer.user_id', read_only=True, allow_null=True)
    engineer_first_name = serializers.CharField(
        source='engineer.first_name', read_only=True, allow_null=True)
    engineer_last_name = serializers.CharField(
        source='engineer.last_name', read_only=True, allow_null=True)
    engineer_accept_sign_path = serializers.SerializerMethodField()

    class Meta:
        model = RecApply
        fields = ['apply_id',
                  'host_company_id',
                  'host_company_name',
                  'host_agent_first_name',
                  'host_agent_last_name',
                  'host_agent_accept_sign_path',
                  'support_company_id',
                  'support_company_name',
                  'support_agent_first_name',
                  'support_agent_last_name',
                  'support_agent_accept_sign_path',
                  'agency_company_id',
                  'agency_company_name',
                  'agency_agent_first_name',
                  'agency_agent_last_name',
                  'agency_agent_accept_sign_path',
                  'engineer_id',
                  'engineer_first_name',
                  'engineer_last_name',
                  'engineer_accept_sign_path'
                  ]

    def get_host_agent_accept_sign_path(self, obj) -> str:
        try:
            apply_id = obj.apply_id
            data = RecAcceptSign.objects.get(
                apply_id=apply_id,
                user=obj.host_agent
            )
            return data.accept_sign_path
        except Exception as e:
            return None

    def get_support_agent_accept_sign_path(self, obj) -> str:
        try:
            apply_id = obj.apply_id
            data = RecAcceptSign.objects.get(
                apply_id=apply_id,
                user=obj.support_agent
            )
            return data.accept_sign_path
        except Exception as e:
            return None

    def get_agency_agent_accept_sign_path(self, obj) -> str:
        try:
            apply_id = obj.apply_id
            data = RecAcceptSign.objects.get(
                apply_id=apply_id,
                user=obj.agency_agent
            )
            return data.accept_sign_path
        except Exception as e:
            return None

    def get_engineer_accept_sign_path(self, obj) -> str:
        try:
            apply_id = obj.apply_id
            data = RecAcceptSign.objects.get(
                apply_id=apply_id,
                user=obj.engineer
            )
            return data.accept_sign_path
        except Exception as e:
            return None


class GeneralCompanyInterviewDataSerializer(BaseModelSerializer):
    engineer_first_name = serializers.CharField(
        source='engineer.first_name', read_only=True, allow_null=True)
    engineer_last_name = serializers.CharField(
        source='engineer.last_name', read_only=True, allow_null=True)
    engineer_nick_name = serializers.CharField(
        source='engineer.nickname', read_only=True, allow_null=True)

    class Meta:
        model = RecApply
        fields = [
            'apply_id',
            'interview_datetime',
            'host_company',
            'host_agent',
            'engineer_first_name',
            'engineer_last_name',
            'engineer_nick_name',
        ]


class GeneralCompanyUserAppliedCompanySerializer(BaseModelSerializer):
    
    age = serializers.SerializerMethodField()
    payroll_price_usd = serializers.CharField(read_only=True, allow_null=True)
    total_recruit_progress_code_active = serializers.IntegerField(read_only=True)
    languages = EngLanguageSerializer(many=True, read_only=True, source='englanguage_set')
    interested_flag = serializers.SerializerMethodField()
    skills = EngSkillSerializer(many=True, read_only=True, source='engskill_set')
    academic_level_name = serializers.SerializerMethodField()
    school_name = serializers.SerializerMethodField()
    exist_apply_id = serializers.SerializerMethodField()
    qualifications = EngLicenseSerializer(many=True, read_only=True, source='englicence_set')
    total_score = serializers.IntegerField(read_only=True)
    total_apply_count = serializers.IntegerField(read_only=True)
    total_company_changed_count = serializers.SerializerMethodField()
    job_code_has_worked_recently = serializers.IntegerField(read_only=True)  # From query_helper annotation
    current_recruit_progress_code_with_my_company = serializers.SerializerMethodField()
    total_match_skill_codes = serializers.IntegerField(read_only=True)
    remote_code_score = serializers.IntegerField(read_only=True)
    salary_score = serializers.IntegerField(read_only=True)
    main_skill = serializers.SerializerMethodField()
    hope_job_codes = serializers.SerializerMethodField()
    work_histories = EngWorkHistory(many=True, read_only=True, source='engcareer_set')
    under_selection_count = serializers.SerializerMethodField()
    requirements = serializers.SerializerMethodField()

    class Meta:
        model = User
        fields = [
            'user_id', 'profile_image_path', 'first_name', 'last_name', 'age',
            'languages', 'last_academic_code', 'interested_flag', 'skills', 'country_code',
            'academic_level_name', 'nickname', 'school_name', 'exist_apply_id',
            'updated', 'qualifications', 'payroll_price_usd', 'last_login', 'total_apply_count',
            'total_company_changed_count', 'job_code_has_worked_recently', 'total_recruit_progress_code_active',
            'current_recruit_progress_code_with_my_company', 'total_match_skill_codes', 'remote_code_score',
            'salary_score', 'total_score', 'sex_type', 'address_code', 'main_skill', 'hope_job_codes',
            'work_histories', 'under_selection_count', 'requirements'
        ]


    def get_age(self, obj) -> int:
        return get_age(obj.birth_date)

    def get_total_company_changed_count(self, obj) -> int:
        return EngCareer.objects.filter(engineer=obj, career_type=1).count()

    @swagger_serializer_method(serializer_or_field=EngHopeSerializer)
    def get_requirements(self, obj):
        try:
            hope = EngHope.objects.filter(engineer=obj).first()
            if not hope:
                return None
            skills = HopeJobSkill.objects.filter(engineer=obj)

            data = EngHopeSerializer(hope, context=self.context).data
            data['job_skills'] = HopeJobSkillSerializer(skills, many=True).data
            return data
        except Exception as e:
            return None

    def get_academic_level_name(self, obj) -> str:
        if obj.last_academic_code is None:
            return None
        current_language_header = get_language_code_from_header(
            self.context.get('request'))
        return get_academic_type_name(obj.last_academic_code, current_language_header)

    def get_school_name(self, obj) -> str:
        try:
            academic = EngAcademic.objects.filter(
                engineer=obj).order_by('-graduation_date').first()
            if academic:
                return academic.school_name
            return None
        except Exception as e:
            return None

    def get_exist_apply_id(self, obj) -> int:
        request = self.context.get('request')
        if isinstance(request.user, AnonymousUser):
            return None
        company_id = request.user.company_id
        try:
            apply = RecApply.objects.filter(
                engineer=obj, host_company_id=company_id
            ).order_by('-created').first()
            return apply.apply_id if apply else None
        except Exception:
            return None

    @swagger_serializer_method(serializer_or_field=MainSkillSerializer(many=True))
    def get_main_skill(self, obj):
        try:
            skills = EngSkill.objects.filter(engineer=obj, skill_type=1)
            return MainSkillSerializer(skills, many=True).data
        except Exception as e:
            return []

    @swagger_serializer_method(serializer_or_field=serializers.ListField())
    def get_hope_job_codes(self, obj):
        try:
            hope_job_skills = HopeJobSkill.objects.filter(engineer=obj)
            return [skill.job_code for skill in hope_job_skills]
        except Exception as e:
            return []

    def get_interested_flag(self, obj) -> int:
        request = self.context.get('request')
        if isinstance(request.user, AnonymousUser):
            return None
        company_id = request.user.company_id
        try:
            data = RecInterestedEngineer.objects.get(
                host_company_id=company_id, engineer_id=obj.user_id)
            return int(data.interested_flag)
        except RecInterestedEngineer.DoesNotExist:
            return None

    def get_under_selection_count(self, obj) -> int:
        """Count of active applications for this engineer"""
        return RecApply.objects.filter(
            engineer=obj,
            recruit_progress_code__in=[1, 2, 3, 4, 5, 6]  # Active progress codes
        ).count()

    def get_current_recruit_progress_code_with_my_company(self, obj) -> int:
        """Get current progress code with the requesting company"""
        request = self.context.get('request')
        if isinstance(request.user, AnonymousUser):
            return None
        company_id = request.user.company_id
        try:
            # Get the most recent application for this engineer and company
            apply = RecApply.objects.filter(
                engineer=obj,
                host_company_id=company_id
            ).order_by('-created').first()
            return apply.recruit_progress_code if apply else None
        except Exception:
            return None



class GeneralCompanyAppliedEngineersSerializer(BaseModelSerializer):
    engineer = GeneralCompanyUserAppliedCompanySerializer()
    title = serializers.SerializerMethodField()
    unread_message_count = serializers.SerializerMethodField()
    recruit_id = serializers.IntegerField()
    group_id = serializers.IntegerField(allow_null=True)
    engineer_id = serializers.IntegerField(allow_null=True)
    host_company_id = serializers.IntegerField(allow_null=True)
    engineer_accept_sign_id = serializers.IntegerField(allow_null=True)
    is_new = serializers.SerializerMethodField()

    class Meta:
        model = RecApply

        fields = ['apply_id', 'recruit_id', 'group_id', 'title', 'engineer_id', 'host_company_id',
                  'progress_update_datetime', 'recruit_progress_code', 'engineer_accept_sign_id', 'interview_datetime', 'is_new', 'unread_message_count', 'engineer', 'created', 'updated']

    def get_title(self, obj):
        return obj.recruit.title

    def get_unread_message_count(self, obj) -> int:
        try:
            request = self.context.get('request')
            user = request.user
            if user:
                group_id = obj.group_id
                if group_id is None:
                    return 0

                map_chat_group = MapChatGroup.objects.filter(
                    group_id=group_id, user_id=user.user_id).first()

                if map_chat_group:
                    last_read_chat_id = map_chat_group.chat_id
                    unread_message_count = RecChat.objects.filter(
                        group_id=group_id, chat_id__gt=last_read_chat_id).count()
                    return unread_message_count
                else:
                    unread_message_count = RecChat.objects.filter(
                        group_id=group_id).count()
                    return unread_message_count
            else:
                return 0
        except Exception as e:
            return 0

    def get_is_new(self, obj) -> bool:
        try:
            data = RecApplyRead.objects.get(pk=obj.recruit_id)
            if data is not None:
                return True
            return False
        except Exception as e:
            return False


class ChatSerializer(BaseModelSerializer):
    class Meta:
        model = RecChat
        fields = "__all__"


class GroupChatSerializer(BaseModelSerializer):
    lasted_message = serializers.SerializerMethodField()
    users = serializers.SerializerMethodField()
    progress_code = serializers.SerializerMethodField()
    engineer = serializers.SerializerMethodField()

    class Meta:
        model = MapChatGroup
        fields = ['id', 'group', 'chat', 'updated',
                  'created', 'lasted_message', 'users', 'progress_code', 'engineer']

    @swagger_serializer_method(serializer_or_field=ChatSerializer)
    def get_lasted_message(self, obj):
        if obj.chat:
            return ChatSerializer(obj.chat).data
        return None

    @swagger_serializer_method(serializer_or_field=UserChatSerializer(many=True))
    def get_users(self, obj):
        data = MapChatGroup.objects.filter(
            group=obj.group).values('user').distinct()
        users = User.objects.filter(user_id__in=data)
        return UserChatSerializer(users, many=True, context={
            "group": obj.group
        }).data

    def get_progress_code(self, obj) -> int:
        try:
            apply = RecApply.objects.filter(group=obj.group).first()
            return apply.recruit_progress_code if apply else None
        except Exception as e:
            return None

    @swagger_serializer_method(serializer_or_field=GeneralCompanyUserAppliedCompanySerializer)
    def get_engineer(self, obj) -> GeneralCompanyUserAppliedCompanySerializer:
        try:
            apply = RecApply.objects.filter(group=obj.group).first()
            return GeneralCompanyUserAppliedCompanySerializer(apply.engineer).data if apply else None
        except Exception as e:
            return None


class GeneralCompanyParamGroupChatParamsSerializer(BaseSerializer):
    page_size = serializers.IntegerField(required=False)
    cursor = serializers.CharField(required=False, allow_blank=True)
    ordering = serializers.CharField(required=False, allow_blank=True)


class GeneralCompanyParamGetContractDetailsSerializer(BaseSerializer):
    apply_id = serializers.IntegerField(required=True)
    host_company_id = serializers.IntegerField(required=False, allow_null=True)


class GeneralCompanyParamGetApplyDetailsSerializer(BaseSerializer):
    apply_id = serializers.IntegerField(required=True)
    host_company_id = serializers.IntegerField(required=False, allow_null=True)


class GeneralCompanyRejectApplySerializer(BaseSerializer):
    apply_id = serializers.IntegerField(required=True)
    host_company_id = serializers.IntegerField(required=False, allow_null=True)


class GeneralCompanyRequestInterviewApplySerializer(BaseSerializer):
    apply_id = serializers.IntegerField(required=True)
    host_company_id = serializers.IntegerField(required=False, allow_null=True)


class GeneralCompanyCalendarInterviewParamsSerializer(BaseSerializer):
    date = serializers.DateField(required=True)
    host_company_id = serializers.IntegerField(required=False, allow_null=True)


class GeneralCompanyUpdateInterviewDateTimeSerializer(BaseSerializer):
    apply_id = serializers.IntegerField(required=True)
    interview_datetime = serializers.DateTimeField(required=True)
    host_company_id = serializers.IntegerField(required=False, allow_null=True)


class GeneralCompanyCalendarInterviewDataSerializer(BaseSerializer):
    date = serializers.DateField(required=True)
    interviews = GeneralCompanyInterviewDataSerializer(many=True)
    host_company_id = serializers.IntegerField(required=False, allow_null=True)


class PagingGeneralCompanyAppliedEngineersResponseModel(BaseSerializer):
    count = serializers.IntegerField()
    next = serializers.CharField(allow_null=True)
    previous = serializers.CharField(allow_null=True)
    results = GeneralCompanyAppliedEngineersSerializer(many=True)


class GeneralCompanyListUserAppliedCompanyResponseModel(BaseSerializer):
    message = serializers.CharField(allow_null=True)
    errors = serializers.ListField(
        child=ErrorDetailSerializer(), allow_null=True)
    data = PagingGeneralCompanyAppliedEngineersResponseModel()


class GeneralCompanyApplyDetailsResponseModel(BaseSerializer):
    message = serializers.CharField(allow_null=True)
    errors = serializers.ListField(
        child=ErrorDetailSerializer(), allow_null=True)
    data = GeneralCompanyGetApplyDetailsSerializer()


class GeneralCompanyCalendarInterviewResponseModel(BaseSerializer):
    message = serializers.CharField(allow_null=True)
    errors = serializers.ListField(
        child=ErrorDetailSerializer(), allow_null=True)
    data = GeneralCompanyInterviewDataSerializer(many=True)


class GeneralCompanyContractDetailsResponseModel(BaseSerializer):
    message = serializers.CharField(allow_null=True)
    errors = serializers.ListField(
        child=ErrorDetailSerializer(), allow_null=True)
    data = GeneralCompanyGetContractDetailsSerializer()


class GeneralCompanyGetContractDetailsResponseModel(BaseSerializer):
    message = serializers.CharField(allow_null=True)
    errors = serializers.ListField(
        child=ErrorDetailSerializer(), allow_null=True)
    data = serializers.DateField()


class GeneralCompanyDetailsSerializer(BaseModelSerializer):
    capital_stock = serializers.CharField(allow_null=True)
    accepting_fee = serializers.CharField(allow_null=True)
    support_outsourcing_fee = serializers.CharField(allow_null=True)

    class Meta:
        model = ComCompany
        fields = "__all__"


class GeneralCompanyResponseModel(BaseSerializer):
    message = serializers.CharField(allow_null=True)
    errors = serializers.ListField(
        child=ErrorDetailSerializer(), allow_null=True)
    data = GeneralCompanyDetailsSerializer()


class PagingGeneralCompanyExploreUserResponseModel(BaseSerializer):
    count = serializers.IntegerField()
    next = serializers.CharField(allow_null=True)
    previous = serializers.CharField(allow_null=True)
    ai_summary = serializers.CharField(allow_null=True)
    ai_summary_ja = serializers.CharField(allow_null=True)
    ai_summary_vi = serializers.CharField(allow_null=True)
    recruit_id = serializers.IntegerField(allow_null=True)
    results = GeneralCompanyExploreUserSerializer(many=True)


class GeneralCompanyListUserExploreResponseModel(BaseSerializer):
    message = serializers.CharField(allow_null=True)
    data = PagingGeneralCompanyExploreUserResponseModel()
    errors = serializers.ListField(
        child=ErrorDetailSerializer(), allow_null=True)


class GeneralCompanyGetUserExploreCountResponseModel(BaseSerializer):
    message = serializers.CharField(allow_null=True)
    data = serializers.IntegerField()
    errors = serializers.ListField(
        child=ErrorDetailSerializer(), allow_null=True)


class GeneralCompanyListFilterResponseModel(BaseSerializer):
    message = serializers.CharField(allow_null=True)
    data = GeneralCompanyFilterSerializer(many=True)
    errors = serializers.ListField(
        child=ErrorDetailSerializer(), allow_null=True)


class GeneralCompanyUpdateFavoriteUserSerializer(BaseSerializer):
    user_id = serializers.IntegerField()
    is_favorite = serializers.BooleanField()


class RequestInterviewSuccessSerializer(BaseSerializer):
    apply_id = serializers.IntegerField()


class GeneralCompanyRequestInterviewApplyResponseModel(BaseSerializer):
    message = serializers.CharField(allow_null=True)
    errors = serializers.ListField(
        child=ErrorDetailSerializer(), allow_null=True)
    data = RequestInterviewSuccessSerializer()


class ListGroupChatSerializer(BaseSerializer):
    next = serializers.CharField(allow_null=True)
    previous = serializers.CharField(allow_null=True)
    results = GroupChatSerializer(many=True)
    total_count = serializers.IntegerField()


class GeneralCompanyListGroupChatResponseModel(BaseSerializer):
    message = serializers.CharField(allow_null=True)
    data = ListGroupChatSerializer()
    errors = serializers.ListField(
        child=ErrorDetailSerializer(), allow_null=True)
