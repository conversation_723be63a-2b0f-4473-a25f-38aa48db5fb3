from rest_framework import serializers
from utils.responses import ErrorDetailSerializer
from utils.logger_mixins import *
from api.serializers.common_serializers import PagingResponseModel
from api.models.email_schedules import *
from django.utils import timezone


class EmailScheduleSerializer(serializers.ModelSerializer):
    class Meta:
        model = EmailSchedules
        fields = "__all__"


class GetEmailScheduleParamsSerializer(BaseSerializer):
    page_size = serializers.IntegerField(required=False)
    cursor = serializers.CharField(required=False, allow_blank=True)
    ordering = serializers.CharField(required=False, allow_blank=True)
    date_from = serializers.DateField(required=False)
    date_to = serializers.DateField(required=False)
    query_search = serializers.CharField(
        required=False, allow_blank=True, allow_null=True)
    created_user = serializers.CharField(
        required=False, allow_blank=True, allow_null=True)


class PagingEmailScheduleSerializer(PagingResponseModel):
    results = EmailScheduleSerializer(many=True)
    next = serializers.CharField(allow_null=True)
    previous = serializers.CharField(allow_null=True)
    total_count = serializers.IntegerField()


class GetEmailScheduleResponseModel(BaseSerializer):
    message = serializers.CharField(allow_null=True)
    data = PagingEmailScheduleSerializer()
    errors = serializers.ListField(
        child=ErrorDetailSerializer(), allow_null=True)


class GetEmailDetailResponseModel(BaseSerializer):
    message = serializers.CharField(allow_null=True)
    data = EmailScheduleSerializer()
    errors = serializers.ListField(
        child=ErrorDetailSerializer(), allow_null=True)

# Create email schedule serializer


class CreateEmailScheduleSerializer(serializers.ModelSerializer):
    type = serializers.IntegerField(required=False, allow_null=True)
    subject = serializers.CharField(required=False, allow_null=True)
    body = serializers.CharField(required=False, allow_null=True)
    # from 0 to 6 (Sunday to Saturday)
    weekday = serializers.CharField(required=False, allow_null=True)
    send_time = serializers.CharField(required=False, allow_null=True)
    send_datetime = serializers.DateTimeField(required=False, allow_null=True)
    is_valid = serializers.IntegerField(required=False, allow_null=True)
    is_repeat = serializers.IntegerField(required=False, allow_null=True)
    target_email = serializers.CharField(required=False, allow_null=True)

    def create(self, validated_data):
        validated_data['is_deleted'] = 0
        validated_data['created'] = timezone.now()
        return EmailSchedules.objects.create(**validated_data)

    def update(self, instance, validated_data):
        instance.type = validated_data.get('type', instance.type)
        instance.subject = validated_data.get('subject', instance.subject)
        instance.body = validated_data.get('body', instance.body)
        if 'weekday' in validated_data:
            instance.weekday = validated_data['weekday']
        else:
            instance.weekday = None
        instance.send_time = validated_data.get(
            'send_time', instance.send_time)
        if 'send_datetime' in validated_data:
            instance.send_datetime = validated_data['send_datetime']
        else:
            instance.send_datetime = None
        instance.is_valid = validated_data.get('is_valid', instance.is_valid)
        instance.is_repeat = validated_data.get(
            'is_repeat', instance.is_repeat)
        instance.save()
        return instance

    class Meta:
        model = EmailSchedules
        fields = ['type', 'subject', 'body', 'weekday',
                  'send_time', 'send_datetime', 'is_valid', 'is_repeat', 'target_email']


class DeleteEmailScheduleListParamsSerializer(serializers.Serializer):
    email_schedule_ids = serializers.ListField(
        child=serializers.IntegerField(), required=True
    )

    def to_internal_value(self, data):
        if 'email_schedule_ids' in data and isinstance(data['email_schedule_ids'], str):
            data = data.copy()  # Create a mutable copy of the data
            data['email_schedule_ids'] = [
                int(x) for x in data['email_schedule_ids'].split(',')]
        return super().to_internal_value(data)
