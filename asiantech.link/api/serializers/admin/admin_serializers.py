import json
import phonenumbers
from django.conf import settings
from rest_framework import serializers
from django.utils.translation import gettext_lazy as _

from utils.custom_fields import *
from api.models.user import User
from api.models.company import *
from drf_yasg.utils import swagger_serializer_method
from utils.responses import ErrorDetailSerializer
from api.models.eng import *
from utils.constants import *
from utils.utils import check_language_code_valid, check_degree_code_valid, check_country_code_valid, check_currency_code_valid, check_address_code_valid
from api.serializers.common_serializers import PagingResponseModel
from datetime import date
from api.models.rec import Rec<PERSON>ilter, RecInterestedEngineer
from django.contrib.auth.models import AnonymousUser
from utils.logger_mixins import *
from django.utils import timezone
from utils.utils import *
from api.models.rec import RecApply
from api.serializers.engineers.user_serializers import ProfileEngHopeSerializer, EngHopeSerializer, HopeJobSkillSerializer, EngSkillSerializer, EngLanguageSerializer


class GetListEngineerParamsSerializer(BaseSerializer):
    page_size = serializers.IntegerField(required=False)
    cursor = serializers.CharField(required=False, allow_blank=True)
    ordering = serializers.CharField(required=False, allow_blank=True)
    register_date_from = serializers.DateTimeField(required=False)
    register_date_to = serializers.DateTimeField(required=False)
    query_search = serializers.CharField(
        required=False, allow_blank=True, allow_null=True)
    created_user = serializers.CharField(
        required=False, allow_blank=True, allow_null=True)
    is_data_policy_accept = serializers.IntegerField(
        required=False, allow_null=True)


class GetListEngineerSerializer(BaseModelSerializer):

    skills = EngSkillSerializer(
        many=True, read_only=True, source='engskill_set')
    last_academic_name = serializers.SerializerMethodField()
    requirements = serializers.SerializerMethodField()
    languages = EngLanguageSerializer(
        many=True, read_only=True, source='englanguage_set')
    is_data_policy_accept = serializers.IntegerField(allow_null=True)
    sales_memo = serializers.CharField(allow_null=True, allow_blank=True)
    employment_status = serializers.IntegerField(allow_null=True)

    class Meta:
        model = User
        fields = ['profile_image_path', 'email', 'first_name', 'last_name',
                  'country_code',
                  'updated',
                  'created',
                  'created_user',
                  'skills',  'user_id', 'last_academic_name',
                  'facebook_url',
                  'linkedin_url',
                  'whatsapp_url',
                  'zalo_id',
                  'requirements',
                  'languages',
                  'employment_status',
                  'sales_memo',
                  'is_data_policy_accept',
                  'tel', 'international_tel'
                  ]

    def get_last_academic_name(self, obj) -> str:
        current_language_header = get_language_code_from_header(
            self.context.get('request'))
        return get_academic_type_name(obj.last_academic_code, current_language_header)

    @swagger_serializer_method(serializer_or_field=EngHopeSerializer)
    def get_requirements(self, obj):
        try:
            hope = EngHope.objects.get(engineer=obj)
            skills = HopeJobSkill.objects.filter(engineer=obj)

            data = EngHopeSerializer(hope, context=self.context).data
            data['job_skills'] = HopeJobSkillSerializer(skills, many=True).data
            return data
        except Exception as e:
            return None


class UpdateEngineerParamsSerializer(BaseSerializer):
    user_id = serializers.IntegerField()


class UpdateEngineerSerializer(BaseModelSerializer):
    employment_status = serializers.IntegerField(
        required=False, allow_null=True)
    sales_memo = serializers.CharField(
        allow_null=True, allow_blank=True, required=False)

    class Meta:
        model = User
        fields = ['employment_status', 'sales_memo']

    def validate(self, attrs):
        if attrs.get('employment_status',  None) is None:
            # REMOVE employment_status from attrs
            attrs.pop('employment_status', None)
        if attrs.get('sales_memo',  None) is None:
            # REMOVE sales_memo from attrs
            attrs.pop('sales_memo', None)
        return attrs


class DeleteEngineersSerializer(BaseSerializer):
    engineer_ids = serializers.ListField(child=serializers.IntegerField())


class ParamsSetTemporaryEngineerSerializer(BaseSerializer):
    engineer_id = serializers.IntegerField()


class ExportUserDataBodySerializer(BaseSerializer):
    engineer_ids = serializers.ListField(child=serializers.IntegerField())
    export_type = serializers.CharField()


class GetListRegistrarResponseModel(BaseSerializer):
    message = serializers.CharField(allow_null=True)
    data = serializers.ListField(child=serializers.CharField())
    errors = serializers.ListField(
        child=ErrorDetailSerializer(), allow_null=True)


class SetTemporaryEngineerSerializer(BaseSerializer):
    access_token = serializers.CharField()
    refresh_token = serializers.CharField()


class SetTemporaryEngineerResponseModel(BaseSerializer):
    message = serializers.CharField(allow_null=True)
    data = SetTemporaryEngineerSerializer()
    errors = serializers.ListField(
        child=ErrorDetailSerializer(), allow_null=True)


class PagingGetListEngineerSerializer(PagingResponseModel):
    results = GetListEngineerSerializer(many=True)
    next = serializers.CharField(allow_null=True)
    previous = serializers.CharField(allow_null=True)
    total_count = serializers.IntegerField()


class GetListEngineerResponseModel(BaseSerializer):
    message = serializers.CharField(allow_null=True)
    data = PagingGetListEngineerSerializer()
    errors = serializers.ListField(
        child=ErrorDetailSerializer(), allow_null=True)
