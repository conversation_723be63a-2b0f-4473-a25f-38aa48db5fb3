from rest_framework import serializers
from django.utils.translation import gettext_lazy as _
from drf_yasg.utils import swagger_serializer_method
from utils.custom_fields import *
from utils.logger_mixins import BaseSerializer, BaseModelSerializer
from api.models.chat import RecChatGroup, RecChat, MapChatGroup
from api.models.user import User
from utils.responses import ErrorDetailSerializer


class MarkReadAllMessagesResponseModel(BaseSerializer):
    """Response model for mark read all messages API"""
    message = serializers.CharField(allow_null=True)
    data = serializers.BooleanField(default=True, allow_null=True)
    errors = serializers.ListField(
        child=ErrorDetailSerializer(), allow_null=True)
