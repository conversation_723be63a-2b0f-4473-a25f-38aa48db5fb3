from rest_framework import serializers
from api.models.rec import RecNotify
from utils.logger_mixins import BaseSerializer


class CountUnreadNotifySerializer(BaseSerializer):
    total_notify = serializers.IntegerField()
    total_unread_notify = serializers.IntegerField()


class CountUnreadNotifyResponseModel(serializers.Serializer):
    message = serializers.CharField(allow_null=True)
    data = CountUnreadNotifySerializer()
    errors = serializers.ListField(
        child=serializers.CharField(), allow_null=True)
