from rest_framework import serializers
from api.models.rec import RecNotify


class NotifySerializer(serializers.ModelSerializer):
    class Meta:
        model = RecNotify
        fields = '__all__'


class ListNotifyResponseModel(serializers.Serializer):
    message = serializers.CharField(allow_null=True)
    data = NotifySerializer(many=True)
    errors = serializers.ListField(
        child=serializers.CharField(), allow_null=True)
