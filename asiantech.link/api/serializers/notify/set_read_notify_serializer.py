from rest_framework import serializers
from utils.logger_mixins import BaseSerializer


class SetReadNotifySerializer(BaseSerializer):
    notify_ids = serializers.ListField(
        child=serializers.IntegerField(), allow_null=True)


class SetReadNotifyResponseModel(serializers.Serializer):
    message = serializers.CharField(allow_null=True)
    data = serializers.BooleanField()
    errors = serializers.ListField(
        child=serializers.CharField(), allow_null=True)
