import json
import phonenumbers
from django.conf import settings
from rest_framework import serializers
from django.utils.translation import gettext_lazy as _

from utils.custom_fields import *
from api.models.user import User
from api.models.company import *
from drf_yasg.utils import swagger_serializer_method
from utils.responses import ErrorDetailSerializer
from api.models.eng import *
from utils.constants import *
from utils.utils import check_language_code_valid, check_degree_code_valid, check_country_code_valid, check_currency_code_valid, check_address_code_valid
from api.serializers.common_serializers import PagingResponseModel
from datetime import date
from api.models.rec import Rec<PERSON>ilter, RecInterestedEngineer
from django.contrib.auth.models import AnonymousUser
from utils.logger_mixins import *
from utils.utils import *
from utils.currency_converter import CurrencyConverter
currency_converter = CurrencyConverter()


class EngineerParamListAgencyCompany(BaseSerializer):
    contact_mail = serializers.CharField(allow_null=True, required=False)


class EngineerListAgencyCompanySerializer(BaseModelSerializer):
    class Meta:
        model = ComCompany
        fields = ['company_id', 'user_type', 'name', 'tel', 'logo_image_path',
                  'contact_mail', 'agent_fee', 'agent_fee_curr_code']


class EngineerListAgencyCompanyResponseModel(BaseSerializer):
    message = serializers.CharField(allow_null=True)
    errors = serializers.ListField(
        child=ErrorDetailSerializer(), allow_null=True)
    data = EngineerListAgencyCompanySerializer(many=True)


class EngineerUpdateAgencyCompanySerializer(BaseSerializer):
    optional_engineer_id = serializers.IntegerField(required=False)
    contact_mail = serializers.EmailField()


class EngineerRemoveAgencyCompanySerializer(BaseSerializer):
    optional_engineer_id = serializers.IntegerField(required=False)
    company_id = serializers.IntegerField()


class EngineerBestCompanySerializer(BaseModelSerializer):

    class Meta:
        model = ComCompany
        fields = ['company_id', 'name', 'logo_image_path']


class EngineerListBestCompanyPaginationSerializer(BaseSerializer):
    next = serializers.CharField(allow_null=True)
    previous = serializers.CharField(allow_null=True)
    results = EngineerBestCompanySerializer(many=True)


class EngineerGetListBestCompaniesResponseModel(BaseSerializer):
    message = serializers.CharField(allow_null=True)
    errors = serializers.ListField(
        child=ErrorDetailSerializer(), allow_null=True)
    data = EngineerListBestCompanyPaginationSerializer()


class EngineerGetListBestCompanyParamsSerializer(BaseSerializer):
    page_size = serializers.IntegerField(allow_null=False, required=False)
    cursor = serializers.CharField(
        allow_null=False, required=False, allow_blank=True)


class EngineerListApplyCompanyInformationSerializer(BaseModelSerializer):
    class Meta:
        model = ComCompany
        fields = ['name', 'logo_image_path']


class EngineerListApplyCompanySerializer(serializers.Serializer):
    apply_id = serializers.IntegerField(allow_null=True, required=False)
    engineer_id = serializers.IntegerField(allow_null=True, required=False)
    recruit_id = serializers.IntegerField(allow_null=True, required=False)
    payroll_price_from = serializers.SerializerMethodField()
    payroll_price_to = serializers.SerializerMethodField()
    payroll_code = serializers.SerializerMethodField()
    host_company_id = serializers.IntegerField(allow_null=True, required=False)
    recruit_progress_code = serializers.IntegerField(
        allow_null=True, required=False)
    # waiting_flag = serializers.IntegerField(
    #     default=0, allow_null=True, required=False)
    updated = serializers.DateTimeField(allow_null=True, required=False)
    progress_update_datetime = serializers.DateTimeField(
        allow_null=True, required=False)
    is_closed = serializers.BooleanField(allow_null=True)
    is_read = serializers.BooleanField(allow_null=True)
    host_company = EngineerListApplyCompanyInformationSerializer(
        allow_null=True, required=False)

    def get_payroll_price_from(self, obj) -> str:
        value = obj.recruit.payroll_price_from
        payroll_code = obj.recruit.payroll_code
        if payroll_code is not None and value is not None:
            try:
                user_currency = get_currency_from_request(
                    self.context.get('request'))
                if user_currency is not None:
                    amount = currency_converter.convert(
                        value,
                        payroll_code,
                        user_currency
                    )
                    return str(amount)

                else:
                    return str(value)

            except Exception as e:
                return str(value)
        return str(value)

    def get_payroll_price_to(self, obj) -> str:
        value = obj.recruit.payroll_price_to
        payroll_code = obj.recruit.payroll_code
        if payroll_code is not None and value is not None:
            try:
                user_currency = get_currency_from_request(
                    self.context.get('request'))
                if user_currency is not None:
                    amount = currency_converter.convert(
                        value,
                        payroll_code,
                        user_currency
                    )
                    return str(amount)

                else:
                    return str(value)

            except Exception as e:
                return str(value)
        return str(value)

    def get_payroll_code(self, obj) -> str:
        payroll_code = obj.recruit.payroll_code
        if payroll_code is not None:
            try:
                user_currency = get_currency_from_request(
                    self.context.get('request'))
                if user_currency is not None:
                    return user_currency
                else:
                    return payroll_code
            except Exception as e:
                return ""
        else:
            return ""


class EngineerListApplyCompanyPaginationSerializer(BaseSerializer):
    next = serializers.CharField(allow_null=True)
    previous = serializers.CharField(allow_null=True)
    results = EngineerListApplyCompanySerializer(many=True)


class EngineerListApplyCompanyResponseModel(BaseSerializer):
    message = serializers.CharField(allow_null=True)
    errors = serializers.ListField(
        child=ErrorDetailSerializer(), allow_null=True)
    data = EngineerListApplyCompanyPaginationSerializer()


class EngineerParamListApplyCompanySerializer(BaseSerializer):
    page_size = serializers.IntegerField(allow_null=False, required=False)
    cursor = serializers.CharField(
        allow_null=False, required=False, allow_blank=True)
    ordering = serializers.CharField(
        allow_null=False, required=False, allow_blank=True)
    apply_status_filter_code = serializers.IntegerField(
        allow_null=True, required=False)


class EngineerUpdateDataPolicySerializer(BaseSerializer):
    is_data_policy_accept = serializers.BooleanField()
    code = serializers.CharField()


class EngineerAgencyCompanySerializer(BaseModelSerializer):
    class Meta:
        model = ComCompany
        fields = ['company_id', 'logo_image_path',
                  'name', 'agent_fee', 'agent_fee_curr_code']


class EngineerAgencyCompanyResponseModel(BaseSerializer):
    message = serializers.CharField(allow_null=True)
    data = EngineerAgencyCompanySerializer(many=True)
    errors = serializers.ListField(
        child=ErrorDetailSerializer(), allow_null=True)
