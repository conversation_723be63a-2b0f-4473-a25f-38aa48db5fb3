import json
import phonenumbers
from django.conf import settings
from rest_framework import serializers
from django.utils.translation import gettext_lazy as _

from utils.custom_fields import *
from api.models.user import User
from api.models.company import *
from drf_yasg.utils import swagger_serializer_method
from utils.responses import ErrorDetailSerializer
from api.models.eng import *
from utils.constants import *
from utils.utils import check_language_code_valid, check_degree_code_valid, check_country_code_valid, check_currency_code_valid, check_address_code_valid
from api.serializers.common_serializers import PagingResponseModel
from datetime import date
from api.models.rec import Rec<PERSON><PERSON>er, RecInterestedEngineer
from django.contrib.auth.models import AnonymousUser
from utils.logger_mixins import *
from django.utils import timezone
from utils.utils import *
from api.models.rec import RecApply
from utils.currency_converter import *
from collections import defaultdict
currency_converter = CurrencyConverter()


class EngAcademicSerializer(BaseModelSerializer):
    engineer_id = serializers.IntegerField()
    type_name = serializers.SerializerMethodField()

    class Meta:
        model = EngAcademic
        fields = ['engineer_id', 'school', 'type',
                  'out_date', 'faculty', 'type_name']

    def get_type_name(self, obj) -> str:
        current_language_header = get_language_code_from_header(
            self.context.get('request'))
        return get_academic_type_name(obj.type, current_language_header)


class UpdateEngAcademicSerializer(BaseModelSerializer):
    class Meta:
        model = EngAcademic
        fields = ['school', 'type', 'out_date', 'faculty']

    def validate(self, attrs):
        # check type
        if not check_degree_code_valid(attrs.get('type')):
            raise serializers.ValidationError({
                'type': _('Invalid degree code.'),
            })
        return attrs


class EngLanguageSerializer(BaseModelSerializer):
    engineer_id = serializers.IntegerField()
    language_name = serializers.SerializerMethodField()
    language_level_name = serializers.SerializerMethodField()

    class Meta:
        model = EngLanguage
        fields = ['engineer_id', 'language_level_type',
                  'language_id', 'language_code', 'language_name', 'language_level_name']

    def get_language_name(self, obj) -> str:
        current_language_header = get_language_code_from_header(
            self.context.get('request'))

        return get_language_model_name(
            obj.language_code, current_language_header)

    def get_language_level_name(self, obj) -> str:

        return get_language_level_model_name(obj.language_level_type)


class UpdateEngLanguageSerializer(BaseModelSerializer):
    class Meta:
        model = EngLanguage
        fields = ['language_level_type', 'language_code']


class EngLicenseSerializer(BaseModelSerializer):
    engineer_id = serializers.IntegerField()
    license_code_name = serializers.SerializerMethodField()
    licence_point = serializers.CharField(allow_null=True)

    class Meta:
        model = EngLicence
        fields = ['engineer_id', 'licence_code',
                  'get_date', 'licence_point', 'licence_name', 'license_code_name']

    def get_license_code_name(self, obj) -> str:
        return get_qualification_name(obj.licence_code)


class UpdateEngLicenseSerializer(BaseModelSerializer):
    class Meta:
        model = EngLicence
        fields = ['licence_code', 'licence_name', 'get_date', 'licence_point']


class EngSkillSerializer(BaseModelSerializer):
    engineer_id = serializers.IntegerField()
    skill_name = serializers.SerializerMethodField()
    level_type_name = serializers.SerializerMethodField()
    temp_name = serializers.CharField(required=False, allow_null=True)
    temp_category_id = serializers.IntegerField(
        required=False, allow_null=True)

    class Meta:
        model = EngSkill
        fields = ['engineer_id', 'skill_code', 'level_type',
                  'skill_id', 'skill_name', 'level_type_name', 'job_code', 'temp_name', 'temp_category_id']

    def get_skill_name(self, obj) -> str:
        if obj.skill_code == Constants.DEFAULT_CUSTOM_SKILL_CODE:
            return obj.temp_name or ""
        else:
            return get_skill_code_name(obj.skill_code)

    def get_level_type_name(self, obj) -> str:
        return get_skill_level_name(obj.level_type)


class UpdateEngSkillSerializer(BaseModelSerializer):
    temp_name = serializers.CharField(required=False, allow_null=True)
    temp_category_id = serializers.IntegerField(
        required=False, allow_null=True)

    class Meta:
        model = EngSkill
        fields = ['skill_code', 'job_code', 'level_type',
                  'temp_name', 'temp_category_id']


class EngCareerJobSkillSerializer(BaseModelSerializer):
    skill_code_name = serializers.SerializerMethodField()
    job_name = serializers.SerializerMethodField()
    level_name = serializers.SerializerMethodField()

    class Meta:
        model = CareerJobSkill
        fields = ['job_code', 'skill_code',
                  'years_of_experience', 'created', 'updated', 'skill_code_name', 'job_name', 'level_name']

    def get_skill_code_name(self, obj) -> str:
        return get_skill_code_name(obj.skill_code)

    def get_job_name(self, obj) -> str:
        language_header = get_language_code_from_header(
            self.context.get('request'))
        return get_job_code_name(obj.job_code, language_header)

    def get_level_name(self, obj) -> str:
        return map_skill_experiences_to_name(obj.years_of_experience)


class EngCareerSerializer(BaseModelSerializer):
    engineer_id = serializers.IntegerField()
    career_job_skills = EngCareerJobSkillSerializer(
        many=True, read_only=True, source='careerjobskill_set')
    job_code = serializers.CharField(required=False, allow_null=True)

    class Meta:
        model = EngCareer
        fields = ['career_id', 'engineer_id', 'company_name', 'job_description',
                  'career_type', 'entering_date', 'quitting_date', 'career_job_skills', 'role_name', 'job_code']


class EngWorkHistory(BaseModelSerializer):
    engineer_id = serializers.IntegerField()

    class Meta:
        model = EngCareer
        fields = ['career_id', 'engineer_id', 'company_name', 'job_description',
                  'career_type', 'entering_date', 'quitting_date', 'role_name']


class EngSelfAssesmentSerializer(BaseModelSerializer):

    class Meta:
        model = EngSelfAssesment
        fields = "__all__"


class UpdateCareerJobSkillSerializer(BaseModelSerializer):
    class Meta:
        model = CareerJobSkill
        fields = ['job_code', 'skill_code', 'years_of_experience']

    def update(self, instance, validated_data):
        return super().update(instance, validated_data)


class UpdateEngCareerSerializer(BaseModelSerializer):
    career_job_skills = UpdateCareerJobSkillSerializer(
        many=True)

    class Meta:
        model = EngCareer
        fields = ['company_name', 'career_type', 'entering_date',
                  'career_job_skills', 'job_description',
                  'quitting_date', 'role_name', 'job_code']

    def update(self, instance, validated_data):
        return super().update(instance, validated_data)


class HopeJobSkillSerializer(BaseModelSerializer):

    class Meta:
        model = HopeJobSkill
        fields = ['job_code', 'skill_code']


class UpdateHopeJobSkillSerializer(BaseModelSerializer):
    class Meta:
        model = HopeJobSkill
        fields = ['job_code', 'skill_code']


class HopeCategorySkillSerializer(BaseSerializer):
    category_id = serializers.CharField()
    skills = HopeJobSkillSerializer(many=True)


class EngHopeSerializer(BaseModelSerializer):
    job_skills = HopeJobSkillSerializer(
        many=True, read_only=True, source='hopjobskill_set')
    payroll_price = serializers.SerializerMethodField()
    place_code1_name = serializers.SerializerMethodField()
    place_code2_name = serializers.SerializerMethodField()
    place_code3_name = serializers.SerializerMethodField()
    country_place_code1_name = serializers.SerializerMethodField()
    country_place_code2_name = serializers.SerializerMethodField()
    country_place_code3_name = serializers.SerializerMethodField()
    payroll_code = serializers.SerializerMethodField()
    category_skills = HopeCategorySkillSerializer(many=True)

    class Meta:
        model = EngHope
        fields = ['employ_code', 'place_code1',
                  'place_code2', 'place_code3', 'payroll_price', 'payroll_code', 'job_skills',
                  'place_code1_name', 'place_code2_name', 'place_code3_name',
                  'country_place_code1_name', 'country_place_code2_name', 'country_place_code3_name', 'remote_code', 'category_skills', 'payroll_price_usd']

    def get_payroll_price(self, obj) -> str:
        salary = None
        user_currency = None
        if obj.payroll_code is not None and obj.payroll_price is not None:
            try:
                user_currency = get_currency_from_request(
                    self.context.get('request'))
                if user_currency is not None:
                    amount = currency_converter.convert(
                        obj.payroll_price,
                        obj.payroll_code,
                        user_currency
                    )
                    salary = amount

                else:
                    salary = obj.payroll_price

            except Exception as e:
                salary = obj.payroll_price
        if user_currency and salary:
            return str(round_salary(salary, user_currency))
        elif obj.payroll_code:
            return str(round_salary(salary, obj.payroll_code))
        else:
            return str(salary)

    def get_payroll_code(self, obj) -> str:
        if obj.payroll_code is not None and obj.payroll_price is not None:
            try:
                user_currency = get_currency_from_request(
                    self.context.get('request'))
                if user_currency is not None:
                    return user_currency
                else:
                    return obj.payroll_code
            except Exception as e:
                return ""
        else:
            return ""

    def get_place_code1_name(self, obj) -> str:
        language_header = get_language_code_from_header(
            self.context.get('request'))
        return get_city_name(obj.place_code1, language_header)

    def get_place_code2_name(self, obj) -> str:
        language_header = get_language_code_from_header(
            self.context.get('request'))
        return get_city_name(obj.place_code2, language_header)

    def get_place_code3_name(self, obj) -> str:
        language_header = get_language_code_from_header(
            self.context.get('request'))
        return get_city_name(obj.place_code3, language_header)

    def get_country_place_code1_name(self, obj) -> str:

        try:
            language_header = get_language_code_from_header(
                self.context.get('request'))

            if obj.place_code1:
                country_code = obj.place_code1.split('-')[0]
                return get_country_name(country_code, language_header)
            else:
                return None
        except Exception as e:
            return None

    def get_country_place_code2_name(self, obj) -> str:

        try:
            language_header = get_language_code_from_header(
                self.context.get('request'))

            if obj.place_code2:
                country_code = obj.place_code2.split('-')[0]
                return get_country_name(country_code, language_header)
            else:
                return None
        except Exception as e:
            return None

    def get_country_place_code3_name(self, obj) -> str:

        try:
            language_header = get_language_code_from_header(
                self.context.get('request'))

            if obj.place_code3:
                country_code = obj.place_code3.split('-')[0]
                return get_country_name(country_code, language_header)
            else:
                return None
        except Exception as e:
            return None

    @swagger_serializer_method(serializer_or_field=HopeCategorySkillSerializer(many=True))
    def get_category_skills(self, obj):
        try:
            skills = obj.hopjobskill_set.all()
            list_all_skills = get_list_skill_codes()
            serializer = EngSkillSerializer(
                skills, many=True, context=self.context)
            data = serializer.data
            # append category_id to each skill
            for skill in data:
                skill_code = skill['skill_code']
                matched_skill = [
                    x for x in list_all_skills if x['id'] == skill_code]

                if matched_skill:
                    skill['category_id'] = matched_skill[0]['category_id']
                else:
                    skill['category_id'] = None

            grouped = defaultdict(list)
            for skill in data:
                grouped[skill['category_id']].append(skill)

            result = []
            for category_id, skills in grouped.items():
                result.append({
                    "category_id": category_id,
                    "skills": skills
                })
            return result
        except Exception as e:
            logger.error(
                f"Error getting category skills: {e}, engineer_id: {obj.engineer_id}")
            return []


class UpdateEngHighLightProjectSerializer(BaseModelSerializer):
    name = serializers.CharField(required=False, allow_null=True)
    description = serializers.CharField(required=False, allow_null=True)
    size = serializers.CharField(required=False, allow_null=True)
    role_name = serializers.CharField(required=False, allow_null=True)
    responsibilities = serializers.CharField(required=False, allow_null=True)
    technology_used = serializers.CharField(required=False, allow_null=True)
    from_date = serializers.DateField(required=False, allow_null=True)
    to_date = serializers.DateField(required=False, allow_null=True)

    class Meta:
        model = EngHighlightProject
        fields = ['name', 'description', 'size',
                  'role_name', 'responsibilities',
                  'technology_used',
                  'from_date',
                  'to_date',
                  ]

    def validate(self, attrs):
        from_date = attrs.get('from_date', None)
        to_date = attrs.get('to_date', None)
        if from_date and to_date:
            if from_date > to_date:
                raise serializers.ValidationError({
                    'from_date': _('From date must be before to date.'),
                })
        return attrs


class EngHighLightProjectSerializer(BaseModelSerializer):
    name = serializers.CharField(allow_null=True)
    description = serializers.CharField(allow_null=True)
    size = serializers.CharField(allow_null=True)
    role_name = serializers.CharField(allow_null=True)
    responsibilities = serializers.CharField(allow_null=True)
    technology_used = serializers.CharField(allow_null=True)
    from_date = serializers.DateField(allow_null=True)
    to_date = serializers.DateField(allow_null=True)

    class Meta:
        model = EngHighlightProject
        fields = ['name', 'description', 'size',
                  'role_name', 'responsibilities',
                  'technology_used',
                  'from_date',
                  'to_date',
                  ]


class HopeCategorySkillSerializer(BaseSerializer):
    category_id = serializers.CharField()
    skills = HopeJobSkillSerializer(many=True)


class ProfileEngHopeSerializer(BaseModelSerializer):
    job_skills = HopeJobSkillSerializer(
        many=True, read_only=True, source='hopjobskill_set')

    place_code1_name = serializers.SerializerMethodField()
    place_code2_name = serializers.SerializerMethodField()
    place_code3_name = serializers.SerializerMethodField()
    country_place_code1_name = serializers.SerializerMethodField()
    country_place_code2_name = serializers.SerializerMethodField()
    country_place_code3_name = serializers.SerializerMethodField()
    category_skills = serializers.SerializerMethodField()

    class Meta:
        model = EngHope
        fields = ['employ_code', 'place_code1',
                  'place_code2', 'place_code3', 'payroll_price', 'payroll_code', 'job_skills',
                  'place_code1_name', 'place_code2_name', 'place_code3_name',
                  'country_place_code1_name', 'country_place_code2_name', 'country_place_code3_name', 'remote_code', 'category_skills', 'payroll_price_usd']

    @swagger_serializer_method(serializer_or_field=HopeCategorySkillSerializer(many=True))
    def get_category_skills(self, obj):
        skills = HopeJobSkill.objects.filter(engineer=obj.engineer)
        list_all_skills = get_list_skill_codes()
        serializer = HopeJobSkillSerializer(
            skills, many=True, context=self.context)
        data = serializer.data
        # append category_id to each skill
        for skill in data:
            skill_code = skill['skill_code']
            matched_skill = [
                x for x in list_all_skills if x['id'] == skill_code]

            if matched_skill:
                skill['category_id'] = matched_skill[0]['category_id']
            else:
                skill['category_id'] = None

        grouped = defaultdict(list)
        for skill in data:
            grouped[skill['category_id']].append(skill)

        result = []
        for category_id, skills in grouped.items():
            result.append({
                "category_id": category_id,
                "skills": skills
            })
        return result

    def get_place_code1_name(self, obj) -> str:
        language_header = get_language_code_from_header(
            self.context.get('request'))
        return get_city_name(obj.place_code1, language_header)

    def get_place_code2_name(self, obj) -> str:
        language_header = get_language_code_from_header(
            self.context.get('request'))
        return get_city_name(obj.place_code2, language_header)

    def get_place_code3_name(self, obj) -> str:
        language_header = get_language_code_from_header(
            self.context.get('request'))
        return get_city_name(obj.place_code3, language_header)

    def get_country_place_code1_name(self, obj) -> str:

        try:
            language_header = get_language_code_from_header(
                self.context.get('request'))

            if obj.place_code1:
                country_code = obj.place_code1.split('-')[0]
                return get_country_name(country_code, language_header)
            else:
                return None
        except Exception as e:
            return None

    def get_country_place_code2_name(self, obj) -> str:

        try:
            language_header = get_language_code_from_header(
                self.context.get('request'))

            if obj.place_code2:
                country_code = obj.place_code2.split('-')[0]
                return get_country_name(country_code, language_header)
            else:
                return None
        except Exception as e:
            return None

    def get_country_place_code3_name(self, obj) -> str:

        try:
            language_header = get_language_code_from_header(
                self.context.get('request'))

            if obj.place_code3:
                country_code = obj.place_code3.split('-')[0]
                return get_country_name(country_code, language_header)
            else:
                return None
        except Exception as e:
            return None


class UpdateEngHopeSerializer(BaseModelSerializer):
    job_skills = UpdateHopeJobSkillSerializer(many=True)
    payroll_price = serializers.CharField(allow_null=True)

    class Meta:
        model = EngHope
        fields = ['place_code1', 'place_code2',
                  'place_code3', 'employ_code', 'payroll_price', 'payroll_code', 'job_skills', 'remote_code']

    def validate(self, data):
       # check place code
        place_code1 = data.get('place_code1')
        place_code2 = data.get('place_code2')
        place_code3 = data.get('place_code3')
        if place_code1 is not None:
            if not check_address_code_valid(place_code1):
                raise serializers.ValidationError({
                    'place_code1': _('Invalid place code.'),
                })
        if place_code2 is not None:
            if not check_address_code_valid(place_code2):
                raise serializers.ValidationError({
                    'place_code2': _('Invalid place code.'),
                })
        if place_code3 is not None:
            if not check_address_code_valid(place_code3):
                raise serializers.ValidationError({
                    'place_code3': _('Invalid place code.'),
                })
        # check payroll code
        payroll_code = data.get('payroll_code')
        if payroll_code is not None:
            if not check_currency_code_valid(payroll_code):
                raise serializers.ValidationError({
                    'payroll_code': _('Invalid payroll price.'),
                })
        return data


class UpdateEngSelfAssesmentSerializer(BaseModelSerializer):

    class Meta:
        model = EngSelfAssesment
        fields = ['remote_exp_years', 'remote_job_description', 'remote_skill_1',
                  'remote_skill_2', 'remote_skill_3',  'global_work_exp', 'global_skill_1', 'global_skill_2',
                  'global_skill_3',
                  'social_style', 'communication_skill_1', 'communication_skill_2', 'communication_skill_3',
                  'report_skill_1', 'report_skill_2', 'report_skill_3',
                  'management_skill_1',
                  'management_skill_2', 'management_skill_3']


class CategorySkillSerializer(BaseSerializer):
    skills = EngSkillSerializer(many=True)
    category_id = serializers.CharField()


class UserDetailsSerializers(BaseModelSerializer):
    educations = EngAcademicSerializer(
        many=True, read_only=True, source='engacademic_set')
    languages = EngLanguageSerializer(
        many=True, read_only=True, source='englanguage_set')
    qualifications = EngLicenseSerializer(
        many=True, read_only=True, source='englicence_set')
    skills = EngSkillSerializer(
        many=True, read_only=True, source='engskill_set')
    experiences = EngCareerSerializer(
        many=True, read_only=True, source='engcareer_set')
    self_assesment = serializers.SerializerMethodField()

    requirements = serializers.SerializerMethodField()
    last_academic_name = serializers.SerializerMethodField()
    whatsapp_url = serializers.CharField(required=False, allow_blank=True)
    facebook_url = serializers.CharField(required=False, allow_blank=True)
    linkedin_url = serializers.CharField(required=False, allow_blank=True)
    category_skills = serializers.SerializerMethodField()
    skills_for_cv_display = serializers.CharField(
        required=False, allow_blank=True, allow_null=True)
    professional_summary = serializers.CharField(
        required=False, allow_blank=True, allow_null=True)
    highlight_projects = EngHighLightProjectSerializer(
        many=True, read_only=True, source='enghighlightproject_set')

    class Meta:
        model = User
        fields = ['profile_image_path', 'email', 'first_name', 'last_name', 'nickname', 'sex_type', 'birth_date',
                  'country_code', 'tel', 'address_code', 'city_name', 'passport_number',
                  'passport_image_path', 'educations', 'languages', 'qualifications',
                  'skills', 'experiences',
                  'requirements',
                  'last_academic_code',
                  'pr', 'self_introduction_url', 'international_tel', 'updated',
                  'user_type', 'facebook_url', 'linkedin_url',
                  'whatsapp_url', 'last_academic_name', 'self_assesment',
                  'email_temp', 'category_skills', 'skills_for_cv_display',
                  'professional_summary', 'highlight_projects']

    @swagger_serializer_method(serializer_or_field=CategorySkillSerializer(many=True))
    def get_category_skills(self, obj):
        try:

            skills = obj.engskill_set.all()
            list_all_skills = get_list_skill_codes()
            serializer = EngSkillSerializer(
                skills, many=True, context=self.context)
            data = serializer.data
            # append category_id to each skill
            for skill in data:
                skill_code = skill['skill_code']
                matched_skill = [
                    x for x in list_all_skills if x['id'] == skill_code]

                if len(matched_skill) > 0:
                    skill['category_id'] = matched_skill[0]['category_id']
                elif skill['temp_category_id']:
                    skill['category_id'] = str(skill['temp_category_id'])
                else:
                    skill['category_id'] = ""

            grouped = defaultdict(list)
            for skill in data:
                grouped[skill['category_id']].append(skill)

            result = []
            for category_id, skills in grouped.items():
                result.append({
                    "category_id": category_id,
                    "skills": skills
                })
            return result
        except Exception as e:
            logger.error(
                f"Error getting category skills: {e}, engineer_id: {obj.engineer_id}")
            return []

    @swagger_serializer_method(serializer_or_field=EngHopeSerializer)
    def get_requirements(self, obj):
        try:
            hope = EngHope.objects.get(engineer=obj)
            skills = HopeJobSkill.objects.filter(engineer=obj)

            data = ProfileEngHopeSerializer(hope).data
            data['job_skills'] = HopeJobSkillSerializer(skills, many=True).data
            return data
        except Exception as e:
            return None

    def get_last_academic_name(self, obj) -> str:
        current_language_header = get_language_code_from_header(
            self.context.get('request'))
        return get_academic_type_name(obj.last_academic_code, current_language_header)

    @swagger_serializer_method(serializer_or_field=EngSelfAssesmentSerializer)
    def get_self_assesment(self, obj):
        assessments = EngSelfAssesment.objects.filter(engineer=obj)
        if assessments.exists():
            serialized_data = EngSelfAssesmentSerializer(
                assessments.first()).data
        else:
            serialized_data = None
        print(f"Serialized data: {serialized_data}")
        return serialized_data


class EngineerSelfAssesmentDetailsSerializer(BaseSerializer):
    remote_exp_years = serializers.IntegerField(allow_null=True)
    remote_job_description = serializers.CharField(allow_null=True)
    total_remote_skill = serializers.IntegerField(allow_null=True)
    total_global_skill = serializers.IntegerField(allow_null=True)
    total_communication_skill = serializers.IntegerField(allow_null=True)
    total_report_skill = serializers.IntegerField(allow_null=True)
    total_management_skill = serializers.IntegerField(allow_null=True)
    social_style = serializers.IntegerField(allow_null=True)
    durability_score = serializers.IntegerField(allow_null=True)
    global_work_exp = serializers.IntegerField(allow_null=True)


class UserExploreDetailsSerializers(BaseModelSerializer):
    educations = EngAcademicSerializer(
        many=True, read_only=True, source='engacademic_set')
    languages = EngLanguageSerializer(
        many=True, read_only=True, source='englanguage_set')
    qualifications = EngLicenseSerializer(
        many=True, read_only=True, source='englicence_set')
    skills = EngSkillSerializer(
        many=True, read_only=True, source='engskill_set')
    experiences = EngCareerSerializer(
        many=True, read_only=True, source='engcareer_set')

    requirements = serializers.SerializerMethodField()
    interested_flag = serializers.SerializerMethodField()
    last_academic_name = serializers.SerializerMethodField()
    whatsapp_url = serializers.CharField(required=False, allow_blank=True)
    facebook_url = serializers.CharField(required=False, allow_blank=True)
    linkedin_url = serializers.CharField(required=False, allow_blank=True)
    self_assesment_details = serializers.SerializerMethodField()
    can_request_interview = serializers.SerializerMethodField()

    class Meta:
        model = User
        fields = ['profile_image_path', 'email', 'first_name', 'last_name', 'sex_type', 'birth_date',
                  'country_code', 'tel', 'address_code', 'city_name', 'passport_number',
                  'passport_image_path', 'educations', 'languages', 'qualifications',
                  'skills', 'experiences',
                  'requirements',
                  'last_academic_code',
                  'pr', 'self_introduction_url', 'international_tel', 'updated',
                  'user_type', 'facebook_url', 'linkedin_url',
                  'whatsapp_url',
                  'interested_flag', 'user_id', 'last_academic_name', 'self_assesment_details', 'can_request_interview',
                  'nickname'
                  ]

    @swagger_serializer_method(serializer_or_field=EngineerSelfAssesmentDetailsSerializer)
    def get_self_assesment_details(self, obj):
        try:
            assessment = EngSelfAssesment.objects.get(engineer=obj)
            list_remote_skills = [assessment.remote_skill_1,
                                  assessment.remote_skill_2, assessment.remote_skill_3]
            list_remote_skills = [
                skill for skill in list_remote_skills if skill]
            total_remote_skill_score = sum(list_remote_skills)
            list_global_skills = [assessment.global_skill_1,
                                  assessment.global_skill_2, assessment.global_skill_3]
            list_global_skills = [
                skill for skill in list_global_skills if skill]
            total_global_skill_score = sum(list_global_skills)
            list_communication_skills = [assessment.communication_skill_1,
                                         assessment.communication_skill_2, assessment.communication_skill_3]
            list_communication_skills = [
                skill for skill in list_communication_skills if skill]
            total_communication_skill_score = sum(list_communication_skills)
            list_report_skills = [assessment.report_skill_1,
                                  assessment.report_skill_2, assessment.report_skill_3]
            list_report_skills = [
                skill for skill in list_report_skills if skill]
            total_report_skill_score = sum(list_report_skills)
            list_management_skills = [assessment.management_skill_1,
                                      assessment.management_skill_2, assessment.management_skill_3]
            list_management_skills = [
                skill for skill in list_management_skills if skill]
            total_management_skill_score = sum(list_management_skills)
            data = {
                "remote_exp_years": assessment.remote_exp_years,
                "remote_job_description": assessment.remote_job_description,
                "total_remote_skill": total_remote_skill_score,
                "total_global_skill": total_global_skill_score,
                "total_communication_skill": total_communication_skill_score,
                "total_report_skill": total_report_skill_score,
                "total_management_skill": total_management_skill_score,
                "social_style": assessment.social_style,
                "durability_score": assessment.durability_score,
                'global_work_exp': assessment.global_work_exp,
            }
            return EngineerSelfAssesmentDetailsSerializer(data).data

        except Exception as e:
            return None

    @swagger_serializer_method(serializer_or_field=EngHopeSerializer)
    def get_requirements(self, obj):
        try:
            hope = EngHope.objects.get(engineer=obj)
            skills = HopeJobSkill.objects.filter(engineer=obj)

            data = ProfileEngHopeSerializer(hope).data
            data['job_skills'] = HopeJobSkillSerializer(skills, many=True).data
            return data
        except Exception as e:
            return None

    def get_interested_flag(self, obj) -> int:
        request = self.context.get('request')
        if isinstance(request.user, AnonymousUser):
            return None
        company_id = request.user.company_id
        try:
            data = RecInterestedEngineer.objects.get(
                host_company_id=company_id, engineer_id=obj.user_id)
            return int(data.interested_flag)
        except RecInterestedEngineer.DoesNotExist:
            return None

    def get_last_academic_name(self, obj) -> str:
        current_language_header = get_language_code_from_header(
            self.context.get('request'))
        return get_academic_type_name(obj.last_academic_code, current_language_header)

    def get_can_request_interview(self, obj) -> bool:
        request = self.context.get('request')
        if isinstance(request.user, AnonymousUser):
            return False
        company_id = request.user.company_id
        try:
            exist = RecApply.objects.filter(
                host_company_id=company_id, engineer_id=obj.user_id).exists()
            return not exist
        except RecInterestedEngineer.DoesNotExist:
            return False


class GetUserProfileParamsSerializer(BaseSerializer):
    optional_engineer_id = serializers.IntegerField(required=False)


class UpdateUserSerializer(BaseModelSerializer):
    educations = UpdateEngAcademicSerializer(
        many=True, source='engacademic_set', allow_null=True)
    languages = UpdateEngLanguageSerializer(
        many=True, source='englanguage_set', allow_null=True)
    qualifications = UpdateEngLicenseSerializer(
        many=True, source='englicence_set', allow_null=True)
    skills = UpdateEngSkillSerializer(
        many=True, source='engskill_set', allow_null=True)
    experiences = UpdateEngCareerSerializer(
        many=True, source='engcareer_set', allow_null=True)
    requirements = UpdateEngHopeSerializer(
        many=False, source='enghope_set', allow_null=True)
    self_assesment = UpdateEngSelfAssesmentSerializer(
        read_only=True, source='engselfassesment_set')
    self_introduction_url = serializers.URLField(
        required=False, allow_blank=True)
    whatsapp_url = serializers.CharField(required=False, allow_blank=True)
    facebook_url = serializers.CharField(required=False, allow_blank=True)
    linkedin_url = serializers.CharField(required=False, allow_blank=True)
    optional_engineer_id = serializers.IntegerField(required=False)
    skills_for_cv_display = serializers.CharField(
        required=False, allow_blank=True, allow_null=True)
    professional_summary = serializers.CharField(
        required=False, allow_blank=True, allow_null=True)
    highlight_projects = UpdateEngHighLightProjectSerializer(
        many=True, source='enghighlightproject_set', allow_null=True)

    class Meta:
        model = User
        fields = ['profile_image_path', 'first_name', 'last_name', 'nickname', 'sex_type', 'birth_date',
                  'country_code', 'tel', 'address_code', 'city_name', 'passport_number',
                  'passport_image_path', 'educations', 'languages', 'qualifications',
                  'skills', 'experiences', 'requirements', 'pr', 'self_introduction_url',
                  'whatsapp_url', 'facebook_url', 'linkedin_url',
                  'international_tel', 'updated', 'last_academic_code', 'self_assesment', 'email_temp', 'optional_engineer_id', 'skills_for_cv_display',
                  'professional_summary', 'highlight_projects']

    def update(self, instance, validated_data):

        educations_data = validated_data.pop('engacademic_set', None)
        languages_data = validated_data.pop('englanguage_set', None)
        qualifications_data = validated_data.pop('englicence_set', None)
        validated_data.pop("engcareer_set", None)
        skills_data = validated_data.pop('engskill_set', None)
        highlight_projects_data = validated_data.pop(
            'enghighlightproject_set', None)
        instance = super().update(instance, validated_data)

        if educations_data is not None:
            instance.engacademic_set.all().delete()
            highest_level = 1
            for education_data in educations_data:
                if education_data['type'] is not None and education_data['type'] > highest_level:
                    highest_level = education_data['type']
                EngAcademic.objects.create(engineer=instance, **education_data)
            instance.last_academic_code = highest_level

        if languages_data is not None:

            instance.englanguage_set.all().delete()
            for language_data in languages_data:
                EngLanguage.objects.create(engineer=instance, **language_data)

        if qualifications_data is not None:
            instance.englicence_set.all().delete()
            for qualification_data in qualifications_data:
                EngLicence.objects.create(
                    engineer=instance, **qualification_data)

        if skills_data is not None:
            instance.engskill_set.all().delete()
            for skill_data in skills_data:
                EngSkill.objects.create(engineer=instance, **skill_data)
        if highlight_projects_data is not None:
            instance.enghighlightproject_set.all().delete()
            for highlight_project_data in highlight_projects_data:
                EngHighlightProject.objects.create(
                    engineer=instance, **highlight_project_data)

        instance.updated = timezone.now()
        instance.save()

        return instance

    def validate(self, data):
        # Validate sex_type field
        sex_type = data.get('sex_type')
        if sex_type is not None and sex_type not in [tag.value for tag in SexType]:
            raise serializers.ValidationError({
                'sex_type': _('Invalid sex type.'),
            })

        # Validate address code
        country_code = data.get('country_code')
        if country_code is not None:
            if check_country_code_valid(country_code) is False:
                raise serializers.ValidationError({
                    'country_code': _('Invalid country code.'),
                })

        # Validate phone number
        tel = data.get('tel')
        international_tel = data.get('international_tel')
        if tel is not None and international_tel is not None:
            try:

                phone_number = phonenumbers.parse(
                    international_tel+tel, keep_raw_input=True)
                if not phonenumbers.is_valid_number(phone_number):
                    raise serializers.ValidationError({
                        'tel': _('Invalid phone number.'),
                    })
            except phonenumbers.NumberParseException:
                raise serializers.ValidationError({
                    'tel': _('Invalid phone number format.'),
                })

        # Validate address code
        address_code = data.get('address_code')
        if address_code is not None:
            if not check_address_code_valid(address_code):
                raise serializers.ValidationError({
                    'address_code': _('Invalid address code.'),
                })
        return data


class SkillFilterSerializer(BaseModelSerializer):
    code = serializers.CharField(allow_null=True)
    level = serializers.CharField(allow_null=True)


class UserAgencyCompanySerializer(BaseModelSerializer):

    class Meta:
        model = ComCompany
        fields = ['company_id', 'logo_image_path',
                  'name', 'agent_fee', 'agent_fee_curr_code']


class ExploreUserParamsSerializer(BaseModelSerializer):
    page_size = serializers.IntegerField(required=False)
    page = serializers.IntegerField(required=False)
    ordering = serializers.CharField(required=False, allow_blank=True)
    filter_id = serializers.IntegerField(required=False)
    show_favorite = serializers.BooleanField(required=False, allow_null=True)
    remote_work_skill_point_type = serializers.IntegerField(
        required=False, allow_null=True)
    global_skill_point_type = serializers.IntegerField(
        required=False, allow_null=True)
    communication_skill_point_type = serializers.IntegerField(
        required=False, allow_null=True)
    horenso_skill_point_type = serializers.IntegerField(
        required=False, allow_null=True)
    project_management_skill_point_type = serializers.IntegerField(
        required=False, allow_null=True)
    payroll_price_from = serializers.CharField(required=False, allow_null=True)
    payroll_price_to = serializers.CharField(required=False, allow_null=True)
    global_work_exp = serializers.IntegerField(required=False, allow_null=True)
    search_type = serializers.CharField(required=False, allow_null=True)
    search_query = serializers.CharField(required=False, allow_null=True)
    career_type = serializers.IntegerField(
        required=False, allow_null=True)
    skills = serializers.ListField(
        child=serializers.CharField(
            required=False, allow_null=True, allow_blank=True),
        allow_empty=True,
        allow_null=True,
        required=False
    )
    recruit_id = serializers.CharField(required=False, allow_null=True)

    class Meta:
        model = RecFilter
        exclude = ['created', 'host_agent', 'filter_name', 'experienced_job_code2',
                   'years_of_experience2', 'experienced_job_code3', 'years_of_experience3',
                   'skill_code1_1', 'skill_level_type1_1', 'skill_code1_2', 'skill_level_type1_2',
                   'skill_code1_3', 'skill_level_type1_3', 'skill_code2_1', 'skill_level_type2_1',
                   'skill_code2_2', 'skill_level_type2_2', 'skill_code2_3', 'skill_level_type2_3',
                   'skill_code3_1', 'skill_level_type3_1', 'skill_code3_2', 'skill_level_type3_2',
                   'skill_code3_3', 'skill_level_type3_3',
                   ]


class GetVideoSocialSourceSerializer(BaseSerializer):
    link = serializers.CharField()


class ExperienceModel(BaseSerializer):
    year = serializers.DecimalField(
        max_digits=10, decimal_places=2)
    job_code = serializers.CharField()


class TotalExperienceSerializer(serializers.ListField):
    child = ExperienceModel()


class RecInterestedEngineerSerializer(BaseModelSerializer):
    class Meta:
        model = RecInterestedEngineer
        fields = ['interested_flag']


class UserDetailsResponseModel(BaseSerializer):
    message = serializers.CharField(allow_null=True)
    data = UserDetailsSerializers(allow_null=True)
    errors = serializers.ListField(
        child=ErrorDetailSerializer(), allow_null=True)


class UserExploreDetailsResponseModel(BaseSerializer):
    message = serializers.CharField(allow_null=True)
    data = UserExploreDetailsSerializers()
    errors = serializers.ListField(
        child=ErrorDetailSerializer(), allow_null=True)


class UpdateUserResponseModel(BaseSerializer):
    message = serializers.CharField(allow_null=True)
    data = UserDetailsSerializers()
    errors = serializers.ListField(
        child=ErrorDetailSerializer(), allow_null=True)


class UpdatePasswordSerializer(BaseSerializer):
    current_password = serializers.CharField(required=True)
    new_password = serializers.CharField(required=True)


class UserAppliedCompanySerializer(BaseModelSerializer):
    class Meta:
        model = User
        fields = ['profile_image_path',
                  'first_name', 'last_name', 'birth_date']


class UserAgencyCompanyResponseModel(BaseSerializer):
    message = serializers.CharField(allow_null=True)
    data = UserAgencyCompanySerializer(many=True)
    errors = serializers.ListField(
        child=ErrorDetailSerializer(), allow_null=True)


class GetVideoSocialSourceResponseModel(BaseSerializer):
    data = serializers.CharField()
