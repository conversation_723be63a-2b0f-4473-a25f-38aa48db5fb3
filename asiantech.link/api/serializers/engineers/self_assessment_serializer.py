from rest_framework import serializers
from api.models.self_assessment import Description, SelfAssessmentSheet, EvaluationMethod, Question, Option, Questions, SelfAssessmentData
from utils.utils import *


class DescriptionSerializer(serializers.ModelSerializer):
    description = Description()

    class Meta:
        model = Description
        fields = ['text', 'text_en', 'text_vi']


class OptionSerializer(serializers.ModelSerializer):
    description = DescriptionSerializer()

    class Meta:
        model = Option
        fields = ['uuid', 'text_en', 'text_vi',
                  'text', 'is_selected', 'description']

    def validate_text_en(self, value):
        if value is None:
            raise serializers.ValidationError("text_en cannot be null.")
        return value


class QuestionSerializer(serializers.ModelSerializer):

    class Meta:
        model = Question
        fields = ['text_en', 'text_vi', 'text', 'is_expanded']

    def to_representation(self, instance):
        representation = super().to_representation(instance)
        representation['is_expanded'] = False
        return representation


class QuestionsSerializer(serializers.ModelSerializer):
    options = OptionSerializer(many=True)
    question = QuestionSerializer()

    class Meta:
        model = Questions
        fields = ['question', 'options']


class EvaluationMethodSerializer(serializers.ModelSerializer):
    class Meta:
        model = EvaluationMethod
        fields = ['text_en', 'text_vi', 'text']


class SelfAssessmentSheetSerializer(serializers.ModelSerializer):
    class Meta:
        model = SelfAssessmentSheet
        fields = ['text_en', 'text_vi', 'text']


class AssessmentQuestionSerializer(serializers.Serializer):
    text = serializers.SerializerMethodField()
    text_en = serializers.CharField()
    text_vi = serializers.CharField()

    def get_text(self, obj) -> str:
        current_language_header = get_language_code_from_header(
            self.context.get('request'))
        if current_language_header == 'en':
            return obj['text_en']
        if current_language_header == 'vi':
            return obj['text_vi']
        return obj['text']


class AssessmentAnswerSerializer(serializers.Serializer):
    uuid = serializers.CharField()
    text = serializers.SerializerMethodField()
    text_en = serializers.CharField()
    text_vi = serializers.CharField()
    is_selected = serializers.BooleanField(allow_null=True)
    description = serializers.SerializerMethodField()

    def get_text(self, obj) -> str:
        current_language_header = get_language_code_from_header(
            self.context.get('request'))
        if current_language_header == 'en':
            return obj['text_en']
        if current_language_header == 'vi':
            return obj['text_vi']
        return obj['text']

    def get_description(self, obj) -> str:
        current_language_header = get_language_code_from_header(
            self.context.get('request'))
        if current_language_header == 'en':
            return obj['description']['text_en']
        if current_language_header == 'vi':
            return obj['description']['text_vi']
        return obj['description']['text']


class AssessmentQuestionDataSerializer(serializers.Serializer):
    question = AssessmentQuestionSerializer()
    options = AssessmentAnswerSerializer(many=True)


class SelfAssessmentDataSerializer(serializers.ModelSerializer):
    remote_work_skills = SelfAssessmentSheetSerializer(
        allow_null=True)
    communication_skills_self_assessment_sheet = SelfAssessmentSheetSerializer(
        allow_null=True)
    global_responsiveness_skills = SelfAssessmentSheetSerializer(
        allow_null=True)
    reporting_consultation_skills_self_evaluation = SelfAssessmentSheetSerializer(
        allow_null=True)
    project_management_skills_self_evaluation = SelfAssessmentSheetSerializer(
        allow_null=True)
    evaluation_methods = EvaluationMethodSerializer(
        many=True, source='evaluationmethod_set')
    questions = QuestionsSerializer(many=True, source='question_set')

    class Meta:
        model = SelfAssessmentSheet
        fields = [
            'remote_work_skills',
            'communication_skills_self_assessment_sheet',
            'global_responsiveness_skills',
            'reporting_consultation_skills_self_evaluation',
            'project_management_skills_self_evaluation',
            'evaluation_methods',
            'questions'
        ]


class SelfAssessmentResponseModel(serializers.Serializer):
    message = serializers.CharField(allow_null=True)
    data = SelfAssessmentDataSerializer()
    errors = serializers.ListField(
        child=serializers.CharField(), allow_null=True)


class SelfAssessmentAnswerSerializer(serializers.Serializer):
    list_answer = serializers.ListField()


class AssessmentQuestionsResponseModel(serializers.Serializer):
    message = serializers.CharField(allow_null=True)
    data = AssessmentQuestionDataSerializer(many=True)
    errors = serializers.ListField(
        child=serializers.CharField(), allow_null=True)
