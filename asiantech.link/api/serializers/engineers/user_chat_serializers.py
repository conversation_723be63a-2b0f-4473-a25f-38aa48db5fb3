from rest_framework import serializers
from django.utils.translation import gettext_lazy as _

from api.models.chat import MapChatGroup
from utils.custom_fields import *
from api.models.user import User
from api.models.company import *
from drf_yasg.utils import swagger_serializer_method
from api.models.eng import *
from utils.constants import *
from utils.logger_mixins import *
from api.serializers.host_company.host_company_serializers import HostCompanySerializer
from utils.utils import get_age


class UserChatSerializer(serializers.ModelSerializer):
    user_id = serializers.IntegerField()
    user_type = serializers.IntegerField()
    email = serializers.EmailField(allow_null=True)
    profile_image_path = serializers.CharField(allow_null=True)
    first_name = serializers.CharField(allow_null=True)
    last_name = serializers.CharField(allow_null=True)
    company = serializers.SerializerMethodField(allow_null=True)
    chat_id = serializers.SerializerMethodField(allow_null=True)
    age = serializers.SerializerMethodField(allow_null=True)

    class Meta:
        model = User
        fields = ['user_id', 'user_type', 'email',
                  'profile_image_path', 'first_name', 'last_name', 'company', 'chat_id', 'age', 'nickname', 'country_code']

    @swagger_serializer_method(serializer_or_field=HostCompanySerializer)
    def get_company(self, obj):
        company = ComCompany.objects.filter(company_id=obj.company_id).first()
        return HostCompanySerializer(company).data if company else None

    def get_chat_id(self, obj) -> int:
        # Assuming obj is a User instance
        chat_id = MapChatGroup.objects.filter(
            user=obj, group=self.context['group']).values_list('chat_id', flat=True).first()
        return chat_id

    def get_age(self, obj) -> int:
        return get_age(obj.birth_date)
