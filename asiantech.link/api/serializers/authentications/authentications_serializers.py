from rest_framework import serializers
from utils.responses import ErrorDetailSerializer
from utils.logger_mixins import *


class SendCodeRequestSerializer(BaseSerializer):
    email = serializers.EmailField()
    captcha_key = serializers.CharField()
    captcha_value = serializers.CharField()
    sns_email = serializers.EmailField(allow_null=True)


class SendWhatsappCodeSerializer(BaseSerializer):
    phone_number = serializers.CharField()


class ConfirmWhatsappCodeSerializer(BaseSerializer):
    phone_number = serializers.CharField()
    code = serializers.CharField()


class RegisterSerializer(BaseSerializer):
    email = serializers.EmailField()
    password = serializers.CharField(max_length=128)


class LoginSerializer(BaseSerializer):
    email = serializers.EmailField()
    password = serializers.Char<PERSON>ield(max_length=128)
    captcha_key = serializers.CharField(allow_null=True)
    captcha_value = serializers.Char<PERSON>ield(allow_null=True)
    user_type = serializers.IntegerField(allow_null=True, required=False)


class LoginWithSNSSerializer(BaseSerializer):
    sns_type = serializers.CharField()
    code = serializers.CharField(allow_null=True, required=False)
    redirect_uri = serializers.CharField(allow_null=True, required=False)
    code_verifier = serializers.CharField(allow_null=True, required=False)
    whatsapp_number = serializers.CharField(allow_null=True, required=False)


class LoginWithSNSResponseDataSerializer(BaseSerializer):
    access_token = serializers.CharField(allow_null=True)
    refresh_token = serializers.CharField(allow_null=True)
    user_type = serializers.CharField(allow_null=True)
    email = serializers.EmailField(allow_null=True)


class LoginWithSNSResponseModel(BaseSerializer):
    message = serializers.CharField(allow_null=True)
    data = LoginWithSNSResponseDataSerializer()
    errors = serializers.ListField(
        child=ErrorDetailSerializer(), allow_null=True)


class VerifyEmailSNSBodySerializer(BaseSerializer):
    email = serializers.EmailField()


class LoginSuccessSerializer(BaseSerializer):
    captcha_key = serializers.CharField(allow_null=True)
    captcha_image_url = serializers.CharField(allow_null=True)
    is_email_verified = serializers.BooleanField()
    refresh = serializers.CharField(allow_null=True)
    access = serializers.CharField(allow_null=True)


class TokenSerializer(BaseSerializer):
    refresh = serializers.CharField()
    access = serializers.CharField()


class VerifyEmailSerializer(BaseSerializer):
    token = serializers.CharField()


class ResetPasswordSerializer(BaseSerializer):
    email = serializers.EmailField()


class ResetPasswordConfirmSerializer(BaseSerializer):
    token = serializers.CharField()


class ConfirmLoginSerializer(BaseSerializer):
    email = serializers.CharField()
    code = serializers.CharField(max_length=6)
    password = serializers.CharField(max_length=128, allow_blank=True)


class LogoutSerializer(BaseSerializer):
    refresh = serializers.CharField()


class CaptchaSerializer(BaseSerializer):
    key = serializers.CharField()
    image_url = serializers.URLField()


class CaptchaResponseModel(BaseSerializer):
    message = serializers.CharField(allow_null=True)
    data = CaptchaSerializer()
    errors = serializers.ListField(
        child=ErrorDetailSerializer(), allow_null=True)


class CheckCaptchaRequireInLoginSerializer(BaseSerializer):
    email = serializers.EmailField()


class CaptchaAndUserStatusSerializer(BaseSerializer):
    require_captcha = serializers.BooleanField()
    is_user_verified = serializers.BooleanField()


class CheckCaptchaRequireInLoginResponseModel(BaseSerializer):
    message = serializers.CharField(allow_null=True)
    data = CaptchaAndUserStatusSerializer()
    errors = serializers.ListField(
        child=ErrorDetailSerializer(), allow_null=True)


class LoginSuccessResponseModel(BaseSerializer):
    message = serializers.CharField(allow_null=True)
    data = LoginSuccessSerializer()
    errors = serializers.ListField(
        child=ErrorDetailSerializer(), allow_null=True)


# Check Facebook Id
class CheckFacebookIdSerializer(BaseSerializer):
    facebook_id = serializers.CharField()


class CheckFacebookIdResponseModel(BaseSerializer):
    message = serializers.CharField(allow_null=True)
    data = serializers.BooleanField()
    errors = serializers.ListField(
        child=ErrorDetailSerializer(), allow_null=True)

# Register with Facebook


class RegisterWithSNSSerializer(BaseSerializer):
    id = serializers.CharField()
    email = serializers.EmailField()
    image_url = serializers.CharField(allow_null=True)
    first_name = serializers.CharField(allow_null=True)
    last_name = serializers.CharField(allow_null=True)
