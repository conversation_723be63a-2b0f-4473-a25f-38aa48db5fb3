from rest_framework import serializers
from utils.responses import ErrorDetailSerializer


class BoolResponseModel(serializers.Serializer):
    message = serializers.CharField(allow_null=True)
    data = serializers.BooleanField()
    errors = serializers.ListField(
        child=ErrorDetailSerializer(), allow_null=True)


class PagingResponseModel(serializers.Serializer):
    next = serializers.CharField(allow_null=True)
    previous = serializers.CharField(allow_null=True)
    results = serializers.ListField()

    def __init__(self, *args, **kwargs):
        input_serializer = kwargs.pop('input_serializer', None)
        super().__init__(*args, **kwargs)
        if input_serializer:
            self.fields['results'] = serializers.ListField(
                child=input_serializer())
