from rest_framework import serializers
from django.utils.translation import gettext_lazy as _

from api.models.chat import MapChatGroup
from api.serializers.host_company.host_company_serializers import HostCompanySerializer
from utils.custom_fields import *
from api.models.user import User
from api.models.company import *
from drf_yasg.utils import swagger_serializer_method
from api.models.eng import *
from utils.constants import *
from utils.logger_mixins import *


class ConvertCurrencySerializer(BaseSerializer):
    amount = serializers.FloatField()
    from_currency = serializers.CharField()
    to_currency = serializers.CharField()


class GetCurrentLocationResponseModel(BaseSerializer):
    country = serializers.CharField()
