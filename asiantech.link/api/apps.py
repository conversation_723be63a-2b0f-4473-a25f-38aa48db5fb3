from django.apps import AppConfig
from django.core.cache import cache
from django.core.management import call_command
from django.db.models.signals import post_migrate
from django.dispatch import receiver
from django.core.cache import cache
from django.core.management import call_command


class ApiConfig(AppConfig):
    default_auto_field = "django.db.models.BigAutoField"
    name = "api"   
   
class AsianTechConfig(AppConfig):
    name = "api"
    def ready(self):
        # Avoid running the command multiple times in development with autoreload
        if not cache.get('tokens_loaded'):
            call_command('load_tokens_into_cache')
            cache.set('tokens_loaded', True, timeout=None)
