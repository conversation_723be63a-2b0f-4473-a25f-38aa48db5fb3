# SOME DESCRIPTIVE TITLE.
# Copyright (C) YEAR THE PACKAGE'S COPYRIGHT HOLDER
# This file is distributed under the same license as the PACKAGE package.
# <AUTHOR> <EMAIL>, YEAR.
#
msgid ""
msgstr ""
"Project-Id-Version: PACKAGE VERSION\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-06-26 15:16+0700\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language-Team: LANGUAGE <<EMAIL>>\n"
"Language: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#: asiantech.link/api/models/user.py:17
msgid "Email should be provided"
msgstr "メールアドレスを入力してください"

#: asiantech.link/api/models/user.py:38
msgid "Superuser must have is_staff as True"
msgstr "「スーパーユーザーは is_staff を True に設定する必要があります」"

#: asiantech.link/api/models/user.py:41
msgid "Superuser must have is_superuser as True"
msgstr "スーパーユーザーは is_superuser を True に設定する必要があります"

#: asiantech.link/api/models/user.py:44
msgid "Superuser must have is_active as True"
msgstr "スーパーユーザーは is_active を True に設定する必要があります"

#: asiantech.link/api/serializers/cv/cv_serializers.py:192
#: asiantech.link/api/serializers/engineers/user_serializers.py:52
#: asiantech.link/api/serializers/recruitment_serializers.py:176
#: asiantech.link/api/serializers/recruitment_serializers.py:681
msgid "Invalid degree code."
msgstr "学位コードが無効です。"

#: asiantech.link/api/serializers/cv/cv_serializers.py:198
#, fuzzy
#| msgid "Invalid language code."
msgid "Invalid language level type."
msgstr "言語コードが無効です。"

#: asiantech.link/api/serializers/engineers/user_serializers.py:409
#, fuzzy
#| msgid "start_date must be less than end_date"
msgid "From date must be before to date."
msgstr "開始日（start_date）は終了日（end_date）より前でなければなりません。"

#: asiantech.link/api/serializers/engineers/user_serializers.py:563
#: asiantech.link/api/serializers/engineers/user_serializers.py:568
#: asiantech.link/api/serializers/engineers/user_serializers.py:573
msgid "Invalid place code."
msgstr "場所コードが無効です。"

#: asiantech.link/api/serializers/engineers/user_serializers.py:580
msgid "Invalid payroll price."
msgstr "給与価格が無効です。"

#: asiantech.link/api/serializers/engineers/user_serializers.py:941
msgid "Invalid sex type."
msgstr "性別タイプが無効です。"

#: asiantech.link/api/serializers/engineers/user_serializers.py:949
#: asiantech.link/api/serializers/general_company/general_company_serializers.py:350
#: asiantech.link/api/serializers/general_company/general_company_serializers.py:447
#: asiantech.link/api/serializers/recruitment_serializers.py:207
#: asiantech.link/api/serializers/recruitment_serializers.py:741
msgid "Invalid country code."
msgstr "国コードが無効です。"

#: asiantech.link/api/serializers/engineers/user_serializers.py:962
#: asiantech.link/api/serializers/general_company/general_company_serializers.py:366
#: asiantech.link/api/serializers/general_company/general_company_serializers.py:466
msgid "Invalid phone number."
msgstr "電話番号が無効です。"

#: asiantech.link/api/serializers/engineers/user_serializers.py:966
#: asiantech.link/api/serializers/general_company/general_company_serializers.py:370
#: asiantech.link/api/serializers/general_company/general_company_serializers.py:470
msgid "Invalid phone number format."
msgstr "電話番号の形式が無効です。"

#: asiantech.link/api/serializers/engineers/user_serializers.py:974
#: asiantech.link/api/serializers/general_company/general_company_serializers.py:356
#: asiantech.link/api/serializers/general_company/general_company_serializers.py:454
#: asiantech.link/api/serializers/recruitment_serializers.py:215
#: asiantech.link/api/serializers/recruitment_serializers.py:218
#: asiantech.link/api/serializers/recruitment_serializers.py:221
#: asiantech.link/api/serializers/recruitment_serializers.py:713
#: asiantech.link/api/serializers/recruitment_serializers.py:716
#: asiantech.link/api/serializers/recruitment_serializers.py:719
msgid "Invalid address code."
msgstr "住所コードが無効です。"

#: asiantech.link/api/serializers/general_company/general_company_serializers.py:290
#: asiantech.link/api/serializers/general_company/general_company_serializers.py:433
#: asiantech.link/api/serializers/general_company/general_company_serializers.py:439
#: asiantech.link/api/serializers/host_company/host_company_serializers.py:59
msgid "Invalid url format."
msgstr "URLの形式が無効です。"

#: asiantech.link/api/serializers/general_company/general_company_serializers.py:296
#: asiantech.link/api/serializers/host_company/host_company_serializers.py:65
msgid "Invalid email format."
msgstr "メールアドレスの形式が無効です。"

#: asiantech.link/api/serializers/general_company/general_company_serializers.py:481
#: asiantech.link/api/serializers/general_company/general_company_serializers.py:487
#: asiantech.link/api/serializers/recruitment_serializers.py:248
#: asiantech.link/api/serializers/recruitment_serializers.py:699
msgid "Invalid currency code."
msgstr "通貨コードが無効です。"

#: asiantech.link/api/serializers/general_company/general_company_serializers.py:640
msgid "compare_content_1"
msgstr ""
"募集要項にあまり合致しない応募者です。\n"
"採用できる可能性はあまり高くないかもしれませんので、ほかの応募者を優先しても"
"よいですが、気になる応募者で\n"
"あれば、面接依頼を出してもよいかもしれません。"

#: asiantech.link/api/serializers/general_company/general_company_serializers.py:642
msgid "compare_content_2"
msgstr ""
"募集要項の多くの条件に合致している応募者です。\n"
"エンジニアの個人情報を確認し、特に問題がなければ面接依頼を出しましょう。\n"
"採用できる可能性が高いですので、提示条件を検討し、チャットで直接、条件面の調"
"整をしましょう。"

#: asiantech.link/api/serializers/general_company/general_company_serializers.py:644
msgid "compare_content_3"
msgstr ""
"募集要項に合致した応募者です！エンジニアの個人情報を確認し、特に問題がなけれ"
"ば面接依頼を出しましょう。"

#: asiantech.link/api/serializers/recruitment_serializers.py:169
#: asiantech.link/api/serializers/recruitment_serializers.py:672
msgid "Invalid sex type"
msgstr "性別タイプが無効です。"

#: asiantech.link/api/serializers/recruitment_serializers.py:183
#: asiantech.link/api/serializers/recruitment_serializers.py:704
msgid "start_date must be less than end_date"
msgstr "開始日（start_date）は終了日（end_date）より前でなければなりません。"

#: asiantech.link/api/serializers/recruitment_serializers.py:192
#: asiantech.link/api/serializers/recruitment_serializers.py:749
msgid "age_from must be less than age_to"
msgstr "age_from は age_to より小さくなければなりません。"

#: asiantech.link/api/serializers/recruitment_serializers.py:200
#: asiantech.link/api/serializers/recruitment_serializers.py:693
msgid "payroll_price_from must be less than payroll_price_to"
msgstr "payroll_price_from は payroll_price_to より小さくなければなりません"

#: asiantech.link/api/serializers/recruitment_serializers.py:236
#: asiantech.link/api/serializers/recruitment_serializers.py:239
#: asiantech.link/api/serializers/recruitment_serializers.py:242
#: asiantech.link/api/serializers/recruitment_serializers.py:727
#: asiantech.link/api/serializers/recruitment_serializers.py:730
#: asiantech.link/api/serializers/recruitment_serializers.py:733
msgid "Invalid pref code."
msgstr "プレファレンスコードが無効です。"

#: asiantech.link/api/serializers/recruitment_serializers.py:255
msgid "Invalid support company."
msgstr "サポート会社が無効です。"

#: asiantech.link/api/serializers/recruitment_serializers.py:765
msgid "years_of_experience must be greater than 0"
msgstr "経験年数（years_of_experience）は0より大きくなければなりません。"

#: asiantech.link/api/serializers/recruitment_serializers.py:779
#: asiantech.link/api/serializers/recruitment_serializers.py:782
msgid "Invalid language code."
msgstr "言語コードが無効です。"

#: asiantech.link/api/services/notify_service/company_notify_service.py:32
msgid "The interview result registration for is incomplete"
msgstr "sender_nameさんの面接合否の登録が完了していません"

#: asiantech.link/api/services/notify_service/company_notify_service.py:61
#: asiantech.link/api/services/notify_service/support_company_notify_service.py:61
#: asiantech.link/api/services/notify_service/support_company_notify_service.py:90
#: asiantech.link/api/services/notify_service/support_company_notify_service.py:125
msgid "Please register the interview date and time with"
msgstr "sender_nameさんとの面接日時を登録してください"

#: asiantech.link/api/services/notify_service/company_notify_service.py:89
msgid "Please register the interview outcome for"
msgstr "sender_nameの面接結果を登録してください"

#: asiantech.link/api/services/notify_service/company_notify_service.py:118
msgid "Please sign the offer acceptance and employment contract with"
msgstr "sender_nameさんとの内定受理入社契約書に署名してください"

#: asiantech.link/api/services/notify_service/engineer_notify_service.py:26
msgid "Please respond to the interview request from"
msgstr "sender_nameからの面接依頼に返答してください"

#: asiantech.link/api/services/notify_service/engineer_notify_service.py:50
msgid "Please respond to the job offer from"
msgstr "sender_nameからの内定に返答してください"

#: asiantech.link/api/services/notify_service/support_company_notify_service.py:32
msgid "Please register the document screening result for"
msgstr "sender_nameさんの書類選考結果を登録してください"

#: asiantech.link/api/views/admin/admin_views.py:52
msgid "List of engineers"
msgstr ""

#: asiantech.link/api/views/admin/admin_views.py:169
#: asiantech.link/api/views/admin/admin_views.py:227
#: asiantech.link/api/views/admin/admin_views.py:259
#: asiantech.link/api/views/admin/admin_views.py:270
#: asiantech.link/api/views/admin/admin_views.py:285
#: asiantech.link/api/views/admin/admin_views.py:578
#: asiantech.link/api/views/admin/admin_views.py:589
#: asiantech.link/api/views/admin/admin_views.py:601
#: asiantech.link/api/views/authentication/authentication_views.py:264
#: asiantech.link/api/views/engineer/engineer_views.py:43
#: asiantech.link/api/views/engineer/engineer_views.py:58
#: asiantech.link/api/views/engineer/engineer_views.py:66
#: asiantech.link/api/views/engineer/engineer_views.py:107
#: asiantech.link/api/views/engineer/engineer_views.py:115
#: asiantech.link/api/views/engineer/engineer_views.py:132
#: asiantech.link/api/views/engineer/engineer_views.py:140
#: asiantech.link/api/views/engineer/engineer_views.py:189
#: asiantech.link/api/views/engineer/engineer_views.py:208
#: asiantech.link/api/views/engineer/engineer_views.py:216
#: asiantech.link/api/views/engineer/engineer_views.py:383
#: asiantech.link/api/views/engineer/engineer_views.py:403
#: asiantech.link/api/views/general/general_company_views.py:49
#: asiantech.link/api/views/general/general_company_views.py:71
#: asiantech.link/api/views/general/general_company_views.py:78
#: asiantech.link/api/views/general/general_company_views.py:84
#: asiantech.link/api/views/general/general_company_views.py:98
#: asiantech.link/api/views/general/general_company_views.py:123
#: asiantech.link/api/views/general/general_company_views.py:198
#: asiantech.link/api/views/general/general_company_views.py:239
#: asiantech.link/api/views/general/general_company_views.py:246
#: asiantech.link/api/views/general/general_company_views.py:262
#: asiantech.link/api/views/general/general_company_views.py:280
#: asiantech.link/api/views/general/general_company_views.py:296
#: asiantech.link/api/views/general/general_company_views.py:313
#: asiantech.link/api/views/general/general_company_views.py:331
#: asiantech.link/api/views/general/general_company_views.py:339
#: asiantech.link/api/views/general/general_company_views.py:358
#: asiantech.link/api/views/general/general_company_views.py:378
#: asiantech.link/api/views/general/general_company_views.py:397
#: asiantech.link/api/views/general/general_company_views.py:416
#: asiantech.link/api/views/general/general_company_views.py:434
#: asiantech.link/api/views/general/general_company_views.py:455
#: asiantech.link/api/views/general/general_company_views.py:536
#: asiantech.link/api/views/general/general_company_views.py:550
#: asiantech.link/api/views/general/general_company_views.py:590
#: asiantech.link/api/views/general/general_company_views.py:600
#: asiantech.link/api/views/general/general_company_views.py:639
#: asiantech.link/api/views/general/general_company_views.py:650
#: asiantech.link/api/views/general/general_company_views.py:700
#: asiantech.link/api/views/general/general_company_views.py:711
#: asiantech.link/api/views/general/general_company_views.py:767
#: asiantech.link/api/views/general/general_company_views.py:804
#: asiantech.link/api/views/general/general_company_views.py:814
#: asiantech.link/api/views/general/general_company_views.py:957
#: asiantech.link/api/views/general/general_company_views.py:978
#: asiantech.link/api/views/general/general_views.py:28
#: asiantech.link/api/views/general/general_views.py:77
#: asiantech.link/api/views/host_company/host_company_views.py:38
#: asiantech.link/api/views/host_company/host_company_views.py:55
#: asiantech.link/api/views/host_company/host_company_views.py:62
#: asiantech.link/api/views/host_company/host_company_views.py:86
#: asiantech.link/api/views/host_company/host_company_views.py:92
#: asiantech.link/api/views/host_company/host_company_views.py:99
#: asiantech.link/api/views/host_company/host_company_views.py:130
#: asiantech.link/api/views/host_company/host_company_views.py:136
#: asiantech.link/api/views/host_company/host_company_views.py:150
#: asiantech.link/api/views/host_company/host_company_views.py:157
#: asiantech.link/api/views/host_company/host_company_views.py:211
#: asiantech.link/api/views/host_company/host_company_views.py:244
#: asiantech.link/api/views/host_company/host_company_views.py:252
#: asiantech.link/api/views/host_company/host_company_views.py:263
#: asiantech.link/api/views/host_company/host_company_views.py:270
#: asiantech.link/api/views/host_company/host_company_views.py:289
#: asiantech.link/api/views/host_company/host_company_views.py:297
#: asiantech.link/api/views/host_company/host_company_views.py:336
#: asiantech.link/api/views/host_support_agency_company/host_support_agency_company_views.py:35
#: asiantech.link/api/views/host_support_agency_company/host_support_agency_company_views.py:81
#: asiantech.link/api/views/host_support_agency_company/host_support_agency_company_views.py:159
#: asiantech.link/api/views/host_support_agency_company/host_support_agency_company_views.py:188
#: asiantech.link/api/views/host_support_agency_company/host_support_agency_company_views.py:196
#: asiantech.link/api/views/host_support_agency_company/host_support_agency_company_views.py:230
#: asiantech.link/api/views/host_support_agency_company/host_support_agency_company_views.py:238
#: asiantech.link/api/views/host_support_agency_company/host_support_agency_company_views.py:295
#: asiantech.link/api/views/notify/count_unread_notify_view.py:24
#: asiantech.link/api/views/notify/count_unread_notify_view.py:50
#: asiantech.link/api/views/notify/get_list_notify_view.py:24
#: asiantech.link/api/views/notify/get_list_notify_view.py:43
#: asiantech.link/api/views/notify/set_read_all_notify_view.py:23
#: asiantech.link/api/views/notify/set_read_all_notify_view.py:32
#: asiantech.link/api/views/notify/set_read_notify_view.py:24
#: asiantech.link/api/views/notify/set_read_notify_view.py:38
#: asiantech.link/api/views/profile/profile_views.py:38
#: asiantech.link/api/views/profile/profile_views.py:53
#: asiantech.link/api/views/profile/profile_views.py:192
#: asiantech.link/api/views/profile/profile_views.py:241
#: asiantech.link/api/views/profile/profile_views.py:285
#: asiantech.link/api/views/profile/profile_views.py:334
#: asiantech.link/api/views/profile/profile_views.py:378
#: asiantech.link/api/views/profile/profile_views.py:427
#: asiantech.link/api/views/profile/profile_views.py:471
#: asiantech.link/api/views/profile/profile_views.py:520
#: asiantech.link/api/views/profile/profile_views.py:564
#: asiantech.link/api/views/profile/profile_views.py:613
#: asiantech.link/api/views/recruit/recruitment_views.py:39
#: asiantech.link/api/views/recruit/recruitment_views.py:60
#: asiantech.link/api/views/recruit/recruitment_views.py:67
#: asiantech.link/api/views/recruit/recruitment_views.py:84
#: asiantech.link/api/views/recruit/recruitment_views.py:91
#: asiantech.link/api/views/recruit/recruitment_views.py:97
#: asiantech.link/api/views/recruit/recruitment_views.py:109
#: asiantech.link/api/views/recruit/recruitment_views.py:122
#: asiantech.link/api/views/recruit/recruitment_views.py:128
#: asiantech.link/api/views/recruit/recruitment_views.py:139
#: asiantech.link/api/views/recruit/recruitment_views.py:146
#: asiantech.link/api/views/recruit/recruitment_views.py:174
#: asiantech.link/api/views/recruit/recruitment_views.py:181
#: asiantech.link/api/views/recruit/recruitment_views.py:305
#: asiantech.link/api/views/recruit/recruitment_views.py:312
#: asiantech.link/api/views/recruit/recruitment_views.py:438
#: asiantech.link/api/views/recruit/recruitment_views.py:444
#: asiantech.link/api/views/recruit/recruitment_views.py:475
#: asiantech.link/api/views/recruit/recruitment_views.py:481
#: asiantech.link/api/views/recruit/recruitment_views.py:567
#: asiantech.link/api/views/recruit/recruitment_views.py:574
#: asiantech.link/api/views/recruit/recruitment_views.py:662
#: asiantech.link/api/views/recruit/recruitment_views.py:670
#: asiantech.link/api/views/recruit/recruitment_views.py:703
#: asiantech.link/api/views/recruit/recruitment_views.py:714
#: asiantech.link/api/views/recruit/recruitment_views.py:751
#: asiantech.link/api/views/recruit/recruitment_views.py:760
#: asiantech.link/api/views/recruit/recruitment_views.py:766
#: asiantech.link/api/views/recruit/recruitment_views.py:783
#: asiantech.link/api/views/recruit/recruitment_views.py:796
#: asiantech.link/api/views/recruit/recruitment_views.py:805
#: asiantech.link/api/views/recruit/recruitment_views.py:852
#: asiantech.link/api/views/recruit/recruitment_views.py:889
msgid "Success"
msgstr "成功"

#: asiantech.link/api/views/admin/admin_views.py:178
msgid "List of email schedules"
msgstr ""

#: asiantech.link/api/views/admin/admin_views.py:237
msgid "List of registrars"
msgstr ""

#: asiantech.link/api/views/admin/admin_views.py:296
#: asiantech.link/api/views/admin/admin_views.py:384
msgid "Email schedule created successfully"
msgstr "メールスケジュールが正常に作成されました。"

#: asiantech.link/api/views/admin/admin_views.py:314
#: asiantech.link/api/views/admin/admin_views.py:417
msgid "Target email not found"
msgstr "メールアドレスが見つかりません。"

#: asiantech.link/api/views/admin/admin_views.py:367
#: asiantech.link/api/views/admin/admin_views.py:470
msgid "Send test email successfully"
msgstr "テストメールを送信しました。"

#: asiantech.link/api/views/admin/admin_views.py:376
#: asiantech.link/api/views/admin/admin_views.py:478
msgid "Email personal information already exists"
msgstr "メールアドレスはすでに登録されています！"

#: asiantech.link/api/views/admin/admin_views.py:395
#: asiantech.link/api/views/admin/admin_views.py:486
msgid "Email schedule updated successfully"
msgstr "メールスケジュールが正常に更新されました"

#: asiantech.link/api/views/admin/admin_views.py:407
#: asiantech.link/api/views/admin/admin_views.py:507
#: asiantech.link/api/views/admin/admin_views.py:572
msgid "Email schedule not found"
msgstr "ユーザーが見つかりません"

#: asiantech.link/api/views/admin/admin_views.py:497
#: asiantech.link/api/views/admin/admin_views.py:512
msgid "Email schedule deleted successfully"
msgstr "メールスケジュールを削除しました"

#: asiantech.link/api/views/admin/admin_views.py:524
#: asiantech.link/api/views/admin/admin_views.py:550
msgid "Email schedule list deleted successfully"
msgstr "メールスケジュールのリストを削除しました"

#: asiantech.link/api/views/admin/admin_views.py:540
msgid "Invalid data"
msgstr "無効なデータです。"

#: asiantech.link/api/views/admin/admin_views.py:545
msgid "No email schedule ids provided"
msgstr "メールスケジュールのIDが提供されていません"

#: asiantech.link/api/views/admin/admin_views.py:561
msgid "Email schedule details"
msgstr "メールスケジュールの詳細"

#: asiantech.link/api/views/admin/admin_views.py:603
#, fuzzy
#| msgid "User not found"
msgid "Engineer not found"
msgstr "ユーザーが見つかりません"

#: asiantech.link/api/views/authentication/authentication_views.py:43
msgid "Verification email sent"
msgstr "確認メールが送信されました。"

#: asiantech.link/api/views/authentication/authentication_views.py:46
msgid "Verification whatsapp sent"
msgstr "確認のWhatsAppを送信しました"

#: asiantech.link/api/views/authentication/authentication_views.py:49
msgid "Email verified successfully"
msgstr "メールアドレスの確認が成功しました。"

#: asiantech.link/api/views/authentication/authentication_views.py:52
msgid "User created successfully"
msgstr "ユーザーが正常に作成されました。"

#: asiantech.link/api/views/authentication/authentication_views.py:55
#: asiantech.link/api/views/authentication/authentication_views.py:415
#: asiantech.link/api/views/authentication/authentication_views.py:540
#: asiantech.link/api/views/authentication/authentication_views.py:584
#: asiantech.link/api/views/authentication/authentication_views.py:801
#: asiantech.link/api/views/authentication/authentication_views.py:817
#: asiantech.link/api/views/authentication/authentication_views.py:1065
msgid "Login successfully"
msgstr "ログインに成功しました。"

#: asiantech.link/api/views/authentication/authentication_views.py:57
msgid "Reset password successfully"
msgstr "パスワードのリセットが成功しました。"

#: asiantech.link/api/views/authentication/authentication_views.py:102
msgid "emailRegisterVerificationTitle"
msgstr "【AsianTech.Link】メールアドレス認証のお願い"

#: asiantech.link/api/views/authentication/authentication_views.py:109
msgid "emailConfirmContent1"
msgstr "この度はAsianTech.Linkにご登録いただき、ありがとうございます。"

#: asiantech.link/api/views/authentication/authentication_views.py:110
msgid "emailConfirmContent2"
msgstr ""
"安全にサービスをご利用いただくために、メールアドレスの認証が必要です。下のボ"
"タンからメールアドレスを認証してください。"

#: asiantech.link/api/views/authentication/authentication_views.py:111
msgid "emailConfirmContent3"
msgstr ""
"安全にご利用いただくために、メールアドレスの認証が必要です。下のボタンから"
"メールアドレスを認証してください。"

#: asiantech.link/api/views/authentication/authentication_views.py:112
#: asiantech.link/api/views/authentication/authentication_views.py:315
msgid "emailConfirmContent4"
msgstr "© Copyright AsianTech.Link All Right Reserved."

#: asiantech.link/api/views/authentication/authentication_views.py:114
msgid "Verification Email"
msgstr "確認メールが送信されました"

#: asiantech.link/api/views/authentication/authentication_views.py:119
msgid "verifyYourAccount"
msgstr "メールアドレスを認証する"

#: asiantech.link/api/views/authentication/authentication_views.py:155
#: asiantech.link/api/views/authentication/authentication_views.py:157
msgid "Email is already linked to an another account"
msgstr ""

#: asiantech.link/api/views/authentication/authentication_views.py:166
#: asiantech.link/api/views/authentication/authentication_views.py:296
#: asiantech.link/api/views/authentication/authentication_views.py:986
#: asiantech.link/api/views/authentication/authentication_views.py:1049
#: asiantech.link/api/views/authentication/authentication_views.py:1101
#: asiantech.link/api/views/authentication/authentication_views.py:1176
msgid "User not found!"
msgstr "ユーザーが見つかりません！"

#: asiantech.link/api/views/authentication/authentication_views.py:169
#: asiantech.link/api/views/authentication/authentication_views.py:298
msgid "User is already verified!"
msgstr "ユーザーはすでに確認済みです！"

#: asiantech.link/api/views/authentication/authentication_views.py:173
#: asiantech.link/api/views/authentication/authentication_views.py:552
msgid "CAPTCHA verification failed"
msgstr "CAPTCHA 認証に失敗しました"

#: asiantech.link/api/views/authentication/authentication_views.py:266
msgid "Invalid code!"
msgstr "無効なコードです!"

#: asiantech.link/api/views/authentication/authentication_views.py:282
msgid "Token is required!"
msgstr "トークンが必要です！"

#: asiantech.link/api/views/authentication/authentication_views.py:306
msgid "emailRegisterSuccessTitle"
msgstr "【アカウント登録完了】AsianTech.Linkへの登録が完了しました"

#: asiantech.link/api/views/authentication/authentication_views.py:313
msgid "emailRegisterSuccessContent1"
msgstr "AsianTech.Link へのアカウント登録が完了しました。"

#: asiantech.link/api/views/authentication/authentication_views.py:314
msgid "emailRegisterSuccessContent2"
msgstr ""
"AsianTech.Link を通してあなたの魅力を伝え、 魅力的な求人とマッチングしましょ"
"う。"

#: asiantech.link/api/views/authentication/authentication_views.py:331
#: asiantech.link/api/views/authentication/authentication_views.py:333
#: asiantech.link/api/views/authentication/authentication_views.py:1006
#: asiantech.link/api/views/authentication/authentication_views.py:1014
#: asiantech.link/api/views/authentication/authentication_views.py:1046
msgid "Verification token has expired!"
msgstr "確認トークンの期限が切れました！"

#: asiantech.link/api/views/authentication/authentication_views.py:335
msgid "Invalid verification token!"
msgstr "無効な確認トークンです！"

#: asiantech.link/api/views/authentication/authentication_views.py:350
#: asiantech.link/api/views/authentication/authentication_views.py:428
msgid "Validation errors in your request"
msgstr "リクエストに検証エラーがあります。"

#: asiantech.link/api/views/authentication/authentication_views.py:391
#: asiantech.link/api/views/authentication/authentication_views.py:501
msgid "The email address is already registered with engineer"
msgstr "「メールアドレスはエンジニアによってすでに登録されています。」"

#: asiantech.link/api/views/authentication/authentication_views.py:393
#: asiantech.link/api/views/authentication/authentication_views.py:495
msgid "The email address is already registered with company"
msgstr "メールアドレスはすでに会社に登録されています"

#: asiantech.link/api/views/authentication/authentication_views.py:394
msgid "Email is already registered!"
msgstr "メールアドレスはすでに登録されています！"

#: asiantech.link/api/views/authentication/authentication_views.py:436
#: asiantech.link/api/views/authentication/authentication_views.py:440
#: asiantech.link/api/views/authentication/authentication_views.py:465
#: asiantech.link/api/views/authentication/authentication_views.py:469
msgid "Email address or password does not match"
msgstr "メールアドレスまたはパスワードが一致しません"

#: asiantech.link/api/views/authentication/authentication_views.py:439
#: asiantech.link/api/views/authentication/authentication_views.py:454
#: asiantech.link/api/views/authentication/authentication_views.py:468
msgid "Login failed."
msgstr "ログインに失敗しました"

#: asiantech.link/api/views/authentication/authentication_views.py:451
#: asiantech.link/api/views/authentication/authentication_views.py:455
msgid "The user ID or password is incorrect."
msgstr "メールアドレスまたはパスワードが一致しません"

#: asiantech.link/api/views/authentication/authentication_views.py:489
#: asiantech.link/api/views/authentication/authentication_views.py:572
msgid "CAPTCHA verification required"
msgstr "CAPTCHA認証が必要です。"

#: asiantech.link/api/views/authentication/authentication_views.py:507
msgid ""
"The account with this email has been registered with the receiving support "
"organization."
msgstr ""

#: asiantech.link/api/views/authentication/authentication_views.py:698
#: asiantech.link/api/views/authentication/authentication_views.py:700
#: asiantech.link/api/views/authentication/authentication_views.py:738
#: asiantech.link/api/views/authentication/authentication_views.py:740
#: asiantech.link/api/views/authentication/authentication_views.py:809
#: asiantech.link/api/views/authentication/authentication_views.py:827
#, fuzzy
#| msgid "Login failed."
msgid "Login failed!"
msgstr "ログインに失敗しました"

#: asiantech.link/api/views/authentication/authentication_views.py:811
#, fuzzy
#| msgid "Invalid sex type"
msgid "Invalid SNS type!"
msgstr "性別タイプが無効です。"

#: asiantech.link/api/views/authentication/authentication_views.py:921
msgid "Your Verification Code"
msgstr "あなたの確認コード"

#: asiantech.link/api/views/authentication/authentication_views.py:926
msgid "Email Verification"
msgstr "メールアドレスの確認"

#: asiantech.link/api/views/authentication/authentication_views.py:929
msgid "Please use the verification code below to complete your login:"
msgstr "ログインを完了するために以下の確認コードをご利用ください："

#: asiantech.link/api/views/authentication/authentication_views.py:932
msgid "Note: This code is valid for a limited time."
msgstr "注意: このコードは期間限定で有効です。"

#: asiantech.link/api/views/authentication/authentication_views.py:946
msgid "We sent you a code to verify your login"
msgstr "ログインを確認するためのコードを送信しました"

#: asiantech.link/api/views/authentication/authentication_views.py:968
#: asiantech.link/api/views/authentication/authentication_views.py:978
#: asiantech.link/api/views/authentication/authentication_views.py:1034
msgid "Password Reset Request"
msgstr "パスワードリセットのリクエスト"

#: asiantech.link/api/views/authentication/authentication_views.py:978
msgid "Dear"
msgstr "拝啓"

#: asiantech.link/api/views/authentication/authentication_views.py:978
msgid "password_request_reset_content_1"
msgstr ""
"パスワードのリセットリクエストを受信しました。新しいパスワードを受け取るため"
"に確認ボタンを押してください："

#: asiantech.link/api/views/authentication/authentication_views.py:979
msgid "password_request_reset_content_2"
msgstr ""
"パスワードのリセットをリクエストしていない場合は、このメールを無視してくださ"
"い。"

#: asiantech.link/api/views/authentication/authentication_views.py:979
#: asiantech.link/api/views/authentication/authentication_views.py:1035
msgid "passwordResetContent2"
msgstr "この新しいパスワードでログインし、すぐに変更してください。"

#: asiantech.link/api/views/authentication/authentication_views.py:979
msgid "Thank you"
msgstr "ありがとうございます"

#: asiantech.link/api/views/authentication/authentication_views.py:984
msgid "Password reset email sent!"
msgstr "パスワードリセットメールが送信されました！"

#: asiantech.link/api/views/authentication/authentication_views.py:1019
msgid "Invalid token type!"
msgstr "無効なトークンタイプです！"

#: asiantech.link/api/views/authentication/authentication_views.py:1026
msgid "Your Password Reset Request"
msgstr "パスワードリセットのリクエスト"

#: asiantech.link/api/views/authentication/authentication_views.py:1032
msgid "Password Reset"
msgstr "パスワードリセット"

#: asiantech.link/api/views/authentication/authentication_views.py:1034
msgid "Hello"
msgstr "こんにちは"

#: asiantech.link/api/views/authentication/authentication_views.py:1034
msgid "passwordResetContent1"
msgstr "パスワードがリセットされました。新しいパスワードは以下の通りです："

#: asiantech.link/api/views/authentication/authentication_views.py:1035
msgid "passwordResetContent3"
msgstr ""
"このパスワードリセットを要求していない場合は、サポートチームにお問い合わせく"
"ださい。"

#: asiantech.link/api/views/authentication/authentication_views.py:1044
msgid "Password reset successfully!"
msgstr "パスワードのリセットが成功しました！"

#: asiantech.link/api/views/authentication/authentication_views.py:1099
msgid "Invalid code or email!"
msgstr "無効なコードまたはメール！"

#: asiantech.link/api/views/authentication/authentication_views.py:1103
msgid "Invalid email or password!"
msgstr "無効なメールアドレスまたはパスワードです！"

#: asiantech.link/api/views/authentication/authentication_views.py:1111
#: asiantech.link/api/views/authentication/authentication_views.py:1125
msgid "Logout successfully"
msgstr "ログインに成功しました"

#: asiantech.link/api/views/authentication/authentication_views.py:1131
#: asiantech.link/api/views/authentication/authentication_views.py:1144
msgid "Captcha generated successfully"
msgstr "CAPTCHAが正常に生成されました。"

#: asiantech.link/api/views/authentication/authentication_views.py:1151
msgid "Check captcha required in login"
msgstr "ログイン時に必要なキャプチャを確認してください"

#: asiantech.link/api/views/authentication/authentication_views.py:1211
#: asiantech.link/api/views/authentication/authentication_views.py:1228
#: asiantech.link/api/views/authentication/authentication_views.py:1237
#: asiantech.link/api/views/authentication/authentication_views.py:1256
msgid "Account deleted successfully"
msgstr "アカウントが正常に削除されました"

#: asiantech.link/api/views/authentication/authentication_views.py:1258
msgid "error"
msgstr "エラー"

#: asiantech.link/api/views/chat/chat_views.py:22
#: asiantech.link/api/views/chat/chat_views.py:56
msgid "All messages marked as read"
msgstr ""

#: asiantech.link/api/views/chat/chat_views.py:38
#, fuzzy
#| msgid "Recruitment does not exist"
msgid "Group does not exist."
msgstr "募集は存在しません"

#: asiantech.link/api/views/chat/chat_views.py:44
msgid "User is not a member of this group."
msgstr ""

#: asiantech.link/api/views/chat/chat_views.py:59
msgid "No messages to mark as read"
msgstr ""

#: asiantech.link/api/views/engineer/engineer_views.py:87
#: asiantech.link/api/views/engineer/engineer_views.py:160
msgid "Agency company not found."
msgstr "代理店会社が見つかりません。"

#: asiantech.link/api/views/engineer/engineer_views.py:94
msgid "This agency company is already linked with the engineer."
msgstr "この代理店会社はすでにエンジニアにリンクされています。"

#: asiantech.link/api/views/engineer/engineer_views.py:169
msgid "This agency company has already been deleted."
msgstr "この代理店会社はすでに削除されています。"

#: asiantech.link/api/views/engineer/engineer_views.py:176
msgid "Agency company successfully deleted."
msgstr "代理店会社が正常に削除されました。"

#: asiantech.link/api/views/engineer/engineer_views.py:179
msgid "This agency company is not linked with the engineer."
msgstr "この代理店会社はエンジニアにリンクされていません。"

#: asiantech.link/api/views/engineer/engineer_views.py:231
#: asiantech.link/api/views/general/general_company_views.py:477
msgid "Invalid ordering field."
msgstr "無効な並べ替えフィールドです。"

#: asiantech.link/api/views/engineer/engineer_views.py:239
#: asiantech.link/api/views/engineer/engineer_views.py:368
msgid "Invalid recruit_progress_code"
msgstr "無効な recruit_progress_code です。"

#: asiantech.link/api/views/engineer/engineer_views.py:396
#: asiantech.link/api/views/general/general_company_views.py:352
#: asiantech.link/api/views/profile/profile_views.py:233
#: asiantech.link/api/views/profile/profile_views.py:326
#: asiantech.link/api/views/profile/profile_views.py:419
#: asiantech.link/api/views/profile/profile_views.py:512
#: asiantech.link/api/views/profile/profile_views.py:605
msgid "User not found"
msgstr "ユーザーが見つかりません"

#: asiantech.link/api/views/general/general_company_views.py:63
msgid "Email already exists"
msgstr "メールはすでに登録されています！"

#: asiantech.link/api/views/general/general_company_views.py:93
#: asiantech.link/api/views/host_company/host_company_views.py:69
#: asiantech.link/api/views/host_company/host_company_views.py:106
msgid "User does not have company"
msgstr "ユーザーに会社がありません"

#: asiantech.link/api/views/general/general_company_views.py:100
#: asiantech.link/api/views/general/general_company_views.py:125
#: asiantech.link/api/views/host_company/host_company_views.py:75
#: asiantech.link/api/views/host_company/host_company_views.py:112
#: asiantech.link/api/views/host_support_agency_company/host_support_agency_company_views.py:45
#: asiantech.link/api/views/host_support_agency_company/host_support_agency_company_views.py:72
#: asiantech.link/api/views/media/company_media_views.py:59
#: asiantech.link/api/views/media/company_media_views.py:122
msgid "Company not found"
msgstr "会社が見つかりません。"

#: asiantech.link/api/views/general/general_company_views.py:274
msgid "Filter saved successfully"
msgstr "フィルターが正常に保存されました"

#: asiantech.link/api/views/general/general_company_views.py:304
msgid "Filter deleted successfully"
msgstr "フィルターが正常に削除されました"

#: asiantech.link/api/views/general/general_company_views.py:327
msgid "You can't update this status!"
msgstr "このステータスを更新することはできません。"

#: asiantech.link/api/views/general/general_company_views.py:485
#: asiantech.link/api/views/general/general_company_views.py:670
#: asiantech.link/api/views/general/general_company_views.py:831
msgid "You don't have permission to access this company"
msgstr ""

#: asiantech.link/api/views/general/general_company_views.py:487
#: asiantech.link/api/views/general/general_company_views.py:668
#: asiantech.link/api/views/general/general_company_views.py:829
msgid "Host company not found"
msgstr "会社が見つかりません。"

#: asiantech.link/api/views/general/general_company_views.py:573
#: asiantech.link/api/views/general/general_company_views.py:576
#: asiantech.link/api/views/general/general_company_views.py:578
#: asiantech.link/api/views/general/general_company_views.py:627
#: asiantech.link/api/views/general/general_company_views.py:630
#: asiantech.link/api/views/general/general_company_views.py:632
#: asiantech.link/api/views/general/general_company_views.py:792
#: asiantech.link/api/views/general/general_company_views.py:795
#: asiantech.link/api/views/general/general_company_views.py:797
#: asiantech.link/api/views/general/general_company_views.py:873
#: asiantech.link/api/views/general/general_company_views.py:876
#: asiantech.link/api/views/general/general_company_views.py:878
msgid "You don't have permission to access this apply"
msgstr ""

#: asiantech.link/api/views/general/general_company_views.py:592
#: asiantech.link/api/views/general/general_company_views.py:641
#: asiantech.link/api/views/general/general_company_views.py:702
#: asiantech.link/api/views/general/general_company_views.py:806
#: asiantech.link/api/views/general/general_company_views.py:843
#: asiantech.link/api/views/general/general_company_views.py:949
#: asiantech.link/api/views/host_company/host_company_views.py:340
msgid "Apply not found"
msgstr "応募が見つかりません。"

#: asiantech.link/api/views/general/general_company_views.py:946
msgid "Something went wrong!"
msgstr "問題が発生しました。"

#: asiantech.link/api/views/general/general_company_views.py:968
#: asiantech.link/api/views/host_support_agency_company/host_support_agency_company_views.py:205
#: asiantech.link/api/views/recruit/recruitment_views.py:49
#: asiantech.link/api/views/recruit/recruitment_views.py:155
msgid "You do not own the company"
msgstr "あなたは会社の所有者ではない"

#: asiantech.link/api/views/general/general_views.py:46
msgid "Current language is: "
msgstr "現在の言語は: "

#: asiantech.link/api/views/host_company/host_company_views.py:88
msgid "User already subscribed"
msgstr "ユーザーはすでに登録済みです。"

#: asiantech.link/api/views/host_company/host_company_views.py:120
msgid "User not subscribed"
msgstr "ユーザーは登録されていません。"

#: asiantech.link/api/views/host_company/host_company_views.py:128
msgid "Data not found"
msgstr "データが見つかりません。"

#: asiantech.link/api/views/host_company/host_company_views.py:143
msgid "User does not have company."
msgstr "ユーザーには会社がありません。"

#: asiantech.link/api/views/host_company/host_company_views.py:172
msgid "You have already make a request interview"
msgstr ""

#: asiantech.link/api/views/host_company/host_company_views.py:213
#: asiantech.link/api/views/host_support_agency_company/host_support_agency_company_views.py:161
msgid "You have requested an interview for this engineer."
msgstr ""

#: asiantech.link/api/views/host_company/host_company_views.py:331
msgid "Notification of acceptance has been sent."
msgstr "受け入れ通知が送信されました。"

#: asiantech.link/api/views/host_company/host_company_views.py:338
msgid "Invalid status"
msgstr "無効なステータスです。"

#: asiantech.link/api/views/host_support_agency_company/host_support_agency_company_views.py:102
#: asiantech.link/api/views/host_support_agency_company/host_support_agency_company_views.py:213
msgid "Host company is not belong to you"
msgstr ""

#: asiantech.link/api/views/host_support_agency_company/host_support_agency_company_views.py:109
msgid "This engineer already make a interview to host company"
msgstr "そのメールアドレスがすでに企業側で登録されています。"

#: asiantech.link/api/views/host_support_agency_company/host_support_agency_company_views.py:247
msgid "You are not authorized to access this endpoint"
msgstr ""

#: asiantech.link/api/views/media/admin_upload_avatar_engineer_view.py:53
#: asiantech.link/api/views/media/admin_upload_avatar_engineer_view.py:77
#: asiantech.link/api/views/media/avatar_views.py:44
#: asiantech.link/api/views/media/avatar_views.py:68
#: asiantech.link/api/views/media/company_media_views.py:48
#: asiantech.link/api/views/media/company_media_views.py:81
#: asiantech.link/api/views/media/company_media_views.py:110
#: asiantech.link/api/views/media/company_media_views.py:174
#: asiantech.link/api/views/media/contract_media_view.py:56
#: asiantech.link/api/views/media/contract_media_view.py:117
#: asiantech.link/api/views/media/contract_media_view.py:148
#: asiantech.link/api/views/media/contract_media_view.py:212
#: asiantech.link/api/views/media/passport_views.py:44
#: asiantech.link/api/views/media/passport_views.py:69
#: asiantech.link/api/views/media/recruit_media_views.py:44
#: asiantech.link/api/views/media/recruit_media_views.py:65
msgid "Image uploaded successfully"
msgstr "画像のアップロードに成功しました"

#: asiantech.link/api/views/media/admin_upload_avatar_engineer_view.py:92
#: asiantech.link/api/views/media/admin_upload_avatar_engineer_view.py:110
#: asiantech.link/api/views/media/avatar_views.py:74
#: asiantech.link/api/views/media/avatar_views.py:92
msgid "Image deleted successfully"
msgstr "画像を削除しました"

#: asiantech.link/api/views/media/company_media_views.py:53
#: asiantech.link/api/views/media/company_media_views.py:116
#: asiantech.link/api/views/media/recruit_media_views.py:49
msgid "You are not a company"
msgstr "あなたは会社ではありません"

#: asiantech.link/api/views/media/company_media_views.py:139
msgid "Index must be 0,1,2"
msgstr "インデックスは0、1、2でなければなりません"

#: asiantech.link/api/views/media/contract_media_view.py:68
msgid "You can not upload contract image now, recruit progress code !=60"
msgstr "現在、契約画像をアップロードできません。採用進捗コード !=60"

#: asiantech.link/api/views/media/contract_media_view.py:167
msgid "You are not authorized to upload contract image"
msgstr ""

#: asiantech.link/api/views/media/contract_media_view.py:171
msgid "You can not upload contract image now, status progress code is invalid"
msgstr "現在、契約画像をアップロードできません。ステータス進行コードが無効です"

#: asiantech.link/api/views/media/passport_views.py:75
#: asiantech.link/api/views/media/passport_views.py:93
msgid "Passport image deleted successfully"
msgstr "パスポート画像が正常に削除されました"

#: asiantech.link/api/views/notify/set_read_notify_view.py:34
msgid "Invalid request"
msgstr "無効なリクエストです。"

#: asiantech.link/api/views/profile/cv_views.py:52
#: asiantech.link/api/views/profile/cv_views.py:321
#: asiantech.link/api/views/profile/cv_views.py:389
#, fuzzy
#| msgid "Image uploaded successfully"
msgid "CV uploaded successfully"
msgstr "画像のアップロードに成功しました"

#: asiantech.link/api/views/profile/cv_views.py:58
msgid "File size must be less than 5MB"
msgstr "ファイルサイズは5MB未満でなければなりません"

#: asiantech.link/api/views/profile/cv_views.py:288
msgid "Invalid document type: not a resume"
msgstr "無効な文書タイプ: 履歴書ではありません"

#: asiantech.link/api/views/profile/cv_views.py:331
#, fuzzy
#| msgid "User not found"
msgid "CV not found"
msgstr "ユーザーが見つかりません"

#: asiantech.link/api/views/profile/profile_views.py:86
msgid "This email is already in use."
msgstr "メールアドレスはすでに登録されています！"

#: asiantech.link/api/views/profile/profile_views.py:146
msgid "Password updated successfully"
msgstr "パスワードが正常に更新されました"

#: asiantech.link/api/views/profile/profile_views.py:162
#: asiantech.link/api/views/profile/profile_views.py:184
msgid "New password must be different from the current password."
msgstr "新しいパスワードは現在のパスワードとは異なる必要があります。"

#: asiantech.link/api/views/profile/profile_views.py:182
msgid "Password updated successfully."
msgstr "パスワードが正常に更新されました。"

#: asiantech.link/api/views/profile/profile_views.py:186
msgid "Invalid current password."
msgstr "現在のパスワードが無効です。"

#: asiantech.link/api/views/recruit/recruitment_views.py:82
#: asiantech.link/api/views/recruit/recruitment_views.py:106
#: asiantech.link/api/views/recruit/recruitment_views.py:115
#: asiantech.link/api/views/recruit/recruitment_views.py:136
#: asiantech.link/api/views/recruit/recruitment_views.py:583
#: asiantech.link/api/views/recruit/recruitment_views.py:658
#: asiantech.link/api/views/recruit/recruitment_views.py:705
#: asiantech.link/api/views/recruit/recruitment_views.py:753
msgid "Recruitment does not exist"
msgstr "募集は存在しません"

#: asiantech.link/api/views/recruit/recruitment_views.py:300
#: asiantech.link/api/views/recruit/recruitment_views.py:436
msgid "Error"
msgstr "エラー"

#: asiantech.link/api/views/recruit/recruitment_views.py:460
msgid "Failed"
msgstr "失敗した"

#: asiantech.link/api/views/recruit/recruitment_views.py:495
msgid "ComCompany not found"
msgstr "ComCompanyが見つかりません"

#: asiantech.link/api/views/recruit/recruitment_views.py:502
msgid "MapEngAgc not found"
msgstr "ユーザーが見つかりません"

#: asiantech.link/api/views/recruit/recruitment_views.py:516
msgid "Already applied"
msgstr "すでに適用済み"

#: asiantech.link/api/views/recruit/recruitment_views.py:533
msgid "You have already applied for this recruitment"
msgstr ""

#: asiantech.link/api/views/recruit/recruitment_views.py:656
msgid "Recruitment does not exist or you do not have access to it"
msgstr "募集が存在しない、または応募する権利がありません"

#: asiantech.link/api/views/recruit/recruitment_views.py:679
#: asiantech.link/api/views/recruit/recruitment_views.py:724
msgid "Recruit ID is required"
msgstr "採用IDが必要です"

#: asiantech.link/api/views/recruit/recruitment_views.py:687
#: asiantech.link/api/views/recruit/recruitment_views.py:776
#: asiantech.link/api/views/recruit/recruitment_views.py:824
msgid "No application found"
msgstr "アプリケーションが見つかりません"

#: asiantech.link/api/views/recruit/recruitment_views.py:698
#: asiantech.link/api/views/recruit/recruitment_views.py:739
msgid "Invalid recruit progress code to cancel"
msgstr "キャンセルするには無効な採用進捗コードが必要です"

#: asiantech.link/api/views/recruit/recruitment_views.py:737
msgid "Interview datetime is required"
msgstr "面接日時は必須です"

#: asiantech.link/api/views/recruit/recruitment_views.py:798
msgid "Invalid recruit progress code to sign contract"
msgstr "契約に署名するための採用進捗コードが無効です"

#: asiantech.link/api/views/recruit/recruitment_views.py:831
msgid "No application found for the provided recruitment ID"
msgstr "提供された採用IDに該当する応募が見つかりません"

#: asiantech.link/api/views/recruit/recruitment_views.py:837
msgid "No accept sign found for the application"
msgstr "アプリケーションの承認サインが見つかりません"

#: asiantech.link/core/settings/common.py:132
msgid "English"
msgstr "英語"

#: asiantech.link/core/settings/common.py:133
msgid "Japanese"
msgstr "日本語"

#: asiantech.link/core/settings/common.py:134
msgid "Myanmar"
msgstr "ミャンマー語"

#: asiantech.link/core/settings/common.py:135
msgid "Indonesian"
msgstr "インドネシア語"

#: asiantech.link/core/settings/common.py:136
msgid "Nepali"
msgstr "ネパール語"

#: asiantech.link/core/settings/common.py:137
msgid "Vietnamese"
msgstr "ベトナム語"

#: asiantech.link/utils/permissions.py:22
#: asiantech.link/utils/permissions.py:36
msgid "Account has not been verified"
msgstr "アカウントは確認されていません"

#: asiantech.link/utils/permissions.py:39
#: asiantech.link/utils/permissions.py:53
#: asiantech.link/utils/permissions.py:64
#: asiantech.link/utils/permissions.py:83
#: asiantech.link/utils/permissions.py:96
#: asiantech.link/utils/permissions.py:107
#: asiantech.link/utils/permissions.py:120
#: asiantech.link/utils/permissions.py:132
msgid "Access denied"
msgstr ""

#: asiantech.link/utils/utils.py:508 asiantech.link/utils/utils.py:644
msgid "Fresher"
msgstr "新人"

#: asiantech.link/utils/utils.py:510 asiantech.link/utils/utils.py:646
msgid "Junior"
msgstr "初級"

#: asiantech.link/utils/utils.py:512 asiantech.link/utils/utils.py:648
msgid "Middle"
msgstr "中級"

#: asiantech.link/utils/utils.py:514 asiantech.link/utils/utils.py:650
msgid "Senior"
msgstr "上級"

#: asiantech.link/utils/validators.py:29
msgid "Email is too long"
msgstr "メールが長すぎます"

#: asiantech.link/utils/validators.py:35
msgid "Email must contain a single '@' symbol"
msgstr "メールには '@' 記号が 1 つ含まれている必要があります"

#: asiantech.link/utils/validators.py:39
msgid "Local part of the email is too long (>= 64 characters)"
msgstr "メールのローカル部分が長すぎます (>= 64 文字)"

#: asiantech.link/utils/validators.py:43
msgid "Email contains invalid characters"
msgstr "メールに無効な文字が含まれています"

#: asiantech.link/utils/validators.py:47
msgid "Email contains consecutive periods"
msgstr "メールに連続したピリオドが含まれている"

#: asiantech.link/utils/validators.py:51
msgid ""
"Email has periods at invalid positions (start/end of local part or end of "
"email)"
msgstr ""
"メールに無効な位置（ローカル部分の開始/終了またはメールの終了）にピリオドがあ"
"ります"

#: asiantech.link/utils/validators.py:55
msgid "Domain part of the email should not be an IP address"
msgstr "メールのドメイン部分はIPアドレスであってはなりません"

#: asiantech.link/utils/validators.py:57
msgid "Email is valid"
msgstr "メールアドレスは有効です"

#: asiantech.link/utils/validators.py:63
msgid "Password length must be between 8 and 20 characters"
msgstr "パスワードの長さは8文字から20文字までです"

#: asiantech.link/utils/validators.py:68
msgid "Password contains full-width characters"
msgstr "パスワードに全角文字が含まれています"

#: asiantech.link/utils/validators.py:72
msgid "Password must contain at least one uppercase letter"
msgstr "パスワードには少なくとも1つの大文字を含める必要があります"

#: asiantech.link/utils/validators.py:76
msgid "Password must contain at least one lowercase letter"
msgstr "パスワードには少なくとも1つの小文字を含める必要があります"

#: asiantech.link/utils/validators.py:80
msgid "Password must contain at least one number"
msgstr "パスワードには少なくとも1つの数字を含める必要があります"

#: asiantech.link/utils/validators.py:84
msgid "Password must contain at least one special character"
msgstr "パスワードには少なくとも1つの特殊文字を含める必要があります"

#: asiantech.link/utils/validators.py:86
msgid "Password is valid"
msgstr "パスワードは有効です"

#~ msgid "Expert"
#~ msgstr "専門家"

#~ msgid "Facebook authentication successful"
#~ msgstr "Facebook認証に成功しました"

#~ msgid "Linkedin authentication successful"
#~ msgstr "Linkedin認証が成功しました"

#~ msgid "Zalo authentication successful"
#~ msgstr "Zalo認証が成功しました"

#~ msgid "Get user info with zalo id"
#~ msgstr "Zalo IDでユーザー情報を取得する"

#~ msgid "Get user info with whatsapp number"
#~ msgstr "WhatsApp番号でユーザー情報を取得する"

#~ msgid "WhatsApp authentication successful"
#~ msgstr "WhatsApp認証が成功しました"

#~ msgid "Please sign the contract for"
#~ msgstr "の契約書に署名してください"

#~ msgid "Failed to read PDF content"
#~ msgstr "PDFコンテンツの読み取りに失敗しました"

#~ msgid "Email is too long (>= 201 characters)"
#~ msgstr "メールが長すぎます (>= 201文字)"
