# SOME DESCRIPTIVE TITLE.
# Copyright (C) YEAR THE PACKAGE'S COPYRIGHT HOLDER
# This file is distributed under the same license as the PACKAGE package.
# <AUTHOR> <EMAIL>, YEAR.
#
msgid ""
msgstr ""
"Project-Id-Version: PACKAGE VERSION\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-06-26 15:16+0700\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language-Team: LANGUAGE <<EMAIL>>\n"
"Language: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#: asiantech.link/api/models/user.py:17
msgid "Email should be provided"
msgstr "Email should be provided"

#: asiantech.link/api/models/user.py:38
msgid "Superuser must have is_staff as True"
msgstr "Superuser must have is_staff as True"

#: asiantech.link/api/models/user.py:41
msgid "Superuser must have is_superuser as True"
msgstr "Superuser must have is_superuser as True"

#: asiantech.link/api/models/user.py:44
msgid "Superuser must have is_active as True"
msgstr "Superuser must have is_active as True"

#: asiantech.link/api/serializers/cv/cv_serializers.py:192
#: asiantech.link/api/serializers/engineers/user_serializers.py:52
#: asiantech.link/api/serializers/recruitment_serializers.py:176
#: asiantech.link/api/serializers/recruitment_serializers.py:681
msgid "Invalid degree code."
msgstr "Invalid degree code."

#: asiantech.link/api/serializers/cv/cv_serializers.py:198
#, fuzzy
#| msgid "Invalid language code."
msgid "Invalid language level type."
msgstr "Invalid language code."

#: asiantech.link/api/serializers/engineers/user_serializers.py:409
#, fuzzy
#| msgid "start_date must be less than end_date"
msgid "From date must be before to date."
msgstr "start_date must be less than end_date"

#: asiantech.link/api/serializers/engineers/user_serializers.py:563
#: asiantech.link/api/serializers/engineers/user_serializers.py:568
#: asiantech.link/api/serializers/engineers/user_serializers.py:573
msgid "Invalid place code."
msgstr "Invalid place code."

#: asiantech.link/api/serializers/engineers/user_serializers.py:580
msgid "Invalid payroll price."
msgstr "Invalid payroll price."

#: asiantech.link/api/serializers/engineers/user_serializers.py:941
msgid "Invalid sex type."
msgstr "Invalid sex type."

#: asiantech.link/api/serializers/engineers/user_serializers.py:949
#: asiantech.link/api/serializers/general_company/general_company_serializers.py:350
#: asiantech.link/api/serializers/general_company/general_company_serializers.py:447
#: asiantech.link/api/serializers/recruitment_serializers.py:207
#: asiantech.link/api/serializers/recruitment_serializers.py:741
msgid "Invalid country code."
msgstr "Invalid country code."

#: asiantech.link/api/serializers/engineers/user_serializers.py:962
#: asiantech.link/api/serializers/general_company/general_company_serializers.py:366
#: asiantech.link/api/serializers/general_company/general_company_serializers.py:466
msgid "Invalid phone number."
msgstr "Invalid phone number."

#: asiantech.link/api/serializers/engineers/user_serializers.py:966
#: asiantech.link/api/serializers/general_company/general_company_serializers.py:370
#: asiantech.link/api/serializers/general_company/general_company_serializers.py:470
msgid "Invalid phone number format."
msgstr "Invalid phone number format."

#: asiantech.link/api/serializers/engineers/user_serializers.py:974
#: asiantech.link/api/serializers/general_company/general_company_serializers.py:356
#: asiantech.link/api/serializers/general_company/general_company_serializers.py:454
#: asiantech.link/api/serializers/recruitment_serializers.py:215
#: asiantech.link/api/serializers/recruitment_serializers.py:218
#: asiantech.link/api/serializers/recruitment_serializers.py:221
#: asiantech.link/api/serializers/recruitment_serializers.py:713
#: asiantech.link/api/serializers/recruitment_serializers.py:716
#: asiantech.link/api/serializers/recruitment_serializers.py:719
msgid "Invalid address code."
msgstr "Invalid address code."

#: asiantech.link/api/serializers/general_company/general_company_serializers.py:290
#: asiantech.link/api/serializers/general_company/general_company_serializers.py:433
#: asiantech.link/api/serializers/general_company/general_company_serializers.py:439
#: asiantech.link/api/serializers/host_company/host_company_serializers.py:59
msgid "Invalid url format."
msgstr "Invalid url format."

#: asiantech.link/api/serializers/general_company/general_company_serializers.py:296
#: asiantech.link/api/serializers/host_company/host_company_serializers.py:65
msgid "Invalid email format."
msgstr "Invalid email format."

#: asiantech.link/api/serializers/general_company/general_company_serializers.py:481
#: asiantech.link/api/serializers/general_company/general_company_serializers.py:487
#: asiantech.link/api/serializers/recruitment_serializers.py:248
#: asiantech.link/api/serializers/recruitment_serializers.py:699
msgid "Invalid currency code."
msgstr "Invalid currency code."

#: asiantech.link/api/serializers/general_company/general_company_serializers.py:640
msgid "compare_content_1"
msgstr ""
"This applicant does not match the job requirements very well.\n"
"The chances of hiring him/her are low, so you may want to prioritize other "
"applicants, but if he/she interests you, you may want to request an "
"interview."

#: asiantech.link/api/serializers/general_company/general_company_serializers.py:642
msgid "compare_content_2"
msgstr ""
"This applicant matches many of the job requirements.\n"
"Check the engineer's personal information, and if there are no particular "
"issues, request an interview.\n"
"There is a high chance of hiring him/her, so consider the proposed "
"conditions and adjust the conditions directly via chat"

#: asiantech.link/api/serializers/general_company/general_company_serializers.py:644
msgid "compare_content_3"
msgstr ""
"This applicant matches the job requirements!\n"
"Check the engineer's personal information, and if there are no particular "
"issues, request an interview."

#: asiantech.link/api/serializers/recruitment_serializers.py:169
#: asiantech.link/api/serializers/recruitment_serializers.py:672
msgid "Invalid sex type"
msgstr "Invalid sex type"

#: asiantech.link/api/serializers/recruitment_serializers.py:183
#: asiantech.link/api/serializers/recruitment_serializers.py:704
msgid "start_date must be less than end_date"
msgstr "start_date must be less than end_date"

#: asiantech.link/api/serializers/recruitment_serializers.py:192
#: asiantech.link/api/serializers/recruitment_serializers.py:749
msgid "age_from must be less than age_to"
msgstr "age_from must be less than age_to"

#: asiantech.link/api/serializers/recruitment_serializers.py:200
#: asiantech.link/api/serializers/recruitment_serializers.py:693
msgid "payroll_price_from must be less than payroll_price_to"
msgstr "payroll_price_from must be less than payroll_price_to"

#: asiantech.link/api/serializers/recruitment_serializers.py:236
#: asiantech.link/api/serializers/recruitment_serializers.py:239
#: asiantech.link/api/serializers/recruitment_serializers.py:242
#: asiantech.link/api/serializers/recruitment_serializers.py:727
#: asiantech.link/api/serializers/recruitment_serializers.py:730
#: asiantech.link/api/serializers/recruitment_serializers.py:733
msgid "Invalid pref code."
msgstr "Invalid pref code."

#: asiantech.link/api/serializers/recruitment_serializers.py:255
msgid "Invalid support company."
msgstr "Invalid support company."

#: asiantech.link/api/serializers/recruitment_serializers.py:765
msgid "years_of_experience must be greater than 0"
msgstr "years_of_experience must be greater than 0"

#: asiantech.link/api/serializers/recruitment_serializers.py:779
#: asiantech.link/api/serializers/recruitment_serializers.py:782
msgid "Invalid language code."
msgstr "Invalid language code."

#: asiantech.link/api/services/notify_service/company_notify_service.py:32
msgid "The interview result registration for is incomplete"
msgstr ""
"Please sign the offer acceptance and employment contract with sender_name"

#: asiantech.link/api/services/notify_service/company_notify_service.py:61
#: asiantech.link/api/services/notify_service/support_company_notify_service.py:61
#: asiantech.link/api/services/notify_service/support_company_notify_service.py:90
#: asiantech.link/api/services/notify_service/support_company_notify_service.py:125
msgid "Please register the interview date and time with"
msgstr "Please register the interview date and time with sender_name"

#: asiantech.link/api/services/notify_service/company_notify_service.py:89
msgid "Please register the interview outcome for"
msgstr "Please register the interview outcome for sender_name"

#: asiantech.link/api/services/notify_service/company_notify_service.py:118
msgid "Please sign the offer acceptance and employment contract with"
msgstr ""
"Please sign the offer acceptance and employment contract with sender_name"

#: asiantech.link/api/services/notify_service/engineer_notify_service.py:26
msgid "Please respond to the interview request from"
msgstr "Please respond to the interview request from sender_name"

#: asiantech.link/api/services/notify_service/engineer_notify_service.py:50
msgid "Please respond to the job offer from"
msgstr "Please respond to the job offer from sender_name"

#: asiantech.link/api/services/notify_service/support_company_notify_service.py:32
msgid "Please register the document screening result for"
msgstr "Please register the document screening result for sender_name"

#: asiantech.link/api/views/admin/admin_views.py:52
msgid "List of engineers"
msgstr ""

#: asiantech.link/api/views/admin/admin_views.py:169
#: asiantech.link/api/views/admin/admin_views.py:227
#: asiantech.link/api/views/admin/admin_views.py:259
#: asiantech.link/api/views/admin/admin_views.py:270
#: asiantech.link/api/views/admin/admin_views.py:285
#: asiantech.link/api/views/admin/admin_views.py:578
#: asiantech.link/api/views/admin/admin_views.py:589
#: asiantech.link/api/views/admin/admin_views.py:601
#: asiantech.link/api/views/authentication/authentication_views.py:264
#: asiantech.link/api/views/engineer/engineer_views.py:43
#: asiantech.link/api/views/engineer/engineer_views.py:58
#: asiantech.link/api/views/engineer/engineer_views.py:66
#: asiantech.link/api/views/engineer/engineer_views.py:107
#: asiantech.link/api/views/engineer/engineer_views.py:115
#: asiantech.link/api/views/engineer/engineer_views.py:132
#: asiantech.link/api/views/engineer/engineer_views.py:140
#: asiantech.link/api/views/engineer/engineer_views.py:189
#: asiantech.link/api/views/engineer/engineer_views.py:208
#: asiantech.link/api/views/engineer/engineer_views.py:216
#: asiantech.link/api/views/engineer/engineer_views.py:383
#: asiantech.link/api/views/engineer/engineer_views.py:403
#: asiantech.link/api/views/general/general_company_views.py:49
#: asiantech.link/api/views/general/general_company_views.py:71
#: asiantech.link/api/views/general/general_company_views.py:78
#: asiantech.link/api/views/general/general_company_views.py:84
#: asiantech.link/api/views/general/general_company_views.py:98
#: asiantech.link/api/views/general/general_company_views.py:123
#: asiantech.link/api/views/general/general_company_views.py:198
#: asiantech.link/api/views/general/general_company_views.py:239
#: asiantech.link/api/views/general/general_company_views.py:246
#: asiantech.link/api/views/general/general_company_views.py:262
#: asiantech.link/api/views/general/general_company_views.py:280
#: asiantech.link/api/views/general/general_company_views.py:296
#: asiantech.link/api/views/general/general_company_views.py:313
#: asiantech.link/api/views/general/general_company_views.py:331
#: asiantech.link/api/views/general/general_company_views.py:339
#: asiantech.link/api/views/general/general_company_views.py:358
#: asiantech.link/api/views/general/general_company_views.py:378
#: asiantech.link/api/views/general/general_company_views.py:397
#: asiantech.link/api/views/general/general_company_views.py:416
#: asiantech.link/api/views/general/general_company_views.py:434
#: asiantech.link/api/views/general/general_company_views.py:455
#: asiantech.link/api/views/general/general_company_views.py:536
#: asiantech.link/api/views/general/general_company_views.py:550
#: asiantech.link/api/views/general/general_company_views.py:590
#: asiantech.link/api/views/general/general_company_views.py:600
#: asiantech.link/api/views/general/general_company_views.py:639
#: asiantech.link/api/views/general/general_company_views.py:650
#: asiantech.link/api/views/general/general_company_views.py:700
#: asiantech.link/api/views/general/general_company_views.py:711
#: asiantech.link/api/views/general/general_company_views.py:767
#: asiantech.link/api/views/general/general_company_views.py:804
#: asiantech.link/api/views/general/general_company_views.py:814
#: asiantech.link/api/views/general/general_company_views.py:957
#: asiantech.link/api/views/general/general_company_views.py:978
#: asiantech.link/api/views/general/general_views.py:28
#: asiantech.link/api/views/general/general_views.py:77
#: asiantech.link/api/views/host_company/host_company_views.py:38
#: asiantech.link/api/views/host_company/host_company_views.py:55
#: asiantech.link/api/views/host_company/host_company_views.py:62
#: asiantech.link/api/views/host_company/host_company_views.py:86
#: asiantech.link/api/views/host_company/host_company_views.py:92
#: asiantech.link/api/views/host_company/host_company_views.py:99
#: asiantech.link/api/views/host_company/host_company_views.py:130
#: asiantech.link/api/views/host_company/host_company_views.py:136
#: asiantech.link/api/views/host_company/host_company_views.py:150
#: asiantech.link/api/views/host_company/host_company_views.py:157
#: asiantech.link/api/views/host_company/host_company_views.py:211
#: asiantech.link/api/views/host_company/host_company_views.py:244
#: asiantech.link/api/views/host_company/host_company_views.py:252
#: asiantech.link/api/views/host_company/host_company_views.py:263
#: asiantech.link/api/views/host_company/host_company_views.py:270
#: asiantech.link/api/views/host_company/host_company_views.py:289
#: asiantech.link/api/views/host_company/host_company_views.py:297
#: asiantech.link/api/views/host_company/host_company_views.py:336
#: asiantech.link/api/views/host_support_agency_company/host_support_agency_company_views.py:35
#: asiantech.link/api/views/host_support_agency_company/host_support_agency_company_views.py:81
#: asiantech.link/api/views/host_support_agency_company/host_support_agency_company_views.py:159
#: asiantech.link/api/views/host_support_agency_company/host_support_agency_company_views.py:188
#: asiantech.link/api/views/host_support_agency_company/host_support_agency_company_views.py:196
#: asiantech.link/api/views/host_support_agency_company/host_support_agency_company_views.py:230
#: asiantech.link/api/views/host_support_agency_company/host_support_agency_company_views.py:238
#: asiantech.link/api/views/host_support_agency_company/host_support_agency_company_views.py:295
#: asiantech.link/api/views/notify/count_unread_notify_view.py:24
#: asiantech.link/api/views/notify/count_unread_notify_view.py:50
#: asiantech.link/api/views/notify/get_list_notify_view.py:24
#: asiantech.link/api/views/notify/get_list_notify_view.py:43
#: asiantech.link/api/views/notify/set_read_all_notify_view.py:23
#: asiantech.link/api/views/notify/set_read_all_notify_view.py:32
#: asiantech.link/api/views/notify/set_read_notify_view.py:24
#: asiantech.link/api/views/notify/set_read_notify_view.py:38
#: asiantech.link/api/views/profile/profile_views.py:38
#: asiantech.link/api/views/profile/profile_views.py:53
#: asiantech.link/api/views/profile/profile_views.py:192
#: asiantech.link/api/views/profile/profile_views.py:241
#: asiantech.link/api/views/profile/profile_views.py:285
#: asiantech.link/api/views/profile/profile_views.py:334
#: asiantech.link/api/views/profile/profile_views.py:378
#: asiantech.link/api/views/profile/profile_views.py:427
#: asiantech.link/api/views/profile/profile_views.py:471
#: asiantech.link/api/views/profile/profile_views.py:520
#: asiantech.link/api/views/profile/profile_views.py:564
#: asiantech.link/api/views/profile/profile_views.py:613
#: asiantech.link/api/views/recruit/recruitment_views.py:39
#: asiantech.link/api/views/recruit/recruitment_views.py:60
#: asiantech.link/api/views/recruit/recruitment_views.py:67
#: asiantech.link/api/views/recruit/recruitment_views.py:84
#: asiantech.link/api/views/recruit/recruitment_views.py:91
#: asiantech.link/api/views/recruit/recruitment_views.py:97
#: asiantech.link/api/views/recruit/recruitment_views.py:109
#: asiantech.link/api/views/recruit/recruitment_views.py:122
#: asiantech.link/api/views/recruit/recruitment_views.py:128
#: asiantech.link/api/views/recruit/recruitment_views.py:139
#: asiantech.link/api/views/recruit/recruitment_views.py:146
#: asiantech.link/api/views/recruit/recruitment_views.py:174
#: asiantech.link/api/views/recruit/recruitment_views.py:181
#: asiantech.link/api/views/recruit/recruitment_views.py:305
#: asiantech.link/api/views/recruit/recruitment_views.py:312
#: asiantech.link/api/views/recruit/recruitment_views.py:438
#: asiantech.link/api/views/recruit/recruitment_views.py:444
#: asiantech.link/api/views/recruit/recruitment_views.py:475
#: asiantech.link/api/views/recruit/recruitment_views.py:481
#: asiantech.link/api/views/recruit/recruitment_views.py:567
#: asiantech.link/api/views/recruit/recruitment_views.py:574
#: asiantech.link/api/views/recruit/recruitment_views.py:662
#: asiantech.link/api/views/recruit/recruitment_views.py:670
#: asiantech.link/api/views/recruit/recruitment_views.py:703
#: asiantech.link/api/views/recruit/recruitment_views.py:714
#: asiantech.link/api/views/recruit/recruitment_views.py:751
#: asiantech.link/api/views/recruit/recruitment_views.py:760
#: asiantech.link/api/views/recruit/recruitment_views.py:766
#: asiantech.link/api/views/recruit/recruitment_views.py:783
#: asiantech.link/api/views/recruit/recruitment_views.py:796
#: asiantech.link/api/views/recruit/recruitment_views.py:805
#: asiantech.link/api/views/recruit/recruitment_views.py:852
#: asiantech.link/api/views/recruit/recruitment_views.py:889
msgid "Success"
msgstr "Success"

#: asiantech.link/api/views/admin/admin_views.py:178
msgid "List of email schedules"
msgstr ""

#: asiantech.link/api/views/admin/admin_views.py:237
msgid "List of registrars"
msgstr ""

#: asiantech.link/api/views/admin/admin_views.py:296
#: asiantech.link/api/views/admin/admin_views.py:384
msgid "Email schedule created successfully"
msgstr "Email schedule created successfully"

#: asiantech.link/api/views/admin/admin_views.py:314
#: asiantech.link/api/views/admin/admin_views.py:417
msgid "Target email not found"
msgstr "Target email not found"

#: asiantech.link/api/views/admin/admin_views.py:367
#: asiantech.link/api/views/admin/admin_views.py:470
msgid "Send test email successfully"
msgstr "Send test email successfully"

#: asiantech.link/api/views/admin/admin_views.py:376
#: asiantech.link/api/views/admin/admin_views.py:478
msgid "Email personal information already exists"
msgstr "Email personal information already exists"

#: asiantech.link/api/views/admin/admin_views.py:395
#: asiantech.link/api/views/admin/admin_views.py:486
msgid "Email schedule updated successfully"
msgstr "Email schedule updated successfully"

#: asiantech.link/api/views/admin/admin_views.py:407
#: asiantech.link/api/views/admin/admin_views.py:507
#: asiantech.link/api/views/admin/admin_views.py:572
msgid "Email schedule not found"
msgstr "Email schedule not found"

#: asiantech.link/api/views/admin/admin_views.py:497
#: asiantech.link/api/views/admin/admin_views.py:512
msgid "Email schedule deleted successfully"
msgstr "Email schedule deleted successfully"

#: asiantech.link/api/views/admin/admin_views.py:524
#: asiantech.link/api/views/admin/admin_views.py:550
msgid "Email schedule list deleted successfully"
msgstr "Email schedule list deleted successfully"

#: asiantech.link/api/views/admin/admin_views.py:540
msgid "Invalid data"
msgstr "Invalid status"

#: asiantech.link/api/views/admin/admin_views.py:545
msgid "No email schedule ids provided"
msgstr "No email schedule ids provided"

#: asiantech.link/api/views/admin/admin_views.py:561
msgid "Email schedule details"
msgstr "Email schedule details"

#: asiantech.link/api/views/admin/admin_views.py:603
#, fuzzy
#| msgid "User not found"
msgid "Engineer not found"
msgstr "User not found"

#: asiantech.link/api/views/authentication/authentication_views.py:43
msgid "Verification email sent"
msgstr "Verification email sent"

#: asiantech.link/api/views/authentication/authentication_views.py:46
msgid "Verification whatsapp sent"
msgstr "Verification whatsapp sent"

#: asiantech.link/api/views/authentication/authentication_views.py:49
msgid "Email verified successfully"
msgstr "Email verified successfully"

#: asiantech.link/api/views/authentication/authentication_views.py:52
msgid "User created successfully"
msgstr "User created successfully"

#: asiantech.link/api/views/authentication/authentication_views.py:55
#: asiantech.link/api/views/authentication/authentication_views.py:415
#: asiantech.link/api/views/authentication/authentication_views.py:540
#: asiantech.link/api/views/authentication/authentication_views.py:584
#: asiantech.link/api/views/authentication/authentication_views.py:801
#: asiantech.link/api/views/authentication/authentication_views.py:817
#: asiantech.link/api/views/authentication/authentication_views.py:1065
msgid "Login successfully"
msgstr "Login successfully"

#: asiantech.link/api/views/authentication/authentication_views.py:57
msgid "Reset password successfully"
msgstr "User created successfully"

#: asiantech.link/api/views/authentication/authentication_views.py:102
msgid "emailRegisterVerificationTitle"
msgstr "【AsianTech.Link】Please Verify Your Email Address"

#: asiantech.link/api/views/authentication/authentication_views.py:109
msgid "emailConfirmContent1"
msgstr "Thank you for signing up with AsianTech.Link"

#: asiantech.link/api/views/authentication/authentication_views.py:110
msgid "emailConfirmContent2"
msgstr ""
"To safely use the service, email address verification is required. Please "
"verify your email address using the button below."

#: asiantech.link/api/views/authentication/authentication_views.py:111
msgid "emailConfirmContent3"
msgstr ""
"For your safety, we require you to verify your email address. Please verify "
"your email address using the button below."

#: asiantech.link/api/views/authentication/authentication_views.py:112
#: asiantech.link/api/views/authentication/authentication_views.py:315
msgid "emailConfirmContent4"
msgstr "© Copyright AsianTech.Link All Right Reserved."

#: asiantech.link/api/views/authentication/authentication_views.py:114
msgid "Verification Email"
msgstr "Verification email sent"

#: asiantech.link/api/views/authentication/authentication_views.py:119
msgid "verifyYourAccount"
msgstr "Verify your email address"

#: asiantech.link/api/views/authentication/authentication_views.py:155
#: asiantech.link/api/views/authentication/authentication_views.py:157
msgid "Email is already linked to an another account"
msgstr ""

#: asiantech.link/api/views/authentication/authentication_views.py:166
#: asiantech.link/api/views/authentication/authentication_views.py:296
#: asiantech.link/api/views/authentication/authentication_views.py:986
#: asiantech.link/api/views/authentication/authentication_views.py:1049
#: asiantech.link/api/views/authentication/authentication_views.py:1101
#: asiantech.link/api/views/authentication/authentication_views.py:1176
msgid "User not found!"
msgstr "User not found!"

#: asiantech.link/api/views/authentication/authentication_views.py:169
#: asiantech.link/api/views/authentication/authentication_views.py:298
msgid "User is already verified!"
msgstr "User is already verified!"

#: asiantech.link/api/views/authentication/authentication_views.py:173
#: asiantech.link/api/views/authentication/authentication_views.py:552
msgid "CAPTCHA verification failed"
msgstr "CAPTCHA verification failed"

#: asiantech.link/api/views/authentication/authentication_views.py:266
msgid "Invalid code!"
msgstr "Invalid code."

#: asiantech.link/api/views/authentication/authentication_views.py:282
msgid "Token is required!"
msgstr "Token is required!"

#: asiantech.link/api/views/authentication/authentication_views.py:306
msgid "emailRegisterSuccessTitle"
msgstr "【Registration Successful】AsianTech.Link Account"

#: asiantech.link/api/views/authentication/authentication_views.py:313
msgid "emailRegisterSuccessContent1"
msgstr "Your account registration to AsianTech.Link has been completed"

#: asiantech.link/api/views/authentication/authentication_views.py:314
msgid "emailRegisterSuccessContent2"
msgstr ""
"Showcase your appeal through Asian Tech.Linkand connect with attractive job "
"opportunities."

#: asiantech.link/api/views/authentication/authentication_views.py:331
#: asiantech.link/api/views/authentication/authentication_views.py:333
#: asiantech.link/api/views/authentication/authentication_views.py:1006
#: asiantech.link/api/views/authentication/authentication_views.py:1014
#: asiantech.link/api/views/authentication/authentication_views.py:1046
msgid "Verification token has expired!"
msgstr "Verification token has expired!"

#: asiantech.link/api/views/authentication/authentication_views.py:335
msgid "Invalid verification token!"
msgstr "Invalid verification token!"

#: asiantech.link/api/views/authentication/authentication_views.py:350
#: asiantech.link/api/views/authentication/authentication_views.py:428
msgid "Validation errors in your request"
msgstr "Validation errors in your request"

#: asiantech.link/api/views/authentication/authentication_views.py:391
#: asiantech.link/api/views/authentication/authentication_views.py:501
msgid "The email address is already registered with engineer"
msgstr "The email address is already registered with engineer"

#: asiantech.link/api/views/authentication/authentication_views.py:393
#: asiantech.link/api/views/authentication/authentication_views.py:495
msgid "The email address is already registered with company"
msgstr "The email address is already registered with company"

#: asiantech.link/api/views/authentication/authentication_views.py:394
msgid "Email is already registered!"
msgstr "Email is already registered!"

#: asiantech.link/api/views/authentication/authentication_views.py:436
#: asiantech.link/api/views/authentication/authentication_views.py:440
#: asiantech.link/api/views/authentication/authentication_views.py:465
#: asiantech.link/api/views/authentication/authentication_views.py:469
msgid "Email address or password does not match"
msgstr "Email address or password does not match"

#: asiantech.link/api/views/authentication/authentication_views.py:439
#: asiantech.link/api/views/authentication/authentication_views.py:454
#: asiantech.link/api/views/authentication/authentication_views.py:468
msgid "Login failed."
msgstr "Login failed"

#: asiantech.link/api/views/authentication/authentication_views.py:451
#: asiantech.link/api/views/authentication/authentication_views.py:455
msgid "The user ID or password is incorrect."
msgstr "Email address or password does not match"

#: asiantech.link/api/views/authentication/authentication_views.py:489
#: asiantech.link/api/views/authentication/authentication_views.py:572
msgid "CAPTCHA verification required"
msgstr "CAPTCHA verification required"

#: asiantech.link/api/views/authentication/authentication_views.py:507
msgid ""
"The account with this email has been registered with the receiving support "
"organization."
msgstr ""

#: asiantech.link/api/views/authentication/authentication_views.py:698
#: asiantech.link/api/views/authentication/authentication_views.py:700
#: asiantech.link/api/views/authentication/authentication_views.py:738
#: asiantech.link/api/views/authentication/authentication_views.py:740
#: asiantech.link/api/views/authentication/authentication_views.py:809
#: asiantech.link/api/views/authentication/authentication_views.py:827
#, fuzzy
#| msgid "Login failed."
msgid "Login failed!"
msgstr "Login failed"

#: asiantech.link/api/views/authentication/authentication_views.py:811
#, fuzzy
#| msgid "Invalid sex type"
msgid "Invalid SNS type!"
msgstr "Invalid sex type"

#: asiantech.link/api/views/authentication/authentication_views.py:921
msgid "Your Verification Code"
msgstr "Your Verification Code"

#: asiantech.link/api/views/authentication/authentication_views.py:926
msgid "Email Verification"
msgstr ""
"Thank you for registering. Please use the following verification code to "
"complete your registration:"

#: asiantech.link/api/views/authentication/authentication_views.py:929
msgid "Please use the verification code below to complete your login:"
msgstr "Please use the verification code below to complete your login:"

#: asiantech.link/api/views/authentication/authentication_views.py:932
msgid "Note: This code is valid for a limited time."
msgstr "Note: This code is valid for a limited time."

#: asiantech.link/api/views/authentication/authentication_views.py:946
msgid "We sent you a code to verify your login"
msgstr "We sent you a code to verify your login"

#: asiantech.link/api/views/authentication/authentication_views.py:968
#: asiantech.link/api/views/authentication/authentication_views.py:978
#: asiantech.link/api/views/authentication/authentication_views.py:1034
msgid "Password Reset Request"
msgstr "Password Reset Request"

#: asiantech.link/api/views/authentication/authentication_views.py:978
msgid "Dear"
msgstr "Dear"

#: asiantech.link/api/views/authentication/authentication_views.py:978
msgid "password_request_reset_content_1"
msgstr ""
"We received a request to reset your password. Please press the confirm "
"button to receive a new password:"

#: asiantech.link/api/views/authentication/authentication_views.py:979
msgid "password_request_reset_content_2"
msgstr "If you did not request a password reset, please ignore this email."

#: asiantech.link/api/views/authentication/authentication_views.py:979
#: asiantech.link/api/views/authentication/authentication_views.py:1035
msgid "passwordResetContent2"
msgstr "Please log in with this new password and change it immediately."

#: asiantech.link/api/views/authentication/authentication_views.py:979
msgid "Thank you"
msgstr "Thank you"

#: asiantech.link/api/views/authentication/authentication_views.py:984
msgid "Password reset email sent!"
msgstr "Password reset email sent!"

#: asiantech.link/api/views/authentication/authentication_views.py:1019
msgid "Invalid token type!"
msgstr "Invalid token type!"

#: asiantech.link/api/views/authentication/authentication_views.py:1026
msgid "Your Password Reset Request"
msgstr "Your Password Reset Request"

#: asiantech.link/api/views/authentication/authentication_views.py:1032
msgid "Password Reset"
msgstr "Password Reset"

#: asiantech.link/api/views/authentication/authentication_views.py:1034
msgid "Hello"
msgstr "Hello"

#: asiantech.link/api/views/authentication/authentication_views.py:1034
msgid "passwordResetContent1"
msgstr "Your password has been reset. Here is your new password:"

#: asiantech.link/api/views/authentication/authentication_views.py:1035
msgid "passwordResetContent3"
msgstr ""
"If you did not request this password reset, please contact our support team."

#: asiantech.link/api/views/authentication/authentication_views.py:1044
msgid "Password reset successfully!"
msgstr "Password reset successfully!"

#: asiantech.link/api/views/authentication/authentication_views.py:1099
msgid "Invalid code or email!"
msgstr "Invalid code or email!"

#: asiantech.link/api/views/authentication/authentication_views.py:1103
msgid "Invalid email or password!"
msgstr "Invalid email or password!"

#: asiantech.link/api/views/authentication/authentication_views.py:1111
#: asiantech.link/api/views/authentication/authentication_views.py:1125
msgid "Logout successfully"
msgstr "Logout successfully"

#: asiantech.link/api/views/authentication/authentication_views.py:1131
#: asiantech.link/api/views/authentication/authentication_views.py:1144
msgid "Captcha generated successfully"
msgstr "Captcha generated successfully"

#: asiantech.link/api/views/authentication/authentication_views.py:1151
msgid "Check captcha required in login"
msgstr "Check captcha required in login"

#: asiantech.link/api/views/authentication/authentication_views.py:1211
#: asiantech.link/api/views/authentication/authentication_views.py:1228
#: asiantech.link/api/views/authentication/authentication_views.py:1237
#: asiantech.link/api/views/authentication/authentication_views.py:1256
msgid "Account deleted successfully"
msgstr "Account deleted successfully"

#: asiantech.link/api/views/authentication/authentication_views.py:1258
msgid "error"
msgstr "Error"

#: asiantech.link/api/views/chat/chat_views.py:22
#: asiantech.link/api/views/chat/chat_views.py:56
msgid "All messages marked as read"
msgstr ""

#: asiantech.link/api/views/chat/chat_views.py:38
#, fuzzy
#| msgid "Recruitment does not exist"
msgid "Group does not exist."
msgstr "Recruitment does not exist"

#: asiantech.link/api/views/chat/chat_views.py:44
msgid "User is not a member of this group."
msgstr ""

#: asiantech.link/api/views/chat/chat_views.py:59
msgid "No messages to mark as read"
msgstr ""

#: asiantech.link/api/views/engineer/engineer_views.py:87
#: asiantech.link/api/views/engineer/engineer_views.py:160
msgid "Agency company not found."
msgstr "Agency company not found."

#: asiantech.link/api/views/engineer/engineer_views.py:94
msgid "This agency company is already linked with the engineer."
msgstr "This agency company is already linked with the engineer."

#: asiantech.link/api/views/engineer/engineer_views.py:169
msgid "This agency company has already been deleted."
msgstr "This agency company has already been deleted."

#: asiantech.link/api/views/engineer/engineer_views.py:176
msgid "Agency company successfully deleted."
msgstr "Agency company successfully deleted."

#: asiantech.link/api/views/engineer/engineer_views.py:179
msgid "This agency company is not linked with the engineer."
msgstr "This agency company is not linked with the engineer."

#: asiantech.link/api/views/engineer/engineer_views.py:231
#: asiantech.link/api/views/general/general_company_views.py:477
msgid "Invalid ordering field."
msgstr "Invalid ordering field."

#: asiantech.link/api/views/engineer/engineer_views.py:239
#: asiantech.link/api/views/engineer/engineer_views.py:368
msgid "Invalid recruit_progress_code"
msgstr "Invalid recruit_progress_code"

#: asiantech.link/api/views/engineer/engineer_views.py:396
#: asiantech.link/api/views/general/general_company_views.py:352
#: asiantech.link/api/views/profile/profile_views.py:233
#: asiantech.link/api/views/profile/profile_views.py:326
#: asiantech.link/api/views/profile/profile_views.py:419
#: asiantech.link/api/views/profile/profile_views.py:512
#: asiantech.link/api/views/profile/profile_views.py:605
msgid "User not found"
msgstr "User not found"

#: asiantech.link/api/views/general/general_company_views.py:63
msgid "Email already exists"
msgstr "Email already exists"

#: asiantech.link/api/views/general/general_company_views.py:93
#: asiantech.link/api/views/host_company/host_company_views.py:69
#: asiantech.link/api/views/host_company/host_company_views.py:106
msgid "User does not have company"
msgstr "User does not have company"

#: asiantech.link/api/views/general/general_company_views.py:100
#: asiantech.link/api/views/general/general_company_views.py:125
#: asiantech.link/api/views/host_company/host_company_views.py:75
#: asiantech.link/api/views/host_company/host_company_views.py:112
#: asiantech.link/api/views/host_support_agency_company/host_support_agency_company_views.py:45
#: asiantech.link/api/views/host_support_agency_company/host_support_agency_company_views.py:72
#: asiantech.link/api/views/media/company_media_views.py:59
#: asiantech.link/api/views/media/company_media_views.py:122
msgid "Company not found"
msgstr "Company not found"

#: asiantech.link/api/views/general/general_company_views.py:274
msgid "Filter saved successfully"
msgstr "Filter saved successfully"

#: asiantech.link/api/views/general/general_company_views.py:304
msgid "Filter deleted successfully"
msgstr "Filter deleted successfully"

#: asiantech.link/api/views/general/general_company_views.py:327
msgid "You can't update this status!"
msgstr "You can't update this status!"

#: asiantech.link/api/views/general/general_company_views.py:485
#: asiantech.link/api/views/general/general_company_views.py:670
#: asiantech.link/api/views/general/general_company_views.py:831
msgid "You don't have permission to access this company"
msgstr ""

#: asiantech.link/api/views/general/general_company_views.py:487
#: asiantech.link/api/views/general/general_company_views.py:668
#: asiantech.link/api/views/general/general_company_views.py:829
msgid "Host company not found"
msgstr "Company not found"

#: asiantech.link/api/views/general/general_company_views.py:573
#: asiantech.link/api/views/general/general_company_views.py:576
#: asiantech.link/api/views/general/general_company_views.py:578
#: asiantech.link/api/views/general/general_company_views.py:627
#: asiantech.link/api/views/general/general_company_views.py:630
#: asiantech.link/api/views/general/general_company_views.py:632
#: asiantech.link/api/views/general/general_company_views.py:792
#: asiantech.link/api/views/general/general_company_views.py:795
#: asiantech.link/api/views/general/general_company_views.py:797
#: asiantech.link/api/views/general/general_company_views.py:873
#: asiantech.link/api/views/general/general_company_views.py:876
#: asiantech.link/api/views/general/general_company_views.py:878
msgid "You don't have permission to access this apply"
msgstr ""

#: asiantech.link/api/views/general/general_company_views.py:592
#: asiantech.link/api/views/general/general_company_views.py:641
#: asiantech.link/api/views/general/general_company_views.py:702
#: asiantech.link/api/views/general/general_company_views.py:806
#: asiantech.link/api/views/general/general_company_views.py:843
#: asiantech.link/api/views/general/general_company_views.py:949
#: asiantech.link/api/views/host_company/host_company_views.py:340
msgid "Apply not found"
msgstr "Apply not found"

#: asiantech.link/api/views/general/general_company_views.py:946
msgid "Something went wrong!"
msgstr "Something went wrong!"

#: asiantech.link/api/views/general/general_company_views.py:968
#: asiantech.link/api/views/host_support_agency_company/host_support_agency_company_views.py:205
#: asiantech.link/api/views/recruit/recruitment_views.py:49
#: asiantech.link/api/views/recruit/recruitment_views.py:155
msgid "You do not own the company"
msgstr "You do not own the company"

#: asiantech.link/api/views/general/general_views.py:46
msgid "Current language is: "
msgstr "Current language is: "

#: asiantech.link/api/views/host_company/host_company_views.py:88
msgid "User already subscribed"
msgstr "User already subscribed"

#: asiantech.link/api/views/host_company/host_company_views.py:120
msgid "User not subscribed"
msgstr "User not subscribed"

#: asiantech.link/api/views/host_company/host_company_views.py:128
msgid "Data not found"
msgstr "Data not found"

#: asiantech.link/api/views/host_company/host_company_views.py:143
msgid "User does not have company."
msgstr "User does not have company."

#: asiantech.link/api/views/host_company/host_company_views.py:172
msgid "You have already make a request interview"
msgstr ""

#: asiantech.link/api/views/host_company/host_company_views.py:213
#: asiantech.link/api/views/host_support_agency_company/host_support_agency_company_views.py:161
msgid "You have requested an interview for this engineer."
msgstr ""

#: asiantech.link/api/views/host_company/host_company_views.py:331
msgid "Notification of acceptance has been sent."
msgstr "Notification of acceptance has been sent."

#: asiantech.link/api/views/host_company/host_company_views.py:338
msgid "Invalid status"
msgstr "Invalid status"

#: asiantech.link/api/views/host_support_agency_company/host_support_agency_company_views.py:102
#: asiantech.link/api/views/host_support_agency_company/host_support_agency_company_views.py:213
msgid "Host company is not belong to you"
msgstr ""

#: asiantech.link/api/views/host_support_agency_company/host_support_agency_company_views.py:109
msgid "This engineer already make a interview to host company"
msgstr "This engineer already make a interview to host company"

#: asiantech.link/api/views/host_support_agency_company/host_support_agency_company_views.py:247
msgid "You are not authorized to access this endpoint"
msgstr ""

#: asiantech.link/api/views/media/admin_upload_avatar_engineer_view.py:53
#: asiantech.link/api/views/media/admin_upload_avatar_engineer_view.py:77
#: asiantech.link/api/views/media/avatar_views.py:44
#: asiantech.link/api/views/media/avatar_views.py:68
#: asiantech.link/api/views/media/company_media_views.py:48
#: asiantech.link/api/views/media/company_media_views.py:81
#: asiantech.link/api/views/media/company_media_views.py:110
#: asiantech.link/api/views/media/company_media_views.py:174
#: asiantech.link/api/views/media/contract_media_view.py:56
#: asiantech.link/api/views/media/contract_media_view.py:117
#: asiantech.link/api/views/media/contract_media_view.py:148
#: asiantech.link/api/views/media/contract_media_view.py:212
#: asiantech.link/api/views/media/passport_views.py:44
#: asiantech.link/api/views/media/passport_views.py:69
#: asiantech.link/api/views/media/recruit_media_views.py:44
#: asiantech.link/api/views/media/recruit_media_views.py:65
msgid "Image uploaded successfully"
msgstr "Image uploaded successfully"

#: asiantech.link/api/views/media/admin_upload_avatar_engineer_view.py:92
#: asiantech.link/api/views/media/admin_upload_avatar_engineer_view.py:110
#: asiantech.link/api/views/media/avatar_views.py:74
#: asiantech.link/api/views/media/avatar_views.py:92
msgid "Image deleted successfully"
msgstr "Image deleted successfully"

#: asiantech.link/api/views/media/company_media_views.py:53
#: asiantech.link/api/views/media/company_media_views.py:116
#: asiantech.link/api/views/media/recruit_media_views.py:49
msgid "You are not a company"
msgstr "You are not a company"

#: asiantech.link/api/views/media/company_media_views.py:139
msgid "Index must be 0,1,2"
msgstr "Index must be 0,1,2"

#: asiantech.link/api/views/media/contract_media_view.py:68
msgid "You can not upload contract image now, recruit progress code !=60"
msgstr "You can not upload contract image now, recruit progress code !=60"

#: asiantech.link/api/views/media/contract_media_view.py:167
msgid "You are not authorized to upload contract image"
msgstr ""

#: asiantech.link/api/views/media/contract_media_view.py:171
msgid "You can not upload contract image now, status progress code is invalid"
msgstr "You can not upload contract image now, status progress code is invalid"

#: asiantech.link/api/views/media/passport_views.py:75
#: asiantech.link/api/views/media/passport_views.py:93
msgid "Passport image deleted successfully"
msgstr "Passport image deleted successfully"

#: asiantech.link/api/views/notify/set_read_notify_view.py:34
msgid "Invalid request"
msgstr "Invalid status"

#: asiantech.link/api/views/profile/cv_views.py:52
#: asiantech.link/api/views/profile/cv_views.py:321
#: asiantech.link/api/views/profile/cv_views.py:389
#, fuzzy
#| msgid "Image uploaded successfully"
msgid "CV uploaded successfully"
msgstr "Image uploaded successfully"

#: asiantech.link/api/views/profile/cv_views.py:58
msgid "File size must be less than 5MB"
msgstr "File size must be less than 5MB"

#: asiantech.link/api/views/profile/cv_views.py:288
msgid "Invalid document type: not a resume"
msgstr "Invalid document type: not a resume"

#: asiantech.link/api/views/profile/cv_views.py:331
#, fuzzy
#| msgid "User not found"
msgid "CV not found"
msgstr "User not found"

#: asiantech.link/api/views/profile/profile_views.py:86
msgid "This email is already in use."
msgstr "Email is already registered!"

#: asiantech.link/api/views/profile/profile_views.py:146
msgid "Password updated successfully"
msgstr "Password updated successfully"

#: asiantech.link/api/views/profile/profile_views.py:162
#: asiantech.link/api/views/profile/profile_views.py:184
msgid "New password must be different from the current password."
msgstr "New password must be different from the current password."

#: asiantech.link/api/views/profile/profile_views.py:182
msgid "Password updated successfully."
msgstr "Password updated successfully."

#: asiantech.link/api/views/profile/profile_views.py:186
msgid "Invalid current password."
msgstr "Invalid current password"

#: asiantech.link/api/views/recruit/recruitment_views.py:82
#: asiantech.link/api/views/recruit/recruitment_views.py:106
#: asiantech.link/api/views/recruit/recruitment_views.py:115
#: asiantech.link/api/views/recruit/recruitment_views.py:136
#: asiantech.link/api/views/recruit/recruitment_views.py:583
#: asiantech.link/api/views/recruit/recruitment_views.py:658
#: asiantech.link/api/views/recruit/recruitment_views.py:705
#: asiantech.link/api/views/recruit/recruitment_views.py:753
msgid "Recruitment does not exist"
msgstr "Recruitment does not exist"

#: asiantech.link/api/views/recruit/recruitment_views.py:300
#: asiantech.link/api/views/recruit/recruitment_views.py:436
msgid "Error"
msgstr "Error"

#: asiantech.link/api/views/recruit/recruitment_views.py:460
msgid "Failed"
msgstr "Failed"

#: asiantech.link/api/views/recruit/recruitment_views.py:495
msgid "ComCompany not found"
msgstr "ComCompany not found"

#: asiantech.link/api/views/recruit/recruitment_views.py:502
msgid "MapEngAgc not found"
msgstr "MapEngAgc not found"

#: asiantech.link/api/views/recruit/recruitment_views.py:516
msgid "Already applied"
msgstr "Already applied"

#: asiantech.link/api/views/recruit/recruitment_views.py:533
msgid "You have already applied for this recruitment"
msgstr ""

#: asiantech.link/api/views/recruit/recruitment_views.py:656
msgid "Recruitment does not exist or you do not have access to it"
msgstr "Recruitment does not exist or you do not have access to it"

#: asiantech.link/api/views/recruit/recruitment_views.py:679
#: asiantech.link/api/views/recruit/recruitment_views.py:724
msgid "Recruit ID is required"
msgstr "Recruit ID is required"

#: asiantech.link/api/views/recruit/recruitment_views.py:687
#: asiantech.link/api/views/recruit/recruitment_views.py:776
#: asiantech.link/api/views/recruit/recruitment_views.py:824
msgid "No application found"
msgstr "No application found"

#: asiantech.link/api/views/recruit/recruitment_views.py:698
#: asiantech.link/api/views/recruit/recruitment_views.py:739
msgid "Invalid recruit progress code to cancel"
msgstr "Invalid recruit progress code to cancel"

#: asiantech.link/api/views/recruit/recruitment_views.py:737
msgid "Interview datetime is required"
msgstr "Interview datetime is required"

#: asiantech.link/api/views/recruit/recruitment_views.py:798
msgid "Invalid recruit progress code to sign contract"
msgstr "Invalid recruit progress code to sign contract"

#: asiantech.link/api/views/recruit/recruitment_views.py:831
msgid "No application found for the provided recruitment ID"
msgstr "No application found for the provided recruitment ID"

#: asiantech.link/api/views/recruit/recruitment_views.py:837
msgid "No accept sign found for the application"
msgstr "No accept sign found for the application"

#: asiantech.link/core/settings/common.py:132
msgid "English"
msgstr "English"

#: asiantech.link/core/settings/common.py:133
msgid "Japanese"
msgstr "Japanese"

#: asiantech.link/core/settings/common.py:134
msgid "Myanmar"
msgstr "Myanmar"

#: asiantech.link/core/settings/common.py:135
msgid "Indonesian"
msgstr "Indonesian"

#: asiantech.link/core/settings/common.py:136
msgid "Nepali"
msgstr "Nepali"

#: asiantech.link/core/settings/common.py:137
msgid "Vietnamese"
msgstr "Vietnamese"

#: asiantech.link/utils/permissions.py:22
#: asiantech.link/utils/permissions.py:36
msgid "Account has not been verified"
msgstr "Account has not been verified"

#: asiantech.link/utils/permissions.py:39
#: asiantech.link/utils/permissions.py:53
#: asiantech.link/utils/permissions.py:64
#: asiantech.link/utils/permissions.py:83
#: asiantech.link/utils/permissions.py:96
#: asiantech.link/utils/permissions.py:107
#: asiantech.link/utils/permissions.py:120
#: asiantech.link/utils/permissions.py:132
msgid "Access denied"
msgstr ""

#: asiantech.link/utils/utils.py:508 asiantech.link/utils/utils.py:644
msgid "Fresher"
msgstr "Fresher"

#: asiantech.link/utils/utils.py:510 asiantech.link/utils/utils.py:646
msgid "Junior"
msgstr "Junior"

#: asiantech.link/utils/utils.py:512 asiantech.link/utils/utils.py:648
msgid "Middle"
msgstr "Middle"

#: asiantech.link/utils/utils.py:514 asiantech.link/utils/utils.py:650
msgid "Senior"
msgstr "Senior"

#: asiantech.link/utils/validators.py:29
msgid "Email is too long"
msgstr "Email is too long"

#: asiantech.link/utils/validators.py:35
msgid "Email must contain a single '@' symbol"
msgstr "Email must contain a single '@' symbol"

#: asiantech.link/utils/validators.py:39
msgid "Local part of the email is too long (>= 64 characters)"
msgstr "Local part of the email is too long (>= 64 characters)"

#: asiantech.link/utils/validators.py:43
msgid "Email contains invalid characters"
msgstr "Email contains invalid characters"

#: asiantech.link/utils/validators.py:47
msgid "Email contains consecutive periods"
msgstr "Email contains consecutive periods"

#: asiantech.link/utils/validators.py:51
msgid ""
"Email has periods at invalid positions (start/end of local part or end of "
"email)"
msgstr ""
"Email has periods at invalid positions (start/end of local part or end of "
"email)"

#: asiantech.link/utils/validators.py:55
msgid "Domain part of the email should not be an IP address"
msgstr "Domain part of the email should not be an IP address"

#: asiantech.link/utils/validators.py:57
msgid "Email is valid"
msgstr "Email is valid"

#: asiantech.link/utils/validators.py:63
msgid "Password length must be between 8 and 20 characters"
msgstr "Password length must be between 8 and 20 characters"

#: asiantech.link/utils/validators.py:68
msgid "Password contains full-width characters"
msgstr "Password contains full-width characters"

#: asiantech.link/utils/validators.py:72
msgid "Password must contain at least one uppercase letter"
msgstr "Password must contain at least one uppercase letter"

#: asiantech.link/utils/validators.py:76
msgid "Password must contain at least one lowercase letter"
msgstr "Password must contain at least one lowercase letter"

#: asiantech.link/utils/validators.py:80
msgid "Password must contain at least one number"
msgstr "Password must contain at least one number"

#: asiantech.link/utils/validators.py:84
msgid "Password must contain at least one special character"
msgstr "Password must contain at least one special character"

#: asiantech.link/utils/validators.py:86
msgid "Password is valid"
msgstr "Password is valid"

#~ msgid "Expert"
#~ msgstr "Expert"

#~ msgid "Facebook authentication successful"
#~ msgstr "Facebook authentication successful"

#~ msgid "Linkedin authentication successful"
#~ msgstr "Linkedin authentication successful"

#~ msgid "Zalo authentication successful"
#~ msgstr "Zalo authentication successful"

#~ msgid "Get user info with zalo id"
#~ msgstr "Get user info with zalo id"

#~ msgid "Get user info with whatsapp number"
#~ msgstr "Get user info with whatsapp number"

#~ msgid "WhatsApp authentication successful"
#~ msgstr "WhatsApp authentication successful"

#~ msgid "Please sign the contract for"
#~ msgstr "Please sign the contract for"

#~ msgid "Failed to read PDF content"
#~ msgstr "Failed to read PDF content"

#~ msgid "Email is too long (>= 201 characters)"
#~ msgstr "Email is too long (>= 201 characters)"
