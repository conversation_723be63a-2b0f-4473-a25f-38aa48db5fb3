# SOME DESCRIPTIVE TITLE.
# Copyright (C) YEAR THE PACKAGE'S COPYRIGHT HOLDER
# This file is distributed under the same license as the PACKAGE package.
# <AUTHOR> <EMAIL>, YEAR.
#
#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: PACKAGE VERSION\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-06-26 15:17+0700\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language-Team: LANGUAGE <<EMAIL>>\n"
"Language: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#: asiantech.link/api/models/user.py:17
msgid "Email should be provided"
msgstr "Cần cung cấp email"

#: asiantech.link/api/models/user.py:38
msgid "Superuser must have is_staff as True"
msgstr "Superuser phải có is_staff là True"

#: asiantech.link/api/models/user.py:41
msgid "Superuser must have is_superuser as True"
msgstr "Superuser phải có is_superuser là True"

#: asiantech.link/api/models/user.py:44
msgid "Superuser must have is_active as True"
msgstr "Superuser phải có is_active là True"

#: asiantech.link/api/serializers/cv/cv_serializers.py:192
#: asiantech.link/api/serializers/engineers/user_serializers.py:52
#: asiantech.link/api/serializers/recruitment_serializers.py:176
#: asiantech.link/api/serializers/recruitment_serializers.py:681
msgid "Invalid degree code."
msgstr "Mã trình độ không hợp lệ."

#: asiantech.link/api/serializers/cv/cv_serializers.py:198
#, fuzzy
#| msgid "Invalid language code."
msgid "Invalid language level type."
msgstr "Mã ngôn ngữ không hợp lệ."

#: asiantech.link/api/serializers/engineers/user_serializers.py:409
#, fuzzy
#| msgid "start_date must be less than end_date"
msgid "From date must be before to date."
msgstr "Ngày bắt đầu phải nhỏ hơn ngày kết thúc"

#: asiantech.link/api/serializers/engineers/user_serializers.py:563
#: asiantech.link/api/serializers/engineers/user_serializers.py:568
#: asiantech.link/api/serializers/engineers/user_serializers.py:573
msgid "Invalid place code."
msgstr "Mã địa điểm không hợp lệ."

#: asiantech.link/api/serializers/engineers/user_serializers.py:580
msgid "Invalid payroll price."
msgstr "Mức lương không hợp lệ."

#: asiantech.link/api/serializers/engineers/user_serializers.py:941
msgid "Invalid sex type."
msgstr "Loại giới tính không hợp lệ."

#: asiantech.link/api/serializers/engineers/user_serializers.py:949
#: asiantech.link/api/serializers/general_company/general_company_serializers.py:350
#: asiantech.link/api/serializers/general_company/general_company_serializers.py:447
#: asiantech.link/api/serializers/recruitment_serializers.py:207
#: asiantech.link/api/serializers/recruitment_serializers.py:741
msgid "Invalid country code."
msgstr "Mã quốc gia không hợp lệ."

#: asiantech.link/api/serializers/engineers/user_serializers.py:962
#: asiantech.link/api/serializers/general_company/general_company_serializers.py:366
#: asiantech.link/api/serializers/general_company/general_company_serializers.py:466
msgid "Invalid phone number."
msgstr "Số điện thoại không hợp lệ."

#: asiantech.link/api/serializers/engineers/user_serializers.py:966
#: asiantech.link/api/serializers/general_company/general_company_serializers.py:370
#: asiantech.link/api/serializers/general_company/general_company_serializers.py:470
msgid "Invalid phone number format."
msgstr "Định dạng số điện thoại không hợp lệ."

#: asiantech.link/api/serializers/engineers/user_serializers.py:974
#: asiantech.link/api/serializers/general_company/general_company_serializers.py:356
#: asiantech.link/api/serializers/general_company/general_company_serializers.py:454
#: asiantech.link/api/serializers/recruitment_serializers.py:215
#: asiantech.link/api/serializers/recruitment_serializers.py:218
#: asiantech.link/api/serializers/recruitment_serializers.py:221
#: asiantech.link/api/serializers/recruitment_serializers.py:713
#: asiantech.link/api/serializers/recruitment_serializers.py:716
#: asiantech.link/api/serializers/recruitment_serializers.py:719
msgid "Invalid address code."
msgstr "Mã địa chỉ không hợp lệ."

#: asiantech.link/api/serializers/general_company/general_company_serializers.py:290
#: asiantech.link/api/serializers/general_company/general_company_serializers.py:433
#: asiantech.link/api/serializers/general_company/general_company_serializers.py:439
#: asiantech.link/api/serializers/host_company/host_company_serializers.py:59
msgid "Invalid url format."
msgstr "Định dạng URL không hợp lệ."

#: asiantech.link/api/serializers/general_company/general_company_serializers.py:296
#: asiantech.link/api/serializers/host_company/host_company_serializers.py:65
msgid "Invalid email format."
msgstr "Định dạng email không hợp lệ."

#: asiantech.link/api/serializers/general_company/general_company_serializers.py:481
#: asiantech.link/api/serializers/general_company/general_company_serializers.py:487
#: asiantech.link/api/serializers/recruitment_serializers.py:248
#: asiantech.link/api/serializers/recruitment_serializers.py:699
msgid "Invalid currency code."
msgstr "Mã tiền tệ không hợp lệ."

#: asiantech.link/api/serializers/general_company/general_company_serializers.py:640
msgid "compare_content_1"
msgstr ""
"Ứng viên này không phù hợp với yêu cầu công việc lắm.\n"
"Khả năng tuyển dụng anh/cô ấy thấp, vì vậy bạn có thể muốn ưu tiên các ứng "
"viên khác, nhưng nếu anh/cô ấy gây hứng thú với bạn, bạn có thể muốn yêu cầu "
"một buổi phỏng vấn."

#: asiantech.link/api/serializers/general_company/general_company_serializers.py:642
msgid "compare_content_2"
msgstr ""
"Ứng viên này phù hợp với nhiều yêu cầu công việc.\n"
"Kiểm tra thông tin cá nhân của kỹ sư, và nếu không có vấn đề gì đặc biệt, "
"yêu cầu một buổi phỏng vấn.\n"
"Có khả năng cao sẽ tuyển dụng anh/cô ấy, vì vậy hãy xem xét các điều kiện "
"được đề xuất và điều chỉnh các điều kiện trực tiếp qua trò chuyện."

#: asiantech.link/api/serializers/general_company/general_company_serializers.py:644
msgid "compare_content_3"
msgstr ""
"Ứng viên này phù hợp với yêu cầu công việc!\n"
"Kiểm tra thông tin cá nhân của kỹ sư, và nếu không có vấn đề gì đặc biệt, "
"yêu cầu một buổi phỏng vấn."

#: asiantech.link/api/serializers/recruitment_serializers.py:169
#: asiantech.link/api/serializers/recruitment_serializers.py:672
msgid "Invalid sex type"
msgstr "Loại giới tính không hợp lệ"

#: asiantech.link/api/serializers/recruitment_serializers.py:183
#: asiantech.link/api/serializers/recruitment_serializers.py:704
msgid "start_date must be less than end_date"
msgstr "Ngày bắt đầu phải nhỏ hơn ngày kết thúc"

#: asiantech.link/api/serializers/recruitment_serializers.py:192
#: asiantech.link/api/serializers/recruitment_serializers.py:749
msgid "age_from must be less than age_to"
msgstr "Tuổi từ phải nhỏ hơn tuổi đến"

#: asiantech.link/api/serializers/recruitment_serializers.py:200
#: asiantech.link/api/serializers/recruitment_serializers.py:693
msgid "payroll_price_from must be less than payroll_price_to"
msgstr "Mức lương từ phải nhỏ hơn mức lương đến"

#: asiantech.link/api/serializers/recruitment_serializers.py:236
#: asiantech.link/api/serializers/recruitment_serializers.py:239
#: asiantech.link/api/serializers/recruitment_serializers.py:242
#: asiantech.link/api/serializers/recruitment_serializers.py:727
#: asiantech.link/api/serializers/recruitment_serializers.py:730
#: asiantech.link/api/serializers/recruitment_serializers.py:733
msgid "Invalid pref code."
msgstr "Mã sở thích không hợp lệ."

#: asiantech.link/api/serializers/recruitment_serializers.py:255
msgid "Invalid support company."
msgstr "Công ty hỗ trợ không hợp lệ."

#: asiantech.link/api/serializers/recruitment_serializers.py:765
msgid "years_of_experience must be greater than 0"
msgstr "Số năm kinh nghiệm phải lớn hơn 0"

#: asiantech.link/api/serializers/recruitment_serializers.py:779
#: asiantech.link/api/serializers/recruitment_serializers.py:782
msgid "Invalid language code."
msgstr "Mã ngôn ngữ không hợp lệ."

#: asiantech.link/api/services/notify_service/company_notify_service.py:32
msgid "The interview result registration for is incomplete"
msgstr "Vui lòng ký hợp đồng nhận việc và hợp đồng lao động với sender_name"

#: asiantech.link/api/services/notify_service/company_notify_service.py:61
#: asiantech.link/api/services/notify_service/support_company_notify_service.py:61
#: asiantech.link/api/services/notify_service/support_company_notify_service.py:90
#: asiantech.link/api/services/notify_service/support_company_notify_service.py:125
msgid "Please register the interview date and time with"
msgstr "Vui lòng đăng ký ngày giờ phỏng vấn với sender_name"

#: asiantech.link/api/services/notify_service/company_notify_service.py:89
msgid "Please register the interview outcome for"
msgstr ""
"Xin vui lòng nhập kết quả cuộc phỏng vấn của sender_name thành công hay thất "
"bại"

#: asiantech.link/api/services/notify_service/company_notify_service.py:118
msgid "Please sign the offer acceptance and employment contract with"
msgstr "Vui lòng ký hợp đồng chấp nhận lời đề nghị với sender_name"

#: asiantech.link/api/services/notify_service/engineer_notify_service.py:26
msgid "Please respond to the interview request from"
msgstr "Vui lòng trả lời yêu cầu phỏng vấn từ sender_name"

#: asiantech.link/api/services/notify_service/engineer_notify_service.py:50
msgid "Please respond to the job offer from"
msgstr "Vui lòng trả lời đề nghị làm việc từ sender_name"

#: asiantech.link/api/services/notify_service/support_company_notify_service.py:32
msgid "Please register the document screening result for"
msgstr "Vui lòng nhập kết quả tuyển chọn hồ sơ của user_name"

#: asiantech.link/api/views/admin/admin_views.py:52
msgid "List of engineers"
msgstr ""

#: asiantech.link/api/views/admin/admin_views.py:169
#: asiantech.link/api/views/admin/admin_views.py:227
#: asiantech.link/api/views/admin/admin_views.py:259
#: asiantech.link/api/views/admin/admin_views.py:270
#: asiantech.link/api/views/admin/admin_views.py:285
#: asiantech.link/api/views/admin/admin_views.py:578
#: asiantech.link/api/views/admin/admin_views.py:589
#: asiantech.link/api/views/admin/admin_views.py:601
#: asiantech.link/api/views/authentication/authentication_views.py:264
#: asiantech.link/api/views/engineer/engineer_views.py:43
#: asiantech.link/api/views/engineer/engineer_views.py:58
#: asiantech.link/api/views/engineer/engineer_views.py:66
#: asiantech.link/api/views/engineer/engineer_views.py:107
#: asiantech.link/api/views/engineer/engineer_views.py:115
#: asiantech.link/api/views/engineer/engineer_views.py:132
#: asiantech.link/api/views/engineer/engineer_views.py:140
#: asiantech.link/api/views/engineer/engineer_views.py:189
#: asiantech.link/api/views/engineer/engineer_views.py:208
#: asiantech.link/api/views/engineer/engineer_views.py:216
#: asiantech.link/api/views/engineer/engineer_views.py:383
#: asiantech.link/api/views/engineer/engineer_views.py:403
#: asiantech.link/api/views/general/general_company_views.py:49
#: asiantech.link/api/views/general/general_company_views.py:71
#: asiantech.link/api/views/general/general_company_views.py:78
#: asiantech.link/api/views/general/general_company_views.py:84
#: asiantech.link/api/views/general/general_company_views.py:98
#: asiantech.link/api/views/general/general_company_views.py:123
#: asiantech.link/api/views/general/general_company_views.py:198
#: asiantech.link/api/views/general/general_company_views.py:239
#: asiantech.link/api/views/general/general_company_views.py:246
#: asiantech.link/api/views/general/general_company_views.py:262
#: asiantech.link/api/views/general/general_company_views.py:280
#: asiantech.link/api/views/general/general_company_views.py:296
#: asiantech.link/api/views/general/general_company_views.py:313
#: asiantech.link/api/views/general/general_company_views.py:331
#: asiantech.link/api/views/general/general_company_views.py:339
#: asiantech.link/api/views/general/general_company_views.py:358
#: asiantech.link/api/views/general/general_company_views.py:378
#: asiantech.link/api/views/general/general_company_views.py:397
#: asiantech.link/api/views/general/general_company_views.py:416
#: asiantech.link/api/views/general/general_company_views.py:434
#: asiantech.link/api/views/general/general_company_views.py:455
#: asiantech.link/api/views/general/general_company_views.py:536
#: asiantech.link/api/views/general/general_company_views.py:550
#: asiantech.link/api/views/general/general_company_views.py:590
#: asiantech.link/api/views/general/general_company_views.py:600
#: asiantech.link/api/views/general/general_company_views.py:639
#: asiantech.link/api/views/general/general_company_views.py:650
#: asiantech.link/api/views/general/general_company_views.py:700
#: asiantech.link/api/views/general/general_company_views.py:711
#: asiantech.link/api/views/general/general_company_views.py:767
#: asiantech.link/api/views/general/general_company_views.py:804
#: asiantech.link/api/views/general/general_company_views.py:814
#: asiantech.link/api/views/general/general_company_views.py:957
#: asiantech.link/api/views/general/general_company_views.py:978
#: asiantech.link/api/views/general/general_views.py:28
#: asiantech.link/api/views/general/general_views.py:77
#: asiantech.link/api/views/host_company/host_company_views.py:38
#: asiantech.link/api/views/host_company/host_company_views.py:55
#: asiantech.link/api/views/host_company/host_company_views.py:62
#: asiantech.link/api/views/host_company/host_company_views.py:86
#: asiantech.link/api/views/host_company/host_company_views.py:92
#: asiantech.link/api/views/host_company/host_company_views.py:99
#: asiantech.link/api/views/host_company/host_company_views.py:130
#: asiantech.link/api/views/host_company/host_company_views.py:136
#: asiantech.link/api/views/host_company/host_company_views.py:150
#: asiantech.link/api/views/host_company/host_company_views.py:157
#: asiantech.link/api/views/host_company/host_company_views.py:211
#: asiantech.link/api/views/host_company/host_company_views.py:244
#: asiantech.link/api/views/host_company/host_company_views.py:252
#: asiantech.link/api/views/host_company/host_company_views.py:263
#: asiantech.link/api/views/host_company/host_company_views.py:270
#: asiantech.link/api/views/host_company/host_company_views.py:289
#: asiantech.link/api/views/host_company/host_company_views.py:297
#: asiantech.link/api/views/host_company/host_company_views.py:336
#: asiantech.link/api/views/host_support_agency_company/host_support_agency_company_views.py:35
#: asiantech.link/api/views/host_support_agency_company/host_support_agency_company_views.py:81
#: asiantech.link/api/views/host_support_agency_company/host_support_agency_company_views.py:159
#: asiantech.link/api/views/host_support_agency_company/host_support_agency_company_views.py:188
#: asiantech.link/api/views/host_support_agency_company/host_support_agency_company_views.py:196
#: asiantech.link/api/views/host_support_agency_company/host_support_agency_company_views.py:230
#: asiantech.link/api/views/host_support_agency_company/host_support_agency_company_views.py:238
#: asiantech.link/api/views/host_support_agency_company/host_support_agency_company_views.py:295
#: asiantech.link/api/views/notify/count_unread_notify_view.py:24
#: asiantech.link/api/views/notify/count_unread_notify_view.py:50
#: asiantech.link/api/views/notify/get_list_notify_view.py:24
#: asiantech.link/api/views/notify/get_list_notify_view.py:43
#: asiantech.link/api/views/notify/set_read_all_notify_view.py:23
#: asiantech.link/api/views/notify/set_read_all_notify_view.py:32
#: asiantech.link/api/views/notify/set_read_notify_view.py:24
#: asiantech.link/api/views/notify/set_read_notify_view.py:38
#: asiantech.link/api/views/profile/profile_views.py:38
#: asiantech.link/api/views/profile/profile_views.py:53
#: asiantech.link/api/views/profile/profile_views.py:192
#: asiantech.link/api/views/profile/profile_views.py:241
#: asiantech.link/api/views/profile/profile_views.py:285
#: asiantech.link/api/views/profile/profile_views.py:334
#: asiantech.link/api/views/profile/profile_views.py:378
#: asiantech.link/api/views/profile/profile_views.py:427
#: asiantech.link/api/views/profile/profile_views.py:471
#: asiantech.link/api/views/profile/profile_views.py:520
#: asiantech.link/api/views/profile/profile_views.py:564
#: asiantech.link/api/views/profile/profile_views.py:613
#: asiantech.link/api/views/recruit/recruitment_views.py:39
#: asiantech.link/api/views/recruit/recruitment_views.py:60
#: asiantech.link/api/views/recruit/recruitment_views.py:67
#: asiantech.link/api/views/recruit/recruitment_views.py:84
#: asiantech.link/api/views/recruit/recruitment_views.py:91
#: asiantech.link/api/views/recruit/recruitment_views.py:97
#: asiantech.link/api/views/recruit/recruitment_views.py:109
#: asiantech.link/api/views/recruit/recruitment_views.py:122
#: asiantech.link/api/views/recruit/recruitment_views.py:128
#: asiantech.link/api/views/recruit/recruitment_views.py:139
#: asiantech.link/api/views/recruit/recruitment_views.py:146
#: asiantech.link/api/views/recruit/recruitment_views.py:174
#: asiantech.link/api/views/recruit/recruitment_views.py:181
#: asiantech.link/api/views/recruit/recruitment_views.py:305
#: asiantech.link/api/views/recruit/recruitment_views.py:312
#: asiantech.link/api/views/recruit/recruitment_views.py:438
#: asiantech.link/api/views/recruit/recruitment_views.py:444
#: asiantech.link/api/views/recruit/recruitment_views.py:475
#: asiantech.link/api/views/recruit/recruitment_views.py:481
#: asiantech.link/api/views/recruit/recruitment_views.py:567
#: asiantech.link/api/views/recruit/recruitment_views.py:574
#: asiantech.link/api/views/recruit/recruitment_views.py:662
#: asiantech.link/api/views/recruit/recruitment_views.py:670
#: asiantech.link/api/views/recruit/recruitment_views.py:703
#: asiantech.link/api/views/recruit/recruitment_views.py:714
#: asiantech.link/api/views/recruit/recruitment_views.py:751
#: asiantech.link/api/views/recruit/recruitment_views.py:760
#: asiantech.link/api/views/recruit/recruitment_views.py:766
#: asiantech.link/api/views/recruit/recruitment_views.py:783
#: asiantech.link/api/views/recruit/recruitment_views.py:796
#: asiantech.link/api/views/recruit/recruitment_views.py:805
#: asiantech.link/api/views/recruit/recruitment_views.py:852
#: asiantech.link/api/views/recruit/recruitment_views.py:889
msgid "Success"
msgstr "Thành công"

#: asiantech.link/api/views/admin/admin_views.py:178
msgid "List of email schedules"
msgstr ""

#: asiantech.link/api/views/admin/admin_views.py:237
msgid "List of registrars"
msgstr ""

#: asiantech.link/api/views/admin/admin_views.py:296
#: asiantech.link/api/views/admin/admin_views.py:384
msgid "Email schedule created successfully"
msgstr "Người dùng đã được tạo thành công"

#: asiantech.link/api/views/admin/admin_views.py:314
#: asiantech.link/api/views/admin/admin_views.py:417
msgid "Target email not found"
msgstr "Không tìm thấy email"

#: asiantech.link/api/views/admin/admin_views.py:367
#: asiantech.link/api/views/admin/admin_views.py:470
msgid "Send test email successfully"
msgstr "Email đã được gửi thành công"

#: asiantech.link/api/views/admin/admin_views.py:376
#: asiantech.link/api/views/admin/admin_views.py:478
msgid "Email personal information already exists"
msgstr "Email thông tin cá nhân đã tồn tại"

#: asiantech.link/api/views/admin/admin_views.py:395
#: asiantech.link/api/views/admin/admin_views.py:486
msgid "Email schedule updated successfully"
msgstr "Email đã được cập nhật thành công"

#: asiantech.link/api/views/admin/admin_views.py:407
#: asiantech.link/api/views/admin/admin_views.py:507
#: asiantech.link/api/views/admin/admin_views.py:572
msgid "Email schedule not found"
msgstr "Không tìm thấy email"

#: asiantech.link/api/views/admin/admin_views.py:497
#: asiantech.link/api/views/admin/admin_views.py:512
msgid "Email schedule deleted successfully"
msgstr "Email đã được xóa thành công"

#: asiantech.link/api/views/admin/admin_views.py:524
#: asiantech.link/api/views/admin/admin_views.py:550
msgid "Email schedule list deleted successfully"
msgstr "Danh sách email đã được xóa thành công"

#: asiantech.link/api/views/admin/admin_views.py:540
msgid "Invalid data"
msgstr "Dữ liệu không hợp lệ"

#: asiantech.link/api/views/admin/admin_views.py:545
msgid "No email schedule ids provided"
msgstr "Không có email đã được cung cấp"

#: asiantech.link/api/views/admin/admin_views.py:561
msgid "Email schedule details"
msgstr "Chi tiết email"

#: asiantech.link/api/views/admin/admin_views.py:603
#, fuzzy
#| msgid "User not found"
msgid "Engineer not found"
msgstr "Không tìm thấy người dùng"

#: asiantech.link/api/views/authentication/authentication_views.py:43
msgid "Verification email sent"
msgstr "Email xác minh đã được gửi"

#: asiantech.link/api/views/authentication/authentication_views.py:46
msgid "Verification whatsapp sent"
msgstr "Xác minh WhatsApp đã được gửi."

#: asiantech.link/api/views/authentication/authentication_views.py:49
msgid "Email verified successfully"
msgstr "Email đã được xác minh thành công"

#: asiantech.link/api/views/authentication/authentication_views.py:52
msgid "User created successfully"
msgstr "Người dùng đã được tạo thành công"

#: asiantech.link/api/views/authentication/authentication_views.py:55
#: asiantech.link/api/views/authentication/authentication_views.py:415
#: asiantech.link/api/views/authentication/authentication_views.py:540
#: asiantech.link/api/views/authentication/authentication_views.py:584
#: asiantech.link/api/views/authentication/authentication_views.py:801
#: asiantech.link/api/views/authentication/authentication_views.py:817
#: asiantech.link/api/views/authentication/authentication_views.py:1065
msgid "Login successfully"
msgstr "Đăng nhập thành công"

#: asiantech.link/api/views/authentication/authentication_views.py:57
msgid "Reset password successfully"
msgstr "Đặt lại mật khẩu thành công"

#: asiantech.link/api/views/authentication/authentication_views.py:102
msgid "emailRegisterVerificationTitle"
msgstr "【AsianTech.Link】Xác thực địa chỉ email của bạn"

#: asiantech.link/api/views/authentication/authentication_views.py:109
msgid "emailConfirmContent1"
msgstr "Cảm ơn bạn đã đăng ký tài khoản tại AsianTech.Link"

#: asiantech.link/api/views/authentication/authentication_views.py:110
msgid "emailConfirmContent2"
msgstr ""
"Để sử dụng dịch vụ một cách an toàn, việc xác thực địa chỉ email là cần "
"thiết. Vui lòng xác thực địa chỉ email của bạn bằng cách sử dụng nút bên "
"dưới."

#: asiantech.link/api/views/authentication/authentication_views.py:111
msgid "emailConfirmContent3"
msgstr ""
"Vì sự an toàn của bạn, chúng tôi yêu cầu bạn xác thực địa chỉ email của "
"mình. Vui lòng xác thực địa chỉ email của bạn bằng cách sử dụng nút bên dưới."

#: asiantech.link/api/views/authentication/authentication_views.py:112
#: asiantech.link/api/views/authentication/authentication_views.py:315
msgid "emailConfirmContent4"
msgstr "© Bản quyền thuộc về AsianTech.Link. Mọi quyền được bảo lưu."

#: asiantech.link/api/views/authentication/authentication_views.py:114
msgid "Verification Email"
msgstr "Email xác thực đã được gửi"

#: asiantech.link/api/views/authentication/authentication_views.py:119
msgid "verifyYourAccount"
msgstr "Xác thực địa chỉ email của bạn"

#: asiantech.link/api/views/authentication/authentication_views.py:155
#: asiantech.link/api/views/authentication/authentication_views.py:157
msgid "Email is already linked to an another account"
msgstr ""

#: asiantech.link/api/views/authentication/authentication_views.py:166
#: asiantech.link/api/views/authentication/authentication_views.py:296
#: asiantech.link/api/views/authentication/authentication_views.py:986
#: asiantech.link/api/views/authentication/authentication_views.py:1049
#: asiantech.link/api/views/authentication/authentication_views.py:1101
#: asiantech.link/api/views/authentication/authentication_views.py:1176
msgid "User not found!"
msgstr "Không tìm thấy người dùng!"

#: asiantech.link/api/views/authentication/authentication_views.py:169
#: asiantech.link/api/views/authentication/authentication_views.py:298
msgid "User is already verified!"
msgstr "Người dùng đã được xác thực!"

#: asiantech.link/api/views/authentication/authentication_views.py:173
#: asiantech.link/api/views/authentication/authentication_views.py:552
msgid "CAPTCHA verification failed"
msgstr "Xác thực CAPTCHA thất bại"

#: asiantech.link/api/views/authentication/authentication_views.py:266
msgid "Invalid code!"
msgstr "Mã không hợp lệ."

#: asiantech.link/api/views/authentication/authentication_views.py:282
msgid "Token is required!"
msgstr "Cần có mã thông báo!"

#: asiantech.link/api/views/authentication/authentication_views.py:306
msgid "emailRegisterSuccessTitle"
msgstr "【Đăng kí thành công】Tài khoản AsianTech.Link"

#: asiantech.link/api/views/authentication/authentication_views.py:313
msgid "emailRegisterSuccessContent1"
msgstr "Đăng ký tài khoản AsianTech.Link đã hoàn tất"

#: asiantech.link/api/views/authentication/authentication_views.py:314
msgid "emailRegisterSuccessContent2"
msgstr ""
"Hãy truyền tải sức hấp dẫn của bạn thông qua Asian Tech.Linkvà kết nối với "
"những cơ hội việc làm hấp dẫn."

#: asiantech.link/api/views/authentication/authentication_views.py:331
#: asiantech.link/api/views/authentication/authentication_views.py:333
#: asiantech.link/api/views/authentication/authentication_views.py:1006
#: asiantech.link/api/views/authentication/authentication_views.py:1014
#: asiantech.link/api/views/authentication/authentication_views.py:1046
msgid "Verification token has expired!"
msgstr "Mã xác thực đã hết hạn!"

#: asiantech.link/api/views/authentication/authentication_views.py:335
msgid "Invalid verification token!"
msgstr "Mã xác thực không hợp lệ!"

#: asiantech.link/api/views/authentication/authentication_views.py:350
#: asiantech.link/api/views/authentication/authentication_views.py:428
msgid "Validation errors in your request"
msgstr "Lỗi xác thực trong yêu cầu của bạn"

#: asiantech.link/api/views/authentication/authentication_views.py:391
#: asiantech.link/api/views/authentication/authentication_views.py:501
msgid "The email address is already registered with engineer"
msgstr "Địa chỉ email đã được đăng ký với kỹ sư."

#: asiantech.link/api/views/authentication/authentication_views.py:393
#: asiantech.link/api/views/authentication/authentication_views.py:495
msgid "The email address is already registered with company"
msgstr "Địa chỉ email đã được đăng ký với công ty"

#: asiantech.link/api/views/authentication/authentication_views.py:394
msgid "Email is already registered!"
msgstr "Email đã được đăng ký!"

#: asiantech.link/api/views/authentication/authentication_views.py:436
#: asiantech.link/api/views/authentication/authentication_views.py:440
#: asiantech.link/api/views/authentication/authentication_views.py:465
#: asiantech.link/api/views/authentication/authentication_views.py:469
msgid "Email address or password does not match"
msgstr "Email hoặc mật khẩu không khớp"

#: asiantech.link/api/views/authentication/authentication_views.py:439
#: asiantech.link/api/views/authentication/authentication_views.py:454
#: asiantech.link/api/views/authentication/authentication_views.py:468
msgid "Login failed."
msgstr "Không thể đăng nhập"

#: asiantech.link/api/views/authentication/authentication_views.py:451
#: asiantech.link/api/views/authentication/authentication_views.py:455
msgid "The user ID or password is incorrect."
msgstr "Địa chỉ email hoặc mật khẩu không khớp"

#: asiantech.link/api/views/authentication/authentication_views.py:489
#: asiantech.link/api/views/authentication/authentication_views.py:572
msgid "CAPTCHA verification required"
msgstr "Cần xác thực CAPTCHA"

#: asiantech.link/api/views/authentication/authentication_views.py:507
msgid ""
"The account with this email has been registered with the receiving support "
"organization."
msgstr ""

#: asiantech.link/api/views/authentication/authentication_views.py:698
#: asiantech.link/api/views/authentication/authentication_views.py:700
#: asiantech.link/api/views/authentication/authentication_views.py:738
#: asiantech.link/api/views/authentication/authentication_views.py:740
#: asiantech.link/api/views/authentication/authentication_views.py:809
#: asiantech.link/api/views/authentication/authentication_views.py:827
#, fuzzy
#| msgid "Login failed."
msgid "Login failed!"
msgstr "Không thể đăng nhập"

#: asiantech.link/api/views/authentication/authentication_views.py:811
#, fuzzy
#| msgid "Invalid sex type"
msgid "Invalid SNS type!"
msgstr "Loại giới tính không hợp lệ"

#: asiantech.link/api/views/authentication/authentication_views.py:921
msgid "Your Verification Code"
msgstr "Mã xác thực của bạn"

#: asiantech.link/api/views/authentication/authentication_views.py:926
msgid "Email Verification"
msgstr ""
"Cảm ơn bạn đã đăng ký. Vui lòng sử dụng mã xác thực dưới đây để hoàn tất "
"đăng ký của bạn:"

#: asiantech.link/api/views/authentication/authentication_views.py:929
msgid "Please use the verification code below to complete your login:"
msgstr "Vui lòng sử dụng mã xác thực dưới đây để hoàn tất đăng nhập của bạn:"

#: asiantech.link/api/views/authentication/authentication_views.py:932
msgid "Note: This code is valid for a limited time."
msgstr "Lưu ý: Mã này có hiệu lực trong thời gian giới hạn."

#: asiantech.link/api/views/authentication/authentication_views.py:946
msgid "We sent you a code to verify your login"
msgstr "Chúng tôi đã gửi cho bạn một mã để xác thực đăng nhập của bạn"

#: asiantech.link/api/views/authentication/authentication_views.py:968
#: asiantech.link/api/views/authentication/authentication_views.py:978
#: asiantech.link/api/views/authentication/authentication_views.py:1034
msgid "Password Reset Request"
msgstr "Yêu cầu đặt lại mật khẩu"

#: asiantech.link/api/views/authentication/authentication_views.py:978
msgid "Dear"
msgstr "Kính gửi"

#: asiantech.link/api/views/authentication/authentication_views.py:978
msgid "password_request_reset_content_1"
msgstr ""
"Chúng tôi nhận được yêu cầu đặt lại mật khẩu của bạn. Vui lòng nhấn nút xác "
"nhận để nhận mật khẩu mới:"

#: asiantech.link/api/views/authentication/authentication_views.py:979
msgid "password_request_reset_content_2"
msgstr "Nếu bạn không yêu cầu đặt lại mật khẩu, vui lòng bỏ qua email này."

#: asiantech.link/api/views/authentication/authentication_views.py:979
#: asiantech.link/api/views/authentication/authentication_views.py:1035
msgid "passwordResetContent2"
msgstr "Vui lòng đăng nhập với mật khẩu mới này và thay đổi ngay lập tức."

#: asiantech.link/api/views/authentication/authentication_views.py:979
msgid "Thank you"
msgstr "Cảm ơn bạn"

#: asiantech.link/api/views/authentication/authentication_views.py:984
msgid "Password reset email sent!"
msgstr "Email đặt lại mật khẩu đã được gửi!"

#: asiantech.link/api/views/authentication/authentication_views.py:1019
msgid "Invalid token type!"
msgstr "Loại mã không hợp lệ!"

#: asiantech.link/api/views/authentication/authentication_views.py:1026
msgid "Your Password Reset Request"
msgstr "Yêu cầu đặt lại mật khẩu của bạn"

#: asiantech.link/api/views/authentication/authentication_views.py:1032
msgid "Password Reset"
msgstr "Đặt lại mật khẩu"

#: asiantech.link/api/views/authentication/authentication_views.py:1034
msgid "Hello"
msgstr "Xin chào"

#: asiantech.link/api/views/authentication/authentication_views.py:1034
msgid "passwordResetContent1"
msgstr "Mật khẩu của bạn đã được đặt lại. Đây là mật khẩu mới của bạn:"

#: asiantech.link/api/views/authentication/authentication_views.py:1035
msgid "passwordResetContent3"
msgstr ""
"Nếu bạn không yêu cầu đặt lại mật khẩu này, vui lòng liên hệ với đội ngũ hỗ "
"trợ của chúng tôi."

#: asiantech.link/api/views/authentication/authentication_views.py:1044
msgid "Password reset successfully!"
msgstr "Đặt lại mật khẩu thành công!"

#: asiantech.link/api/views/authentication/authentication_views.py:1099
msgid "Invalid code or email!"
msgstr "Mã hoặc email không hợp lệ!"

#: asiantech.link/api/views/authentication/authentication_views.py:1103
msgid "Invalid email or password!"
msgstr "Email hoặc mật khẩu không hợp lệ!"

#: asiantech.link/api/views/authentication/authentication_views.py:1111
#: asiantech.link/api/views/authentication/authentication_views.py:1125
msgid "Logout successfully"
msgstr "Đăng xuất thành công"

#: asiantech.link/api/views/authentication/authentication_views.py:1131
#: asiantech.link/api/views/authentication/authentication_views.py:1144
msgid "Captcha generated successfully"
msgstr "Captcha được tạo thành công"

#: asiantech.link/api/views/authentication/authentication_views.py:1151
msgid "Check captcha required in login"
msgstr "Kiểm tra captcha yêu cầu khi đăng nhập"

#: asiantech.link/api/views/authentication/authentication_views.py:1211
#: asiantech.link/api/views/authentication/authentication_views.py:1228
#: asiantech.link/api/views/authentication/authentication_views.py:1237
#: asiantech.link/api/views/authentication/authentication_views.py:1256
msgid "Account deleted successfully"
msgstr "Tài khoản đã được xóa thành công"

#: asiantech.link/api/views/authentication/authentication_views.py:1258
msgid "error"
msgstr "Lỗi"

#: asiantech.link/api/views/chat/chat_views.py:22
#: asiantech.link/api/views/chat/chat_views.py:56
msgid "All messages marked as read"
msgstr ""

#: asiantech.link/api/views/chat/chat_views.py:38
#, fuzzy
#| msgid "Recruitment does not exist"
msgid "Group does not exist."
msgstr "Tuyển dụng không tồn tại"

#: asiantech.link/api/views/chat/chat_views.py:44
msgid "User is not a member of this group."
msgstr ""

#: asiantech.link/api/views/chat/chat_views.py:59
msgid "No messages to mark as read"
msgstr ""

#: asiantech.link/api/views/engineer/engineer_views.py:87
#: asiantech.link/api/views/engineer/engineer_views.py:160
msgid "Agency company not found."
msgstr "Không tìm thấy công ty đại lý."

#: asiantech.link/api/views/engineer/engineer_views.py:94
msgid "This agency company is already linked with the engineer."
msgstr "Công ty đại lý này đã được liên kết với kỹ sư."

#: asiantech.link/api/views/engineer/engineer_views.py:169
msgid "This agency company has already been deleted."
msgstr "Công ty đại lý này đã bị xóa."

#: asiantech.link/api/views/engineer/engineer_views.py:176
msgid "Agency company successfully deleted."
msgstr "Công ty đại lý đã được xóa thành công."

#: asiantech.link/api/views/engineer/engineer_views.py:179
msgid "This agency company is not linked with the engineer."
msgstr "Công ty đại lý này không được liên kết với kỹ sư."

#: asiantech.link/api/views/engineer/engineer_views.py:231
#: asiantech.link/api/views/general/general_company_views.py:477
msgid "Invalid ordering field."
msgstr "Trường sắp xếp không hợp lệ."

#: asiantech.link/api/views/engineer/engineer_views.py:239
#: asiantech.link/api/views/engineer/engineer_views.py:368
msgid "Invalid recruit_progress_code"
msgstr "Mã tiến độ tuyển dụng không hợp lệ"

#: asiantech.link/api/views/engineer/engineer_views.py:396
#: asiantech.link/api/views/general/general_company_views.py:352
#: asiantech.link/api/views/profile/profile_views.py:233
#: asiantech.link/api/views/profile/profile_views.py:326
#: asiantech.link/api/views/profile/profile_views.py:419
#: asiantech.link/api/views/profile/profile_views.py:512
#: asiantech.link/api/views/profile/profile_views.py:605
msgid "User not found"
msgstr "Không tìm thấy người dùng"

#: asiantech.link/api/views/general/general_company_views.py:63
msgid "Email already exists"
msgstr "Email đã tồn tại"

#: asiantech.link/api/views/general/general_company_views.py:93
#: asiantech.link/api/views/host_company/host_company_views.py:69
#: asiantech.link/api/views/host_company/host_company_views.py:106
msgid "User does not have company"
msgstr "Người dùng không có công ty"

#: asiantech.link/api/views/general/general_company_views.py:100
#: asiantech.link/api/views/general/general_company_views.py:125
#: asiantech.link/api/views/host_company/host_company_views.py:75
#: asiantech.link/api/views/host_company/host_company_views.py:112
#: asiantech.link/api/views/host_support_agency_company/host_support_agency_company_views.py:45
#: asiantech.link/api/views/host_support_agency_company/host_support_agency_company_views.py:72
#: asiantech.link/api/views/media/company_media_views.py:59
#: asiantech.link/api/views/media/company_media_views.py:122
msgid "Company not found"
msgstr "Không tìm thấy công ty"

#: asiantech.link/api/views/general/general_company_views.py:274
msgid "Filter saved successfully"
msgstr "Lọc đã được lưu thành công"

#: asiantech.link/api/views/general/general_company_views.py:304
msgid "Filter deleted successfully"
msgstr "Lọc đã được xóa thành công"

#: asiantech.link/api/views/general/general_company_views.py:327
msgid "You can't update this status!"
msgstr "Bạn không thể cập nhật trạng thái này!"

#: asiantech.link/api/views/general/general_company_views.py:485
#: asiantech.link/api/views/general/general_company_views.py:670
#: asiantech.link/api/views/general/general_company_views.py:831
msgid "You don't have permission to access this company"
msgstr ""

#: asiantech.link/api/views/general/general_company_views.py:487
#: asiantech.link/api/views/general/general_company_views.py:668
#: asiantech.link/api/views/general/general_company_views.py:829
msgid "Host company not found"
msgstr "Không tìm thấy công ty"

#: asiantech.link/api/views/general/general_company_views.py:573
#: asiantech.link/api/views/general/general_company_views.py:576
#: asiantech.link/api/views/general/general_company_views.py:578
#: asiantech.link/api/views/general/general_company_views.py:627
#: asiantech.link/api/views/general/general_company_views.py:630
#: asiantech.link/api/views/general/general_company_views.py:632
#: asiantech.link/api/views/general/general_company_views.py:792
#: asiantech.link/api/views/general/general_company_views.py:795
#: asiantech.link/api/views/general/general_company_views.py:797
#: asiantech.link/api/views/general/general_company_views.py:873
#: asiantech.link/api/views/general/general_company_views.py:876
#: asiantech.link/api/views/general/general_company_views.py:878
msgid "You don't have permission to access this apply"
msgstr ""

#: asiantech.link/api/views/general/general_company_views.py:592
#: asiantech.link/api/views/general/general_company_views.py:641
#: asiantech.link/api/views/general/general_company_views.py:702
#: asiantech.link/api/views/general/general_company_views.py:806
#: asiantech.link/api/views/general/general_company_views.py:843
#: asiantech.link/api/views/general/general_company_views.py:949
#: asiantech.link/api/views/host_company/host_company_views.py:340
msgid "Apply not found"
msgstr "Không tìm thấy đơn ứng tuyển"

#: asiantech.link/api/views/general/general_company_views.py:946
msgid "Something went wrong!"
msgstr "Có lỗi xảy ra!"

#: asiantech.link/api/views/general/general_company_views.py:968
#: asiantech.link/api/views/host_support_agency_company/host_support_agency_company_views.py:205
#: asiantech.link/api/views/recruit/recruitment_views.py:49
#: asiantech.link/api/views/recruit/recruitment_views.py:155
msgid "You do not own the company"
msgstr "Bạn không sở hữu công ty"

#: asiantech.link/api/views/general/general_views.py:46
msgid "Current language is: "
msgstr "Ngôn ngữ hiện tại là: "

#: asiantech.link/api/views/host_company/host_company_views.py:88
msgid "User already subscribed"
msgstr "Người dùng đã đăng ký"

#: asiantech.link/api/views/host_company/host_company_views.py:120
msgid "User not subscribed"
msgstr "Người dùng chưa đăng ký"

#: asiantech.link/api/views/host_company/host_company_views.py:128
msgid "Data not found"
msgstr "Dữ liệu không tìm thấy"

#: asiantech.link/api/views/host_company/host_company_views.py:143
msgid "User does not have company."
msgstr "Người dùng không có công ty."

#: asiantech.link/api/views/host_company/host_company_views.py:172
msgid "You have already make a request interview"
msgstr ""

#: asiantech.link/api/views/host_company/host_company_views.py:213
#: asiantech.link/api/views/host_support_agency_company/host_support_agency_company_views.py:161
msgid "You have requested an interview for this engineer."
msgstr ""

#: asiantech.link/api/views/host_company/host_company_views.py:331
msgid "Notification of acceptance has been sent."
msgstr "Thông báo chấp nhận đã được gửi."

#: asiantech.link/api/views/host_company/host_company_views.py:338
msgid "Invalid status"
msgstr "Trạng thái không hợp lệ"

#: asiantech.link/api/views/host_support_agency_company/host_support_agency_company_views.py:102
#: asiantech.link/api/views/host_support_agency_company/host_support_agency_company_views.py:213
msgid "Host company is not belong to you"
msgstr "Host company không thuộc về bạn"

#: asiantech.link/api/views/host_support_agency_company/host_support_agency_company_views.py:109
msgid "This engineer already make a interview to host company"
msgstr "Địa chỉ email đã được đăng ký với công ty"

#: asiantech.link/api/views/host_support_agency_company/host_support_agency_company_views.py:247
msgid "You are not authorized to access this endpoint"
msgstr "Bạn không có quyền truy cập vào điểm cuối này"

#: asiantech.link/api/views/media/admin_upload_avatar_engineer_view.py:53
#: asiantech.link/api/views/media/admin_upload_avatar_engineer_view.py:77
#: asiantech.link/api/views/media/avatar_views.py:44
#: asiantech.link/api/views/media/avatar_views.py:68
#: asiantech.link/api/views/media/company_media_views.py:48
#: asiantech.link/api/views/media/company_media_views.py:81
#: asiantech.link/api/views/media/company_media_views.py:110
#: asiantech.link/api/views/media/company_media_views.py:174
#: asiantech.link/api/views/media/contract_media_view.py:56
#: asiantech.link/api/views/media/contract_media_view.py:117
#: asiantech.link/api/views/media/contract_media_view.py:148
#: asiantech.link/api/views/media/contract_media_view.py:212
#: asiantech.link/api/views/media/passport_views.py:44
#: asiantech.link/api/views/media/passport_views.py:69
#: asiantech.link/api/views/media/recruit_media_views.py:44
#: asiantech.link/api/views/media/recruit_media_views.py:65
msgid "Image uploaded successfully"
msgstr "Hình ảnh đã được tải lên thành công"

#: asiantech.link/api/views/media/admin_upload_avatar_engineer_view.py:92
#: asiantech.link/api/views/media/admin_upload_avatar_engineer_view.py:110
#: asiantech.link/api/views/media/avatar_views.py:74
#: asiantech.link/api/views/media/avatar_views.py:92
msgid "Image deleted successfully"
msgstr "Hình ảnh đã được xóa thành công"

#: asiantech.link/api/views/media/company_media_views.py:53
#: asiantech.link/api/views/media/company_media_views.py:116
#: asiantech.link/api/views/media/recruit_media_views.py:49
msgid "You are not a company"
msgstr "Bạn không phải là công ty"

#: asiantech.link/api/views/media/company_media_views.py:139
msgid "Index must be 0,1,2"
msgstr "Chỉ số phải là 0, 1, 2"

#: asiantech.link/api/views/media/contract_media_view.py:68
msgid "You can not upload contract image now, recruit progress code !=60"
msgstr ""
"Bạn không thể tải lên hình ảnh hợp đồng bây giờ, mã tiến độ tuyển dụng không "
"phải là 60"

#: asiantech.link/api/views/media/contract_media_view.py:167
msgid "You are not authorized to upload contract image"
msgstr ""

#: asiantech.link/api/views/media/contract_media_view.py:171
msgid "You can not upload contract image now, status progress code is invalid"
msgstr ""
"Bạn không thể tải lên hình ảnh hợp đồng bây giờ, mã tiến độ trạng thái không "
"hợp lệ"

#: asiantech.link/api/views/media/passport_views.py:75
#: asiantech.link/api/views/media/passport_views.py:93
msgid "Passport image deleted successfully"
msgstr "Hình ảnh hộ chiếu đã được xóa thành công"

#: asiantech.link/api/views/notify/set_read_notify_view.py:34
msgid "Invalid request"
msgstr "Yêu cầu không hợp lệ"

#: asiantech.link/api/views/profile/cv_views.py:52
#: asiantech.link/api/views/profile/cv_views.py:321
#: asiantech.link/api/views/profile/cv_views.py:389
#, fuzzy
#| msgid "Image uploaded successfully"
msgid "CV uploaded successfully"
msgstr "Hình ảnh đã được tải lên thành công"

#: asiantech.link/api/views/profile/cv_views.py:58
msgid "File size must be less than 5MB"
msgstr "Kích thước tệp phải nhỏ hơn 5MB"

#: asiantech.link/api/views/profile/cv_views.py:288
msgid "Invalid document type: not a resume"
msgstr "Loại tài liệu không hợp lệ: không phải là CV"

#: asiantech.link/api/views/profile/cv_views.py:331
#, fuzzy
#| msgid "User not found"
msgid "CV not found"
msgstr "Không tìm thấy người dùng"

#: asiantech.link/api/views/profile/profile_views.py:86
msgid "This email is already in use."
msgstr "Email đã được sử dụng"

#: asiantech.link/api/views/profile/profile_views.py:146
msgid "Password updated successfully"
msgstr "Mật khẩu đã được cập nhật thành công"

#: asiantech.link/api/views/profile/profile_views.py:162
#: asiantech.link/api/views/profile/profile_views.py:184
msgid "New password must be different from the current password."
msgstr "Mật khẩu mới phải khác với mật khẩu hiện tại."

#: asiantech.link/api/views/profile/profile_views.py:182
msgid "Password updated successfully."
msgstr "Mật khẩu đã được cập nhật thành công."

#: asiantech.link/api/views/profile/profile_views.py:186
msgid "Invalid current password."
msgstr "Mật khẩu hiện tại không hợp lệ."

#: asiantech.link/api/views/recruit/recruitment_views.py:82
#: asiantech.link/api/views/recruit/recruitment_views.py:106
#: asiantech.link/api/views/recruit/recruitment_views.py:115
#: asiantech.link/api/views/recruit/recruitment_views.py:136
#: asiantech.link/api/views/recruit/recruitment_views.py:583
#: asiantech.link/api/views/recruit/recruitment_views.py:658
#: asiantech.link/api/views/recruit/recruitment_views.py:705
#: asiantech.link/api/views/recruit/recruitment_views.py:753
msgid "Recruitment does not exist"
msgstr "Tuyển dụng không tồn tại"

#: asiantech.link/api/views/recruit/recruitment_views.py:300
#: asiantech.link/api/views/recruit/recruitment_views.py:436
msgid "Error"
msgstr "Lỗi"

#: asiantech.link/api/views/recruit/recruitment_views.py:460
msgid "Failed"
msgstr "Thất bại"

#: asiantech.link/api/views/recruit/recruitment_views.py:495
msgid "ComCompany not found"
msgstr "Không tìm thấy ComCompany"

#: asiantech.link/api/views/recruit/recruitment_views.py:502
msgid "MapEngAgc not found"
msgstr "Không tìm thấy MapEngAgc"

#: asiantech.link/api/views/recruit/recruitment_views.py:516
msgid "Already applied"
msgstr "Đã ứng tuyển"

#: asiantech.link/api/views/recruit/recruitment_views.py:533
msgid "You have already applied for this recruitment"
msgstr ""

#: asiantech.link/api/views/recruit/recruitment_views.py:656
msgid "Recruitment does not exist or you do not have access to it"
msgstr "Tuyển dụng không tồn tại hoặc bạn không có quyền truy cập"

#: asiantech.link/api/views/recruit/recruitment_views.py:679
#: asiantech.link/api/views/recruit/recruitment_views.py:724
msgid "Recruit ID is required"
msgstr "Cần có ID tuyển dụng"

#: asiantech.link/api/views/recruit/recruitment_views.py:687
#: asiantech.link/api/views/recruit/recruitment_views.py:776
#: asiantech.link/api/views/recruit/recruitment_views.py:824
msgid "No application found"
msgstr "Không tìm thấy đơn ứng tuyển"

#: asiantech.link/api/views/recruit/recruitment_views.py:698
#: asiantech.link/api/views/recruit/recruitment_views.py:739
msgid "Invalid recruit progress code to cancel"
msgstr "Mã tiến độ tuyển dụng không hợp lệ để hủy"

#: asiantech.link/api/views/recruit/recruitment_views.py:737
msgid "Interview datetime is required"
msgstr "Ngày giờ phỏng vấn là bắt buộc"

#: asiantech.link/api/views/recruit/recruitment_views.py:798
msgid "Invalid recruit progress code to sign contract"
msgstr "Mã tiến độ tuyển dụng không hợp lệ để ký hợp đồng"

#: asiantech.link/api/views/recruit/recruitment_views.py:831
msgid "No application found for the provided recruitment ID"
msgstr "Không tìm thấy đơn ứng tuyển cho ID tuyển dụng đã cung cấp"

#: asiantech.link/api/views/recruit/recruitment_views.py:837
msgid "No accept sign found for the application"
msgstr "Không tìm thấy chữ ký chấp nhận cho đơn ứng tuyển"

#: asiantech.link/core/settings/common.py:132
msgid "English"
msgstr "Tiếng Anh"

#: asiantech.link/core/settings/common.py:133
msgid "Japanese"
msgstr "Tiếng Nhật"

#: asiantech.link/core/settings/common.py:134
msgid "Myanmar"
msgstr "Tiếng Myanmar"

#: asiantech.link/core/settings/common.py:135
msgid "Indonesian"
msgstr "Tiếng Indonesia"

#: asiantech.link/core/settings/common.py:136
msgid "Nepali"
msgstr "Tiếng Nepal"

#: asiantech.link/core/settings/common.py:137
msgid "Vietnamese"
msgstr "Tiếng Việt"

#: asiantech.link/utils/permissions.py:22
#: asiantech.link/utils/permissions.py:36
msgid "Account has not been verified"
msgstr "Tài khoản chưa được xác minh"

#: asiantech.link/utils/permissions.py:39
#: asiantech.link/utils/permissions.py:53
#: asiantech.link/utils/permissions.py:64
#: asiantech.link/utils/permissions.py:83
#: asiantech.link/utils/permissions.py:96
#: asiantech.link/utils/permissions.py:107
#: asiantech.link/utils/permissions.py:120
#: asiantech.link/utils/permissions.py:132
msgid "Access denied"
msgstr ""

#: asiantech.link/utils/utils.py:508 asiantech.link/utils/utils.py:644
msgid "Fresher"
msgstr "Mới ra trường"

#: asiantech.link/utils/utils.py:510 asiantech.link/utils/utils.py:646
msgid "Junior"
msgstr "Mới vào nghề"

#: asiantech.link/utils/utils.py:512 asiantech.link/utils/utils.py:648
msgid "Middle"
msgstr "Trung cấp"

#: asiantech.link/utils/utils.py:514 asiantech.link/utils/utils.py:650
msgid "Senior"
msgstr "Cao cấp"

#: asiantech.link/utils/validators.py:29
msgid "Email is too long"
msgstr "Email quá dài"

#: asiantech.link/utils/validators.py:35
msgid "Email must contain a single '@' symbol"
msgstr "Email phải chứa một ký tự '@' duy nhất"

#: asiantech.link/utils/validators.py:39
msgid "Local part of the email is too long (>= 64 characters)"
msgstr "Phần địa phương của email quá dài (>= 64 ký tự)"

#: asiantech.link/utils/validators.py:43
msgid "Email contains invalid characters"
msgstr "Email chứa ký tự không hợp lệ"

#: asiantech.link/utils/validators.py:47
msgid "Email contains consecutive periods"
msgstr "Email chứa các dấu chấm liền kề nhau"

#: asiantech.link/utils/validators.py:51
msgid ""
"Email has periods at invalid positions (start/end of local part or end of "
"email)"
msgstr ""
"Email có dấu chấm ở vị trí không hợp lệ (đầu/cuối phần địa phương hoặc cuối "
"email)"

#: asiantech.link/utils/validators.py:55
msgid "Domain part of the email should not be an IP address"
msgstr "Phần miền của email không nên là địa chỉ IP"

#: asiantech.link/utils/validators.py:57
msgid "Email is valid"
msgstr "Email hợp lệ"

#: asiantech.link/utils/validators.py:63
msgid "Password length must be between 8 and 20 characters"
msgstr "Độ dài mật khẩu phải từ 8 đến 20 ký tự"

#: asiantech.link/utils/validators.py:68
msgid "Password contains full-width characters"
msgstr "Mật khẩu chứa ký tự dạng full-width"

#: asiantech.link/utils/validators.py:72
msgid "Password must contain at least one uppercase letter"
msgstr "Mật khẩu phải chứa ít nhất một chữ cái viết hoa"

#: asiantech.link/utils/validators.py:76
msgid "Password must contain at least one lowercase letter"
msgstr "Mật khẩu phải chứa ít nhất một chữ cái viết thường"

#: asiantech.link/utils/validators.py:80
msgid "Password must contain at least one number"
msgstr "Mật khẩu phải chứa ít nhất một chữ số"

#: asiantech.link/utils/validators.py:84
msgid "Password must contain at least one special character"
msgstr "Mật khẩu phải chứa ít nhất một ký tự đặc biệt"

#: asiantech.link/utils/validators.py:86
msgid "Password is valid"
msgstr "Mật khẩu hợp lệ"

#~ msgid "Expert"
#~ msgstr "Chuyên gia"

#~ msgid "Facebook authentication successful"
#~ msgstr "Xác thực Facebook thành công"

#~ msgid "Linkedin authentication successful"
#~ msgstr "Xác thực Linkedin thành công"

#~ msgid "Zalo authentication successful"
#~ msgstr "Xác thực Zalo thành công."

#~ msgid "Get user info with zalo id"
#~ msgstr "Lấy thông tin người dùng bằng zalo id"

#~ msgid "Get user info with whatsapp number"
#~ msgstr "Nhận thông tin người dùng bằng số whatsapp"

#~ msgid "WhatsApp authentication successful"
#~ msgstr "Xác thực WhatsApp thành công"

#~ msgid "Please sign the contract for"
#~ msgstr "Vui lòng ký hợp đồng cho"

#~ msgid "Failed to read PDF content"
#~ msgstr "Đọc nội dung PDF thất bại"
