# SOME DESCRIPTIVE TITLE.
# Copyright (C) YEAR THE PACKAGE'S COPYRIGHT HOLDER
# This file is distributed under the same license as the PACKAGE package.
# <AUTHOR> <EMAIL>, YEAR.
#
msgid ""
msgstr ""
"Project-Id-Version: PACKAGE VERSION\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-06-26 15:17+0700\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language-Team: LANGUAGE <<EMAIL>>\n"
"Language: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#: asiantech.link/api/models/user.py:17
msgid "Email should be provided"
msgstr "इमेल प्रदान गर्नुपर्छ"

#: asiantech.link/api/models/user.py:38
msgid "Superuser must have is_staff as True"
msgstr "Superuser मा is_staff को रूपमा True हुनुपर्छ"

#: asiantech.link/api/models/user.py:41
msgid "Superuser must have is_superuser as True"
msgstr "Superuser मा is_superuser को रूपमा True हुनुपर्छ"

#: asiantech.link/api/models/user.py:44
msgid "Superuser must have is_active as True"
msgstr "Superuser मा is_active हुनु पर्छ True को रूपमा"

#: asiantech.link/api/serializers/cv/cv_serializers.py:192
#: asiantech.link/api/serializers/engineers/user_serializers.py:52
#: asiantech.link/api/serializers/recruitment_serializers.py:176
#: asiantech.link/api/serializers/recruitment_serializers.py:681
msgid "Invalid degree code."
msgstr "अमान्य डिग्री कोड।"

#: asiantech.link/api/serializers/cv/cv_serializers.py:198
#, fuzzy
#| msgid "Invalid language code."
msgid "Invalid language level type."
msgstr "अमान्य भाषा कोड।"

#: asiantech.link/api/serializers/engineers/user_serializers.py:409
#, fuzzy
#| msgid "start_date must be less than end_date"
msgid "From date must be before to date."
msgstr "start_date end_date भन्दा कम हुनुपर्छ"

#: asiantech.link/api/serializers/engineers/user_serializers.py:563
#: asiantech.link/api/serializers/engineers/user_serializers.py:568
#: asiantech.link/api/serializers/engineers/user_serializers.py:573
msgid "Invalid place code."
msgstr "अमान्य स्थान कोड।"

#: asiantech.link/api/serializers/engineers/user_serializers.py:580
msgid "Invalid payroll price."
msgstr "अवैध पेरोल मूल्य।"

#: asiantech.link/api/serializers/engineers/user_serializers.py:941
msgid "Invalid sex type."
msgstr "अवैध यौन प्रकार।"

#: asiantech.link/api/serializers/engineers/user_serializers.py:949
#: asiantech.link/api/serializers/general_company/general_company_serializers.py:350
#: asiantech.link/api/serializers/general_company/general_company_serializers.py:447
#: asiantech.link/api/serializers/recruitment_serializers.py:207
#: asiantech.link/api/serializers/recruitment_serializers.py:741
msgid "Invalid country code."
msgstr "अवैध देश कोड।"

#: asiantech.link/api/serializers/engineers/user_serializers.py:962
#: asiantech.link/api/serializers/general_company/general_company_serializers.py:366
#: asiantech.link/api/serializers/general_company/general_company_serializers.py:466
msgid "Invalid phone number."
msgstr "अवैध फोन नम्बर।"

#: asiantech.link/api/serializers/engineers/user_serializers.py:966
#: asiantech.link/api/serializers/general_company/general_company_serializers.py:370
#: asiantech.link/api/serializers/general_company/general_company_serializers.py:470
msgid "Invalid phone number format."
msgstr "अवैध फोन नम्बर ढाँचा।"

#: asiantech.link/api/serializers/engineers/user_serializers.py:974
#: asiantech.link/api/serializers/general_company/general_company_serializers.py:356
#: asiantech.link/api/serializers/general_company/general_company_serializers.py:454
#: asiantech.link/api/serializers/recruitment_serializers.py:215
#: asiantech.link/api/serializers/recruitment_serializers.py:218
#: asiantech.link/api/serializers/recruitment_serializers.py:221
#: asiantech.link/api/serializers/recruitment_serializers.py:713
#: asiantech.link/api/serializers/recruitment_serializers.py:716
#: asiantech.link/api/serializers/recruitment_serializers.py:719
msgid "Invalid address code."
msgstr "अमान्य ठेगाना कोड।"

#: asiantech.link/api/serializers/general_company/general_company_serializers.py:290
#: asiantech.link/api/serializers/general_company/general_company_serializers.py:433
#: asiantech.link/api/serializers/general_company/general_company_serializers.py:439
#: asiantech.link/api/serializers/host_company/host_company_serializers.py:59
msgid "Invalid url format."
msgstr "अमान्य url ढाँचा।"

#: asiantech.link/api/serializers/general_company/general_company_serializers.py:296
#: asiantech.link/api/serializers/host_company/host_company_serializers.py:65
msgid "Invalid email format."
msgstr "अवैध इमेल ढाँचा।"

#: asiantech.link/api/serializers/general_company/general_company_serializers.py:481
#: asiantech.link/api/serializers/general_company/general_company_serializers.py:487
#: asiantech.link/api/serializers/recruitment_serializers.py:248
#: asiantech.link/api/serializers/recruitment_serializers.py:699
msgid "Invalid currency code."
msgstr "अवैध मुद्रा कोड।"

#: asiantech.link/api/serializers/general_company/general_company_serializers.py:640
msgid "compare_content_1"
msgstr ""
"यस आवेदकले जागिर आवश्यकतासँग राम्रोसँग मेल खाँदैन।\n"
"उहाँलाई भर्ना गर्ने सम्भावना कम छ, त्यसैले तपाईंले अन्य आवेदकहरूलाई प्राथमिकता दिन सक्नुहुन्छ, "
"तर यदि उहाँले तपाईंको रुचि राख्नुभएको छ भने, तपाईंले अन्तरवार्ता अनुरोध गर्न चाहनुहुन्छ।"

#: asiantech.link/api/serializers/general_company/general_company_serializers.py:642
msgid "compare_content_2"
msgstr ""
"यस आवेदकले धेरै जागिर आवश्यकताहरू पूरा गर्दछ।\n"
"ईन्जिनियरको व्यक्तिगत जानकारी जाँच गर्नुहोस्, र यदि कुनै विशेष समस्या छैन भने, अन्तरवार्ता "
"अनुरोध गर्नुहोस्।\n"
"उहाँलाई भर्ना गर्ने सम्भावना उच्च छ, त्यसैले प्रस्तावित सर्तहरू विचार गर्नुहोस् र च्याट मार्फत "
"सिधै सर्तहरू समायोजन गर्नुहोस्।"

#: asiantech.link/api/serializers/general_company/general_company_serializers.py:644
msgid "compare_content_3"
msgstr ""
"यस आवेदकले जागिर आवश्यकताहरू पूरा गर्दछ!\n"
"ईन्जिनियरको व्यक्तिगत जानकारी जाँच गर्नुहोस्, र यदि कुनै विशेष समस्या छैन भने, अन्तरवार्ता "
"अनुरोध गर्नुहोस्।"

#: asiantech.link/api/serializers/recruitment_serializers.py:169
#: asiantech.link/api/serializers/recruitment_serializers.py:672
msgid "Invalid sex type"
msgstr "अवैध यौन प्रकार"

#: asiantech.link/api/serializers/recruitment_serializers.py:183
#: asiantech.link/api/serializers/recruitment_serializers.py:704
msgid "start_date must be less than end_date"
msgstr "start_date end_date भन्दा कम हुनुपर्छ"

#: asiantech.link/api/serializers/recruitment_serializers.py:192
#: asiantech.link/api/serializers/recruitment_serializers.py:749
msgid "age_from must be less than age_to"
msgstr "age_from लाई उमेर भन्दा कम हुनुपर्छ"

#: asiantech.link/api/serializers/recruitment_serializers.py:200
#: asiantech.link/api/serializers/recruitment_serializers.py:693
msgid "payroll_price_from must be less than payroll_price_to"
msgstr "payroll_price_from payroll_price_to भन्दा कम हुनुपर्छ"

#: asiantech.link/api/serializers/recruitment_serializers.py:236
#: asiantech.link/api/serializers/recruitment_serializers.py:239
#: asiantech.link/api/serializers/recruitment_serializers.py:242
#: asiantech.link/api/serializers/recruitment_serializers.py:727
#: asiantech.link/api/serializers/recruitment_serializers.py:730
#: asiantech.link/api/serializers/recruitment_serializers.py:733
msgid "Invalid pref code."
msgstr "अवैध pref कोड।"

#: asiantech.link/api/serializers/recruitment_serializers.py:255
msgid "Invalid support company."
msgstr "अमान्य समर्थन कम्पनी।"

#: asiantech.link/api/serializers/recruitment_serializers.py:765
msgid "years_of_experience must be greater than 0"
msgstr "years_of_experience ० भन्दा बढी हुनुपर्छ"

#: asiantech.link/api/serializers/recruitment_serializers.py:779
#: asiantech.link/api/serializers/recruitment_serializers.py:782
msgid "Invalid language code."
msgstr "अमान्य भाषा कोड।"

#: asiantech.link/api/services/notify_service/company_notify_service.py:32
msgid "The interview result registration for is incomplete"
msgstr ""

#: asiantech.link/api/services/notify_service/company_notify_service.py:61
#: asiantech.link/api/services/notify_service/support_company_notify_service.py:61
#: asiantech.link/api/services/notify_service/support_company_notify_service.py:90
#: asiantech.link/api/services/notify_service/support_company_notify_service.py:125
msgid "Please register the interview date and time with"
msgstr ""

#: asiantech.link/api/services/notify_service/company_notify_service.py:89
msgid "Please register the interview outcome for"
msgstr ""

#: asiantech.link/api/services/notify_service/company_notify_service.py:118
msgid "Please sign the offer acceptance and employment contract with"
msgstr ""

#: asiantech.link/api/services/notify_service/engineer_notify_service.py:26
msgid "Please respond to the interview request from"
msgstr ""

#: asiantech.link/api/services/notify_service/engineer_notify_service.py:50
msgid "Please respond to the job offer from"
msgstr ""

#: asiantech.link/api/services/notify_service/support_company_notify_service.py:32
msgid "Please register the document screening result for"
msgstr ""

#: asiantech.link/api/views/admin/admin_views.py:52
msgid "List of engineers"
msgstr ""

#: asiantech.link/api/views/admin/admin_views.py:169
#: asiantech.link/api/views/admin/admin_views.py:227
#: asiantech.link/api/views/admin/admin_views.py:259
#: asiantech.link/api/views/admin/admin_views.py:270
#: asiantech.link/api/views/admin/admin_views.py:285
#: asiantech.link/api/views/admin/admin_views.py:578
#: asiantech.link/api/views/admin/admin_views.py:589
#: asiantech.link/api/views/admin/admin_views.py:601
#: asiantech.link/api/views/authentication/authentication_views.py:264
#: asiantech.link/api/views/engineer/engineer_views.py:43
#: asiantech.link/api/views/engineer/engineer_views.py:58
#: asiantech.link/api/views/engineer/engineer_views.py:66
#: asiantech.link/api/views/engineer/engineer_views.py:107
#: asiantech.link/api/views/engineer/engineer_views.py:115
#: asiantech.link/api/views/engineer/engineer_views.py:132
#: asiantech.link/api/views/engineer/engineer_views.py:140
#: asiantech.link/api/views/engineer/engineer_views.py:189
#: asiantech.link/api/views/engineer/engineer_views.py:208
#: asiantech.link/api/views/engineer/engineer_views.py:216
#: asiantech.link/api/views/engineer/engineer_views.py:383
#: asiantech.link/api/views/engineer/engineer_views.py:403
#: asiantech.link/api/views/general/general_company_views.py:49
#: asiantech.link/api/views/general/general_company_views.py:71
#: asiantech.link/api/views/general/general_company_views.py:78
#: asiantech.link/api/views/general/general_company_views.py:84
#: asiantech.link/api/views/general/general_company_views.py:98
#: asiantech.link/api/views/general/general_company_views.py:123
#: asiantech.link/api/views/general/general_company_views.py:198
#: asiantech.link/api/views/general/general_company_views.py:239
#: asiantech.link/api/views/general/general_company_views.py:246
#: asiantech.link/api/views/general/general_company_views.py:262
#: asiantech.link/api/views/general/general_company_views.py:280
#: asiantech.link/api/views/general/general_company_views.py:296
#: asiantech.link/api/views/general/general_company_views.py:313
#: asiantech.link/api/views/general/general_company_views.py:331
#: asiantech.link/api/views/general/general_company_views.py:339
#: asiantech.link/api/views/general/general_company_views.py:358
#: asiantech.link/api/views/general/general_company_views.py:378
#: asiantech.link/api/views/general/general_company_views.py:397
#: asiantech.link/api/views/general/general_company_views.py:416
#: asiantech.link/api/views/general/general_company_views.py:434
#: asiantech.link/api/views/general/general_company_views.py:455
#: asiantech.link/api/views/general/general_company_views.py:536
#: asiantech.link/api/views/general/general_company_views.py:550
#: asiantech.link/api/views/general/general_company_views.py:590
#: asiantech.link/api/views/general/general_company_views.py:600
#: asiantech.link/api/views/general/general_company_views.py:639
#: asiantech.link/api/views/general/general_company_views.py:650
#: asiantech.link/api/views/general/general_company_views.py:700
#: asiantech.link/api/views/general/general_company_views.py:711
#: asiantech.link/api/views/general/general_company_views.py:767
#: asiantech.link/api/views/general/general_company_views.py:804
#: asiantech.link/api/views/general/general_company_views.py:814
#: asiantech.link/api/views/general/general_company_views.py:957
#: asiantech.link/api/views/general/general_company_views.py:978
#: asiantech.link/api/views/general/general_views.py:28
#: asiantech.link/api/views/general/general_views.py:77
#: asiantech.link/api/views/host_company/host_company_views.py:38
#: asiantech.link/api/views/host_company/host_company_views.py:55
#: asiantech.link/api/views/host_company/host_company_views.py:62
#: asiantech.link/api/views/host_company/host_company_views.py:86
#: asiantech.link/api/views/host_company/host_company_views.py:92
#: asiantech.link/api/views/host_company/host_company_views.py:99
#: asiantech.link/api/views/host_company/host_company_views.py:130
#: asiantech.link/api/views/host_company/host_company_views.py:136
#: asiantech.link/api/views/host_company/host_company_views.py:150
#: asiantech.link/api/views/host_company/host_company_views.py:157
#: asiantech.link/api/views/host_company/host_company_views.py:211
#: asiantech.link/api/views/host_company/host_company_views.py:244
#: asiantech.link/api/views/host_company/host_company_views.py:252
#: asiantech.link/api/views/host_company/host_company_views.py:263
#: asiantech.link/api/views/host_company/host_company_views.py:270
#: asiantech.link/api/views/host_company/host_company_views.py:289
#: asiantech.link/api/views/host_company/host_company_views.py:297
#: asiantech.link/api/views/host_company/host_company_views.py:336
#: asiantech.link/api/views/host_support_agency_company/host_support_agency_company_views.py:35
#: asiantech.link/api/views/host_support_agency_company/host_support_agency_company_views.py:81
#: asiantech.link/api/views/host_support_agency_company/host_support_agency_company_views.py:159
#: asiantech.link/api/views/host_support_agency_company/host_support_agency_company_views.py:188
#: asiantech.link/api/views/host_support_agency_company/host_support_agency_company_views.py:196
#: asiantech.link/api/views/host_support_agency_company/host_support_agency_company_views.py:230
#: asiantech.link/api/views/host_support_agency_company/host_support_agency_company_views.py:238
#: asiantech.link/api/views/host_support_agency_company/host_support_agency_company_views.py:295
#: asiantech.link/api/views/notify/count_unread_notify_view.py:24
#: asiantech.link/api/views/notify/count_unread_notify_view.py:50
#: asiantech.link/api/views/notify/get_list_notify_view.py:24
#: asiantech.link/api/views/notify/get_list_notify_view.py:43
#: asiantech.link/api/views/notify/set_read_all_notify_view.py:23
#: asiantech.link/api/views/notify/set_read_all_notify_view.py:32
#: asiantech.link/api/views/notify/set_read_notify_view.py:24
#: asiantech.link/api/views/notify/set_read_notify_view.py:38
#: asiantech.link/api/views/profile/profile_views.py:38
#: asiantech.link/api/views/profile/profile_views.py:53
#: asiantech.link/api/views/profile/profile_views.py:192
#: asiantech.link/api/views/profile/profile_views.py:241
#: asiantech.link/api/views/profile/profile_views.py:285
#: asiantech.link/api/views/profile/profile_views.py:334
#: asiantech.link/api/views/profile/profile_views.py:378
#: asiantech.link/api/views/profile/profile_views.py:427
#: asiantech.link/api/views/profile/profile_views.py:471
#: asiantech.link/api/views/profile/profile_views.py:520
#: asiantech.link/api/views/profile/profile_views.py:564
#: asiantech.link/api/views/profile/profile_views.py:613
#: asiantech.link/api/views/recruit/recruitment_views.py:39
#: asiantech.link/api/views/recruit/recruitment_views.py:60
#: asiantech.link/api/views/recruit/recruitment_views.py:67
#: asiantech.link/api/views/recruit/recruitment_views.py:84
#: asiantech.link/api/views/recruit/recruitment_views.py:91
#: asiantech.link/api/views/recruit/recruitment_views.py:97
#: asiantech.link/api/views/recruit/recruitment_views.py:109
#: asiantech.link/api/views/recruit/recruitment_views.py:122
#: asiantech.link/api/views/recruit/recruitment_views.py:128
#: asiantech.link/api/views/recruit/recruitment_views.py:139
#: asiantech.link/api/views/recruit/recruitment_views.py:146
#: asiantech.link/api/views/recruit/recruitment_views.py:174
#: asiantech.link/api/views/recruit/recruitment_views.py:181
#: asiantech.link/api/views/recruit/recruitment_views.py:305
#: asiantech.link/api/views/recruit/recruitment_views.py:312
#: asiantech.link/api/views/recruit/recruitment_views.py:438
#: asiantech.link/api/views/recruit/recruitment_views.py:444
#: asiantech.link/api/views/recruit/recruitment_views.py:475
#: asiantech.link/api/views/recruit/recruitment_views.py:481
#: asiantech.link/api/views/recruit/recruitment_views.py:567
#: asiantech.link/api/views/recruit/recruitment_views.py:574
#: asiantech.link/api/views/recruit/recruitment_views.py:662
#: asiantech.link/api/views/recruit/recruitment_views.py:670
#: asiantech.link/api/views/recruit/recruitment_views.py:703
#: asiantech.link/api/views/recruit/recruitment_views.py:714
#: asiantech.link/api/views/recruit/recruitment_views.py:751
#: asiantech.link/api/views/recruit/recruitment_views.py:760
#: asiantech.link/api/views/recruit/recruitment_views.py:766
#: asiantech.link/api/views/recruit/recruitment_views.py:783
#: asiantech.link/api/views/recruit/recruitment_views.py:796
#: asiantech.link/api/views/recruit/recruitment_views.py:805
#: asiantech.link/api/views/recruit/recruitment_views.py:852
#: asiantech.link/api/views/recruit/recruitment_views.py:889
msgid "Success"
msgstr "सफलता"

#: asiantech.link/api/views/admin/admin_views.py:178
msgid "List of email schedules"
msgstr ""

#: asiantech.link/api/views/admin/admin_views.py:237
msgid "List of registrars"
msgstr ""

#: asiantech.link/api/views/admin/admin_views.py:296
#: asiantech.link/api/views/admin/admin_views.py:384
msgid "Email schedule created successfully"
msgstr "प्रयोगकर्ता सफलतापूर्वक सिर्जना गरियो"

#: asiantech.link/api/views/admin/admin_views.py:314
#: asiantech.link/api/views/admin/admin_views.py:417
msgid "Target email not found"
msgstr "डाटा फेला परेन"

#: asiantech.link/api/views/admin/admin_views.py:367
#: asiantech.link/api/views/admin/admin_views.py:470
msgid "Send test email successfully"
msgstr "प्रयोगकर्ता सफलतापूर्वक सिर्जना गरियो"

#: asiantech.link/api/views/admin/admin_views.py:376
#: asiantech.link/api/views/admin/admin_views.py:478
msgid "Email personal information already exists"
msgstr "इमेल पहिले नै अवस्थित छ"

#: asiantech.link/api/views/admin/admin_views.py:395
#: asiantech.link/api/views/admin/admin_views.py:486
msgid "Email schedule updated successfully"
msgstr "पासवर्ड सफलतापूर्वक अपडेट गरियो"

#: asiantech.link/api/views/admin/admin_views.py:407
#: asiantech.link/api/views/admin/admin_views.py:507
#: asiantech.link/api/views/admin/admin_views.py:572
msgid "Email schedule not found"
msgstr "प्रयोगकर्ता फेला परेन"

#: asiantech.link/api/views/admin/admin_views.py:497
#: asiantech.link/api/views/admin/admin_views.py:512
msgid "Email schedule deleted successfully"
msgstr "छवि सफलतापूर्वक मेटाइयो"

#: asiantech.link/api/views/admin/admin_views.py:524
#: asiantech.link/api/views/admin/admin_views.py:550
msgid "Email schedule list deleted successfully"
msgstr "छवि सफलतापूर्वक मेटाइयो"

#: asiantech.link/api/views/admin/admin_views.py:540
msgid "Invalid data"
msgstr "अवैध स्थिति"

#: asiantech.link/api/views/admin/admin_views.py:545
msgid "No email schedule ids provided"
msgstr "इमेल प्रदान गर्नुपर्छ"

#: asiantech.link/api/views/admin/admin_views.py:561
msgid "Email schedule details"
msgstr "इमेल प्रदान गर्नुपर्छ"

#: asiantech.link/api/views/admin/admin_views.py:603
#, fuzzy
#| msgid "User not found"
msgid "Engineer not found"
msgstr "प्रयोगकर्ता फेला परेन"

#: asiantech.link/api/views/authentication/authentication_views.py:43
msgid "Verification email sent"
msgstr "प्रमाणीकरण इमेल पठाइयो"

#: asiantech.link/api/views/authentication/authentication_views.py:46
msgid "Verification whatsapp sent"
msgstr "पुष्टि WhatsApp पठाइयो।"

#: asiantech.link/api/views/authentication/authentication_views.py:49
msgid "Email verified successfully"
msgstr "इमेल सफलतापूर्वक प्रमाणित भयो"

#: asiantech.link/api/views/authentication/authentication_views.py:52
msgid "User created successfully"
msgstr "प्रयोगकर्ता सफलतापूर्वक सिर्जना गरियो"

#: asiantech.link/api/views/authentication/authentication_views.py:55
#: asiantech.link/api/views/authentication/authentication_views.py:415
#: asiantech.link/api/views/authentication/authentication_views.py:540
#: asiantech.link/api/views/authentication/authentication_views.py:584
#: asiantech.link/api/views/authentication/authentication_views.py:801
#: asiantech.link/api/views/authentication/authentication_views.py:817
#: asiantech.link/api/views/authentication/authentication_views.py:1065
msgid "Login successfully"
msgstr "सफलतापूर्वक लगइन गर्नुहोस्"

#: asiantech.link/api/views/authentication/authentication_views.py:57
msgid "Reset password successfully"
msgstr "पासवर्ड सफलतापूर्वक रिसेट गर्नुहोस्"

#: asiantech.link/api/views/authentication/authentication_views.py:102
msgid "emailRegisterVerificationTitle"
msgstr "【AsianTech.Link】तपाईंको खाता प्रमाणित गर्नुहोस् - दर्ता पूरा गर्नुहोस्"

#: asiantech.link/api/views/authentication/authentication_views.py:109
msgid "emailConfirmContent1"
msgstr "AsianTech.Link मा साइन अप गर्नुभएकोमा धन्यवाद।"

#: asiantech.link/api/views/authentication/authentication_views.py:110
msgid "emailConfirmContent2"
msgstr ""
"सेवाको सुरक्षित प्रयोगका लागि, इमेल ठेगाना प्रमाणित गर्नु आवश्यक छ। तलको बटन प्रयोग गरेर "
"आफ्नो इमेल ठेगाना प्रमाणित गर्नुहोस्।"

#: asiantech.link/api/views/authentication/authentication_views.py:111
msgid "emailConfirmContent3"
msgstr ""
"तपाईंको सुरक्षा को लागी, हामीले तपाईंको इमेल ठेगाना प्रमाणित गर्न आवश्यक छ। कृपया तलको "
"बटन प्रयोग गरेर तपाईंको इमेल ठेगाना प्रमाणित गर्नुहोस्।"

#: asiantech.link/api/views/authentication/authentication_views.py:112
#: asiantech.link/api/views/authentication/authentication_views.py:315
msgid "emailConfirmContent4"
msgstr "© कपीराइट AsianTech.Link सबै अधिकार सुरक्षित।"

#: asiantech.link/api/views/authentication/authentication_views.py:114
msgid "Verification Email"
msgstr "सत्यापन इमेल पठाइयो"

#: asiantech.link/api/views/authentication/authentication_views.py:119
msgid "verifyYourAccount"
msgstr "तपाईंको इमेल ठेगाना प्रमाणित गर्नुहोस्"

#: asiantech.link/api/views/authentication/authentication_views.py:155
#: asiantech.link/api/views/authentication/authentication_views.py:157
msgid "Email is already linked to an another account"
msgstr ""

#: asiantech.link/api/views/authentication/authentication_views.py:166
#: asiantech.link/api/views/authentication/authentication_views.py:296
#: asiantech.link/api/views/authentication/authentication_views.py:986
#: asiantech.link/api/views/authentication/authentication_views.py:1049
#: asiantech.link/api/views/authentication/authentication_views.py:1101
#: asiantech.link/api/views/authentication/authentication_views.py:1176
msgid "User not found!"
msgstr "प्रयोगकर्ता फेला परेन!"

#: asiantech.link/api/views/authentication/authentication_views.py:169
#: asiantech.link/api/views/authentication/authentication_views.py:298
msgid "User is already verified!"
msgstr "प्रयोगकर्ता पहिले नै प्रमाणित छ"

#: asiantech.link/api/views/authentication/authentication_views.py:173
#: asiantech.link/api/views/authentication/authentication_views.py:552
msgid "CAPTCHA verification failed"
msgstr "क्याप्चा प्रमाणीकरण असफल भयो"

#: asiantech.link/api/views/authentication/authentication_views.py:266
msgid "Invalid code!"
msgstr "अवैध कोड!"

#: asiantech.link/api/views/authentication/authentication_views.py:282
msgid "Token is required!"
msgstr "टोकन आवश्यक छ!"

#: asiantech.link/api/views/authentication/authentication_views.py:306
msgid "emailRegisterSuccessTitle"
msgstr "【दर्ता सफल】AsianTech.Link खाता"

#: asiantech.link/api/views/authentication/authentication_views.py:313
msgid "emailRegisterSuccessContent1"
msgstr "तपाईंको AsianTech.Link खाता दर्ता सम्पन्न भएको छ"

#: asiantech.link/api/views/authentication/authentication_views.py:314
msgid "emailRegisterSuccessContent2"
msgstr ""
"Asian Tech.Link मार्फत तपाईंको आकर्षण प्रस्तुत गर्नुहोस्र आकर्षक जागिरका अवसरहरूसँग "
"जोडिनुहोस्।"

#: asiantech.link/api/views/authentication/authentication_views.py:331
#: asiantech.link/api/views/authentication/authentication_views.py:333
#: asiantech.link/api/views/authentication/authentication_views.py:1006
#: asiantech.link/api/views/authentication/authentication_views.py:1014
#: asiantech.link/api/views/authentication/authentication_views.py:1046
msgid "Verification token has expired!"
msgstr "प्रमाणीकरण टोकनको म्याद सकिएको छ!"

#: asiantech.link/api/views/authentication/authentication_views.py:335
msgid "Invalid verification token!"
msgstr "अवैध प्रमाणिकरण टोकन!"

#: asiantech.link/api/views/authentication/authentication_views.py:350
#: asiantech.link/api/views/authentication/authentication_views.py:428
msgid "Validation errors in your request"
msgstr "तपाईंको अनुरोधमा प्रमाणीकरण त्रुटिहरू"

#: asiantech.link/api/views/authentication/authentication_views.py:391
#: asiantech.link/api/views/authentication/authentication_views.py:501
msgid "The email address is already registered with engineer"
msgstr "ईमेल ठेगाना पहिले नै इन्जिनियरसँग दर्ता भएको छ।"

#: asiantech.link/api/views/authentication/authentication_views.py:393
#: asiantech.link/api/views/authentication/authentication_views.py:495
msgid "The email address is already registered with company"
msgstr "ईमेल ठेगाना पहिले नै कम्पनीसँग दर्ता भएको छ।"

#: asiantech.link/api/views/authentication/authentication_views.py:394
msgid "Email is already registered!"
msgstr "इमेल पहिले नै दर्ता गरिएको छ!"

#: asiantech.link/api/views/authentication/authentication_views.py:436
#: asiantech.link/api/views/authentication/authentication_views.py:440
#: asiantech.link/api/views/authentication/authentication_views.py:465
#: asiantech.link/api/views/authentication/authentication_views.py:469
msgid "Email address or password does not match"
msgstr "ईमेल ठेगाना वा पासवर्ड मेल खातिर छैन"

#: asiantech.link/api/views/authentication/authentication_views.py:439
#: asiantech.link/api/views/authentication/authentication_views.py:454
#: asiantech.link/api/views/authentication/authentication_views.py:468
msgid "Login failed."
msgstr "लग इन गर्न असमर्थ।"

#: asiantech.link/api/views/authentication/authentication_views.py:451
#: asiantech.link/api/views/authentication/authentication_views.py:455
msgid "The user ID or password is incorrect."
msgstr "ईमेल ठेगाना वा पासवर्ड मेल खातिर छैन"

#: asiantech.link/api/views/authentication/authentication_views.py:489
#: asiantech.link/api/views/authentication/authentication_views.py:572
msgid "CAPTCHA verification required"
msgstr "क्याप्चा प्रमाणीकरण आवश्यक छ"

#: asiantech.link/api/views/authentication/authentication_views.py:507
msgid ""
"The account with this email has been registered with the receiving support "
"organization."
msgstr ""

#: asiantech.link/api/views/authentication/authentication_views.py:698
#: asiantech.link/api/views/authentication/authentication_views.py:700
#: asiantech.link/api/views/authentication/authentication_views.py:738
#: asiantech.link/api/views/authentication/authentication_views.py:740
#: asiantech.link/api/views/authentication/authentication_views.py:809
#: asiantech.link/api/views/authentication/authentication_views.py:827
#, fuzzy
#| msgid "Login failed."
msgid "Login failed!"
msgstr "लग इन गर्न असमर्थ।"

#: asiantech.link/api/views/authentication/authentication_views.py:811
#, fuzzy
#| msgid "Invalid sex type"
msgid "Invalid SNS type!"
msgstr "अवैध यौन प्रकार"

#: asiantech.link/api/views/authentication/authentication_views.py:921
msgid "Your Verification Code"
msgstr "तपाईंको प्रमाणीकरण कोड"

#: asiantech.link/api/views/authentication/authentication_views.py:926
msgid "Email Verification"
msgstr ""
"दर्ता गर्नुभएकोमा धन्यवाद। कृपया आफ्नो दर्ता पूरा गर्न निम्न प्रमाणिकरण कोड प्रयोग "
"गर्नुहोस्:"

#: asiantech.link/api/views/authentication/authentication_views.py:929
msgid "Please use the verification code below to complete your login:"
msgstr "आफ्नो लगइन पूरा गर्नको लागि कृपया तलको प्रमाणिकरण कोड प्रयोग गर्नुहोस्:"

#: asiantech.link/api/views/authentication/authentication_views.py:932
msgid "Note: This code is valid for a limited time."
msgstr "नोट: यो कोड सीमित समयको लागि मान्य छ।"

#: asiantech.link/api/views/authentication/authentication_views.py:946
msgid "We sent you a code to verify your login"
msgstr "हामीले तपाईँको लगइन प्रमाणीकरण गर्न कोड पठाएका छौं"

#: asiantech.link/api/views/authentication/authentication_views.py:968
#: asiantech.link/api/views/authentication/authentication_views.py:978
#: asiantech.link/api/views/authentication/authentication_views.py:1034
msgid "Password Reset Request"
msgstr "पासवर्ड रिसेट अनुरोध"

#: asiantech.link/api/views/authentication/authentication_views.py:978
msgid "Dear"
msgstr "प्रिय"

#: asiantech.link/api/views/authentication/authentication_views.py:978
msgid "password_request_reset_content_1"
msgstr ""
"हामीले तपाईंको पासवर्ड रिसेट गर्ने अनुरोध प्राप्त गरेका छ। कृपया नयाँ पासवर्ड प्राप्त गर्नका "
"लागि पुष्टि बटनमा थिच्नुहोस्:"

#: asiantech.link/api/views/authentication/authentication_views.py:979
msgid "password_request_reset_content_2"
msgstr "तपाईंले पासवर्ड रिसेट गर्नको अनुरोध गरेको छैन भने, कृपया यो इमेललाई अनदेखा गर्नुहोस्।"

#: asiantech.link/api/views/authentication/authentication_views.py:979
#: asiantech.link/api/views/authentication/authentication_views.py:1035
msgid "passwordResetContent2"
msgstr ""
"कृपया यो नयाँ पासवर्ड बाट लगइन गर्नुहोस् र तत्कालै तपाईंको पासवर्ड परिवर्तन गर्नुहोस्।"

#: asiantech.link/api/views/authentication/authentication_views.py:979
msgid "Thank you"
msgstr "धन्यवाद"

#: asiantech.link/api/views/authentication/authentication_views.py:984
msgid "Password reset email sent!"
msgstr "पासवर्ड रिसेट इमेल पठाइयो!"

#: asiantech.link/api/views/authentication/authentication_views.py:1019
msgid "Invalid token type!"
msgstr "अवैध टोकन प्रकार!"

#: asiantech.link/api/views/authentication/authentication_views.py:1026
msgid "Your Password Reset Request"
msgstr "तपाईंको पासवर्ड रिसेट अनुरोध"

#: asiantech.link/api/views/authentication/authentication_views.py:1032
msgid "Password Reset"
msgstr "पासवर्ड रिसेट"

#: asiantech.link/api/views/authentication/authentication_views.py:1034
msgid "Hello"
msgstr "नमस्कार"

#: asiantech.link/api/views/authentication/authentication_views.py:1034
msgid "passwordResetContent1"
msgstr "तपाईंको पासवर्ड रिसेट गरिएको छ। यहाँ तपाईंको नयाँ पासवर्ड छ:"

#: asiantech.link/api/views/authentication/authentication_views.py:1035
msgid "passwordResetContent3"
msgstr ""
"यदि तपाईंले यो पासवर्ड रिसेट गर्नको अनुरोध नगरेका भएमा, कृपया हाम्रो समर्थन टोलमा "
"सम्पर्क गर्नुहोस्।"

#: asiantech.link/api/views/authentication/authentication_views.py:1044
msgid "Password reset successfully!"
msgstr "पासवर्ड सफलतापूर्वक रिसेट!"

#: asiantech.link/api/views/authentication/authentication_views.py:1099
msgid "Invalid code or email!"
msgstr "अवैध कोड वा इमेल!"

#: asiantech.link/api/views/authentication/authentication_views.py:1103
msgid "Invalid email or password!"
msgstr "अवैध इमेल वा पासवर्ड!"

#: asiantech.link/api/views/authentication/authentication_views.py:1111
#: asiantech.link/api/views/authentication/authentication_views.py:1125
msgid "Logout successfully"
msgstr "सफलतापूर्वक लगआउट गर्नुहोस्"

#: asiantech.link/api/views/authentication/authentication_views.py:1131
#: asiantech.link/api/views/authentication/authentication_views.py:1144
msgid "Captcha generated successfully"
msgstr "क्याप्चा सफलतापूर्वक उत्पन्न भयो"

#: asiantech.link/api/views/authentication/authentication_views.py:1151
msgid "Check captcha required in login"
msgstr "लगइनमा आवश्यक क्याप्चा जाँच गर्नुहोस्"

#: asiantech.link/api/views/authentication/authentication_views.py:1211
#: asiantech.link/api/views/authentication/authentication_views.py:1228
#: asiantech.link/api/views/authentication/authentication_views.py:1237
#: asiantech.link/api/views/authentication/authentication_views.py:1256
msgid "Account deleted successfully"
msgstr "खाता सफलतापूर्वक मेटाइयो।"

#: asiantech.link/api/views/authentication/authentication_views.py:1258
msgid "error"
msgstr "त्रुटि "

#: asiantech.link/api/views/chat/chat_views.py:22
#: asiantech.link/api/views/chat/chat_views.py:56
msgid "All messages marked as read"
msgstr ""

#: asiantech.link/api/views/chat/chat_views.py:38
#, fuzzy
#| msgid "Recruitment does not exist"
msgid "Group does not exist."
msgstr "भर्ती अवस्थित छैन"

#: asiantech.link/api/views/chat/chat_views.py:44
msgid "User is not a member of this group."
msgstr ""

#: asiantech.link/api/views/chat/chat_views.py:59
msgid "No messages to mark as read"
msgstr ""

#: asiantech.link/api/views/engineer/engineer_views.py:87
#: asiantech.link/api/views/engineer/engineer_views.py:160
msgid "Agency company not found."
msgstr "एजेन्सी कम्पनी फेला परेन।"

#: asiantech.link/api/views/engineer/engineer_views.py:94
msgid "This agency company is already linked with the engineer."
msgstr "यो एजेन्सी कम्पनी पहिले नै इन्जिनियरसँग जोडिएको छ।"

#: asiantech.link/api/views/engineer/engineer_views.py:169
msgid "This agency company has already been deleted."
msgstr "यो एजेन्सी कम्पनी पहिले नै मेटाइएको छ।"

#: asiantech.link/api/views/engineer/engineer_views.py:176
msgid "Agency company successfully deleted."
msgstr "एजेन्सी कम्पनी सफलतापूर्वक मेटाइयो।"

#: asiantech.link/api/views/engineer/engineer_views.py:179
msgid "This agency company is not linked with the engineer."
msgstr "यो एजेन्सी कम्पनी इन्जिनियरसँग जोडिएको छैन।"

#: asiantech.link/api/views/engineer/engineer_views.py:231
#: asiantech.link/api/views/general/general_company_views.py:477
msgid "Invalid ordering field."
msgstr "अवैध अर्डरिङ क्षेत्र।"

#: asiantech.link/api/views/engineer/engineer_views.py:239
#: asiantech.link/api/views/engineer/engineer_views.py:368
msgid "Invalid recruit_progress_code"
msgstr "अवैध recruit_progress_code"

#: asiantech.link/api/views/engineer/engineer_views.py:396
#: asiantech.link/api/views/general/general_company_views.py:352
#: asiantech.link/api/views/profile/profile_views.py:233
#: asiantech.link/api/views/profile/profile_views.py:326
#: asiantech.link/api/views/profile/profile_views.py:419
#: asiantech.link/api/views/profile/profile_views.py:512
#: asiantech.link/api/views/profile/profile_views.py:605
msgid "User not found"
msgstr "प्रयोगकर्ता फेला परेन"

#: asiantech.link/api/views/general/general_company_views.py:63
msgid "Email already exists"
msgstr "इमेल पहिले नै अवस्थित छ"

#: asiantech.link/api/views/general/general_company_views.py:93
#: asiantech.link/api/views/host_company/host_company_views.py:69
#: asiantech.link/api/views/host_company/host_company_views.py:106
msgid "User does not have company"
msgstr "प्रयोगकर्ताको कम्पनी छैन"

#: asiantech.link/api/views/general/general_company_views.py:100
#: asiantech.link/api/views/general/general_company_views.py:125
#: asiantech.link/api/views/host_company/host_company_views.py:75
#: asiantech.link/api/views/host_company/host_company_views.py:112
#: asiantech.link/api/views/host_support_agency_company/host_support_agency_company_views.py:45
#: asiantech.link/api/views/host_support_agency_company/host_support_agency_company_views.py:72
#: asiantech.link/api/views/media/company_media_views.py:59
#: asiantech.link/api/views/media/company_media_views.py:122
msgid "Company not found"
msgstr "कम्पनी फेला परेन"

#: asiantech.link/api/views/general/general_company_views.py:274
msgid "Filter saved successfully"
msgstr "फिल्टर सफलतापूर्वक बचत गरियो।"

#: asiantech.link/api/views/general/general_company_views.py:304
msgid "Filter deleted successfully"
msgstr "फिल्टर सफलतापूर्वक मेटाइयो।"

#: asiantech.link/api/views/general/general_company_views.py:327
msgid "You can't update this status!"
msgstr "तपाईंले यो स्थिति अद्यावधिक गर्न सक्नुहुन्न!"

#: asiantech.link/api/views/general/general_company_views.py:485
#: asiantech.link/api/views/general/general_company_views.py:670
#: asiantech.link/api/views/general/general_company_views.py:831
msgid "You don't have permission to access this company"
msgstr ""

#: asiantech.link/api/views/general/general_company_views.py:487
#: asiantech.link/api/views/general/general_company_views.py:668
#: asiantech.link/api/views/general/general_company_views.py:829
msgid "Host company not found"
msgstr "कम्पनी फेला परेन"

#: asiantech.link/api/views/general/general_company_views.py:573
#: asiantech.link/api/views/general/general_company_views.py:576
#: asiantech.link/api/views/general/general_company_views.py:578
#: asiantech.link/api/views/general/general_company_views.py:627
#: asiantech.link/api/views/general/general_company_views.py:630
#: asiantech.link/api/views/general/general_company_views.py:632
#: asiantech.link/api/views/general/general_company_views.py:792
#: asiantech.link/api/views/general/general_company_views.py:795
#: asiantech.link/api/views/general/general_company_views.py:797
#: asiantech.link/api/views/general/general_company_views.py:873
#: asiantech.link/api/views/general/general_company_views.py:876
#: asiantech.link/api/views/general/general_company_views.py:878
msgid "You don't have permission to access this apply"
msgstr ""

#: asiantech.link/api/views/general/general_company_views.py:592
#: asiantech.link/api/views/general/general_company_views.py:641
#: asiantech.link/api/views/general/general_company_views.py:702
#: asiantech.link/api/views/general/general_company_views.py:806
#: asiantech.link/api/views/general/general_company_views.py:843
#: asiantech.link/api/views/general/general_company_views.py:949
#: asiantech.link/api/views/host_company/host_company_views.py:340
msgid "Apply not found"
msgstr "आवेदन फेला परेन"

#: asiantech.link/api/views/general/general_company_views.py:946
msgid "Something went wrong!"
msgstr "केही गडबड भयो!"

#: asiantech.link/api/views/general/general_company_views.py:968
#: asiantech.link/api/views/host_support_agency_company/host_support_agency_company_views.py:205
#: asiantech.link/api/views/recruit/recruitment_views.py:49
#: asiantech.link/api/views/recruit/recruitment_views.py:155
msgid "You do not own the company"
msgstr "तपाईं कम्पनीको मालिक हुनुहुन्न"

#: asiantech.link/api/views/general/general_views.py:46
msgid "Current language is: "
msgstr "हालको भाषा हो:"

#: asiantech.link/api/views/host_company/host_company_views.py:88
msgid "User already subscribed"
msgstr "प्रयोगकर्ताले पहिले नै सदस्यता लिइसकेको छ"

#: asiantech.link/api/views/host_company/host_company_views.py:120
msgid "User not subscribed"
msgstr "प्रयोगकर्ताले सदस्यता लिएको छैन"

#: asiantech.link/api/views/host_company/host_company_views.py:128
msgid "Data not found"
msgstr "डाटा फेला परेन"

#: asiantech.link/api/views/host_company/host_company_views.py:143
msgid "User does not have company."
msgstr "प्रयोगकर्ताको कम्पनी छैन।"

#: asiantech.link/api/views/host_company/host_company_views.py:172
msgid "You have already make a request interview"
msgstr ""

#: asiantech.link/api/views/host_company/host_company_views.py:213
#: asiantech.link/api/views/host_support_agency_company/host_support_agency_company_views.py:161
msgid "You have requested an interview for this engineer."
msgstr ""

#: asiantech.link/api/views/host_company/host_company_views.py:331
msgid "Notification of acceptance has been sent."
msgstr "स्वीकृतिको सूचना पठाइएको छ।"

#: asiantech.link/api/views/host_company/host_company_views.py:338
msgid "Invalid status"
msgstr "अवैध स्थिति"

#: asiantech.link/api/views/host_support_agency_company/host_support_agency_company_views.py:102
#: asiantech.link/api/views/host_support_agency_company/host_support_agency_company_views.py:213
msgid "Host company is not belong to you"
msgstr ""

#: asiantech.link/api/views/host_support_agency_company/host_support_agency_company_views.py:109
msgid "This engineer already make a interview to host company"
msgstr "ईमेल ठेगाना पहिले नै कम्पनीसँग दर्ता भएको छ।"

#: asiantech.link/api/views/host_support_agency_company/host_support_agency_company_views.py:247
msgid "You are not authorized to access this endpoint"
msgstr ""

#: asiantech.link/api/views/media/admin_upload_avatar_engineer_view.py:53
#: asiantech.link/api/views/media/admin_upload_avatar_engineer_view.py:77
#: asiantech.link/api/views/media/avatar_views.py:44
#: asiantech.link/api/views/media/avatar_views.py:68
#: asiantech.link/api/views/media/company_media_views.py:48
#: asiantech.link/api/views/media/company_media_views.py:81
#: asiantech.link/api/views/media/company_media_views.py:110
#: asiantech.link/api/views/media/company_media_views.py:174
#: asiantech.link/api/views/media/contract_media_view.py:56
#: asiantech.link/api/views/media/contract_media_view.py:117
#: asiantech.link/api/views/media/contract_media_view.py:148
#: asiantech.link/api/views/media/contract_media_view.py:212
#: asiantech.link/api/views/media/passport_views.py:44
#: asiantech.link/api/views/media/passport_views.py:69
#: asiantech.link/api/views/media/recruit_media_views.py:44
#: asiantech.link/api/views/media/recruit_media_views.py:65
msgid "Image uploaded successfully"
msgstr "छवि सफलतापूर्वक अपलोड गरियो"

#: asiantech.link/api/views/media/admin_upload_avatar_engineer_view.py:92
#: asiantech.link/api/views/media/admin_upload_avatar_engineer_view.py:110
#: asiantech.link/api/views/media/avatar_views.py:74
#: asiantech.link/api/views/media/avatar_views.py:92
msgid "Image deleted successfully"
msgstr "छवि सफलतापूर्वक मेटाइयो"

#: asiantech.link/api/views/media/company_media_views.py:53
#: asiantech.link/api/views/media/company_media_views.py:116
#: asiantech.link/api/views/media/recruit_media_views.py:49
msgid "You are not a company"
msgstr "तपाईं एक कम्पनी होइन"

#: asiantech.link/api/views/media/company_media_views.py:139
msgid "Index must be 0,1,2"
msgstr "अनुक्रमणिका ०,१,२ हुनुपर्छ"

#: asiantech.link/api/views/media/contract_media_view.py:68
msgid "You can not upload contract image now, recruit progress code !=60"
msgstr "अब भिडियो अपलोड गर्न सक्कन, प्रगति कोड भर्ती गर्नुहोस्!=60"

#: asiantech.link/api/views/media/contract_media_view.py:167
msgid "You are not authorized to upload contract image"
msgstr ""

#: asiantech.link/api/views/media/contract_media_view.py:171
msgid "You can not upload contract image now, status progress code is invalid"
msgstr "तपाईंले अहिले सम्झौता छवि अपलोड गर्न सक्नुहुन्न, स्थिति प्रगति कोड अमान्य छ"

#: asiantech.link/api/views/media/passport_views.py:75
#: asiantech.link/api/views/media/passport_views.py:93
msgid "Passport image deleted successfully"
msgstr "पासपोर्ट छवि सफलतापूर्वक मेटाइयो।"

#: asiantech.link/api/views/notify/set_read_notify_view.py:34
msgid "Invalid request"
msgstr "अवैध स्थिति"

#: asiantech.link/api/views/profile/cv_views.py:52
#: asiantech.link/api/views/profile/cv_views.py:321
#: asiantech.link/api/views/profile/cv_views.py:389
#, fuzzy
#| msgid "Image uploaded successfully"
msgid "CV uploaded successfully"
msgstr "छवि सफलतापूर्वक अपलोड गरियो"

#: asiantech.link/api/views/profile/cv_views.py:58
msgid "File size must be less than 5MB"
msgstr "फाइलको आकार 5MB छैन भन्दा कम हुनुपर्छ"

#: asiantech.link/api/views/profile/cv_views.py:288
msgid "Invalid document type: not a resume"
msgstr "अवैध दस्तावेज प्रकार: रिज्युमे छैन"

#: asiantech.link/api/views/profile/cv_views.py:331
#, fuzzy
#| msgid "User not found"
msgid "CV not found"
msgstr "प्रयोगकर्ता फेला परेन"

#: asiantech.link/api/views/profile/profile_views.py:86
msgid "This email is already in use."
msgstr "इमेल पहिले नै दर्ता गरिएको छ!"

#: asiantech.link/api/views/profile/profile_views.py:146
msgid "Password updated successfully"
msgstr "पासवर्ड सफलतापूर्वक अपडेट गरियो"

#: asiantech.link/api/views/profile/profile_views.py:162
#: asiantech.link/api/views/profile/profile_views.py:184
msgid "New password must be different from the current password."
msgstr "नयाँ पासवर्ड हालको पासवर्ड भन्दा फरक हुनुपर्छ।"

#: asiantech.link/api/views/profile/profile_views.py:182
msgid "Password updated successfully."
msgstr "पासवर्ड सफलतापूर्वक अपडेट गरियो।"

#: asiantech.link/api/views/profile/profile_views.py:186
msgid "Invalid current password."
msgstr "अवैध हालको पासवर्ड।"

#: asiantech.link/api/views/recruit/recruitment_views.py:82
#: asiantech.link/api/views/recruit/recruitment_views.py:106
#: asiantech.link/api/views/recruit/recruitment_views.py:115
#: asiantech.link/api/views/recruit/recruitment_views.py:136
#: asiantech.link/api/views/recruit/recruitment_views.py:583
#: asiantech.link/api/views/recruit/recruitment_views.py:658
#: asiantech.link/api/views/recruit/recruitment_views.py:705
#: asiantech.link/api/views/recruit/recruitment_views.py:753
msgid "Recruitment does not exist"
msgstr "भर्ती अवस्थित छैन"

#: asiantech.link/api/views/recruit/recruitment_views.py:300
#: asiantech.link/api/views/recruit/recruitment_views.py:436
msgid "Error"
msgstr "त्रुटि "

#: asiantech.link/api/views/recruit/recruitment_views.py:460
msgid "Failed"
msgstr "असफल"

#: asiantech.link/api/views/recruit/recruitment_views.py:495
msgid "ComCompany not found"
msgstr "Comकम्पनी फेला परेन"

#: asiantech.link/api/views/recruit/recruitment_views.py:502
msgid "MapEngAgc not found"
msgstr "MapEngAgc फेला परेन"

#: asiantech.link/api/views/recruit/recruitment_views.py:516
msgid "Already applied"
msgstr "पहिले नै लागू गरियो"

#: asiantech.link/api/views/recruit/recruitment_views.py:533
msgid "You have already applied for this recruitment"
msgstr ""

#: asiantech.link/api/views/recruit/recruitment_views.py:656
msgid "Recruitment does not exist or you do not have access to it"
msgstr "भर्ती अवस्थित छैन वा तपाईंसँग यसमा पहुँच छैन"

#: asiantech.link/api/views/recruit/recruitment_views.py:679
#: asiantech.link/api/views/recruit/recruitment_views.py:724
msgid "Recruit ID is required"
msgstr "भर्ती आईडी आवश्यक छ"

#: asiantech.link/api/views/recruit/recruitment_views.py:687
#: asiantech.link/api/views/recruit/recruitment_views.py:776
#: asiantech.link/api/views/recruit/recruitment_views.py:824
msgid "No application found"
msgstr "कुनै आवेदन भेटिएन"

#: asiantech.link/api/views/recruit/recruitment_views.py:698
#: asiantech.link/api/views/recruit/recruitment_views.py:739
msgid "Invalid recruit progress code to cancel"
msgstr "रद्द गर्नको लागि अवैध भर्ती प्रगति कोड"

#: asiantech.link/api/views/recruit/recruitment_views.py:737
msgid "Interview datetime is required"
msgstr "अन्तर्वार्ता मिति आवश्यक छ"

#: asiantech.link/api/views/recruit/recruitment_views.py:798
msgid "Invalid recruit progress code to sign contract"
msgstr "सम्झौतामा हस्ताक्षर गर्नको लागि अवैध भर्ती प्रगति कोड"

#: asiantech.link/api/views/recruit/recruitment_views.py:831
msgid "No application found for the provided recruitment ID"
msgstr "प्रदान गरिएको भर्ती ID को लागी कुनै आवेदन फेला परेन"

#: asiantech.link/api/views/recruit/recruitment_views.py:837
msgid "No accept sign found for the application"
msgstr "आवेदनको लागि कुनै स्वीकार चिन्ह फेला परेन"

#: asiantech.link/core/settings/common.py:132
msgid "English"
msgstr "अंग्रेजी"

#: asiantech.link/core/settings/common.py:133
msgid "Japanese"
msgstr "जापानी"

#: asiantech.link/core/settings/common.py:134
msgid "Myanmar"
msgstr "म्यान्मार"

#: asiantech.link/core/settings/common.py:135
msgid "Indonesian"
msgstr "इन्डोनेशियन"

#: asiantech.link/core/settings/common.py:136
msgid "Nepali"
msgstr "नेपाली"

#: asiantech.link/core/settings/common.py:137
msgid "Vietnamese"
msgstr "भियतनामी"

#: asiantech.link/utils/permissions.py:22
#: asiantech.link/utils/permissions.py:36
msgid "Account has not been verified"
msgstr "खाता प्रमाणीकरण गरिएको छैन"

#: asiantech.link/utils/permissions.py:39
#: asiantech.link/utils/permissions.py:53
#: asiantech.link/utils/permissions.py:64
#: asiantech.link/utils/permissions.py:83
#: asiantech.link/utils/permissions.py:96
#: asiantech.link/utils/permissions.py:107
#: asiantech.link/utils/permissions.py:120
#: asiantech.link/utils/permissions.py:132
msgid "Access denied"
msgstr ""

#: asiantech.link/utils/utils.py:508 asiantech.link/utils/utils.py:644
msgid "Fresher"
msgstr "नयाँ कामदार"

#: asiantech.link/utils/utils.py:510 asiantech.link/utils/utils.py:646
msgid "Junior"
msgstr "जुनियर"

#: asiantech.link/utils/utils.py:512 asiantech.link/utils/utils.py:648
msgid "Middle"
msgstr "मध्यम"

#: asiantech.link/utils/utils.py:514 asiantech.link/utils/utils.py:650
msgid "Senior"
msgstr "वरिष्ठ"

#: asiantech.link/utils/validators.py:29
msgid "Email is too long"
msgstr "इमेल धेरै लामो छ"

#: asiantech.link/utils/validators.py:35
msgid "Email must contain a single '@' symbol"
msgstr "इमेलमा एकल '@' प्रतीक हुनु पर्छ"

#: asiantech.link/utils/validators.py:39
msgid "Local part of the email is too long (>= 64 characters)"
msgstr "इमेलको स्थानीय भाग धेरै लामो छ (>= 64 अक्षरहरू)"

#: asiantech.link/utils/validators.py:43
msgid "Email contains invalid characters"
msgstr "इमेलमा अमान्य वर्णहरू छन्"

#: asiantech.link/utils/validators.py:47
msgid "Email contains consecutive periods"
msgstr "इमेलले लगातार अवधिहरू समावेश गर्दछ"

#: asiantech.link/utils/validators.py:51
msgid ""
"Email has periods at invalid positions (start/end of local part or end of "
"email)"
msgstr "इमेलको अवैध स्थानहरूमा अवधि छ (स्थानीय भागको सुरु/अन्त्य वा इमेलको अन्त्यमा)"

#: asiantech.link/utils/validators.py:55
msgid "Domain part of the email should not be an IP address"
msgstr "इमेलको डोमेन भाग IP ठेगाना हुनु हुँदैन"

#: asiantech.link/utils/validators.py:57
msgid "Email is valid"
msgstr "इमेल मान्य छ"

#: asiantech.link/utils/validators.py:63
msgid "Password length must be between 8 and 20 characters"
msgstr "पासवर्डको लम्बाइ 8 देखि 20 अक्षरहरू बीचमा हुनुपर्छ"

#: asiantech.link/utils/validators.py:68
msgid "Password contains full-width characters"
msgstr "पासवर्डमा पूर्ण चौडाइ वर्णहरू छन्"

#: asiantech.link/utils/validators.py:72
msgid "Password must contain at least one uppercase letter"
msgstr "पासवर्डमा कम्तिमा एउटा ठूलो अक्षर हुनु पर्छ"

#: asiantech.link/utils/validators.py:76
msgid "Password must contain at least one lowercase letter"
msgstr "पासवर्डमा कम्तिमा एउटा सानो अक्षर हुनु पर्छ"

#: asiantech.link/utils/validators.py:80
msgid "Password must contain at least one number"
msgstr "पासवर्डमा कम्तिमा एउटा नम्बर हुनुपर्छ"

#: asiantech.link/utils/validators.py:84
msgid "Password must contain at least one special character"
msgstr "पासवर्डमा कम्तिमा एक विशेष पात्र हुनुपर्दछ।"

#: asiantech.link/utils/validators.py:86
msgid "Password is valid"
msgstr "पासवर्ड मान्य छ"

#~ msgid "Expert"
#~ msgstr "विशेषज्ञ"

#~ msgid "Facebook authentication successful"
#~ msgstr "Facebook प्रमाणिकरण सफल भयो।"

#~ msgid "Linkedin authentication successful"
#~ msgstr "LinkedIn प्रमाणिकरण सफल भयो।"

#~ msgid "Zalo authentication successful"
#~ msgstr "Zalo प्रमाणीकरण सफल भयो।"

#~ msgid "Get user info with zalo id"
#~ msgstr "Zalo ID सँग प्रयोगकर्ता जानकारी प्राप्त गर्नुहोस्।"

#~ msgid "Get user info with whatsapp number"
#~ msgstr "WhatsApp नम्बरसँग प्रयोगकर्ता जानकारी प्राप्त गर्नुहोस्।"

#~ msgid "WhatsApp authentication successful"
#~ msgstr "WhatsApp प्रमाणीकरण सफल भयो।"

#~ msgid "Failed to read PDF content"
#~ msgstr "PDF सामग्री पढ्न असफल भयो"

#~ msgid "Email is too long (>= 201 characters)"
#~ msgstr "इमेल धेरै लामो छ (>= 201 अक्षरहरू)"
