# SOME DESCRIPTIVE TITLE.
# Copyright (C) YEAR THE PACKAGE'S COPYRIGHT HOLDER
# This file is distributed under the same license as the PACKAGE package.
# <AUTHOR> <EMAIL>, YEAR.
#
msgid ""
msgstr ""
"Project-Id-Version: PACKAGE VERSION\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-06-26 15:16+0700\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language-Team: LANGUAGE <<EMAIL>>\n"
"Language: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#: asiantech.link/api/models/user.py:17
msgid "Email should be provided"
msgstr "Email harus disediakan"

#: asiantech.link/api/models/user.py:38
msgid "Superuser must have is_staff as True"
msgstr "Superuser harus memiliki is_staff bernilai True"

#: asiantech.link/api/models/user.py:41
msgid "Superuser must have is_superuser as True"
msgstr "Superuser harus memiliki is_superuser bernilai True"

#: asiantech.link/api/models/user.py:44
msgid "Superuser must have is_active as True"
msgstr "Superuser harus memiliki is_active bernilai True"

#: asiantech.link/api/serializers/cv/cv_serializers.py:192
#: asiantech.link/api/serializers/engineers/user_serializers.py:52
#: asiantech.link/api/serializers/recruitment_serializers.py:176
#: asiantech.link/api/serializers/recruitment_serializers.py:681
msgid "Invalid degree code."
msgstr "Kode gelar tidak valid."

#: asiantech.link/api/serializers/cv/cv_serializers.py:198
#, fuzzy
#| msgid "Invalid language code."
msgid "Invalid language level type."
msgstr "Kode bahasa tidak valid."

#: asiantech.link/api/serializers/engineers/user_serializers.py:409
#, fuzzy
#| msgid "start_date must be less than end_date"
msgid "From date must be before to date."
msgstr "tanggal_mulai harus lebih kecil dari tanggal_akhir"

#: asiantech.link/api/serializers/engineers/user_serializers.py:563
#: asiantech.link/api/serializers/engineers/user_serializers.py:568
#: asiantech.link/api/serializers/engineers/user_serializers.py:573
msgid "Invalid place code."
msgstr "Kode tempat tidak valid."

#: asiantech.link/api/serializers/engineers/user_serializers.py:580
msgid "Invalid payroll price."
msgstr "Harga gaji tidak valid."

#: asiantech.link/api/serializers/engineers/user_serializers.py:941
msgid "Invalid sex type."
msgstr "Jenis jenis kelamin tidak valid."

#: asiantech.link/api/serializers/engineers/user_serializers.py:949
#: asiantech.link/api/serializers/general_company/general_company_serializers.py:350
#: asiantech.link/api/serializers/general_company/general_company_serializers.py:447
#: asiantech.link/api/serializers/recruitment_serializers.py:207
#: asiantech.link/api/serializers/recruitment_serializers.py:741
msgid "Invalid country code."
msgstr "Kode negara tidak valid."

#: asiantech.link/api/serializers/engineers/user_serializers.py:962
#: asiantech.link/api/serializers/general_company/general_company_serializers.py:366
#: asiantech.link/api/serializers/general_company/general_company_serializers.py:466
msgid "Invalid phone number."
msgstr "Nomor telepon tidak valid."

#: asiantech.link/api/serializers/engineers/user_serializers.py:966
#: asiantech.link/api/serializers/general_company/general_company_serializers.py:370
#: asiantech.link/api/serializers/general_company/general_company_serializers.py:470
msgid "Invalid phone number format."
msgstr "Format nomor telepon tidak valid."

#: asiantech.link/api/serializers/engineers/user_serializers.py:974
#: asiantech.link/api/serializers/general_company/general_company_serializers.py:356
#: asiantech.link/api/serializers/general_company/general_company_serializers.py:454
#: asiantech.link/api/serializers/recruitment_serializers.py:215
#: asiantech.link/api/serializers/recruitment_serializers.py:218
#: asiantech.link/api/serializers/recruitment_serializers.py:221
#: asiantech.link/api/serializers/recruitment_serializers.py:713
#: asiantech.link/api/serializers/recruitment_serializers.py:716
#: asiantech.link/api/serializers/recruitment_serializers.py:719
msgid "Invalid address code."
msgstr "Kode alamat tidak valid."

#: asiantech.link/api/serializers/general_company/general_company_serializers.py:290
#: asiantech.link/api/serializers/general_company/general_company_serializers.py:433
#: asiantech.link/api/serializers/general_company/general_company_serializers.py:439
#: asiantech.link/api/serializers/host_company/host_company_serializers.py:59
msgid "Invalid url format."
msgstr "Format url tidak valid."

#: asiantech.link/api/serializers/general_company/general_company_serializers.py:296
#: asiantech.link/api/serializers/host_company/host_company_serializers.py:65
msgid "Invalid email format."
msgstr "Format email tidak valid."

#: asiantech.link/api/serializers/general_company/general_company_serializers.py:481
#: asiantech.link/api/serializers/general_company/general_company_serializers.py:487
#: asiantech.link/api/serializers/recruitment_serializers.py:248
#: asiantech.link/api/serializers/recruitment_serializers.py:699
msgid "Invalid currency code."
msgstr "Kode mata uang tidak valid."

#: asiantech.link/api/serializers/general_company/general_company_serializers.py:640
msgid "compare_content_1"
msgstr ""
"Pelamar ini tidak terlalu cocok dengan persyaratan pekerjaan.\n"
"Kemungkinan untuk merekrutnya rendah, jadi Anda mungkin ingin "
"memprioritaskan pelamar lain, tetapi jika dia menarik minat Anda, Anda "
"mungkin ingin meminta wawancara."

#: asiantech.link/api/serializers/general_company/general_company_serializers.py:642
msgid "compare_content_2"
msgstr ""
"Pelamar ini memenuhi banyak persyaratan pekerjaan.\n"
"Periksa informasi pribadi insinyur, dan jika tidak ada masalah khusus, minta "
"wawancara.\n"
"Kemungkinan merekrutnya tinggi, jadi pertimbangkan kondisi yang diusulkan "
"dan sesuaikan kondisi langsung melalui obrolan."

#: asiantech.link/api/serializers/general_company/general_company_serializers.py:644
msgid "compare_content_3"
msgstr ""
"Pelamar ini cocok dengan persyaratan pekerjaan!\n"
"Periksa informasi pribadi insinyur, dan jika tidak ada masalah khusus, minta "
"wawancara."

#: asiantech.link/api/serializers/recruitment_serializers.py:169
#: asiantech.link/api/serializers/recruitment_serializers.py:672
msgid "Invalid sex type"
msgstr "Tipe jenis kelamin tidak valid"

#: asiantech.link/api/serializers/recruitment_serializers.py:183
#: asiantech.link/api/serializers/recruitment_serializers.py:704
msgid "start_date must be less than end_date"
msgstr "tanggal_mulai harus lebih kecil dari tanggal_akhir"

#: asiantech.link/api/serializers/recruitment_serializers.py:192
#: asiantech.link/api/serializers/recruitment_serializers.py:749
msgid "age_from must be less than age_to"
msgstr "age_from harus lebih kecil dari age_to"

#: asiantech.link/api/serializers/recruitment_serializers.py:200
#: asiantech.link/api/serializers/recruitment_serializers.py:693
msgid "payroll_price_from must be less than payroll_price_to"
msgstr "payroll_price_from harus lebih kecil dari payroll_price_to"

#: asiantech.link/api/serializers/recruitment_serializers.py:236
#: asiantech.link/api/serializers/recruitment_serializers.py:239
#: asiantech.link/api/serializers/recruitment_serializers.py:242
#: asiantech.link/api/serializers/recruitment_serializers.py:727
#: asiantech.link/api/serializers/recruitment_serializers.py:730
#: asiantech.link/api/serializers/recruitment_serializers.py:733
msgid "Invalid pref code."
msgstr "Kode pref tidak valid."

#: asiantech.link/api/serializers/recruitment_serializers.py:255
msgid "Invalid support company."
msgstr "Perusahaan dukungan tidak valid."

#: asiantech.link/api/serializers/recruitment_serializers.py:765
msgid "years_of_experience must be greater than 0"
msgstr "years_of_experience harus lebih besar dari 0"

#: asiantech.link/api/serializers/recruitment_serializers.py:779
#: asiantech.link/api/serializers/recruitment_serializers.py:782
msgid "Invalid language code."
msgstr "Kode bahasa tidak valid."

#: asiantech.link/api/services/notify_service/company_notify_service.py:32
msgid "The interview result registration for is incomplete"
msgstr ""

#: asiantech.link/api/services/notify_service/company_notify_service.py:61
#: asiantech.link/api/services/notify_service/support_company_notify_service.py:61
#: asiantech.link/api/services/notify_service/support_company_notify_service.py:90
#: asiantech.link/api/services/notify_service/support_company_notify_service.py:125
msgid "Please register the interview date and time with"
msgstr ""

#: asiantech.link/api/services/notify_service/company_notify_service.py:89
msgid "Please register the interview outcome for"
msgstr ""

#: asiantech.link/api/services/notify_service/company_notify_service.py:118
msgid "Please sign the offer acceptance and employment contract with"
msgstr ""

#: asiantech.link/api/services/notify_service/engineer_notify_service.py:26
msgid "Please respond to the interview request from"
msgstr ""

#: asiantech.link/api/services/notify_service/engineer_notify_service.py:50
msgid "Please respond to the job offer from"
msgstr ""

#: asiantech.link/api/services/notify_service/support_company_notify_service.py:32
msgid "Please register the document screening result for"
msgstr ""

#: asiantech.link/api/views/admin/admin_views.py:52
msgid "List of engineers"
msgstr ""

#: asiantech.link/api/views/admin/admin_views.py:169
#: asiantech.link/api/views/admin/admin_views.py:227
#: asiantech.link/api/views/admin/admin_views.py:259
#: asiantech.link/api/views/admin/admin_views.py:270
#: asiantech.link/api/views/admin/admin_views.py:285
#: asiantech.link/api/views/admin/admin_views.py:578
#: asiantech.link/api/views/admin/admin_views.py:589
#: asiantech.link/api/views/admin/admin_views.py:601
#: asiantech.link/api/views/authentication/authentication_views.py:264
#: asiantech.link/api/views/engineer/engineer_views.py:43
#: asiantech.link/api/views/engineer/engineer_views.py:58
#: asiantech.link/api/views/engineer/engineer_views.py:66
#: asiantech.link/api/views/engineer/engineer_views.py:107
#: asiantech.link/api/views/engineer/engineer_views.py:115
#: asiantech.link/api/views/engineer/engineer_views.py:132
#: asiantech.link/api/views/engineer/engineer_views.py:140
#: asiantech.link/api/views/engineer/engineer_views.py:189
#: asiantech.link/api/views/engineer/engineer_views.py:208
#: asiantech.link/api/views/engineer/engineer_views.py:216
#: asiantech.link/api/views/engineer/engineer_views.py:383
#: asiantech.link/api/views/engineer/engineer_views.py:403
#: asiantech.link/api/views/general/general_company_views.py:49
#: asiantech.link/api/views/general/general_company_views.py:71
#: asiantech.link/api/views/general/general_company_views.py:78
#: asiantech.link/api/views/general/general_company_views.py:84
#: asiantech.link/api/views/general/general_company_views.py:98
#: asiantech.link/api/views/general/general_company_views.py:123
#: asiantech.link/api/views/general/general_company_views.py:198
#: asiantech.link/api/views/general/general_company_views.py:239
#: asiantech.link/api/views/general/general_company_views.py:246
#: asiantech.link/api/views/general/general_company_views.py:262
#: asiantech.link/api/views/general/general_company_views.py:280
#: asiantech.link/api/views/general/general_company_views.py:296
#: asiantech.link/api/views/general/general_company_views.py:313
#: asiantech.link/api/views/general/general_company_views.py:331
#: asiantech.link/api/views/general/general_company_views.py:339
#: asiantech.link/api/views/general/general_company_views.py:358
#: asiantech.link/api/views/general/general_company_views.py:378
#: asiantech.link/api/views/general/general_company_views.py:397
#: asiantech.link/api/views/general/general_company_views.py:416
#: asiantech.link/api/views/general/general_company_views.py:434
#: asiantech.link/api/views/general/general_company_views.py:455
#: asiantech.link/api/views/general/general_company_views.py:536
#: asiantech.link/api/views/general/general_company_views.py:550
#: asiantech.link/api/views/general/general_company_views.py:590
#: asiantech.link/api/views/general/general_company_views.py:600
#: asiantech.link/api/views/general/general_company_views.py:639
#: asiantech.link/api/views/general/general_company_views.py:650
#: asiantech.link/api/views/general/general_company_views.py:700
#: asiantech.link/api/views/general/general_company_views.py:711
#: asiantech.link/api/views/general/general_company_views.py:767
#: asiantech.link/api/views/general/general_company_views.py:804
#: asiantech.link/api/views/general/general_company_views.py:814
#: asiantech.link/api/views/general/general_company_views.py:957
#: asiantech.link/api/views/general/general_company_views.py:978
#: asiantech.link/api/views/general/general_views.py:28
#: asiantech.link/api/views/general/general_views.py:77
#: asiantech.link/api/views/host_company/host_company_views.py:38
#: asiantech.link/api/views/host_company/host_company_views.py:55
#: asiantech.link/api/views/host_company/host_company_views.py:62
#: asiantech.link/api/views/host_company/host_company_views.py:86
#: asiantech.link/api/views/host_company/host_company_views.py:92
#: asiantech.link/api/views/host_company/host_company_views.py:99
#: asiantech.link/api/views/host_company/host_company_views.py:130
#: asiantech.link/api/views/host_company/host_company_views.py:136
#: asiantech.link/api/views/host_company/host_company_views.py:150
#: asiantech.link/api/views/host_company/host_company_views.py:157
#: asiantech.link/api/views/host_company/host_company_views.py:211
#: asiantech.link/api/views/host_company/host_company_views.py:244
#: asiantech.link/api/views/host_company/host_company_views.py:252
#: asiantech.link/api/views/host_company/host_company_views.py:263
#: asiantech.link/api/views/host_company/host_company_views.py:270
#: asiantech.link/api/views/host_company/host_company_views.py:289
#: asiantech.link/api/views/host_company/host_company_views.py:297
#: asiantech.link/api/views/host_company/host_company_views.py:336
#: asiantech.link/api/views/host_support_agency_company/host_support_agency_company_views.py:35
#: asiantech.link/api/views/host_support_agency_company/host_support_agency_company_views.py:81
#: asiantech.link/api/views/host_support_agency_company/host_support_agency_company_views.py:159
#: asiantech.link/api/views/host_support_agency_company/host_support_agency_company_views.py:188
#: asiantech.link/api/views/host_support_agency_company/host_support_agency_company_views.py:196
#: asiantech.link/api/views/host_support_agency_company/host_support_agency_company_views.py:230
#: asiantech.link/api/views/host_support_agency_company/host_support_agency_company_views.py:238
#: asiantech.link/api/views/host_support_agency_company/host_support_agency_company_views.py:295
#: asiantech.link/api/views/notify/count_unread_notify_view.py:24
#: asiantech.link/api/views/notify/count_unread_notify_view.py:50
#: asiantech.link/api/views/notify/get_list_notify_view.py:24
#: asiantech.link/api/views/notify/get_list_notify_view.py:43
#: asiantech.link/api/views/notify/set_read_all_notify_view.py:23
#: asiantech.link/api/views/notify/set_read_all_notify_view.py:32
#: asiantech.link/api/views/notify/set_read_notify_view.py:24
#: asiantech.link/api/views/notify/set_read_notify_view.py:38
#: asiantech.link/api/views/profile/profile_views.py:38
#: asiantech.link/api/views/profile/profile_views.py:53
#: asiantech.link/api/views/profile/profile_views.py:192
#: asiantech.link/api/views/profile/profile_views.py:241
#: asiantech.link/api/views/profile/profile_views.py:285
#: asiantech.link/api/views/profile/profile_views.py:334
#: asiantech.link/api/views/profile/profile_views.py:378
#: asiantech.link/api/views/profile/profile_views.py:427
#: asiantech.link/api/views/profile/profile_views.py:471
#: asiantech.link/api/views/profile/profile_views.py:520
#: asiantech.link/api/views/profile/profile_views.py:564
#: asiantech.link/api/views/profile/profile_views.py:613
#: asiantech.link/api/views/recruit/recruitment_views.py:39
#: asiantech.link/api/views/recruit/recruitment_views.py:60
#: asiantech.link/api/views/recruit/recruitment_views.py:67
#: asiantech.link/api/views/recruit/recruitment_views.py:84
#: asiantech.link/api/views/recruit/recruitment_views.py:91
#: asiantech.link/api/views/recruit/recruitment_views.py:97
#: asiantech.link/api/views/recruit/recruitment_views.py:109
#: asiantech.link/api/views/recruit/recruitment_views.py:122
#: asiantech.link/api/views/recruit/recruitment_views.py:128
#: asiantech.link/api/views/recruit/recruitment_views.py:139
#: asiantech.link/api/views/recruit/recruitment_views.py:146
#: asiantech.link/api/views/recruit/recruitment_views.py:174
#: asiantech.link/api/views/recruit/recruitment_views.py:181
#: asiantech.link/api/views/recruit/recruitment_views.py:305
#: asiantech.link/api/views/recruit/recruitment_views.py:312
#: asiantech.link/api/views/recruit/recruitment_views.py:438
#: asiantech.link/api/views/recruit/recruitment_views.py:444
#: asiantech.link/api/views/recruit/recruitment_views.py:475
#: asiantech.link/api/views/recruit/recruitment_views.py:481
#: asiantech.link/api/views/recruit/recruitment_views.py:567
#: asiantech.link/api/views/recruit/recruitment_views.py:574
#: asiantech.link/api/views/recruit/recruitment_views.py:662
#: asiantech.link/api/views/recruit/recruitment_views.py:670
#: asiantech.link/api/views/recruit/recruitment_views.py:703
#: asiantech.link/api/views/recruit/recruitment_views.py:714
#: asiantech.link/api/views/recruit/recruitment_views.py:751
#: asiantech.link/api/views/recruit/recruitment_views.py:760
#: asiantech.link/api/views/recruit/recruitment_views.py:766
#: asiantech.link/api/views/recruit/recruitment_views.py:783
#: asiantech.link/api/views/recruit/recruitment_views.py:796
#: asiantech.link/api/views/recruit/recruitment_views.py:805
#: asiantech.link/api/views/recruit/recruitment_views.py:852
#: asiantech.link/api/views/recruit/recruitment_views.py:889
msgid "Success"
msgstr "Sukses"

#: asiantech.link/api/views/admin/admin_views.py:178
msgid "List of email schedules"
msgstr ""

#: asiantech.link/api/views/admin/admin_views.py:237
msgid "List of registrars"
msgstr ""

#: asiantech.link/api/views/admin/admin_views.py:296
#: asiantech.link/api/views/admin/admin_views.py:384
msgid "Email schedule created successfully"
msgstr "Jadwal email berhasil dibuat"

#: asiantech.link/api/views/admin/admin_views.py:314
#: asiantech.link/api/views/admin/admin_views.py:417
msgid "Target email not found"
msgstr "Email tujuan tidak ditemukan"

#: asiantech.link/api/views/admin/admin_views.py:367
#: asiantech.link/api/views/admin/admin_views.py:470
msgid "Send test email successfully"
msgstr "Email pengujian berhasil dikirim"

#: asiantech.link/api/views/admin/admin_views.py:376
#: asiantech.link/api/views/admin/admin_views.py:478
msgid "Email personal information already exists"
msgstr "Email sudah ada"

#: asiantech.link/api/views/admin/admin_views.py:395
#: asiantech.link/api/views/admin/admin_views.py:486
msgid "Email schedule updated successfully"
msgstr "Jadwal email berhasil diperbarui"

#: asiantech.link/api/views/admin/admin_views.py:407
#: asiantech.link/api/views/admin/admin_views.py:507
#: asiantech.link/api/views/admin/admin_views.py:572
msgid "Email schedule not found"
msgstr "Jadwal email tidak ditemukan"

#: asiantech.link/api/views/admin/admin_views.py:497
#: asiantech.link/api/views/admin/admin_views.py:512
msgid "Email schedule deleted successfully"
msgstr "Jadwal email berhasil dihapus"

#: asiantech.link/api/views/admin/admin_views.py:524
#: asiantech.link/api/views/admin/admin_views.py:550
msgid "Email schedule list deleted successfully"
msgstr "Jadwal email berhasil dihapus"

#: asiantech.link/api/views/admin/admin_views.py:540
msgid "Invalid data"
msgstr "Status tidak valid"

#: asiantech.link/api/views/admin/admin_views.py:545
msgid "No email schedule ids provided"
msgstr "Email harus disediakan"

#: asiantech.link/api/views/admin/admin_views.py:561
msgid "Email schedule details"
msgstr "Email harus disediakan"

#: asiantech.link/api/views/admin/admin_views.py:603
#, fuzzy
#| msgid "User not found"
msgid "Engineer not found"
msgstr "Pengguna tidak ditemukan."

#: asiantech.link/api/views/authentication/authentication_views.py:43
msgid "Verification email sent"
msgstr "Email verifikasi telah dikirim"

#: asiantech.link/api/views/authentication/authentication_views.py:46
msgid "Verification whatsapp sent"
msgstr "Verifikasi WhatsApp telah dikirim."

#: asiantech.link/api/views/authentication/authentication_views.py:49
msgid "Email verified successfully"
msgstr "Email berhasil diverifikasi"

#: asiantech.link/api/views/authentication/authentication_views.py:52
msgid "User created successfully"
msgstr "Pengguna berhasil dibuat"

#: asiantech.link/api/views/authentication/authentication_views.py:55
#: asiantech.link/api/views/authentication/authentication_views.py:415
#: asiantech.link/api/views/authentication/authentication_views.py:540
#: asiantech.link/api/views/authentication/authentication_views.py:584
#: asiantech.link/api/views/authentication/authentication_views.py:801
#: asiantech.link/api/views/authentication/authentication_views.py:817
#: asiantech.link/api/views/authentication/authentication_views.py:1065
msgid "Login successfully"
msgstr "Login berhasil"

#: asiantech.link/api/views/authentication/authentication_views.py:57
msgid "Reset password successfully"
msgstr "Pengguna berhasil dibuat"

#: asiantech.link/api/views/authentication/authentication_views.py:102
msgid "emailRegisterVerificationTitle"
msgstr "【AsianTech.Link】Verifikasi Akun Anda - Selesaikan Pendaftaran"

#: asiantech.link/api/views/authentication/authentication_views.py:109
msgid "emailConfirmContent1"
msgstr "Terima kasih telah mendaftar di AsianTech.Link"

#: asiantech.link/api/views/authentication/authentication_views.py:110
msgid "emailConfirmContent2"
msgstr ""
"Untuk menggunakan layanan dengan aman, verifikasi alamat email diperlukan. "
"Silakan verifikasi alamat email Anda menggunakan tombol di bawah ini."

#: asiantech.link/api/views/authentication/authentication_views.py:111
msgid "emailConfirmContent3"
msgstr ""
"Untuk keamanan Anda, kami memerlukan Anda untuk memverifikasi alamat email "
"Anda. Harap verifikasi alamat email Anda menggunakan tombol di bawah ini."

#: asiantech.link/api/views/authentication/authentication_views.py:112
#: asiantech.link/api/views/authentication/authentication_views.py:315
msgid "emailConfirmContent4"
msgstr "© Hak Cipta AsianTech.Link Semua Hak Dilindungi."

#: asiantech.link/api/views/authentication/authentication_views.py:114
msgid "Verification Email"
msgstr "Email verifikasi telah dikirim"

#: asiantech.link/api/views/authentication/authentication_views.py:119
msgid "verifyYourAccount"
msgstr "Verifikasi alamat email Anda"

#: asiantech.link/api/views/authentication/authentication_views.py:155
#: asiantech.link/api/views/authentication/authentication_views.py:157
msgid "Email is already linked to an another account"
msgstr ""

#: asiantech.link/api/views/authentication/authentication_views.py:166
#: asiantech.link/api/views/authentication/authentication_views.py:296
#: asiantech.link/api/views/authentication/authentication_views.py:986
#: asiantech.link/api/views/authentication/authentication_views.py:1049
#: asiantech.link/api/views/authentication/authentication_views.py:1101
#: asiantech.link/api/views/authentication/authentication_views.py:1176
msgid "User not found!"
msgstr "Pengguna tidak ditemukan!"

#: asiantech.link/api/views/authentication/authentication_views.py:169
#: asiantech.link/api/views/authentication/authentication_views.py:298
msgid "User is already verified!"
msgstr "Pengguna sudah diverifikasi!"

#: asiantech.link/api/views/authentication/authentication_views.py:173
#: asiantech.link/api/views/authentication/authentication_views.py:552
msgid "CAPTCHA verification failed"
msgstr "Verifikasi CAPTCHA gagal"

#: asiantech.link/api/views/authentication/authentication_views.py:266
msgid "Invalid code!"
msgstr "Kode tidak valid!"

#: asiantech.link/api/views/authentication/authentication_views.py:282
msgid "Token is required!"
msgstr "Token diperlukan!"

#: asiantech.link/api/views/authentication/authentication_views.py:306
msgid "emailRegisterSuccessTitle"
msgstr "【Pendaftaran Berhasil】Akun AsianTech.Link"

#: asiantech.link/api/views/authentication/authentication_views.py:313
msgid "emailRegisterSuccessContent1"
msgstr "Pendaftaran akun Anda di AsianTech.Link telah selesai."

#: asiantech.link/api/views/authentication/authentication_views.py:314
msgid "emailRegisterSuccessContent2"
msgstr ""
"Tunjukkan daya tarik Anda melalui Asian Tech.Linkdan terhubung dengan "
"peluang kerja yang menarik."

#: asiantech.link/api/views/authentication/authentication_views.py:331
#: asiantech.link/api/views/authentication/authentication_views.py:333
#: asiantech.link/api/views/authentication/authentication_views.py:1006
#: asiantech.link/api/views/authentication/authentication_views.py:1014
#: asiantech.link/api/views/authentication/authentication_views.py:1046
msgid "Verification token has expired!"
msgstr "Email verifikasi telah dikirim"

#: asiantech.link/api/views/authentication/authentication_views.py:335
msgid "Invalid verification token!"
msgstr "Kode Verifikasi"

#: asiantech.link/api/views/authentication/authentication_views.py:350
#: asiantech.link/api/views/authentication/authentication_views.py:428
msgid "Validation errors in your request"
msgstr "Ada kesalahan validasi dalam permintaan Anda"

#: asiantech.link/api/views/authentication/authentication_views.py:391
#: asiantech.link/api/views/authentication/authentication_views.py:501
msgid "The email address is already registered with engineer"
msgstr "Alamat email sudah terdaftar dengan insinyur."

#: asiantech.link/api/views/authentication/authentication_views.py:393
#: asiantech.link/api/views/authentication/authentication_views.py:495
msgid "The email address is already registered with company"
msgstr "Alamat email sudah terdaftar dengan perusahaan."

#: asiantech.link/api/views/authentication/authentication_views.py:394
msgid "Email is already registered!"
msgstr "Email sudah terdaftar!"

#: asiantech.link/api/views/authentication/authentication_views.py:436
#: asiantech.link/api/views/authentication/authentication_views.py:440
#: asiantech.link/api/views/authentication/authentication_views.py:465
#: asiantech.link/api/views/authentication/authentication_views.py:469
msgid "Email address or password does not match"
msgstr "Email atau password tidak cocok"

#: asiantech.link/api/views/authentication/authentication_views.py:439
#: asiantech.link/api/views/authentication/authentication_views.py:454
#: asiantech.link/api/views/authentication/authentication_views.py:468
msgid "Login failed."
msgstr "Tidak dapat masuk"

#: asiantech.link/api/views/authentication/authentication_views.py:451
#: asiantech.link/api/views/authentication/authentication_views.py:455
msgid "The user ID or password is incorrect."
msgstr "ID pengguna atau kata sandi tidak valid"

#: asiantech.link/api/views/authentication/authentication_views.py:489
#: asiantech.link/api/views/authentication/authentication_views.py:572
msgid "CAPTCHA verification required"
msgstr "Email verifikasi telah dikirim"

#: asiantech.link/api/views/authentication/authentication_views.py:507
msgid ""
"The account with this email has been registered with the receiving support "
"organization."
msgstr ""

#: asiantech.link/api/views/authentication/authentication_views.py:698
#: asiantech.link/api/views/authentication/authentication_views.py:700
#: asiantech.link/api/views/authentication/authentication_views.py:738
#: asiantech.link/api/views/authentication/authentication_views.py:740
#: asiantech.link/api/views/authentication/authentication_views.py:809
#: asiantech.link/api/views/authentication/authentication_views.py:827
#, fuzzy
#| msgid "Login failed."
msgid "Login failed!"
msgstr "Tidak dapat masuk"

#: asiantech.link/api/views/authentication/authentication_views.py:811
#, fuzzy
#| msgid "Invalid sex type"
msgid "Invalid SNS type!"
msgstr "Tipe jenis kelamin tidak valid"

#: asiantech.link/api/views/authentication/authentication_views.py:921
msgid "Your Verification Code"
msgstr "Kode Verifikasi Anda"

#: asiantech.link/api/views/authentication/authentication_views.py:926
msgid "Email Verification"
msgstr ""
"Terima kasih telah mendaftar. Silakan gunakan kode verifikasi berikut untuk "
"menyelesaikan pendaftaran Anda:"

#: asiantech.link/api/views/authentication/authentication_views.py:929
msgid "Please use the verification code below to complete your login:"
msgstr ""
"Silakan gunakan kode verifikasi di bawah ini untuk menyelesaikan login Anda:"

#: asiantech.link/api/views/authentication/authentication_views.py:932
msgid "Note: This code is valid for a limited time."
msgstr "Catatan: Kode ini hanya berlaku untuk waktu terbatas."

#: asiantech.link/api/views/authentication/authentication_views.py:946
msgid "We sent you a code to verify your login"
msgstr "Kami telah mengirimkan kode untuk memverifikasi login Anda."

#: asiantech.link/api/views/authentication/authentication_views.py:968
#: asiantech.link/api/views/authentication/authentication_views.py:978
#: asiantech.link/api/views/authentication/authentication_views.py:1034
msgid "Password Reset Request"
msgstr "Permintaan Reset Kata Sandi"

#: asiantech.link/api/views/authentication/authentication_views.py:978
msgid "Dear"
msgstr "Halo"

#: asiantech.link/api/views/authentication/authentication_views.py:978
msgid "password_request_reset_content_1"
msgstr ""
"Kami menerima permintaan untuk mereset kata sandi Anda. Silakan tekan tombol "
"konfirmasi untuk menerima kata sandi baru:"

#: asiantech.link/api/views/authentication/authentication_views.py:979
msgid "password_request_reset_content_2"
msgstr "Jika Anda tidak meminta reset kata sandi, silakan abaikan email ini."

#: asiantech.link/api/views/authentication/authentication_views.py:979
#: asiantech.link/api/views/authentication/authentication_views.py:1035
msgid "passwordResetContent2"
msgstr ""
"Silakan login dengan kata sandi baru ini dan segera ubah kata sandi Anda."

#: asiantech.link/api/views/authentication/authentication_views.py:979
msgid "Thank you"
msgstr "Terima kasih"

#: asiantech.link/api/views/authentication/authentication_views.py:984
msgid "Password reset email sent!"
msgstr "Permintaan Reset Kata Sandi"

#: asiantech.link/api/views/authentication/authentication_views.py:1019
msgid "Invalid token type!"
msgstr "Kode Verifikasi"

#: asiantech.link/api/views/authentication/authentication_views.py:1026
msgid "Your Password Reset Request"
msgstr "Permintaan Reset Kata Sandi"

#: asiantech.link/api/views/authentication/authentication_views.py:1032
msgid "Password Reset"
msgstr "Reset Kata Sandi"

#: asiantech.link/api/views/authentication/authentication_views.py:1034
msgid "Hello"
msgstr "Halo"

#: asiantech.link/api/views/authentication/authentication_views.py:1034
msgid "passwordResetContent1"
msgstr "Kata sandi Anda telah direset. Berikut adalah kata sandi baru Anda:"

#: asiantech.link/api/views/authentication/authentication_views.py:1035
msgid "passwordResetContent3"
msgstr ""
"Jika Anda tidak meminta reset kata sandi ini, silakan hubungi tim dukungan "
"kami."

#: asiantech.link/api/views/authentication/authentication_views.py:1044
msgid "Password reset successfully!"
msgstr "Pengguna berhasil dibuat"

#: asiantech.link/api/views/authentication/authentication_views.py:1099
msgid "Invalid code or email!"
msgstr "Kode atau email tidak valid!"

#: asiantech.link/api/views/authentication/authentication_views.py:1103
msgid "Invalid email or password!"
msgstr "Email atau password tidak valid!"

#: asiantech.link/api/views/authentication/authentication_views.py:1111
#: asiantech.link/api/views/authentication/authentication_views.py:1125
msgid "Logout successfully"
msgstr "Login berhasil"

#: asiantech.link/api/views/authentication/authentication_views.py:1131
#: asiantech.link/api/views/authentication/authentication_views.py:1144
msgid "Captcha generated successfully"
msgstr "Pengguna berhasil dibuat"

#: asiantech.link/api/views/authentication/authentication_views.py:1151
msgid "Check captcha required in login"
msgstr "Periksa captcha yang diperlukan untuk login"

#: asiantech.link/api/views/authentication/authentication_views.py:1211
#: asiantech.link/api/views/authentication/authentication_views.py:1228
#: asiantech.link/api/views/authentication/authentication_views.py:1237
#: asiantech.link/api/views/authentication/authentication_views.py:1256
msgid "Account deleted successfully"
msgstr "Akun berhasil dihapus."

#: asiantech.link/api/views/authentication/authentication_views.py:1258
msgid "error"
msgstr "Kesalahan"

#: asiantech.link/api/views/chat/chat_views.py:22
#: asiantech.link/api/views/chat/chat_views.py:56
msgid "All messages marked as read"
msgstr ""

#: asiantech.link/api/views/chat/chat_views.py:38
#, fuzzy
#| msgid "Recruitment does not exist"
msgid "Group does not exist."
msgstr "Rekrutmen tidak ada"

#: asiantech.link/api/views/chat/chat_views.py:44
msgid "User is not a member of this group."
msgstr ""

#: asiantech.link/api/views/chat/chat_views.py:59
msgid "No messages to mark as read"
msgstr ""

#: asiantech.link/api/views/engineer/engineer_views.py:87
#: asiantech.link/api/views/engineer/engineer_views.py:160
msgid "Agency company not found."
msgstr "Perusahaan agensi tidak ditemukan."

#: asiantech.link/api/views/engineer/engineer_views.py:94
msgid "This agency company is already linked with the engineer."
msgstr "Perusahaan agensi ini sudah terhubung dengan insinyur tersebut."

#: asiantech.link/api/views/engineer/engineer_views.py:169
msgid "This agency company has already been deleted."
msgstr "Perusahaan agensi ini telah dihapus."

#: asiantech.link/api/views/engineer/engineer_views.py:176
msgid "Agency company successfully deleted."
msgstr "Perusahaan agensi berhasil dihapus."

#: asiantech.link/api/views/engineer/engineer_views.py:179
msgid "This agency company is not linked with the engineer."
msgstr "Perusahaan agensi ini tidak ada hubungannya dengan insinyur."

#: asiantech.link/api/views/engineer/engineer_views.py:231
#: asiantech.link/api/views/general/general_company_views.py:477
msgid "Invalid ordering field."
msgstr "Bidang pemesanan tidak valid."

#: asiantech.link/api/views/engineer/engineer_views.py:239
#: asiantech.link/api/views/engineer/engineer_views.py:368
msgid "Invalid recruit_progress_code"
msgstr "Kode kemajuan perekrutan tidak valid"

#: asiantech.link/api/views/engineer/engineer_views.py:396
#: asiantech.link/api/views/general/general_company_views.py:352
#: asiantech.link/api/views/profile/profile_views.py:233
#: asiantech.link/api/views/profile/profile_views.py:326
#: asiantech.link/api/views/profile/profile_views.py:419
#: asiantech.link/api/views/profile/profile_views.py:512
#: asiantech.link/api/views/profile/profile_views.py:605
msgid "User not found"
msgstr "Pengguna tidak ditemukan."

#: asiantech.link/api/views/general/general_company_views.py:63
msgid "Email already exists"
msgstr "Email sudah ada"

#: asiantech.link/api/views/general/general_company_views.py:93
#: asiantech.link/api/views/host_company/host_company_views.py:69
#: asiantech.link/api/views/host_company/host_company_views.py:106
msgid "User does not have company"
msgstr "Pengguna tidak memiliki perusahaan"

#: asiantech.link/api/views/general/general_company_views.py:100
#: asiantech.link/api/views/general/general_company_views.py:125
#: asiantech.link/api/views/host_company/host_company_views.py:75
#: asiantech.link/api/views/host_company/host_company_views.py:112
#: asiantech.link/api/views/host_support_agency_company/host_support_agency_company_views.py:45
#: asiantech.link/api/views/host_support_agency_company/host_support_agency_company_views.py:72
#: asiantech.link/api/views/media/company_media_views.py:59
#: asiantech.link/api/views/media/company_media_views.py:122
msgid "Company not found"
msgstr "Perusahaan tidak ditemukan"

#: asiantech.link/api/views/general/general_company_views.py:274
msgid "Filter saved successfully"
msgstr "Filter berhasil disimpan."

#: asiantech.link/api/views/general/general_company_views.py:304
msgid "Filter deleted successfully"
msgstr "Filter berhasil dihapus."

#: asiantech.link/api/views/general/general_company_views.py:327
msgid "You can't update this status!"
msgstr "Anda tidak dapat memperbarui status ini!"

#: asiantech.link/api/views/general/general_company_views.py:485
#: asiantech.link/api/views/general/general_company_views.py:670
#: asiantech.link/api/views/general/general_company_views.py:831
msgid "You don't have permission to access this company"
msgstr ""

#: asiantech.link/api/views/general/general_company_views.py:487
#: asiantech.link/api/views/general/general_company_views.py:668
#: asiantech.link/api/views/general/general_company_views.py:829
msgid "Host company not found"
msgstr "Perusahaan tidak ditemukan"

#: asiantech.link/api/views/general/general_company_views.py:573
#: asiantech.link/api/views/general/general_company_views.py:576
#: asiantech.link/api/views/general/general_company_views.py:578
#: asiantech.link/api/views/general/general_company_views.py:627
#: asiantech.link/api/views/general/general_company_views.py:630
#: asiantech.link/api/views/general/general_company_views.py:632
#: asiantech.link/api/views/general/general_company_views.py:792
#: asiantech.link/api/views/general/general_company_views.py:795
#: asiantech.link/api/views/general/general_company_views.py:797
#: asiantech.link/api/views/general/general_company_views.py:873
#: asiantech.link/api/views/general/general_company_views.py:876
#: asiantech.link/api/views/general/general_company_views.py:878
msgid "You don't have permission to access this apply"
msgstr ""

#: asiantech.link/api/views/general/general_company_views.py:592
#: asiantech.link/api/views/general/general_company_views.py:641
#: asiantech.link/api/views/general/general_company_views.py:702
#: asiantech.link/api/views/general/general_company_views.py:806
#: asiantech.link/api/views/general/general_company_views.py:843
#: asiantech.link/api/views/general/general_company_views.py:949
#: asiantech.link/api/views/host_company/host_company_views.py:340
msgid "Apply not found"
msgstr "Aplikasi tidak ditemukan"

#: asiantech.link/api/views/general/general_company_views.py:946
msgid "Something went wrong!"
msgstr "Ada yang salah!"

#: asiantech.link/api/views/general/general_company_views.py:968
#: asiantech.link/api/views/host_support_agency_company/host_support_agency_company_views.py:205
#: asiantech.link/api/views/recruit/recruitment_views.py:49
#: asiantech.link/api/views/recruit/recruitment_views.py:155
msgid "You do not own the company"
msgstr "Anda tidak memiliki perusahaan tersebut"

#: asiantech.link/api/views/general/general_views.py:46
msgid "Current language is: "
msgstr "Bahasa saat ini adalah: "

#: asiantech.link/api/views/host_company/host_company_views.py:88
msgid "User already subscribed"
msgstr "Pengguna sudah berlangganan"

#: asiantech.link/api/views/host_company/host_company_views.py:120
msgid "User not subscribed"
msgstr "Pengguna tidak berlangganan"

#: asiantech.link/api/views/host_company/host_company_views.py:128
msgid "Data not found"
msgstr "Data tidak ditemukan"

#: asiantech.link/api/views/host_company/host_company_views.py:143
msgid "User does not have company."
msgstr "Pengguna tidak memiliki perusahaan."

#: asiantech.link/api/views/host_company/host_company_views.py:172
msgid "You have already make a request interview"
msgstr ""

#: asiantech.link/api/views/host_company/host_company_views.py:213
#: asiantech.link/api/views/host_support_agency_company/host_support_agency_company_views.py:161
msgid "You have requested an interview for this engineer."
msgstr ""

#: asiantech.link/api/views/host_company/host_company_views.py:331
msgid "Notification of acceptance has been sent."
msgstr "Pemberitahuan penerimaan telah dikirim."

#: asiantech.link/api/views/host_company/host_company_views.py:338
msgid "Invalid status"
msgstr "Status tidak valid"

#: asiantech.link/api/views/host_support_agency_company/host_support_agency_company_views.py:102
#: asiantech.link/api/views/host_support_agency_company/host_support_agency_company_views.py:213
msgid "Host company is not belong to you"
msgstr ""

#: asiantech.link/api/views/host_support_agency_company/host_support_agency_company_views.py:109
msgid "This engineer already make a interview to host company"
msgstr "Alamat email sudah terdaftar dengan perusahaan."

#: asiantech.link/api/views/host_support_agency_company/host_support_agency_company_views.py:247
msgid "You are not authorized to access this endpoint"
msgstr ""

#: asiantech.link/api/views/media/admin_upload_avatar_engineer_view.py:53
#: asiantech.link/api/views/media/admin_upload_avatar_engineer_view.py:77
#: asiantech.link/api/views/media/avatar_views.py:44
#: asiantech.link/api/views/media/avatar_views.py:68
#: asiantech.link/api/views/media/company_media_views.py:48
#: asiantech.link/api/views/media/company_media_views.py:81
#: asiantech.link/api/views/media/company_media_views.py:110
#: asiantech.link/api/views/media/company_media_views.py:174
#: asiantech.link/api/views/media/contract_media_view.py:56
#: asiantech.link/api/views/media/contract_media_view.py:117
#: asiantech.link/api/views/media/contract_media_view.py:148
#: asiantech.link/api/views/media/contract_media_view.py:212
#: asiantech.link/api/views/media/passport_views.py:44
#: asiantech.link/api/views/media/passport_views.py:69
#: asiantech.link/api/views/media/recruit_media_views.py:44
#: asiantech.link/api/views/media/recruit_media_views.py:65
msgid "Image uploaded successfully"
msgstr "Gambar berhasil diunggah"

#: asiantech.link/api/views/media/admin_upload_avatar_engineer_view.py:92
#: asiantech.link/api/views/media/admin_upload_avatar_engineer_view.py:110
#: asiantech.link/api/views/media/avatar_views.py:74
#: asiantech.link/api/views/media/avatar_views.py:92
msgid "Image deleted successfully"
msgstr "Gambar berhasil dihapus"

#: asiantech.link/api/views/media/company_media_views.py:53
#: asiantech.link/api/views/media/company_media_views.py:116
#: asiantech.link/api/views/media/recruit_media_views.py:49
msgid "You are not a company"
msgstr "Anda bukan sebuah perusahaan"

#: asiantech.link/api/views/media/company_media_views.py:139
msgid "Index must be 0,1,2"
msgstr "Indeks harus 0,1,2"

#: asiantech.link/api/views/media/contract_media_view.py:68
msgid "You can not upload contract image now, recruit progress code !=60"
msgstr ""
"Anda tidak dapat mengunggah gambar kontrak sekarang, kode kemajuan "
"perekrutan !=60"

#: asiantech.link/api/views/media/contract_media_view.py:167
msgid "You are not authorized to upload contract image"
msgstr ""

#: asiantech.link/api/views/media/contract_media_view.py:171
msgid "You can not upload contract image now, status progress code is invalid"
msgstr ""
"Anda tidak dapat mengunggah gambar kontrak sekarang, kode kemajuan status "
"tidak valid"

#: asiantech.link/api/views/media/passport_views.py:75
#: asiantech.link/api/views/media/passport_views.py:93
msgid "Passport image deleted successfully"
msgstr "Gambar paspor berhasil dihapus"

#: asiantech.link/api/views/notify/set_read_notify_view.py:34
msgid "Invalid request"
msgstr "Status tidak valid"

#: asiantech.link/api/views/profile/cv_views.py:52
#: asiantech.link/api/views/profile/cv_views.py:321
#: asiantech.link/api/views/profile/cv_views.py:389
#, fuzzy
#| msgid "Image uploaded successfully"
msgid "CV uploaded successfully"
msgstr "Gambar berhasil diunggah"

#: asiantech.link/api/views/profile/cv_views.py:58
msgid "File size must be less than 5MB"
msgstr "Ukuran file harus kurang dari 5MB"

#: asiantech.link/api/views/profile/cv_views.py:288
msgid "Invalid document type: not a resume"
msgstr "Jenis dokumen tidak valid: bukan resume"

#: asiantech.link/api/views/profile/cv_views.py:331
#, fuzzy
#| msgid "User not found"
msgid "CV not found"
msgstr "Pengguna tidak ditemukan."

#: asiantech.link/api/views/profile/profile_views.py:86
msgid "This email is already in use."
msgstr "Email sudah terdaftar!"

#: asiantech.link/api/views/profile/profile_views.py:146
msgid "Password updated successfully"
msgstr "Kata sandi berhasil diperbarui"

#: asiantech.link/api/views/profile/profile_views.py:162
#: asiantech.link/api/views/profile/profile_views.py:184
msgid "New password must be different from the current password."
msgstr "Kata sandi baru harus berbeda dari kata sandi saat ini."

#: asiantech.link/api/views/profile/profile_views.py:182
msgid "Password updated successfully."
msgstr "Kata sandi berhasil diperbarui."

#: asiantech.link/api/views/profile/profile_views.py:186
msgid "Invalid current password."
msgstr "Kata sandi saat ini tidak valid."

#: asiantech.link/api/views/recruit/recruitment_views.py:82
#: asiantech.link/api/views/recruit/recruitment_views.py:106
#: asiantech.link/api/views/recruit/recruitment_views.py:115
#: asiantech.link/api/views/recruit/recruitment_views.py:136
#: asiantech.link/api/views/recruit/recruitment_views.py:583
#: asiantech.link/api/views/recruit/recruitment_views.py:658
#: asiantech.link/api/views/recruit/recruitment_views.py:705
#: asiantech.link/api/views/recruit/recruitment_views.py:753
msgid "Recruitment does not exist"
msgstr "Rekrutmen tidak ada"

#: asiantech.link/api/views/recruit/recruitment_views.py:300
#: asiantech.link/api/views/recruit/recruitment_views.py:436
msgid "Error"
msgstr "Kesalahan"

#: asiantech.link/api/views/recruit/recruitment_views.py:460
msgid "Failed"
msgstr "Gagal"

#: asiantech.link/api/views/recruit/recruitment_views.py:495
msgid "ComCompany not found"
msgstr "ComPerusahaan tidak ditemukan"

#: asiantech.link/api/views/recruit/recruitment_views.py:502
msgid "MapEngAgc not found"
msgstr "MapEngAgc tidak ditemukan"

#: asiantech.link/api/views/recruit/recruitment_views.py:516
msgid "Already applied"
msgstr "Sudah diterapkan"

#: asiantech.link/api/views/recruit/recruitment_views.py:533
msgid "You have already applied for this recruitment"
msgstr ""

#: asiantech.link/api/views/recruit/recruitment_views.py:656
msgid "Recruitment does not exist or you do not have access to it"
msgstr "Perekrutan tidak ada atau Anda tidak memiliki akses ke sana"

#: asiantech.link/api/views/recruit/recruitment_views.py:679
#: asiantech.link/api/views/recruit/recruitment_views.py:724
msgid "Recruit ID is required"
msgstr "ID Rekrutmen diperlukan"

#: asiantech.link/api/views/recruit/recruitment_views.py:687
#: asiantech.link/api/views/recruit/recruitment_views.py:776
#: asiantech.link/api/views/recruit/recruitment_views.py:824
msgid "No application found"
msgstr "Tidak ada aplikasi yang ditemukan"

#: asiantech.link/api/views/recruit/recruitment_views.py:698
#: asiantech.link/api/views/recruit/recruitment_views.py:739
msgid "Invalid recruit progress code to cancel"
msgstr "Kode kemajuan rekrutmen tidak valid untuk dibatalkan"

#: asiantech.link/api/views/recruit/recruitment_views.py:737
msgid "Interview datetime is required"
msgstr "Tanggal dan waktu wawancara diperlukan"

#: asiantech.link/api/views/recruit/recruitment_views.py:798
msgid "Invalid recruit progress code to sign contract"
msgstr "Kode kemajuan rekrutmen tidak valid untuk menandatangani kontrak"

#: asiantech.link/api/views/recruit/recruitment_views.py:831
msgid "No application found for the provided recruitment ID"
msgstr "Tidak ada aplikasi yang ditemukan untuk ID rekrutmen yang diberikan"

#: asiantech.link/api/views/recruit/recruitment_views.py:837
msgid "No accept sign found for the application"
msgstr "Tidak ditemukan tanda terima untuk aplikasi"

#: asiantech.link/core/settings/common.py:132
msgid "English"
msgstr "Inggris"

#: asiantech.link/core/settings/common.py:133
msgid "Japanese"
msgstr "Jepang"

#: asiantech.link/core/settings/common.py:134
msgid "Myanmar"
msgstr "Myanmar"

#: asiantech.link/core/settings/common.py:135
msgid "Indonesian"
msgstr "Indonesia"

#: asiantech.link/core/settings/common.py:136
msgid "Nepali"
msgstr "Nepal"

#: asiantech.link/core/settings/common.py:137
msgid "Vietnamese"
msgstr "Vietnam"

#: asiantech.link/utils/permissions.py:22
#: asiantech.link/utils/permissions.py:36
msgid "Account has not been verified"
msgstr "Akun belum diverifikasi"

#: asiantech.link/utils/permissions.py:39
#: asiantech.link/utils/permissions.py:53
#: asiantech.link/utils/permissions.py:64
#: asiantech.link/utils/permissions.py:83
#: asiantech.link/utils/permissions.py:96
#: asiantech.link/utils/permissions.py:107
#: asiantech.link/utils/permissions.py:120
#: asiantech.link/utils/permissions.py:132
msgid "Access denied"
msgstr ""

#: asiantech.link/utils/utils.py:508 asiantech.link/utils/utils.py:644
msgid "Fresher"
msgstr "Lebih segar"

#: asiantech.link/utils/utils.py:510 asiantech.link/utils/utils.py:646
msgid "Junior"
msgstr "Muda"

#: asiantech.link/utils/utils.py:512 asiantech.link/utils/utils.py:648
msgid "Middle"
msgstr "Tengah"

#: asiantech.link/utils/utils.py:514 asiantech.link/utils/utils.py:650
msgid "Senior"
msgstr "Senior"

#: asiantech.link/utils/validators.py:29
msgid "Email is too long"
msgstr "Email terlalu panjang"

#: asiantech.link/utils/validators.py:35
msgid "Email must contain a single '@' symbol"
msgstr "Email harus mengandung satu simbol '@'"

#: asiantech.link/utils/validators.py:39
msgid "Local part of the email is too long (>= 64 characters)"
msgstr "Bagian lokal email terlalu panjang (>= 64 karakter)"

#: asiantech.link/utils/validators.py:43
msgid "Email contains invalid characters"
msgstr "Email berisi karakter yang tidak valid"

#: asiantech.link/utils/validators.py:47
msgid "Email contains consecutive periods"
msgstr "Email berisi titik berurutan"

#: asiantech.link/utils/validators.py:51
msgid ""
"Email has periods at invalid positions (start/end of local part or end of "
"email)"
msgstr ""
"Email memiliki periode pada posisi yang tidak valid (mulai/akhir bagian "
"lokal atau akhir email)"

#: asiantech.link/utils/validators.py:55
msgid "Domain part of the email should not be an IP address"
msgstr "Bagian domain dari email seharusnya bukan alamat IP"

#: asiantech.link/utils/validators.py:57
msgid "Email is valid"
msgstr "Emailnya valid"

#: asiantech.link/utils/validators.py:63
msgid "Password length must be between 8 and 20 characters"
msgstr "Panjang kata sandi harus antara 8 dan 20 karakter"

#: asiantech.link/utils/validators.py:68
msgid "Password contains full-width characters"
msgstr "Kata sandi berisi karakter lebar penuh"

#: asiantech.link/utils/validators.py:72
msgid "Password must contain at least one uppercase letter"
msgstr "Kata sandi harus mengandung setidaknya satu huruf kapital"

#: asiantech.link/utils/validators.py:76
msgid "Password must contain at least one lowercase letter"
msgstr "Kata sandi harus mengandung setidaknya satu huruf kecil"

#: asiantech.link/utils/validators.py:80
msgid "Password must contain at least one number"
msgstr "Kata sandi harus mengandung setidaknya satu angka"

#: asiantech.link/utils/validators.py:84
msgid "Password must contain at least one special character"
msgstr "Kata sandi harus mengandung setidaknya satu karakter khusus."

#: asiantech.link/utils/validators.py:86
msgid "Password is valid"
msgstr "Password valid"

#~ msgid "Expert"
#~ msgstr "Pakar"

#~ msgid "Facebook authentication successful"
#~ msgstr "Autentikasi Facebook berhasil."

#~ msgid "Linkedin authentication successful"
#~ msgstr "Autentikasi LinkedIn berhasil."

#~ msgid "Zalo authentication successful"
#~ msgstr "Autentikasi Zalo berhasil."

#~ msgid "Get user info with zalo id"
#~ msgstr "Dapatkan informasi pengguna dengan ID Zalo."

#~ msgid "Get user info with whatsapp number"
#~ msgstr "Dapatkan informasi pengguna dengan nomor WhatsApp."

#~ msgid "WhatsApp authentication successful"
#~ msgstr "Autentikasi WhatsApp berhasil."

#~ msgid "Failed to read PDF content"
#~ msgstr "Gagal membaca konten PDF"

#~ msgid "Email is too long (>= 201 characters)"
#~ msgstr "Email terlalu panjang (>= 201 karakter)"
