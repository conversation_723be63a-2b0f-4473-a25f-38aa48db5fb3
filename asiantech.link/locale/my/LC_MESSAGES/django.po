# SOME DESCRIPTIVE TITLE.
# Copyright (C) YEAR THE PACKAGE'S COPYRIGHT HOLDER
# This file is distributed under the same license as the PACKAGE package.
# <AUTHOR> <EMAIL>, YEAR.
#
msgid ""
msgstr ""
"Project-Id-Version: PACKAGE VERSION\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-06-26 15:16+0700\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language-Team: LANGUAGE <<EMAIL>>\n"
"Language: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#: asiantech.link/api/models/user.py:17
msgid "Email should be provided"
msgstr "အီးမေးလ်ပေးသင့်သည်။"

#: asiantech.link/api/models/user.py:38
msgid "Superuser must have is_staff as True"
msgstr "Superuser တွင် is_staff သည် True ဖြစ်ရပါမည်။"

#: asiantech.link/api/models/user.py:41
msgid "Superuser must have is_superuser as True"
msgstr "Superuser သည် True အဖြစ် is_superuser ရှိရပါမည်။"

#: asiantech.link/api/models/user.py:44
msgid "Superuser must have is_active as True"
msgstr "Superuser သည် True အဖြစ် is_active ရှိရပါမည်။"

#: asiantech.link/api/serializers/cv/cv_serializers.py:192
#: asiantech.link/api/serializers/engineers/user_serializers.py:52
#: asiantech.link/api/serializers/recruitment_serializers.py:176
#: asiantech.link/api/serializers/recruitment_serializers.py:681
msgid "Invalid degree code."
msgstr "ဒီဂရီကုဒ် မမှန်ကန်ပါ။"

#: asiantech.link/api/serializers/cv/cv_serializers.py:198
#, fuzzy
#| msgid "Invalid language code."
msgid "Invalid language level type."
msgstr "မမှန်ကန်သော ဘာသာစကားကုဒ်။"

#: asiantech.link/api/serializers/engineers/user_serializers.py:409
#, fuzzy
#| msgid "start_date must be less than end_date"
msgid "From date must be before to date."
msgstr "start_date သည် end_date ထက်နည်းရမည်။"

#: asiantech.link/api/serializers/engineers/user_serializers.py:563
#: asiantech.link/api/serializers/engineers/user_serializers.py:568
#: asiantech.link/api/serializers/engineers/user_serializers.py:573
msgid "Invalid place code."
msgstr "နေရာကုဒ် မမှန်ကန်ပါ။"

#: asiantech.link/api/serializers/engineers/user_serializers.py:580
msgid "Invalid payroll price."
msgstr "မမှန်ကန်သော လုပ်ခလစာစျေးနှုန်း။"

#: asiantech.link/api/serializers/engineers/user_serializers.py:941
msgid "Invalid sex type."
msgstr "မမှန်ကန်သော လိင်အမျိုးအစား။"

#: asiantech.link/api/serializers/engineers/user_serializers.py:949
#: asiantech.link/api/serializers/general_company/general_company_serializers.py:350
#: asiantech.link/api/serializers/general_company/general_company_serializers.py:447
#: asiantech.link/api/serializers/recruitment_serializers.py:207
#: asiantech.link/api/serializers/recruitment_serializers.py:741
msgid "Invalid country code."
msgstr "နိုင်ငံကုဒ် မမှန်ကန်ပါ။"

#: asiantech.link/api/serializers/engineers/user_serializers.py:962
#: asiantech.link/api/serializers/general_company/general_company_serializers.py:366
#: asiantech.link/api/serializers/general_company/general_company_serializers.py:466
msgid "Invalid phone number."
msgstr "ဖုန်းနံပါတ် မမှန်ကန်ပါ။"

#: asiantech.link/api/serializers/engineers/user_serializers.py:966
#: asiantech.link/api/serializers/general_company/general_company_serializers.py:370
#: asiantech.link/api/serializers/general_company/general_company_serializers.py:470
msgid "Invalid phone number format."
msgstr "ဖုန်းနံပါတ်ဖော်မတ် မမှန်ကန်ပါ။"

#: asiantech.link/api/serializers/engineers/user_serializers.py:974
#: asiantech.link/api/serializers/general_company/general_company_serializers.py:356
#: asiantech.link/api/serializers/general_company/general_company_serializers.py:454
#: asiantech.link/api/serializers/recruitment_serializers.py:215
#: asiantech.link/api/serializers/recruitment_serializers.py:218
#: asiantech.link/api/serializers/recruitment_serializers.py:221
#: asiantech.link/api/serializers/recruitment_serializers.py:713
#: asiantech.link/api/serializers/recruitment_serializers.py:716
#: asiantech.link/api/serializers/recruitment_serializers.py:719
msgid "Invalid address code."
msgstr "လိပ်စာကုဒ် မမှန်ကန်ပါ။"

#: asiantech.link/api/serializers/general_company/general_company_serializers.py:290
#: asiantech.link/api/serializers/general_company/general_company_serializers.py:433
#: asiantech.link/api/serializers/general_company/general_company_serializers.py:439
#: asiantech.link/api/serializers/host_company/host_company_serializers.py:59
msgid "Invalid url format."
msgstr "မမှန်ကန်သော url ဖော်မတ်။"

#: asiantech.link/api/serializers/general_company/general_company_serializers.py:296
#: asiantech.link/api/serializers/host_company/host_company_serializers.py:65
msgid "Invalid email format."
msgstr "မမှန်ကန်သော အီးမေးလ်ဖော်မတ်။"

#: asiantech.link/api/serializers/general_company/general_company_serializers.py:481
#: asiantech.link/api/serializers/general_company/general_company_serializers.py:487
#: asiantech.link/api/serializers/recruitment_serializers.py:248
#: asiantech.link/api/serializers/recruitment_serializers.py:699
msgid "Invalid currency code."
msgstr "မမှန်ကန်သော ငွေကြေးကုဒ်။"

#: asiantech.link/api/serializers/general_company/general_company_serializers.py:640
msgid "compare_content_1"
msgstr ""
"ဤလျှောက်ထားသူသည် အလုပ်လိုအပ်ချက်များနှင့် လုံးဝကိုက်ညီမှုမရှိပါ။\n"
"သူ/သူမကို ခန့်ထားနိုင်ခြေအနည်းကြောင့် အခြားလျှောက်ထားသူများကို ဦးစားပေးပါ၊ သို့သော် သူ/သူမက "
"သင့်စိတ်ဝင်စားစေပါက အင်တာဗျူးတောင်းဆိုမှုပြုလုပ်နိုင်သည်။"

#: asiantech.link/api/serializers/general_company/general_company_serializers.py:642
msgid "compare_content_2"
msgstr ""
"ဤလျှောက်ထားသူသည် အလုပ်လိုအပ်ချက်များစွာနှင့် ကိုက်ညီပါသည်။\n"
"အင်ဂျင်နီယာ၏ ကိုယ်ရေးကိုယ်တာအချက်အလက်များကို စစ်ဆေးပြီး ထူးခြားသောပြဿနာမရှိပါက "
"အင်တာဗျူးတောင်းဆိုပါ။\n"
"ခန့်ထားနိုင်ခြေမြင့်မားသဖြင့် အဆိုပြုလိုက်သောအခြေအနေများကို တွေ့ဆုံ၍ "
"သေချာစွာတစ်ခါထပ်သုံးသပ်ခြင်းပြုလေ့လာပါ။"

#: asiantech.link/api/serializers/general_company/general_company_serializers.py:644
msgid "compare_content_3"
msgstr ""
"ဤလျှောက်ထားသူသည် အလုပ်လိုအပ်ချက်များနှင့် ကိုက်ညီပါသည်!\n"
"အင်ဂျင်နီယာ၏ ကိုယ်ရေးကိုယ်တာအချက်အလက်များကို စစ်ဆေးပြီး ထူးခြားသောပြဿနာမရှိပါက အင်တာဗျူးတောင်းဆိုပါ။"

#: asiantech.link/api/serializers/recruitment_serializers.py:169
#: asiantech.link/api/serializers/recruitment_serializers.py:672
msgid "Invalid sex type"
msgstr "မမှန်ကန်သော လိင်အမျိုးအစား"

#: asiantech.link/api/serializers/recruitment_serializers.py:183
#: asiantech.link/api/serializers/recruitment_serializers.py:704
msgid "start_date must be less than end_date"
msgstr "start_date သည် end_date ထက်နည်းရမည်။"

#: asiantech.link/api/serializers/recruitment_serializers.py:192
#: asiantech.link/api/serializers/recruitment_serializers.py:749
msgid "age_from must be less than age_to"
msgstr "age_from သည် age_to ထက်နည်းရမည်။"

#: asiantech.link/api/serializers/recruitment_serializers.py:200
#: asiantech.link/api/serializers/recruitment_serializers.py:693
msgid "payroll_price_from must be less than payroll_price_to"
msgstr "payroll_price_f သည် payroll_price_to ထက်နည်းရမည်။"

#: asiantech.link/api/serializers/recruitment_serializers.py:236
#: asiantech.link/api/serializers/recruitment_serializers.py:239
#: asiantech.link/api/serializers/recruitment_serializers.py:242
#: asiantech.link/api/serializers/recruitment_serializers.py:727
#: asiantech.link/api/serializers/recruitment_serializers.py:730
#: asiantech.link/api/serializers/recruitment_serializers.py:733
msgid "Invalid pref code."
msgstr "ကြိုတင်ကုဒ် မမှန်ကန်ပါ။"

#: asiantech.link/api/serializers/recruitment_serializers.py:255
msgid "Invalid support company."
msgstr "ပံ့ပိုးမှုကုမ္ပဏီ မမှန်ပါ။"

#: asiantech.link/api/serializers/recruitment_serializers.py:765
msgid "years_of_experience must be greater than 0"
msgstr "years_of_experience သည် 0 ထက်ကြီးရမည်။"

#: asiantech.link/api/serializers/recruitment_serializers.py:779
#: asiantech.link/api/serializers/recruitment_serializers.py:782
msgid "Invalid language code."
msgstr "မမှန်ကန်သော ဘာသာစကားကုဒ်။"

#: asiantech.link/api/services/notify_service/company_notify_service.py:32
msgid "The interview result registration for is incomplete"
msgstr ""

#: asiantech.link/api/services/notify_service/company_notify_service.py:61
#: asiantech.link/api/services/notify_service/support_company_notify_service.py:61
#: asiantech.link/api/services/notify_service/support_company_notify_service.py:90
#: asiantech.link/api/services/notify_service/support_company_notify_service.py:125
msgid "Please register the interview date and time with"
msgstr ""

#: asiantech.link/api/services/notify_service/company_notify_service.py:89
msgid "Please register the interview outcome for"
msgstr ""

#: asiantech.link/api/services/notify_service/company_notify_service.py:118
msgid "Please sign the offer acceptance and employment contract with"
msgstr ""

#: asiantech.link/api/services/notify_service/engineer_notify_service.py:26
msgid "Please respond to the interview request from"
msgstr ""

#: asiantech.link/api/services/notify_service/engineer_notify_service.py:50
msgid "Please respond to the job offer from"
msgstr ""

#: asiantech.link/api/services/notify_service/support_company_notify_service.py:32
msgid "Please register the document screening result for"
msgstr ""

#: asiantech.link/api/views/admin/admin_views.py:52
msgid "List of engineers"
msgstr ""

#: asiantech.link/api/views/admin/admin_views.py:169
#: asiantech.link/api/views/admin/admin_views.py:227
#: asiantech.link/api/views/admin/admin_views.py:259
#: asiantech.link/api/views/admin/admin_views.py:270
#: asiantech.link/api/views/admin/admin_views.py:285
#: asiantech.link/api/views/admin/admin_views.py:578
#: asiantech.link/api/views/admin/admin_views.py:589
#: asiantech.link/api/views/admin/admin_views.py:601
#: asiantech.link/api/views/authentication/authentication_views.py:264
#: asiantech.link/api/views/engineer/engineer_views.py:43
#: asiantech.link/api/views/engineer/engineer_views.py:58
#: asiantech.link/api/views/engineer/engineer_views.py:66
#: asiantech.link/api/views/engineer/engineer_views.py:107
#: asiantech.link/api/views/engineer/engineer_views.py:115
#: asiantech.link/api/views/engineer/engineer_views.py:132
#: asiantech.link/api/views/engineer/engineer_views.py:140
#: asiantech.link/api/views/engineer/engineer_views.py:189
#: asiantech.link/api/views/engineer/engineer_views.py:208
#: asiantech.link/api/views/engineer/engineer_views.py:216
#: asiantech.link/api/views/engineer/engineer_views.py:383
#: asiantech.link/api/views/engineer/engineer_views.py:403
#: asiantech.link/api/views/general/general_company_views.py:49
#: asiantech.link/api/views/general/general_company_views.py:71
#: asiantech.link/api/views/general/general_company_views.py:78
#: asiantech.link/api/views/general/general_company_views.py:84
#: asiantech.link/api/views/general/general_company_views.py:98
#: asiantech.link/api/views/general/general_company_views.py:123
#: asiantech.link/api/views/general/general_company_views.py:198
#: asiantech.link/api/views/general/general_company_views.py:239
#: asiantech.link/api/views/general/general_company_views.py:246
#: asiantech.link/api/views/general/general_company_views.py:262
#: asiantech.link/api/views/general/general_company_views.py:280
#: asiantech.link/api/views/general/general_company_views.py:296
#: asiantech.link/api/views/general/general_company_views.py:313
#: asiantech.link/api/views/general/general_company_views.py:331
#: asiantech.link/api/views/general/general_company_views.py:339
#: asiantech.link/api/views/general/general_company_views.py:358
#: asiantech.link/api/views/general/general_company_views.py:378
#: asiantech.link/api/views/general/general_company_views.py:397
#: asiantech.link/api/views/general/general_company_views.py:416
#: asiantech.link/api/views/general/general_company_views.py:434
#: asiantech.link/api/views/general/general_company_views.py:455
#: asiantech.link/api/views/general/general_company_views.py:536
#: asiantech.link/api/views/general/general_company_views.py:550
#: asiantech.link/api/views/general/general_company_views.py:590
#: asiantech.link/api/views/general/general_company_views.py:600
#: asiantech.link/api/views/general/general_company_views.py:639
#: asiantech.link/api/views/general/general_company_views.py:650
#: asiantech.link/api/views/general/general_company_views.py:700
#: asiantech.link/api/views/general/general_company_views.py:711
#: asiantech.link/api/views/general/general_company_views.py:767
#: asiantech.link/api/views/general/general_company_views.py:804
#: asiantech.link/api/views/general/general_company_views.py:814
#: asiantech.link/api/views/general/general_company_views.py:957
#: asiantech.link/api/views/general/general_company_views.py:978
#: asiantech.link/api/views/general/general_views.py:28
#: asiantech.link/api/views/general/general_views.py:77
#: asiantech.link/api/views/host_company/host_company_views.py:38
#: asiantech.link/api/views/host_company/host_company_views.py:55
#: asiantech.link/api/views/host_company/host_company_views.py:62
#: asiantech.link/api/views/host_company/host_company_views.py:86
#: asiantech.link/api/views/host_company/host_company_views.py:92
#: asiantech.link/api/views/host_company/host_company_views.py:99
#: asiantech.link/api/views/host_company/host_company_views.py:130
#: asiantech.link/api/views/host_company/host_company_views.py:136
#: asiantech.link/api/views/host_company/host_company_views.py:150
#: asiantech.link/api/views/host_company/host_company_views.py:157
#: asiantech.link/api/views/host_company/host_company_views.py:211
#: asiantech.link/api/views/host_company/host_company_views.py:244
#: asiantech.link/api/views/host_company/host_company_views.py:252
#: asiantech.link/api/views/host_company/host_company_views.py:263
#: asiantech.link/api/views/host_company/host_company_views.py:270
#: asiantech.link/api/views/host_company/host_company_views.py:289
#: asiantech.link/api/views/host_company/host_company_views.py:297
#: asiantech.link/api/views/host_company/host_company_views.py:336
#: asiantech.link/api/views/host_support_agency_company/host_support_agency_company_views.py:35
#: asiantech.link/api/views/host_support_agency_company/host_support_agency_company_views.py:81
#: asiantech.link/api/views/host_support_agency_company/host_support_agency_company_views.py:159
#: asiantech.link/api/views/host_support_agency_company/host_support_agency_company_views.py:188
#: asiantech.link/api/views/host_support_agency_company/host_support_agency_company_views.py:196
#: asiantech.link/api/views/host_support_agency_company/host_support_agency_company_views.py:230
#: asiantech.link/api/views/host_support_agency_company/host_support_agency_company_views.py:238
#: asiantech.link/api/views/host_support_agency_company/host_support_agency_company_views.py:295
#: asiantech.link/api/views/notify/count_unread_notify_view.py:24
#: asiantech.link/api/views/notify/count_unread_notify_view.py:50
#: asiantech.link/api/views/notify/get_list_notify_view.py:24
#: asiantech.link/api/views/notify/get_list_notify_view.py:43
#: asiantech.link/api/views/notify/set_read_all_notify_view.py:23
#: asiantech.link/api/views/notify/set_read_all_notify_view.py:32
#: asiantech.link/api/views/notify/set_read_notify_view.py:24
#: asiantech.link/api/views/notify/set_read_notify_view.py:38
#: asiantech.link/api/views/profile/profile_views.py:38
#: asiantech.link/api/views/profile/profile_views.py:53
#: asiantech.link/api/views/profile/profile_views.py:192
#: asiantech.link/api/views/profile/profile_views.py:241
#: asiantech.link/api/views/profile/profile_views.py:285
#: asiantech.link/api/views/profile/profile_views.py:334
#: asiantech.link/api/views/profile/profile_views.py:378
#: asiantech.link/api/views/profile/profile_views.py:427
#: asiantech.link/api/views/profile/profile_views.py:471
#: asiantech.link/api/views/profile/profile_views.py:520
#: asiantech.link/api/views/profile/profile_views.py:564
#: asiantech.link/api/views/profile/profile_views.py:613
#: asiantech.link/api/views/recruit/recruitment_views.py:39
#: asiantech.link/api/views/recruit/recruitment_views.py:60
#: asiantech.link/api/views/recruit/recruitment_views.py:67
#: asiantech.link/api/views/recruit/recruitment_views.py:84
#: asiantech.link/api/views/recruit/recruitment_views.py:91
#: asiantech.link/api/views/recruit/recruitment_views.py:97
#: asiantech.link/api/views/recruit/recruitment_views.py:109
#: asiantech.link/api/views/recruit/recruitment_views.py:122
#: asiantech.link/api/views/recruit/recruitment_views.py:128
#: asiantech.link/api/views/recruit/recruitment_views.py:139
#: asiantech.link/api/views/recruit/recruitment_views.py:146
#: asiantech.link/api/views/recruit/recruitment_views.py:174
#: asiantech.link/api/views/recruit/recruitment_views.py:181
#: asiantech.link/api/views/recruit/recruitment_views.py:305
#: asiantech.link/api/views/recruit/recruitment_views.py:312
#: asiantech.link/api/views/recruit/recruitment_views.py:438
#: asiantech.link/api/views/recruit/recruitment_views.py:444
#: asiantech.link/api/views/recruit/recruitment_views.py:475
#: asiantech.link/api/views/recruit/recruitment_views.py:481
#: asiantech.link/api/views/recruit/recruitment_views.py:567
#: asiantech.link/api/views/recruit/recruitment_views.py:574
#: asiantech.link/api/views/recruit/recruitment_views.py:662
#: asiantech.link/api/views/recruit/recruitment_views.py:670
#: asiantech.link/api/views/recruit/recruitment_views.py:703
#: asiantech.link/api/views/recruit/recruitment_views.py:714
#: asiantech.link/api/views/recruit/recruitment_views.py:751
#: asiantech.link/api/views/recruit/recruitment_views.py:760
#: asiantech.link/api/views/recruit/recruitment_views.py:766
#: asiantech.link/api/views/recruit/recruitment_views.py:783
#: asiantech.link/api/views/recruit/recruitment_views.py:796
#: asiantech.link/api/views/recruit/recruitment_views.py:805
#: asiantech.link/api/views/recruit/recruitment_views.py:852
#: asiantech.link/api/views/recruit/recruitment_views.py:889
msgid "Success"
msgstr "အောင်မြင်မှု"

#: asiantech.link/api/views/admin/admin_views.py:178
msgid "List of email schedules"
msgstr ""

#: asiantech.link/api/views/admin/admin_views.py:237
msgid "List of registrars"
msgstr ""

#: asiantech.link/api/views/admin/admin_views.py:296
#: asiantech.link/api/views/admin/admin_views.py:384
msgid "Email schedule created successfully"
msgstr "အသုံးပြုသူကို အောင်မြင်စွာ ဖန်တီးခဲ့သည်။"

#: asiantech.link/api/views/admin/admin_views.py:314
#: asiantech.link/api/views/admin/admin_views.py:417
msgid "Target email not found"
msgstr "အသုံးပြုသူ မတွေ့ပါ"

#: asiantech.link/api/views/admin/admin_views.py:367
#: asiantech.link/api/views/admin/admin_views.py:470
msgid "Send test email successfully"
msgstr "အသုံးပြုသူကို အောင်မြင်စွာ ဖန်တီးခဲ့သည်။"

#: asiantech.link/api/views/admin/admin_views.py:376
#: asiantech.link/api/views/admin/admin_views.py:478
msgid "Email personal information already exists"
msgstr "အီးမေးလ် မှတ်ပုံတင်ပြီးသားပါ!"

#: asiantech.link/api/views/admin/admin_views.py:395
#: asiantech.link/api/views/admin/admin_views.py:486
msgid "Email schedule updated successfully"
msgstr "စကားဝှက်ကို အောင်မြင်စွာ မွမ်းမံပြီးပါပြီ။"

#: asiantech.link/api/views/admin/admin_views.py:407
#: asiantech.link/api/views/admin/admin_views.py:507
#: asiantech.link/api/views/admin/admin_views.py:572
msgid "Email schedule not found"
msgstr "အသုံးပြုသူကို ရှာမတွေ့ပါ။"

#: asiantech.link/api/views/admin/admin_views.py:497
#: asiantech.link/api/views/admin/admin_views.py:512
msgid "Email schedule deleted successfully"
msgstr "အသုံးပြုသူ ဖန်တီးမှု အောင်မြင်ပါသည်"

#: asiantech.link/api/views/admin/admin_views.py:524
#: asiantech.link/api/views/admin/admin_views.py:550
msgid "Email schedule list deleted successfully"
msgstr "အသုံးပြုသူ ဖန်တီးမှု အောင်မြင်ပါသည်"

#: asiantech.link/api/views/admin/admin_views.py:540
msgid "Invalid data"
msgstr "အတည်ပြု ကုဒ်"

#: asiantech.link/api/views/admin/admin_views.py:545
msgid "No email schedule ids provided"
msgstr "အီးမေးလ်ပေးသင့်သည်။"

#: asiantech.link/api/views/admin/admin_views.py:561
msgid "Email schedule details"
msgstr "အီးမေးလ်ပေးသင့်သည်။"

#: asiantech.link/api/views/admin/admin_views.py:603
#, fuzzy
#| msgid "User not found"
msgid "Engineer not found"
msgstr "အသုံးပြုသူကို ရှာမတွေ့ပါ။"

#: asiantech.link/api/views/authentication/authentication_views.py:43
msgid "Verification email sent"
msgstr "အတည်ပြုအီးမေးလ်ပို့ပြီးပါပြီ။"

#: asiantech.link/api/views/authentication/authentication_views.py:46
msgid "Verification whatsapp sent"
msgstr "WhatsApp အတည်ပြုမှု ပို့ပေးပြီးပါပြီ။"

#: asiantech.link/api/views/authentication/authentication_views.py:49
msgid "Email verified successfully"
msgstr "အီးမေးလ်ကို အောင်မြင်စွာ အတည်ပြုပြီးပါပြီ။"

#: asiantech.link/api/views/authentication/authentication_views.py:52
msgid "User created successfully"
msgstr "အသုံးပြုသူကို အောင်မြင်စွာ ဖန်တီးခဲ့သည်။"

#: asiantech.link/api/views/authentication/authentication_views.py:55
#: asiantech.link/api/views/authentication/authentication_views.py:415
#: asiantech.link/api/views/authentication/authentication_views.py:540
#: asiantech.link/api/views/authentication/authentication_views.py:584
#: asiantech.link/api/views/authentication/authentication_views.py:801
#: asiantech.link/api/views/authentication/authentication_views.py:817
#: asiantech.link/api/views/authentication/authentication_views.py:1065
msgid "Login successfully"
msgstr "အောင်မြင်စွာ အကောင့်ဝင်ပါ။"

#: asiantech.link/api/views/authentication/authentication_views.py:57
msgid "Reset password successfully"
msgstr "စကားဝှက်ကို အောင်မြင်စွာ ပြန်လည်သတ်မှတ်ပါ။"

#: asiantech.link/api/views/authentication/authentication_views.py:102
msgid "emailRegisterVerificationTitle"
msgstr "【AsianTech.Link】သင့်အကောင့်ကို အတည်ပြုပါ - မှတ်ပုံတင်ခြင်းကို ပြီးမြောက်စေပါ"

#: asiantech.link/api/views/authentication/authentication_views.py:109
msgid "emailConfirmContent1"
msgstr "AsianTech.Link တွင် မှတ်ပုံတင်ပေးသည့်အတွက် ကျေး"

#: asiantech.link/api/views/authentication/authentication_views.py:110
msgid "emailConfirmContent2"
msgstr ""
"ဝန်ဆောင်မှုကို လုံခြုံစွာ အသုံးပြုရန်အတွက် အီးမေးလ်လိပ်စာ အတည်ပြုခြင်းသည် လိုအပ်ပါသည်။ အောက်ပါခလုတ်ကို အသုံးပြု၍ "
"သင့်အီးမေးလ်လိပ်စာကို အတည်ပြုပါ။"

#: asiantech.link/api/views/authentication/authentication_views.py:111
msgid "emailConfirmContent3"
msgstr ""
"သင်၏အခြေအနေကို ကာကွယ်ရန်အတွက်၊ သင့်အီးမေးလ်လိပ်စာကို အတည်ပြုရန် လိုအပ်ပါသည်။ အောက်တွင် ပါသော ခလုတ်ကို "
"အသုံးပြုပြီး သင့်အီးမေးလ်လိပ်စာကို အတည်ပြုပါ။"

#: asiantech.link/api/views/authentication/authentication_views.py:112
#: asiantech.link/api/views/authentication/authentication_views.py:315
msgid "emailConfirmContent4"
msgstr "© ကော်ပီရိုက်ရှက် AsianTech.Link အားလုံးအခွင့်အရေးများကို ကာကွယ်ထားသည်။"

#: asiantech.link/api/views/authentication/authentication_views.py:114
msgid "Verification Email"
msgstr "အတည်ပြု အီးမေးလ် ပို့ပြီးပါပြီ"

#: asiantech.link/api/views/authentication/authentication_views.py:119
msgid "verifyYourAccount"
msgstr "သင့်အီးမေးလ်လိပ်စာကို အတည်ပြုပါ"

#: asiantech.link/api/views/authentication/authentication_views.py:155
#: asiantech.link/api/views/authentication/authentication_views.py:157
msgid "Email is already linked to an another account"
msgstr ""

#: asiantech.link/api/views/authentication/authentication_views.py:166
#: asiantech.link/api/views/authentication/authentication_views.py:296
#: asiantech.link/api/views/authentication/authentication_views.py:986
#: asiantech.link/api/views/authentication/authentication_views.py:1049
#: asiantech.link/api/views/authentication/authentication_views.py:1101
#: asiantech.link/api/views/authentication/authentication_views.py:1176
msgid "User not found!"
msgstr "အသုံးပြုသူကို ရှာမတွေ့ပါ။"

#: asiantech.link/api/views/authentication/authentication_views.py:169
#: asiantech.link/api/views/authentication/authentication_views.py:298
msgid "User is already verified!"
msgstr "အသုံးပြုသူကို စစ်ဆေးပြီးပါပြီ။"

#: asiantech.link/api/views/authentication/authentication_views.py:173
#: asiantech.link/api/views/authentication/authentication_views.py:552
msgid "CAPTCHA verification failed"
msgstr "CAPTCHA အတည်ပြုမှု မအောင်မြင်ပါ။"

#: asiantech.link/api/views/authentication/authentication_views.py:266
msgid "Invalid code!"
msgstr "မှားသောကုဒ်ဖြစ်သည်!"

#: asiantech.link/api/views/authentication/authentication_views.py:282
msgid "Token is required!"
msgstr "တိုကင် လိုအပ်ပါသည်။"

#: asiantech.link/api/views/authentication/authentication_views.py:306
msgid "emailRegisterSuccessTitle"
msgstr "【မှတ်ပုံတင်ခြင်း အောင်မြင်】AsianTech.Link အကောင့်"

#: asiantech.link/api/views/authentication/authentication_views.py:313
msgid "emailRegisterSuccessContent1"
msgstr "သင့် AsianTech.Link အကောင့်မှတ်ပုံတင်ခြင်း ပြီးမြောက်ပါပြီ"

#: asiantech.link/api/views/authentication/authentication_views.py:314
msgid "emailRegisterSuccessContent2"
msgstr ""
"Asian Tech.Link မှတစ်ဆင့် သင့်ရဲ့ ဆွဲဆောင်မှုကို ပြသပါပြီးတော့ ဆွဲဆောင်မှုရှိသော "
"အလုပ်အကိုင်အခင်းအကျင်းများနှင့် ချိတ်ဆက်ပါ။"

#: asiantech.link/api/views/authentication/authentication_views.py:331
#: asiantech.link/api/views/authentication/authentication_views.py:333
#: asiantech.link/api/views/authentication/authentication_views.py:1006
#: asiantech.link/api/views/authentication/authentication_views.py:1014
#: asiantech.link/api/views/authentication/authentication_views.py:1046
msgid "Verification token has expired!"
msgstr "အတည်ပြုခြင်းတိုကင် သက်တမ်းကုန်သွားပါပြီ။"

#: asiantech.link/api/views/authentication/authentication_views.py:335
msgid "Invalid verification token!"
msgstr "မမှန်ကန်ကြောင်း အတည်ပြုခြင်းတိုကင်။"

#: asiantech.link/api/views/authentication/authentication_views.py:350
#: asiantech.link/api/views/authentication/authentication_views.py:428
msgid "Validation errors in your request"
msgstr "သင့်တောင်းဆိုချက်တွင် အတည်ပြုခြင်း အမှားများ"

#: asiantech.link/api/views/authentication/authentication_views.py:391
#: asiantech.link/api/views/authentication/authentication_views.py:501
msgid "The email address is already registered with engineer"
msgstr "ဤအေဂျင်စီကုမ္ပဏီသည် အင်ဂျင်နီယာနှင့် ချိတ်ဆက်ထားပြီးဖြစ်သည်။"

#: asiantech.link/api/views/authentication/authentication_views.py:393
#: asiantech.link/api/views/authentication/authentication_views.py:495
msgid "The email address is already registered with company"
msgstr "အမိ်းပို့ရာနံပါတ်သည် ကုမ္ပဏီမှ အတည်ပြုခံရပြီးပါပြီ။"

#: asiantech.link/api/views/authentication/authentication_views.py:394
msgid "Email is already registered!"
msgstr "အီးမေးလ်ကို မှတ်ပုံတင်ပြီးဖြစ်သည်။"

#: asiantech.link/api/views/authentication/authentication_views.py:436
#: asiantech.link/api/views/authentication/authentication_views.py:440
#: asiantech.link/api/views/authentication/authentication_views.py:465
#: asiantech.link/api/views/authentication/authentication_views.py:469
msgid "Email address or password does not match"
msgstr "အီးမေးလ်လိပ်စာ သို့မဟုတ် စကားဝှက် မမှန်ကန်ပါ။"

#: asiantech.link/api/views/authentication/authentication_views.py:439
#: asiantech.link/api/views/authentication/authentication_views.py:454
#: asiantech.link/api/views/authentication/authentication_views.py:468
msgid "Login failed."
msgstr "အကောင့်ဝင်၍မရပါ။"

#: asiantech.link/api/views/authentication/authentication_views.py:451
#: asiantech.link/api/views/authentication/authentication_views.py:455
msgid "The user ID or password is incorrect."
msgstr "အီးမေးလ်လိပ်စာ သို့မဟုတ် စကားဝှက် မမှန်ကန်ပါ။"

#: asiantech.link/api/views/authentication/authentication_views.py:489
#: asiantech.link/api/views/authentication/authentication_views.py:572
msgid "CAPTCHA verification required"
msgstr "CAPTCHA အတည်ပြုချက် လိုအပ်ပါသည်။"

#: asiantech.link/api/views/authentication/authentication_views.py:507
msgid ""
"The account with this email has been registered with the receiving support "
"organization."
msgstr ""

#: asiantech.link/api/views/authentication/authentication_views.py:698
#: asiantech.link/api/views/authentication/authentication_views.py:700
#: asiantech.link/api/views/authentication/authentication_views.py:738
#: asiantech.link/api/views/authentication/authentication_views.py:740
#: asiantech.link/api/views/authentication/authentication_views.py:809
#: asiantech.link/api/views/authentication/authentication_views.py:827
#, fuzzy
#| msgid "Login failed."
msgid "Login failed!"
msgstr "အကောင့်ဝင်၍မရပါ။"

#: asiantech.link/api/views/authentication/authentication_views.py:811
#, fuzzy
#| msgid "Invalid sex type"
msgid "Invalid SNS type!"
msgstr "မမှန်ကန်သော လိင်အမျိုးအစား"

#: asiantech.link/api/views/authentication/authentication_views.py:921
msgid "Your Verification Code"
msgstr "သင်၏အတည်ပြုကုဒ်"

#: asiantech.link/api/views/authentication/authentication_views.py:926
msgid "Email Verification"
msgstr ""
"စာရင်းသွင်းသည့်အတွက် ကျေးဇူးတင်ပါသည်။ သင်၏မှတ်ပုံတင်ခြင်းကို အပြီးသတ်ရန် အောက်ပါအတည်ပြုကုဒ်ကို အသုံးပြုပါ။"

#: asiantech.link/api/views/authentication/authentication_views.py:929
msgid "Please use the verification code below to complete your login:"
msgstr "သင့်ဝင်ရောက်မှုကို အပြီးသတ်ရန် အောက်ပါအတည်ပြုကုဒ်ကို အသုံးပြုပါ။"

#: asiantech.link/api/views/authentication/authentication_views.py:932
msgid "Note: This code is valid for a limited time."
msgstr "မှတ်ချက်- ဤကုဒ်သည် အချိန်ကန့်သတ်ချက်တစ်ခုအတွက် တရားဝင်သည်။"

#: asiantech.link/api/views/authentication/authentication_views.py:946
msgid "We sent you a code to verify your login"
msgstr "သင့်ဝင်ရောက်မှုကို အတည်ပြုရန် ကုဒ်တစ်ခု သင့်ထံ ပေးပို့ထားပါသည်။"

#: asiantech.link/api/views/authentication/authentication_views.py:968
#: asiantech.link/api/views/authentication/authentication_views.py:978
#: asiantech.link/api/views/authentication/authentication_views.py:1034
msgid "Password Reset Request"
msgstr "စကားဝှက် ပြန်လည်သတ်မှတ်ရန် တောင်းဆိုခြင်း။"

#: asiantech.link/api/views/authentication/authentication_views.py:978
msgid "Dear"
msgstr "မင်္ဂလာပါ"

#: asiantech.link/api/views/authentication/authentication_views.py:978
msgid "password_request_reset_content_1"
msgstr ""
"သင့်စကားဝွက်ကို စကားဝွက်အသစ်ရန်တောင် တောင်းဆိုခြင်းအတွက် တွေ့ရှိခဲ့သည်။ မည်သည့်စာသားနှင့် သင့်ကိုစကားဝွက်ကို "
"လွတ်လပ်စွာအသုံးပြုပါ။"

#: asiantech.link/api/views/authentication/authentication_views.py:979
msgid "password_request_reset_content_2"
msgstr "သင့်စကားဝွက်ကို စကားဝွက်အသစ်မှာ မတင်ပါနဲ့မားပါနဲ့။"

#: asiantech.link/api/views/authentication/authentication_views.py:979
#: asiantech.link/api/views/authentication/authentication_views.py:1035
msgid "passwordResetContent2"
msgstr "ဤတန်ဖိုးတောင် သင်တို့ဝင်ပါက ဒီစကားဝွက်နှင့် ခွင့်ပြုပါလိမ့်မည်။"

#: asiantech.link/api/views/authentication/authentication_views.py:979
msgid "Thank you"
msgstr "ကျေးဇူးတင်ပါတယ်"

#: asiantech.link/api/views/authentication/authentication_views.py:984
msgid "Password reset email sent!"
msgstr "စကားဝှက်ပြန်လည်သတ်မှတ်ရန် အီးမေးလ်ပို့လိုက်ပါပြီ။"

#: asiantech.link/api/views/authentication/authentication_views.py:1019
msgid "Invalid token type!"
msgstr "မမှန်ကန်သော တိုကင်အမျိုးအစား။"

#: asiantech.link/api/views/authentication/authentication_views.py:1026
msgid "Your Password Reset Request"
msgstr "သင့်စကားဝှက်ကို ပြန်လည်သတ်မှတ်ရန် တောင်းဆိုချက်"

#: asiantech.link/api/views/authentication/authentication_views.py:1032
msgid "Password Reset"
msgstr "စကားဝှက်ကို ပြန်လည်သတ်မှတ်ပါ။"

#: asiantech.link/api/views/authentication/authentication_views.py:1034
msgid "Hello"
msgstr "မင်္ဂလာပါ"

#: asiantech.link/api/views/authentication/authentication_views.py:1034
msgid "passwordResetContent1"
msgstr "သင့်စကားဝှက် ပြန်စစ်လိုက်တာသည့်အချက်အလက်ကို ဖြည့်စွက်လိုက်ပါပြီ။ ဒီနေ့မှာ အသစ်တစ်ခုနဲ့ ဝင်ပါ။"

#: asiantech.link/api/views/authentication/authentication_views.py:1035
msgid "passwordResetContent3"
msgstr "သင့်ဒီ စကားဝှက်ကို ပြန်လည်ပြန်သွားပြီးသားမှာ ရှိပါတယ်ဆိုတာကိုအတည်ပြုပါလိမ့်မယ်။"

#: asiantech.link/api/views/authentication/authentication_views.py:1044
msgid "Password reset successfully!"
msgstr "စကားဝှက်ကို အောင်မြင်စွာ ပြင်ဆင်သတ်မှတ်ပါ။"

#: asiantech.link/api/views/authentication/authentication_views.py:1099
msgid "Invalid code or email!"
msgstr "မမှန်ကန်သောကုဒ် သို့မဟုတ် အီးမေးလ်။"

#: asiantech.link/api/views/authentication/authentication_views.py:1103
msgid "Invalid email or password!"
msgstr "မမှန်ကန်သော အီးမေးလ် သို့မဟုတ် စကားဝှက်။"

#: asiantech.link/api/views/authentication/authentication_views.py:1111
#: asiantech.link/api/views/authentication/authentication_views.py:1125
msgid "Logout successfully"
msgstr "အောင်မြင်စွာ ထွက်လိုက်ပါ။"

#: asiantech.link/api/views/authentication/authentication_views.py:1131
#: asiantech.link/api/views/authentication/authentication_views.py:1144
msgid "Captcha generated successfully"
msgstr "Captcha ကို အောင်မြင်စွာ ထုတ်လုပ်ခဲ့သည်။"

#: asiantech.link/api/views/authentication/authentication_views.py:1151
msgid "Check captcha required in login"
msgstr "လော့ဂ်အင်တွင် လိုအပ်သည့် captcha ကို စစ်ဆေးပါ။"

#: asiantech.link/api/views/authentication/authentication_views.py:1211
#: asiantech.link/api/views/authentication/authentication_views.py:1228
#: asiantech.link/api/views/authentication/authentication_views.py:1237
#: asiantech.link/api/views/authentication/authentication_views.py:1256
msgid "Account deleted successfully"
msgstr "အကောင့်ကို အောင်မြင်စွာ ဖျက်သိမ်းပြီးပါပြီ။"

#: asiantech.link/api/views/authentication/authentication_views.py:1258
msgid "error"
msgstr "အမှား"

#: asiantech.link/api/views/chat/chat_views.py:22
#: asiantech.link/api/views/chat/chat_views.py:56
msgid "All messages marked as read"
msgstr ""

#: asiantech.link/api/views/chat/chat_views.py:38
#, fuzzy
#| msgid "Recruitment does not exist"
msgid "Group does not exist."
msgstr "ခေါ်ယူမှု မရှိပါ။"

#: asiantech.link/api/views/chat/chat_views.py:44
msgid "User is not a member of this group."
msgstr ""

#: asiantech.link/api/views/chat/chat_views.py:59
msgid "No messages to mark as read"
msgstr ""

#: asiantech.link/api/views/engineer/engineer_views.py:87
#: asiantech.link/api/views/engineer/engineer_views.py:160
msgid "Agency company not found."
msgstr "အေဂျင်စီကုမ္ပဏီ မတွေ့ပါ။"

#: asiantech.link/api/views/engineer/engineer_views.py:94
msgid "This agency company is already linked with the engineer."
msgstr "ဤအေဂျင်စီကုမ္ပဏီသည် အင်ဂျင်နီယာနှင့် ချိတ်ဆက်ထားပြီးဖြစ်သည်။"

#: asiantech.link/api/views/engineer/engineer_views.py:169
msgid "This agency company has already been deleted."
msgstr "ဤအေဂျင်စီကုမ္ပဏီကို ဖျက်ပြီးဖြစ်သည်။"

#: asiantech.link/api/views/engineer/engineer_views.py:176
msgid "Agency company successfully deleted."
msgstr "အေဂျင်စီကုမ္ပဏီကို အောင်မြင်စွာ ဖျက်လိုက်ပါပြီ။"

#: asiantech.link/api/views/engineer/engineer_views.py:179
msgid "This agency company is not linked with the engineer."
msgstr "ဤအေဂျင်စီကုမ္ပဏီသည် အင်ဂျင်နီယာနှင့် မချိတ်ဆက်ပါ။"

#: asiantech.link/api/views/engineer/engineer_views.py:231
#: asiantech.link/api/views/general/general_company_views.py:477
msgid "Invalid ordering field."
msgstr "မမှန်ကန်သော ကုဒ် သို့မဟုတ် အီးမေးလ်!"

#: asiantech.link/api/views/engineer/engineer_views.py:239
#: asiantech.link/api/views/engineer/engineer_views.py:368
msgid "Invalid recruit_progress_code"
msgstr "မမှန်ကန်သော အီးမေးလ် သို့မဟုတ် စကားဝှက်!"

#: asiantech.link/api/views/engineer/engineer_views.py:396
#: asiantech.link/api/views/general/general_company_views.py:352
#: asiantech.link/api/views/profile/profile_views.py:233
#: asiantech.link/api/views/profile/profile_views.py:326
#: asiantech.link/api/views/profile/profile_views.py:419
#: asiantech.link/api/views/profile/profile_views.py:512
#: asiantech.link/api/views/profile/profile_views.py:605
msgid "User not found"
msgstr "အသုံးပြုသူကို ရှာမတွေ့ပါ။"

#: asiantech.link/api/views/general/general_company_views.py:63
msgid "Email already exists"
msgstr "အီးမေးလ် မှတ်ပုံတင်ပြီးသားပါ!"

#: asiantech.link/api/views/general/general_company_views.py:93
#: asiantech.link/api/views/host_company/host_company_views.py:69
#: asiantech.link/api/views/host_company/host_company_views.py:106
msgid "User does not have company"
msgstr "အသုံးပြုသူတွင် ကုမ္ပဏီမရှိပါ။"

#: asiantech.link/api/views/general/general_company_views.py:100
#: asiantech.link/api/views/general/general_company_views.py:125
#: asiantech.link/api/views/host_company/host_company_views.py:75
#: asiantech.link/api/views/host_company/host_company_views.py:112
#: asiantech.link/api/views/host_support_agency_company/host_support_agency_company_views.py:45
#: asiantech.link/api/views/host_support_agency_company/host_support_agency_company_views.py:72
#: asiantech.link/api/views/media/company_media_views.py:59
#: asiantech.link/api/views/media/company_media_views.py:122
msgid "Company not found"
msgstr "အသုံးပြုသူ မတွေ့ပါ"

#: asiantech.link/api/views/general/general_company_views.py:274
msgid "Filter saved successfully"
msgstr "စစ်ထုတ်မှုကို အောင်မြင်စွာ သိမ်းဆည်းပြီးပါပြီ။"

#: asiantech.link/api/views/general/general_company_views.py:304
msgid "Filter deleted successfully"
msgstr "ဖော်ပြချက်ကို အောင်မြင်စွာ သိမ်းဆည်းပြီးပါပြီ။"

#: asiantech.link/api/views/general/general_company_views.py:327
msgid "You can't update this status!"
msgstr "သင်သည် ဤအခြေအနေကို အပ်ဒိတ်လုပ်၍မရပါ။"

#: asiantech.link/api/views/general/general_company_views.py:485
#: asiantech.link/api/views/general/general_company_views.py:670
#: asiantech.link/api/views/general/general_company_views.py:831
msgid "You don't have permission to access this company"
msgstr ""

#: asiantech.link/api/views/general/general_company_views.py:487
#: asiantech.link/api/views/general/general_company_views.py:668
#: asiantech.link/api/views/general/general_company_views.py:829
msgid "Host company not found"
msgstr "အသုံးပြုသူ မတွေ့ပါ"

#: asiantech.link/api/views/general/general_company_views.py:573
#: asiantech.link/api/views/general/general_company_views.py:576
#: asiantech.link/api/views/general/general_company_views.py:578
#: asiantech.link/api/views/general/general_company_views.py:627
#: asiantech.link/api/views/general/general_company_views.py:630
#: asiantech.link/api/views/general/general_company_views.py:632
#: asiantech.link/api/views/general/general_company_views.py:792
#: asiantech.link/api/views/general/general_company_views.py:795
#: asiantech.link/api/views/general/general_company_views.py:797
#: asiantech.link/api/views/general/general_company_views.py:873
#: asiantech.link/api/views/general/general_company_views.py:876
#: asiantech.link/api/views/general/general_company_views.py:878
msgid "You don't have permission to access this apply"
msgstr ""

#: asiantech.link/api/views/general/general_company_views.py:592
#: asiantech.link/api/views/general/general_company_views.py:641
#: asiantech.link/api/views/general/general_company_views.py:702
#: asiantech.link/api/views/general/general_company_views.py:806
#: asiantech.link/api/views/general/general_company_views.py:843
#: asiantech.link/api/views/general/general_company_views.py:949
#: asiantech.link/api/views/host_company/host_company_views.py:340
msgid "Apply not found"
msgstr "အသုံးပြုသူ မတွေ့ပါ"

#: asiantech.link/api/views/general/general_company_views.py:946
msgid "Something went wrong!"
msgstr "တစ်ခုခုမှားသွားသည်။"

#: asiantech.link/api/views/general/general_company_views.py:968
#: asiantech.link/api/views/host_support_agency_company/host_support_agency_company_views.py:205
#: asiantech.link/api/views/recruit/recruitment_views.py:49
#: asiantech.link/api/views/recruit/recruitment_views.py:155
msgid "You do not own the company"
msgstr "မင်းကုမ္ပဏီက မပိုင်ဘူး။"

#: asiantech.link/api/views/general/general_views.py:46
msgid "Current language is: "
msgstr "လက်ရှိဘာသာစကားမှာ: "

#: asiantech.link/api/views/host_company/host_company_views.py:88
msgid "User already subscribed"
msgstr "အသုံးပြုသူ အတည်ပြုပြီးပါပြီ!"

#: asiantech.link/api/views/host_company/host_company_views.py:120
msgid "User not subscribed"
msgstr "အသုံးပြုသူ မတွေ့ပါ"

#: asiantech.link/api/views/host_company/host_company_views.py:128
msgid "Data not found"
msgstr "အသုံးပြုသူ မတွေ့ပါ"

#: asiantech.link/api/views/host_company/host_company_views.py:143
msgid "User does not have company."
msgstr "အသုံးပြုသူတွင် ကုမ္ပဏီမရှိပါ။"

#: asiantech.link/api/views/host_company/host_company_views.py:172
msgid "You have already make a request interview"
msgstr ""

#: asiantech.link/api/views/host_company/host_company_views.py:213
#: asiantech.link/api/views/host_support_agency_company/host_support_agency_company_views.py:161
msgid "You have requested an interview for this engineer."
msgstr ""

#: asiantech.link/api/views/host_company/host_company_views.py:331
msgid "Notification of acceptance has been sent."
msgstr "လက်ခံကြောင်း အကြောင်းကြားစာ ပေးပို့ထားပါသည်။"

#: asiantech.link/api/views/host_company/host_company_views.py:338
msgid "Invalid status"
msgstr "အတည်ပြု ကုဒ်"

#: asiantech.link/api/views/host_support_agency_company/host_support_agency_company_views.py:102
#: asiantech.link/api/views/host_support_agency_company/host_support_agency_company_views.py:213
msgid "Host company is not belong to you"
msgstr ""

#: asiantech.link/api/views/host_support_agency_company/host_support_agency_company_views.py:109
msgid "This engineer already make a interview to host company"
msgstr "အမိ်းပို့ရာနံပါတ်သည် ကုမ္ပဏီမှ အတည်ပြုခံရပြီးပါပြီ။"

#: asiantech.link/api/views/host_support_agency_company/host_support_agency_company_views.py:247
msgid "You are not authorized to access this endpoint"
msgstr ""

#: asiantech.link/api/views/media/admin_upload_avatar_engineer_view.py:53
#: asiantech.link/api/views/media/admin_upload_avatar_engineer_view.py:77
#: asiantech.link/api/views/media/avatar_views.py:44
#: asiantech.link/api/views/media/avatar_views.py:68
#: asiantech.link/api/views/media/company_media_views.py:48
#: asiantech.link/api/views/media/company_media_views.py:81
#: asiantech.link/api/views/media/company_media_views.py:110
#: asiantech.link/api/views/media/company_media_views.py:174
#: asiantech.link/api/views/media/contract_media_view.py:56
#: asiantech.link/api/views/media/contract_media_view.py:117
#: asiantech.link/api/views/media/contract_media_view.py:148
#: asiantech.link/api/views/media/contract_media_view.py:212
#: asiantech.link/api/views/media/passport_views.py:44
#: asiantech.link/api/views/media/passport_views.py:69
#: asiantech.link/api/views/media/recruit_media_views.py:44
#: asiantech.link/api/views/media/recruit_media_views.py:65
msgid "Image uploaded successfully"
msgstr "အသုံးပြုသူ ဖန်တီးမှု အောင်မြင်ပါသည်"

#: asiantech.link/api/views/media/admin_upload_avatar_engineer_view.py:92
#: asiantech.link/api/views/media/admin_upload_avatar_engineer_view.py:110
#: asiantech.link/api/views/media/avatar_views.py:74
#: asiantech.link/api/views/media/avatar_views.py:92
msgid "Image deleted successfully"
msgstr "အသုံးပြုသူ ဖန်တီးမှု အောင်မြင်ပါသည်"

#: asiantech.link/api/views/media/company_media_views.py:53
#: asiantech.link/api/views/media/company_media_views.py:116
#: asiantech.link/api/views/media/recruit_media_views.py:49
msgid "You are not a company"
msgstr "မင်းဟာ ကုမ္ပဏီမဟုတ်ဘူး။"

#: asiantech.link/api/views/media/company_media_views.py:139
msgid "Index must be 0,1,2"
msgstr "အညွှန်းသည် 0၊1၊2 ဖြစ်ရမည်။"

#: asiantech.link/api/views/media/contract_media_view.py:68
msgid "You can not upload contract image now, recruit progress code !=60"
msgstr "စာချုပ်ပုံအား ယခု အပ်လုဒ်လုပ်၍ မရပါ၊ တိုးတက်မှုကုဒ် !=60 ကို စုဆောင်းပါ။"

#: asiantech.link/api/views/media/contract_media_view.py:167
msgid "You are not authorized to upload contract image"
msgstr ""

#: asiantech.link/api/views/media/contract_media_view.py:171
msgid "You can not upload contract image now, status progress code is invalid"
msgstr "စာချုပ်ပုံအား ယခု အပ်လုဒ်လုပ်၍မရပါ၊ အခြေအနေတိုးတက်မှုကုဒ်သည် မမှန်ကန်ပါ။"

#: asiantech.link/api/views/media/passport_views.py:75
#: asiantech.link/api/views/media/passport_views.py:93
msgid "Passport image deleted successfully"
msgstr "ပတ်စပို့ ပုံဓာတ်ပုံ ဖျက်သိမ်းပြီးပါပြီ။"

#: asiantech.link/api/views/notify/set_read_notify_view.py:34
msgid "Invalid request"
msgstr "အတည်ပြု ကုဒ်"

#: asiantech.link/api/views/profile/cv_views.py:52
#: asiantech.link/api/views/profile/cv_views.py:321
#: asiantech.link/api/views/profile/cv_views.py:389
#, fuzzy
#| msgid "Image uploaded successfully"
msgid "CV uploaded successfully"
msgstr "အသုံးပြုသူ ဖန်တီးမှု အောင်မြင်ပါသည်"

#: asiantech.link/api/views/profile/cv_views.py:58
msgid "File size must be less than 5MB"
msgstr "ဖိုင်အရှည်သည် 5MB ထက် နည်းနည်းသောက် ဖြစ်ရမည်။"

#: asiantech.link/api/views/profile/cv_views.py:288
msgid "Invalid document type: not a resume"
msgstr "မမှန်ကန်သောစာရွက်စာတမ်းအမျိုးအစား- ကိုယ်ရေးရာဇဝင်မဟုတ်ပါ။"

#: asiantech.link/api/views/profile/cv_views.py:331
#, fuzzy
#| msgid "User not found"
msgid "CV not found"
msgstr "အသုံးပြုသူကို ရှာမတွေ့ပါ။"

#: asiantech.link/api/views/profile/profile_views.py:86
#, fuzzy
#| msgid "Email is already registered!"
msgid "This email is already in use."
msgstr "အီးမေးလ်ကို မှတ်ပုံတင်ပြီးဖြစ်သည်။"

#: asiantech.link/api/views/profile/profile_views.py:146
msgid "Password updated successfully"
msgstr "စကားဝှက်ကို အောင်မြင်စွာ မွမ်းမံပြီးပါပြီ။"

#: asiantech.link/api/views/profile/profile_views.py:162
#: asiantech.link/api/views/profile/profile_views.py:184
msgid "New password must be different from the current password."
msgstr "စကားဝှက်အသစ်သည် လက်ရှိစကားဝှက်နှင့် ကွဲပြားရပါမည်။"

#: asiantech.link/api/views/profile/profile_views.py:182
msgid "Password updated successfully."
msgstr "စာဝှက်နံပါတ် ပြောင်းလဲမည်ဖြစ်ပါသည်။"

#: asiantech.link/api/views/profile/profile_views.py:186
msgid "Invalid current password."
msgstr "မမှန်ကန်သော အီးမေးလ် သို့မဟုတ် စကားဝှက်!"

#: asiantech.link/api/views/recruit/recruitment_views.py:82
#: asiantech.link/api/views/recruit/recruitment_views.py:106
#: asiantech.link/api/views/recruit/recruitment_views.py:115
#: asiantech.link/api/views/recruit/recruitment_views.py:136
#: asiantech.link/api/views/recruit/recruitment_views.py:583
#: asiantech.link/api/views/recruit/recruitment_views.py:658
#: asiantech.link/api/views/recruit/recruitment_views.py:705
#: asiantech.link/api/views/recruit/recruitment_views.py:753
msgid "Recruitment does not exist"
msgstr "ခေါ်ယူမှု မရှိပါ။"

#: asiantech.link/api/views/recruit/recruitment_views.py:300
#: asiantech.link/api/views/recruit/recruitment_views.py:436
msgid "Error"
msgstr "အမှား"

#: asiantech.link/api/views/recruit/recruitment_views.py:460
msgid "Failed"
msgstr "မအောင်မြင်"

#: asiantech.link/api/views/recruit/recruitment_views.py:495
msgid "ComCompany not found"
msgstr "ကုမ္ပဏီကို ရှာမတွေ့ပါ။"

#: asiantech.link/api/views/recruit/recruitment_views.py:502
msgid "MapEngAgc not found"
msgstr "အသုံးပြုသူ မတွေ့ပါ"

#: asiantech.link/api/views/recruit/recruitment_views.py:516
msgid "Already applied"
msgstr "လျှောက်ထားပြီးဖြစ်သည်။"

#: asiantech.link/api/views/recruit/recruitment_views.py:533
msgid "You have already applied for this recruitment"
msgstr ""

#: asiantech.link/api/views/recruit/recruitment_views.py:656
msgid "Recruitment does not exist or you do not have access to it"
msgstr "ခေါ်ယူမှု မရှိပါ သို့မဟုတ် သင်ဝင်ရောက်ခွင့်မရှိပါ။"

#: asiantech.link/api/views/recruit/recruitment_views.py:679
#: asiantech.link/api/views/recruit/recruitment_views.py:724
msgid "Recruit ID is required"
msgstr "Recruit ID လိုအပ်ပါသည်။"

#: asiantech.link/api/views/recruit/recruitment_views.py:687
#: asiantech.link/api/views/recruit/recruitment_views.py:776
#: asiantech.link/api/views/recruit/recruitment_views.py:824
msgid "No application found"
msgstr "အပလီကေးရှင်းမတွေ့ပါ။"

#: asiantech.link/api/views/recruit/recruitment_views.py:698
#: asiantech.link/api/views/recruit/recruitment_views.py:739
msgid "Invalid recruit progress code to cancel"
msgstr "ပယ်ဖျက်ရန် စုဆောင်းရေး တိုးတက်မှုကုဒ် မမှန်ကန်ပါ။"

#: asiantech.link/api/views/recruit/recruitment_views.py:737
msgid "Interview datetime is required"
msgstr "အင်တာဗျူးရက်ချိန်း လိုအပ်ပါသည်။"

#: asiantech.link/api/views/recruit/recruitment_views.py:798
msgid "Invalid recruit progress code to sign contract"
msgstr "စာချုပ်ကိုလက်မှတ်ထိုးရန် စုဆောင်းမှုတိုးတက်မှုကုဒ် မမှန်ကန်ပါ။"

#: asiantech.link/api/views/recruit/recruitment_views.py:831
msgid "No application found for the provided recruitment ID"
msgstr "ပေးထားသော စုဆောင်းရေး ID အတွက် လျှောက်လွှာ မတွေ့ပါ။"

#: asiantech.link/api/views/recruit/recruitment_views.py:837
msgid "No accept sign found for the application"
msgstr "လျှောက်လွှာအတွက် လက်ခံသည့် ဆိုင်းဘုတ် မတွေ့ပါ။"

#: asiantech.link/core/settings/common.py:132
msgid "English"
msgstr "အင်္ဂလိပ်"

#: asiantech.link/core/settings/common.py:133
msgid "Japanese"
msgstr "ဂျပန်"

#: asiantech.link/core/settings/common.py:134
msgid "Myanmar"
msgstr "မြန်မာ"

#: asiantech.link/core/settings/common.py:135
msgid "Indonesian"
msgstr "အင်ဒိုနီးရှား"

#: asiantech.link/core/settings/common.py:136
msgid "Nepali"
msgstr "နီပေါ"

#: asiantech.link/core/settings/common.py:137
msgid "Vietnamese"
msgstr "ဗီယက်နမ်"

#: asiantech.link/utils/permissions.py:22
#: asiantech.link/utils/permissions.py:36
msgid "Account has not been verified"
msgstr "အကောင့်ကို အတည်မပြုရသေးပါ။"

#: asiantech.link/utils/permissions.py:39
#: asiantech.link/utils/permissions.py:53
#: asiantech.link/utils/permissions.py:64
#: asiantech.link/utils/permissions.py:83
#: asiantech.link/utils/permissions.py:96
#: asiantech.link/utils/permissions.py:107
#: asiantech.link/utils/permissions.py:120
#: asiantech.link/utils/permissions.py:132
msgid "Access denied"
msgstr ""

#: asiantech.link/utils/utils.py:508 asiantech.link/utils/utils.py:644
msgid "Fresher"
msgstr "အသစ်တက်လာသူ"

#: asiantech.link/utils/utils.py:510 asiantech.link/utils/utils.py:646
msgid "Junior"
msgstr "အငယ်တန်း"

#: asiantech.link/utils/utils.py:512 asiantech.link/utils/utils.py:648
msgid "Middle"
msgstr "အလယ်အလတ်"

#: asiantech.link/utils/utils.py:514 asiantech.link/utils/utils.py:650
msgid "Senior"
msgstr "အငယ်စားကြီး"

#: asiantech.link/utils/validators.py:29
msgid "Email is too long"
msgstr "အီးမေးလ်သည် ရှည်လွန်းသည်။"

#: asiantech.link/utils/validators.py:35
msgid "Email must contain a single '@' symbol"
msgstr "အီးမေးလ်တွင် '@' သင်္ကေတ တစ်ခု ပါရမည်"

#: asiantech.link/utils/validators.py:39
msgid "Local part of the email is too long (>= 64 characters)"
msgstr "အီးမေးလ်၏ ဒေသပိုင်း အပိုင်း အလွန်ရှည်လျားသည် (>= 64 အက္ခရာများ)"

#: asiantech.link/utils/validators.py:43
msgid "Email contains invalid characters"
msgstr "အီးမေးလ်တွင် မမှန်ကန်သော အက္ခရာများ ပါဝင်သည်"

#: asiantech.link/utils/validators.py:47
msgid "Email contains consecutive periods"
msgstr "အီးမေးလ်တွင် အစဉ်တစိုက်အချိန်များ ပါဝင်သည်"

#: asiantech.link/utils/validators.py:51
msgid ""
"Email has periods at invalid positions (start/end of local part or end of "
"email)"
msgstr ""
"အီးမေးလ်တွင် မမှန်ကန်သောနေရာများတွင် အချိန်များရှိသည် (ဒေသပိုင်း အစ/အဆုံး သို့မဟုတ် အီးမေးလ် အဆုံး)"

#: asiantech.link/utils/validators.py:55
msgid "Domain part of the email should not be an IP address"
msgstr "အီးမေးလ်၏ ဒိုမိန်း အပိုင်းသည် IP လိပ်စာ မဖြစ်သင့်ပါ"

#: asiantech.link/utils/validators.py:57
msgid "Email is valid"
msgstr "အီးမေးလ်သည် မှန်ကန်ပါသည်"

#: asiantech.link/utils/validators.py:63
msgid "Password length must be between 8 and 20 characters"
msgstr "စကားဝှက် အရှည်သည် 8 နှင့် 20 အက္ခရာများအကြား ဖြစ်ရမည်"

#: asiantech.link/utils/validators.py:68
msgid "Password contains full-width characters"
msgstr "စကားဝှက်တွင် အပြည့်အဝ အက္ခရာများ ပါဝင်သည်"

#: asiantech.link/utils/validators.py:72
msgid "Password must contain at least one uppercase letter"
msgstr "စကားဝှက်တွင် အနည်းဆုံး စာလုံးအကြီးတစ်လုံး ပါဝင်ရပါမည်။"

#: asiantech.link/utils/validators.py:76
msgid "Password must contain at least one lowercase letter"
msgstr "စကားဝှက်တွင် အနည်းဆုံး စာလုံးအသေးတစ်ခု ပါဝင်ရပါမည်။"

#: asiantech.link/utils/validators.py:80
msgid "Password must contain at least one number"
msgstr "စကားဝှက်တွင် အနည်းဆုံး နံပါတ်တစ်ခု ပါဝင်ရပါမည်။"

#: asiantech.link/utils/validators.py:84
msgid "Password must contain at least one special character"
msgstr "စကားဝှက်တွင် အနည်းဆုံး အထူးသင်္ကေတတစ်ခု ပါရှိရမည်။"

#: asiantech.link/utils/validators.py:86
msgid "Password is valid"
msgstr "ကစားဝှက်မှန်ကန်ပါသည်။"

#~ msgid "Expert"
#~ msgstr "ကျွမ်းကျင်သူ"

#~ msgid "Facebook authentication successful"
#~ msgstr "ဖေ့စ်ဘုတ် အတည်ပြုမှု အောင်မြင်ပါသည်။"

#~ msgid "Linkedin authentication successful"
#~ msgstr "LinkedIn အတည်ပြုမှု အောင်မြင်ပါသည်။"

#~ msgid "Zalo authentication successful"
#~ msgstr "Zalo အတည်ပြုမှု အောင်မြင်ပါသည်။"

#~ msgid "Get user info with zalo id"
#~ msgstr "Zalo ID ဖြင့် အသုံးပြုသူအချက်အလက်များရယူပါ။"

#~ msgid "Get user info with whatsapp number"
#~ msgstr "WhatsApp နံပါတ်ဖြင့် အသုံးပြုသူအချက်အလက်များရယူပါ။"

#~ msgid "WhatsApp authentication successful"
#~ msgstr "WhatsApp အတည်ပြုမှု အောင်မြင်ပါသည်။"

#~ msgid "Failed to read PDF content"
#~ msgstr "PDF အကြောင်းအရာ ဖတ်ရန် မအောင်မြင်ပါ"

#~ msgid "Email is too long (>= 201 characters)"
#~ msgstr "အီးမေးလ် ရှည်လွန်းသည် (>= 201 အက္ခရာများ)"
