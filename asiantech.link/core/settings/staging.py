from .common import *
from datetime import timedelta
import os

# Security settings
os.environ.setdefault(
    'SECRET_KEY', 'django-insecure-h!!db#tf4=nulyux&e7yohnb7q4^s_f&%td@_syg_fz&f+fo$g')
os.environ.setdefault("CRYPTOGRAPHY_KEY",
                      "3XA0YZtU9uxIcfgXqrOG8clddzWPkcz6imnG3r4d3Fo=")
SECRET_KEY = os.getenv('SECRET_KEY')
DEBUG = True
ALLOWED_HOSTS = ['*']

# Database configuration
DATABASES = {
    'default': {
        'ENGINE': 'django.db.backends.mysql',
        'NAME': 'staging_asiantechlink',
        'USER': 'root',
        'PASSWORD': 'Fois_0327',
        'HOST': '***********',
        'PORT': '3306'
    }
}

# REST Framework settings
REST_FRAMEWORK = {
    'NON_FIELD_ERRORS_KEY': 'error',
    'DEFAULT_AUTHENTICATION_CLASSES': (
        'rest_framework_simplejwt.authentication.JWTAuthentication',
        'rest_framework.authentication.SessionAuthentication',
    ),
    'DEFAULT_RENDERER_CLASSES': (
        "rest_framework.renderers.JSONRenderer",
    )
}

# JWT settings
SIMPLE_JWT = {
    'AUTH_HEADER_TYPES': ('Bearer',),
    'ACCESS_TOKEN_LIFETIME': timedelta(days=1),
    'REFRESH_TOKEN_LIFETIME': timedelta(days=30),
    'USER_ID_FIELD': 'user_id',
    'USER_ID_CLAIM': 'user_id',
    'BLACKLIST_AFTER_ROTATION': True,
    'UPDATE_LAST_LOGIN': True,
    'ROTATE_REFRESH_TOKENS': True,
}

# CORS settings
CORS_ORIGIN_ALLOW_ALL = True
CORS_ALLOW_CREDENTIALS = True
CORS_ALLOWED_ORIGINS = [
    "http://localhost:3000",
    "http://127.0.0.1:3000",
    "http://localhost:8000",
    "http://127.0.0.1:8000",
    "http://localhost",
    "https://staging.asiantech.link",
    "http://staging.asiantech.link",
    "http://***********"
]

CORS_ALLOW_METHODS = [
    'DELETE',
    'GET',
    'OPTIONS',
    'PATCH',
    'POST',
    'PUT',
]

CORS_ALLOW_HEADERS = [
    'accept',
    'accept-encoding',
    'authorization',
    'content-type',
    'dnt',
    'origin',
    'user-agent',
    'x-csrftoken',
    'x-requested-with',
    'access-control-allow-methods',
    'access-control-allow-origin',
    'access-control-allow-headers',
    'access-control-allow-credentials',
    'referral-agency-currency-key',
    '*',
]

CORS_EXPOSE_HEADERS = [
    'content-type',
    'x-csrftoken',
    'referral-agency-currency-key',
]

# SSL/HTTPS settings
SECURE_PROXY_SSL_HEADER = ('HTTP_X_FORWARDED_PROTO', 'https')
SECURE_SSL_REDIRECT = True
SESSION_COOKIE_SECURE = True
CSRF_COOKIE_SECURE = True
