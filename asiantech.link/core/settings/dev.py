from .common import *
from datetime import timedelta
import os

# Security settings
os.environ.setdefault(
    'SECRET_KEY', 'django-insecure-h!!db#tf4=nulyux&e7yohnb7q4^s_f&%td@_syg_fz&f+fo$g')
os.environ.setdefault("CRYPTOGRAPHY_KEY",
                      "5nPgePboAByg1BJBXkFCTPmPU2ejvmTl3zJNanqQ8Ek=")
os.environ.setdefault("LOG_AI_RECOMMENDATION_REPORT", "True")
SECRET_KEY = os.getenv('SECRET_KEY')
DEBUG = True
ALLOWED_HOSTS = ['*']

# Database configuration
DATABASES = {
    'default': {
        'ENGINE': 'django.db.backends.mysql',
        'NAME': 'dev_asiantechlink',
        'USER': 'dev_user',
        'PASSWORD': 'C8UHw36n5Zgfz4m',
        'HOST': '*************',
        'PORT': '3307'
    }
}

# REST Framework settings
REST_FRAMEWORK = {
    'NON_FIELD_ERRORS_KEY': 'error',
    'DEFAULT_AUTHENTICATION_CLASSES': (
        'rest_framework_simplejwt.authentication.JWTAuthentication',
        'rest_framework.authentication.SessionAuthentication',
    ),
    'DEFAULT_RENDERER_CLASSES': (
        "rest_framework.renderers.JSONRenderer",
    )
}

# JWT settings
SIMPLE_JWT = {
    'AUTH_HEADER_TYPES': ('Bearer',),
    'ACCESS_TOKEN_LIFETIME': timedelta(days=1),
    'REFRESH_TOKEN_LIFETIME': timedelta(days=30),
    'USER_ID_FIELD': 'user_id',
    'USER_ID_CLAIM': 'user_id',
    'BLACKLIST_AFTER_ROTATION': True,
    'UPDATE_LAST_LOGIN': True,
    'ROTATE_REFRESH_TOKENS': True,
}

# CORS settings
CORS_ORIGIN_ALLOW_ALL = True
CORS_ALLOW_CREDENTIALS = True
CORS_ALLOWED_ORIGINS = [
    "http://localhost:3000",
    "http://127.0.0.1:3000",
    "http://localhost:8000",
    "http://127.0.0.1:8000",
    "http://localhost",
]

CORS_ALLOW_METHODS = [
    'DELETE',
    'GET',
    'OPTIONS',
    'PATCH',
    'POST',
    'PUT',
]

CORS_ALLOW_HEADERS = [
    'accept',
    'accept-encoding',
    'authorization',
    'content-type',
    'dnt',
    'origin',
    'user-agent',
    'x-csrftoken',
    'x-requested-with',
    'access-control-allow-methods',
    'access-control-allow-origin',
    'access-control-allow-headers',
    'access-control-allow-credentials',
    'referral-agency-currency-key',
    '*',
]

CORS_EXPOSE_HEADERS = [
    'content-type',
    'x-csrftoken',
    'referral-agency-currency-key',
]
