from .common import *
import os
from datetime import timedelta


os.environ.setdefault(
    'SECRET_KEY', 'django-insecure-h!!db#tf4=nulyux&e7yohnb7q4^s_f&%td@_syg_fz&f+fo$g')
os.environ.setdefault("SHOW_LOG_SERIALIZER", "False")
os.environ.setdefault("CRYPTOGRAPHY_KEY",
                      "5Ks94Qz6-eLJfVa9014JgzseP95aY5t8sYpiXTCSP-E=")
SECRET_KEY = os.getenv('SECRET_KEY')
DEBUG = False
# ALLOWED_HOSTS = ['your_domain.com', 'www.your_domain.com']
ALLOWED_HOSTS = ['*']

DATABASES = {
    'default': {
        'ENGINE': 'django.db.backends.mysql',
        'NAME': 'asiantechlink',
        'USER': 'root',
        'PASSWORD': 'Fois_0327',
        'HOST': 'localhost',
        'PORT': '3306'
    }
}

REST_FRAMEWORK = {
    'NON_FIELD_ERRORS_KEY': 'error',
    'DEFAULT_AUTHENTICATION_CLASSES': (
        'rest_framework_simplejwt.authentication.JWTAuthentication',
    ),
    'DEFAULT_RENDERER_CLASSES': (
        "rest_framework.renderers.JSONRenderer",
    )
}

SIMPLE_JWT = {
    'AUTH_HEADER_TYPES': ('Bearer',),
    'ACCESS_TOKEN_LIFETIME': timedelta(days=1),
    'REFRESH_TOKEN_LIFETIME': timedelta(days=30),
    'USER_ID_FIELD': 'user_id',
    'USER_ID_CLAIM': 'user_id',
    'BLACKLIST_AFTER_ROTATION': True,
    'UPDATE_LAST_LOGIN': True,
    'ROTATE_REFRESH_TOKENS': True,
}

SECURE_PROXY_SSL_HEADER = ('HTTP_X_FORWARDED_PROTO', 'https')

SECURE_SSL_REDIRECT = True
SESSION_COOKIE_SECURE = True
CSRF_COOKIE_SECURE = True
