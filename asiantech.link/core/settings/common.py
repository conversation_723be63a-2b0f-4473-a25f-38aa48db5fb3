"""
Django settings for core project.

Generated by 'django-admin startproject' using Django 5.0.6.

For more information on this file, see
https://docs.djangoproject.com/en/5.0/topics/settings/

For the full list of settings and their values, see
https://docs.djangoproject.com/en/5.0/ref/settings/
"""

from pathlib import Path
import os
import datetime
import logging
import logging.config
from django.utils.translation import gettext_lazy as _
from datetime import timedelta, datetime
from django.utils import timezone as tz
from logging.handlers import TimedRotatingFileHandler

# Build paths inside the project like this: BASE_DIR / 'subdir'.
BASE_DIR = Path(__file__).resolve().parent.parent.parent
CORS_ORIGIN_ALLOW_ALL = True
# Application definition

INSTALLED_APPS = [
    "django.contrib.admin",
    "django.contrib.auth",
    "django.contrib.contenttypes",
    "django.contrib.sessions",
    "django.contrib.messages",
    "django.contrib.staticfiles",
    'api',
    'rest_framework',
    'drf_yasg',
    'corsheaders',
    "rest_framework_simplejwt",
    'rest_framework_simplejwt.token_blacklist',
    'captcha'
]

MIDDLEWARE = [
    'corsheaders.middleware.CorsMiddleware',
    "django.middleware.security.SecurityMiddleware",
    "django.contrib.sessions.middleware.SessionMiddleware",
    "django.middleware.common.CommonMiddleware",
    "django.middleware.csrf.CsrfViewMiddleware",
    "django.contrib.auth.middleware.AuthenticationMiddleware",
    "django.contrib.messages.middleware.MessageMiddleware",
    "django.middleware.clickjacking.XFrameOptionsMiddleware",
    'utils.middleware.LoggingMiddleware',
    'django.middleware.locale.LocaleMiddleware',
    'utils.middleware.LanguageMiddleware',
    'utils.middleware.CheckBlacklistMiddleware',
]
REST_FRAMEWORK = {
    'EXCEPTION_HANDLER': 'utils.middleware.custom_exception_handler',
    'DEFAULT_AUTHENTICATION_CLASSES': (
        'rest_framework_simplejwt.authentication.JWTAuthentication',
    ),
    'DEFAULT_PAGINATION_CLASS': 'rest_framework.pagination.CursorPagination',
    'PAGE_SIZE': 20

}

ROOT_URLCONF = "core.urls"

TEMPLATES = [
    {
        "BACKEND": "django.template.backends.django.DjangoTemplates",
        "DIRS": [],
        "APP_DIRS": True,
        "OPTIONS": {
            "context_processors": [
                "django.template.context_processors.debug",
                "django.template.context_processors.request",
                "django.contrib.auth.context_processors.auth",
                "django.contrib.messages.context_processors.messages",
            ],
        },
    },
]

WSGI_APPLICATION = "core.wsgi.application"


# Password validation
# https://docs.djangoproject.com/en/5.0/ref/settings/#auth-password-validators

AUTH_PASSWORD_VALIDATORS = [
    {
        "NAME": "django.contrib.auth.password_validation.UserAttributeSimilarityValidator",
    },
    {
        "NAME": "django.contrib.auth.password_validation.MinimumLengthValidator",
    },
    {
        "NAME": "django.contrib.auth.password_validation.CommonPasswordValidator",
    },
    {
        "NAME": "django.contrib.auth.password_validation.NumericPasswordValidator",
    },
]

# Swagger settings
SWAGGER_SETTINGS = {
    'USE_SESSION_AUTH': False,
    'SECURITY_DEFINITIONS': {
        'Bearer': {
            'type': 'apiKey',
            'name': 'Authorization',
            'in': 'header'
        }
    }
}

# Internationalization
# https://docs.djangoproject.com/en/5.0/topics/i18n/

LANGUAGE_CODE = "ja"

TIME_ZONE = "UTC"

USE_I18N = True

USE_TZ = True

# Available Languages
LANGUAGES = [
    ('en', _('English')),
    ('ja', _('Japanese')),
    ('my', _('Myanmar')),
    ('id', _('Indonesian')),
    ('ne', _('Nepali')),
    ('vi', _('Vietnamese')),
]

# Locales available path
LOCALE_PATHS = [
    os.path.join(BASE_DIR, 'locale/')
]


# Static files (CSS, JavaScript, Images)
# https://docs.djangoproject.com/en/5.0/howto/static-files/

STATIC_URL = "api/static/"

# Default primary key field type
# https://docs.djangoproject.com/en/5.0/ref/settings/#default-auto-field

DEFAULT_AUTO_FIELD = "django.db.models.BigAutoField"

AUTH_USER_MODEL = 'api.User'


# SMTP Settings
SMTP_SETTINGS = {
    "SMTP_SERVER": "smtp.gmail.com",
    "SMTP_PORT": 587,
    "SENDER_MAIL": "<EMAIL>",
    "PASSWORD": "rctl fnar bukr ozrf"

}

# Settings
MAX_EXPIRED_CODE = timedelta(minutes=30)


# Assets
EMAIL_TEMPLATE_PATH = os.path.join(
    BASE_DIR, 'api/static/verification_code_email.html')
RESET_PASSWORD_TEMPLATE_PATH = os.path.join(
    BASE_DIR, 'api/static/reset_password_template.html')
VERIFICATION_LOGIN_PATH = os.path.join(
    BASE_DIR, 'api/static/verification_code_login.html')
RESET_PASSWORD_REQUEST_TEMPLATE_PATH = os.path.join(
    BASE_DIR, 'api/static/password_request_reset_template.html')
COUNTRY_CODE_PATH = os.path.join(BASE_DIR, 'api/static/data/country_code.json')
CITY_DATA_PATH = os.path.join(BASE_DIR, 'api/static/data/city.json')
CURRENCIES_PATH = os.path.join(BASE_DIR, 'api/static/data/currencies.json')
DEGREE_CODE_PATH = os.path.join(BASE_DIR, 'api/static/data/degree_code.json')
LANGUAGE_CODE_PATH = os.path.join(
    BASE_DIR, 'api/static/data/language_code.json')
LANGUAGE_TYPE_PATH = os.path.join(BASE_DIR,
                                  'api/static/data/language_type.json')
SKILL_CODE_PATH = os.path.join(BASE_DIR, 'api/static/data/skill_code.json')
CATEGORY_SKILL_PATH = os.path.join(
    BASE_DIR, 'api/static/data/category_skill.json')
QUALIFICATION_PATH = os.path.join(
    BASE_DIR, 'api/static/data/qualification.json')
COUNTRY_PATH = os.path.join(BASE_DIR, 'api/static/data/country.json')
EMAIL_REGISTER_SUCCESS_TEMPLATE_PATH = os.path.join(
    BASE_DIR, "api/static/register_success_email_template.html")
EMPLOYEE_CONTRACT_TEMPLATE_PATH = os.path.join(
    BASE_DIR, "api/static/employment_contract_template.html")
CURRENCY_RATE_PATH = os.path.join(
    BASE_DIR, "api/static/data/currency_rates.json")
CURRENT_CURRENCY_RATE_NEW_PATH = os.path.join(
    BASE_DIR, "api/static/data/currency_rates_new.json")
GEO_LITE2_COUNTRY = os.path.join(
    BASE_DIR, "api/static/data/GeoLite2-Country.mmdb")
CURRENCY_CODE_BY_COUNTRY_CODE = os.path.join(
    BASE_DIR, "api/static/data/currency_code_by_country_code.json")
CV_TEMPLATE_PATH = os.path.join(
    BASE_DIR, "api/static/cv_template.html")


JOB_CODE_PATH = os.path.join(
    BASE_DIR, "api/static/data/job_code.json")


class InfoFilter(logging.Filter):
    def filter(self, record):
        return record.levelno == logging.INFO


class ErrorFilter(logging.Filter):
    def filter(self, record):
        return record.levelno == logging.ERROR


class SuppressTraceback(logging.Filter):
    def filter(self, record):
        if record.exc_info:
            exc_type, exc_value, exc_traceback = record.exc_info
            # Only log the exception type
            record.exc_text = f"{exc_type.__name__}: {exc_value}"
            record.exc_info = None

        return True


class CustomDateFileHandler(TimedRotatingFileHandler):

    def __init__(self, base_filename, when='D', interval=1, backupCount=7, encoding=None, delay=False, utc=False):
        # Append the date to the base filename dynamically
        date_str = datetime.now().strftime('%Y-%m-%d')
        levelFolder = "logs/info"
        self.base_name = base_filename
        if "error" in base_filename:
            levelFolder = "logs/error"
        self.log_dir = os.path.join(BASE_DIR, levelFolder)
        log_filename = f"{os.path.basename(base_filename)}_{date_str}.log"
        print(f"log_dir {self.log_dir}")
        print(f"log_filename {log_filename}")
        # Ensure the directory exists
        if not os.path.exists(self.log_dir):
            os.makedirs(self.log_dir)

        # Construct the full filename
        full_filename = os.path.join(self.log_dir, log_filename)

        super().__init__(full_filename, when, interval, backupCount, encoding, delay, utc)

    def get_new_filename(self):
        """Helper to generate the log filename with the current date."""
        date_str = datetime.now().strftime('%Y-%m-%d')
        log_filename = f"{os.path.basename(self.base_name)}_{date_str}.log"
        path = os.path.join(self.log_dir, log_filename)
        return path

    def doRollover(self):
        """Override rollover to ensure custom naming without conflicting with TimedRotatingFileHandler."""
        # Close the current stream
        if self.stream:
            self.stream.close()

        # Update the base filename for the new log
        self.baseFilename = self.get_new_filename()

        # Open a new log file
        self.stream = self._open()

        all_files = os.listdir(self.log_dir)

        # delete old log files
        if len(all_files) > self.backupCount:
            all_files.sort()
            for i in range(0, len(all_files) - self.backupCount):
                os.remove(os.path.join(self.log_dir, all_files[i]))


LOGGING = {
    'version': 1,
    'disable_existing_loggers': False,
    'formatters': {
        'verbose': {
            'format': '{asctime} {levelname} {message}',
            'style': '{',
        },
    },
    'handlers': {
        'file_info': {
            'level': 'INFO',
            'class': 'core.settings.common.CustomDateFileHandler',
            'base_filename': 'logs/info/log_info',  # Change to base_filename
            'formatter': 'verbose',
            'when': 'D',
            'interval': 1,
            'backupCount': 30,  # 30 days
        },
        'file_error': {
            'level': 'ERROR',
            'class': 'core.settings.common.CustomDateFileHandler',
            'base_filename': 'logs/error/log_error',  # Change to base_filename
            'formatter': 'verbose',
            'when': 'D',
            'interval': 1,
            'backupCount': 30,  # 30 days
        },

    },
    'loggers': {
        'api_logger': {
            'handlers': ['file_info'],
            'level': 'INFO',
            'propagate': True,
        },
        'django.request': {
            'handlers': ['file_error'],
            'level': 'ERROR',
            'propagate': True,
        },
        'django': {
            'handlers': ['file_error'],
            'level': 'ERROR',
            'propagate': True,
        },
    },
}
# Actual directory user files go to
MEDIA_ROOT = os.path.join(os.path.dirname(BASE_DIR), 'media_files')

# URL used to access the media
MEDIA_URL = '/media/'

DEFAULT_CHARSET = 'utf-8'

# config cache
CACHES = {
    'default': {
        'BACKEND': 'django.core.cache.backends.filebased.FileBasedCache',
        'LOCATION': '/var/tmp/django_cache',
    }
}


# config upload file size
DATA_UPLOAD_MAX_MEMORY_SIZE = 5*1024*1024  # 5 MB
FILE_UPLOAD_MAX_MEMORY_SIZE = 5*1024*1024  # 5 MB
