# AsianTech.Link API



## Getting started

To make it easy for you to get started with GitLab, here's a list of recommended next steps.

Already a pro? Just edit this README.md and make it your own. Want to make it easy? [Use the template at the bottom](#editing-this-readme)!

## Development Environment Setup (macOS)

### Prerequisites
- macOS operating system
- Python 3.x (install via Homebrew: `brew install python3`)
- pip3 (comes with Python3)
- virtualenv (install via pip: `pip3 install virtualenv`)

### Setup Development Environment

1. **Create and activate virtual environment**:
```bash
python3 -m venv venv
source venv/bin/activate
```

2. **Install dependencies**:
```bash
pip3 install -r requirements.txt
```

3. **Set up development environment variables**:
```bash
export DJANGO_SETTINGS_MODULE=core.settings.dev
```

4. **Start development server**:
```bash
python3 manage.py runserver
```

The development server will be available at `http://127.0.0.1:8000/`


## Add your files

- [ ] [Create](https://docs.gitlab.com/ee/user/project/repository/web_editor.html#create-a-file) or [upload](https://docs.gitlab.com/ee/user/project/repository/web_editor.html#upload-a-file) files
- [ ] [Add files using the command line](https://docs.gitlab.com/ee/gitlab-basics/add-file.html#add-a-file-using-the-command-line) or push an existing Git repository with the following command:

```
cd existing_repo
git remote add origin https://gitlab.com/dicksonphu/python-api-new.git
git branch -M main
git push -uf origin main
```



## Generating and Applying Translations

To manage translations for different languages, follow these steps:

#### 1. **Generate Translation Templates**:
Generate or update `.po` files for each language you want to support. Replace `<language_code>` with the appropriate language codes (e.g., `en`, `ja`, `id`).

#### 2. **Provide Translations**:
Open each generated `.po` file in `locale/<language_code>/LC_MESSAGES/django.po` and provide translations for the strings marked for translation (`msgid` entries).

#### 3. **Compile Translations**:
Compile `.po` files into `.mo` files that Django can use:
`django-admin compilemessages`

**Example**:
##### generate file .po
```
django-admin makemessages -l en &&
django-admin makemessages -l ja &&
django-admin makemessages -l id &&
django-admin makemessages -l my &&
django-admin makemessages -l ne &&
django-admin makemessages -l vi
```
##### generate file .mo

```django-admin compilemessages```


### Run test api
#### Run test api by run this command:
`python3 manage.py test`
#### For example, to run the file tests\test_api_list_apply_company.py, run the command:
`python manage.py test tests.test_api_list_apply_company`
#### For example to run 1 test case "test_apply_company_list" in file "tests\test_api_list_apply_company.py" then run the command:

```python manage.py test tests.test_api_list_apply_company.URLTest.test_apply_company_list ```

## Export Model DB
#### Command:
`python3 manage.py inspectdb > models.py`


## UPDATE VARIABLE ENV:
#### Command:
`export DJANGO_SETTINGS_MODULE=core.settings.staging `


## Setting Up a Cron Job to Run Every 5 Minutes

This guide will walk you through the process of setting up a cron job to execute a shell script every 5 minutes on your system.

### 1. Edit the Crontab File

To set the cron job to run every 5 minutes, open the crontab file by running the following command:

```bash
cd asiantech.link
```

```bash
crontab -e
```

###  2. Add the Cron Job
In the crontab file, add the following line to run the script every 5 minutes:


```bash
*/5 * * * * /var/www/asiantechlink/code/asiantech.link/scripts/update_apply_status.sh >> /var/www/asiantechlink/code/asiantech.link/logs/update_apply_status.log 2>&1
```