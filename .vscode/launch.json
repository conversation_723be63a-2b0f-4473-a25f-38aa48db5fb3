{
    // Use IntelliSense to learn about possible attributes.
    // Hover to view descriptions of existing attributes.
    // For more information, visit: https://go.microsoft.com/fwlink/?linkid=830387
    "version": "0.2.0",
    "configurations": [
        


    
    

        {
            "name": "Python Debugger: Django",
            "type": "debugpy",
            "request": "launch",
            "program": "${workspaceFolder}/asiantech.link/manage.py",
            "args": [
                "runserver"
            ],
            "django": true
        }
        ,
        {
            "name": "Local Server",
            "type": "debugpy",
            "request": "launch",
            "program": "${workspaceFolder}/asiantech.link/manage.py",
            "args": [
                "runserver",
                "************:8001"
            ],
            "django": true,
            "justMyCode": true
        },
        {
            "name": "Local Server 2",
            "type": "debugpy",
            "request": "launch",
            "program": "${workspaceFolder}/asiantech.link/manage.py",
            "args": [
                "runserver",
                "192.168.1.21:8001"
            ],
            "django": true,
            "justMyCode": true
        },
        {
            "name": "Local Server Stage",
            "type": "debugpy",
            "request": "launch",
            "program": "${workspaceFolder}/asiantech.link/manage.py",
            "args": [
                "runserver",
                "************:8000"
            ],
            "django": true,
            "justMyCode": true
        },
        {
            "name": "NGROK",
            "type": "debugpy",
            "request": "launch",
            "program": "${workspaceFolder}/asiantech.link/manage.py",
            "args": [
                "runserver",
                "localhost:8080"
            ],
            "django": true,
            "justMyCode": true
        },

    ]
}